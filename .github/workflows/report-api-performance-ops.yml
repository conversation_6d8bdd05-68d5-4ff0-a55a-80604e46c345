# Report - API Performance Ops
# This workflow generates automated daily API performance operations reports using AI with observability data
# and sends them to Slack for engineering team visibility
# Test trigger - MCP configuration should now work properly
#
# Prerequisites:
# - Set AUGMENT_ACCESS_TOKEN secret in repository settings
# - Set SLACK_BOT_TOKEN secret for Slack integration
# - Ensure repository has appropriate permissions for OIDC token access
# - Observability workspace should be configured for this repository

name: Report - API Performance Ops

on:
  schedule:
    # Daily at 8:00 AM PST (16:00 UTC) / 8:00 AM PDT (15:00 UTC during daylight saving)
    # Using 15:00 UTC to align with Pacific Daylight Time
    - cron: '0 15 * * *'
  push:
    branches:
      - PLAT-11737/hud-create-daily-hud-heads-up-slack
  workflow_dispatch:
    inputs:
      additional_context:
        description: 'Additional context to append to the daily report prompt (optional)'
        required: false
        type: string
      model:
        description: 'AI model to use for analysis'
        required: true
        default: 'sonnet4'
        type: choice
        options:
          - sonnet4
          - gpt5
      rules_file:
        description: 'Path to rules file (optional, e.g., .github/rules.md)'
        required: false
        type: string
      prompt_name:
        description: 'Prompt file name under .github/reports/prompts (e.g., api-performance-ops.txt)'
        required: false
        default: 'api-performance-ops.txt'
        type: string
      slack_channel:
        description: 'Slack channel to send the report to (e.g., engineering, alerts)'
        required: false
        default: 'hud-heads-up-alerts'
        type: string

permissions:
  id-token: write    # Required for OIDC token generation
  contents: read     # Required for repository access

jobs:
  daily-api-performance-ops:
    name: Generate Daily API Performance Ops
    runs-on: [ runs-on, "run-id=${{ github.run_id }}", runner=nonprod-small, tag=api-performance-ops ]
    timeout-minutes: 30
    
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 1
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '22'
        
      - name: Setup Observability MCP Integration
        uses: ./.github/actions/setup-hud-mcp
        id: setup-observability
        with:
          workspace-name: ${{ github.repository }}
        
      - name: Install AI CLI
        run: |
          echo "Installing AI CLI..."
          npm install -g @augmentcode/auggie
          auggie --version
        
      - name: Generate API Performance Ops
        id: analysis
        run: |
          echo "🚀 Starting daily API performance ops generation..."

          # Set default values for push triggers (which don't have inputs)
          MODEL="${{ inputs.model }}"
          if [ -z "$MODEL" ]; then
            MODEL="sonnet4"
          fi
          echo "Model: $MODEL"
          
          # Read base prompt from .github/reports/prompts/${{ inputs.prompt_name || 'api-performance-ops.txt' }}
          PROMPT_NAME="${{ inputs.prompt_name }}"
          if [ -z "$PROMPT_NAME" ]; then
            PROMPT_NAME="api-performance-ops.txt"
          fi
          PROMPT_PATH=".github/reports/prompts/$PROMPT_NAME"
          if [ ! -f "$PROMPT_PATH" ]; then
            echo "❌ Error: prompt file not found at $PROMPT_PATH"
            exit 1
          fi

          BASE_PROMPT=$(cat "$PROMPT_PATH")
          echo "Base prompt loaded from $PROMPT_PATH"
          
          # Set default slack channel for push triggers
          SLACK_CHANNEL="${{ inputs.slack_channel }}"
          if [ -z "$SLACK_CHANNEL" ]; then
            SLACK_CHANNEL="hud-heads-up-alerts"
          fi

          # Replace {{SLACK_CHANNEL}} placeholder with actual slack_channel input
          FULL_PROMPT=$(echo "$BASE_PROMPT" | sed "s/{{SLACK_CHANNEL}}/$SLACK_CHANNEL/g")
          
          # Combine with additional context if provided
          if [ -n "${{ inputs.additional_context }}" ]; then
            FULL_PROMPT="$FULL_PROMPT

          Additional context: ${{ inputs.additional_context }}"
            echo "Additional context appended to prompt"
          fi
          
          echo "Full prompt: $FULL_PROMPT"
          
          # Add rules file if specified
          RULES_ARG=""
          if [ -n "${{ inputs.rules_file }}" ]; then
            RULES_ARG="--rules ${{ inputs.rules_file }}"
          fi
          
          # Capture AI output and add to step summary
          AI_OUTPUT=$(auggie --instruction "$FULL_PROMPT" \
            --model "$MODEL" \
            --mcp-config ${{ steps.setup-observability.outputs.mcp-config-path }} \
            $RULES_ARG \
            --quiet 2>&1)

          # Save output for Slack step
          echo "AI_OUTPUT<<EOF" >> $GITHUB_ENV
          echo "$AI_OUTPUT" >> $GITHUB_ENV
          echo "EOF" >> $GITHUB_ENV
          
          echo "## 📊 Daily API Performance Ops" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "**Repository:** ${{ github.repository }}" >> $GITHUB_STEP_SUMMARY
          echo "**Model:** $MODEL" >> $GITHUB_STEP_SUMMARY
          echo "**Base Prompt:** $PROMPT_PATH" >> $GITHUB_STEP_SUMMARY
          if [ -n "${{ inputs.additional_context }}" ]; then
            echo "**Additional Context:** ${{ inputs.additional_context }}" >> $GITHUB_STEP_SUMMARY
          fi
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### Analysis Output" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo '```' >> $GITHUB_STEP_SUMMARY
          echo "$AI_OUTPUT" >> $GITHUB_STEP_SUMMARY
          echo '```' >> $GITHUB_STEP_SUMMARY
        env:
          AUGMENT_SESSION_AUTH: '{"accessToken":"${{ secrets.AUGMENT_ACCESS_TOKEN }}","tenantURL":"https://e1.api.augmentcode.com/","scopes":["read","write"]}'
        continue-on-error: true

      - name: Send report to Slack
        if: success()
        run: |
          echo "Sending report to Slack..."
          
          # Extract JSON and clean control characters using Python
          TRIMMED_OUTPUT=$(echo "$AI_OUTPUT" | python3 -c "
          import sys
          import re
          import json
          
          content = sys.stdin.read()
          # Find JSON block between first { and last }
          match = re.search(r'(\{.*\})', content, re.DOTALL)
          if match:
              # Remove control characters except newlines, tabs, and carriage returns
              clean_json = ''.join(char for char in match.group(1) if ord(char) >= 32 or char in '\n\r\t')
              # Validate JSON before outputting
              try:
                  json.loads(clean_json)
                  print(clean_json)
              except json.JSONDecodeError:
                  print('')
          else:
              print('')
          ")
          
          # Check if trimmed output is valid JSON
          if [ -n "$TRIMMED_OUTPUT" ] && echo "$TRIMMED_OUTPUT" | jq empty 2>/dev/null; then
            echo "Trimmed output is valid JSON, sending directly to Slack"
            # Write JSON to temporary file to avoid shell escaping issues
            echo "$TRIMMED_OUTPUT" > /tmp/slack_payload.json
            curl -X POST "https://slack.com/api/chat.postMessage" \
              -H "Authorization: Bearer ${{ secrets.SLACK_BOT_TOKEN }}" \
              -H "Content-Type: application/json" \
              --data-binary @/tmp/slack_payload.json
          else
            echo "Output is not valid JSON, sending as fallback message"
            curl -X POST "https://slack.com/api/chat.postMessage" \
              -H "Authorization: Bearer ${{ secrets.SLACK_BOT_TOKEN }}" \
              -H "Content-Type: application/json" \
              --data "{
                \"channel\": \"${SLACK_CHANNEL}\",
                \"text\": \"📊 Daily API Performance Ops\",
                \"blocks\": [
                  {
                    \"type\": \"header\",
                    \"text\": {
                      \"type\": \"plain_text\",
                      \"text\": \"📊 Daily API Performance Ops\"
                    }
                  },
                  {
                    \"type\": \"section\",
                    \"text\": {
                      \"type\": \"mrkdwn\",
                      \"text\": \"*Repository:* ${{ github.repository }}\\n*Model:* ${MODEL}\\n*Run:* <${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}|View Workflow>\"
                    }
                  },
                  {
                    \"type\": \"section\",
                    \"text\": {
                      \"type\": \"mrkdwn\",
                      \"text\": \"Report generation completed. See workflow artifacts for detailed output.\"
                    }
                  }
                ]
              }"
          fi

      - name: Upload report artifacts
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: daily-api-performance-ops-${{ github.run_number }}
          path: |
            ~/.hud/logs/
          retention-days: 3
          if-no-files-found: warn
      
      - name: Report status
        if: always()
        run: |
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "---" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          if [ "${{ steps.analysis.outcome }}" = "failure" ]; then
            echo "❌ **Report Status:** Failed - Check the logs for details." >> $GITHUB_STEP_SUMMARY
          else
            echo "✅ **Report Status:** Completed successfully." >> $GITHUB_STEP_SUMMARY
          fi
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "📁 **Artifacts:** Report logs are available in the workflow artifacts." >> $GITHUB_STEP_SUMMARY

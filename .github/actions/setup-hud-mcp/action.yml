name: 'Setup Hud MCP'
description: 'Downloads Hud MCP binaries, configures authentication, and sets up MCP file ready to be used'
author: 'Hud Team'

inputs:
  hud-token-endpoint:
    description: 'Hud token exchange endpoint'
    required: false
    default: 'https://api-prod.hud.io/github-oidc/token-exchange'
  hud-api-base-url:
    description: 'Hud API base URL'
    required: false
    default: 'https://api-prod.hud.io'
  hud-audience:
    description: 'OIDC audience for Hud token'
    required: false
    default: 'https://api-prod.hud.io/'
  mcp-binary-url:
    description: 'URL to download MCP binary'
    required: false
    default: 'https://cdn.hud.io/claude-action/mcp-linux-x64-v1.0.13'
  ide-tools-binary-url:
    description: 'URL to download IDE Tools binary'
    required: false
    default: 'https://cdn.hud.io/claude-action/hud-ide-tools-linux-x64-v0.0.1'
  workspace-name:
    description: 'Workspace name key for <PERSON><PERSON>'
    required: false
    default: ${{ github.repository }}

outputs:
  mcp-config-path:
    description: 'Path to the generated MCP config file'
    value: ${{ steps.setup-mcp.outputs.mcp-config-path }}
  hud-config-path:
    description: 'Path to the generated Hud config file'
    value: ${{ steps.setup-hud.outputs.hud-config-path }}

runs:
  using: 'composite'
  steps:
    - name: Download Hud MCP binary
      shell: bash
      run: |
        echo "Downloading MCP binary from ${{ inputs.mcp-binary-url }}..."
        curl -L -o mcp-linux-x64 "${{ inputs.mcp-binary-url }}"
        chmod +x mcp-linux-x64
        echo "Downloaded MCP binary: $(ls -la mcp-linux-x64)"

    - name: Download Hud IDE Tools binary
      shell: bash
      run: |
        echo "Downloading IDE Tools binary from ${{ inputs.ide-tools-binary-url }}..."
        curl -L -o hud-ide-tools-linux-x64 "${{ inputs.ide-tools-binary-url }}"
        chmod +x hud-ide-tools-linux-x64
        echo "Downloaded IDE Tools binary: $(ls -la hud-ide-tools-linux-x64)"

    - name: Get Hud access token via OIDC
      id: get-token
      shell: bash
      run: |
        echo "Getting Hud access token using GitHub OIDC..."
        
        # Get GitHub OIDC token
        GITHUB_TOKEN=$(curl -s -H "Authorization: bearer $ACTIONS_ID_TOKEN_REQUEST_TOKEN" \
          "$ACTIONS_ID_TOKEN_REQUEST_URL&audience=${{ inputs.hud-audience }}" | jq -r '.value')
        
        if [ "$GITHUB_TOKEN" = "null" ] || [ -z "$GITHUB_TOKEN" ]; then
          echo "Error: Failed to get GitHub OIDC token"
          exit 1
        fi
        
        echo "✅ GitHub OIDC token obtained successfully"
        
        # Exchange GitHub token for Hud access token
        HUD_TOKEN_RESPONSE=$(curl -s -X POST "${{ inputs.hud-token-endpoint }}" \
          -H "Authorization: Bearer $GITHUB_TOKEN" \
          -H "Content-Type: application/json")
        
        HUD_ACCESS_TOKEN=$(echo "$HUD_TOKEN_RESPONSE" | jq -r '.access_token')
        
        if [ "$HUD_ACCESS_TOKEN" = "null" ] || [ -z "$HUD_ACCESS_TOKEN" ]; then
          echo "❌ Error: Failed to get Hud access token"
          echo "Response: $HUD_TOKEN_RESPONSE"
          exit 1
        fi
        
        echo "✅ Hud access token obtained successfully"
        echo "ACCESS_TOKEN=$HUD_ACCESS_TOKEN" >> $GITHUB_OUTPUT

    - name: Setup Hud configuration
      id: setup-hud
      shell: bash
      run: |
        echo "⚙️ Setting up Hud configuration..."
        
        # Create .hud directory
        mkdir -p ~/.hud/logs
        
        # Create Hud config file
        HUD_CONFIG_PATH="$HOME/.hud/config.json"
        cat > "$HUD_CONFIG_PATH" << EOF
        {
          "accessToken": "${{ steps.get-token.outputs.ACCESS_TOKEN }}",
          "baseUrl": "${{ inputs.hud-api-base-url }}",
          "detectionUrl": "${{ inputs.hud-api-base-url }}",
          "logLevel": "info",
          "logEndpoint": "https://yqp37dddrsewm6wdwwpwfraxqm0shdmx.lambda-url.eu-central-1.on.aws",
          "ideToolsPath": "$(pwd)/hud-ide-tools-linux-x64"
        }
        EOF
        
        echo "✅ Hud configuration created at: $HUD_CONFIG_PATH"
        echo "hud-config-path=$HUD_CONFIG_PATH" >> $GITHUB_OUTPUT

    - name: Create MCP configuration
      id: setup-mcp
      shell: bash
      run: |
        echo "🔧 Creating MCP configuration..."
        
        MCP_CONFIG_PATH="$(pwd)/.mcp.json"
        cat > "$MCP_CONFIG_PATH" << EOF
        {
          "mcpServers": {
            "hud": {
              "command": "$(pwd)/mcp-linux-x64",
              "args": ["start"],
              "env": {
                "WORKSPACE_NAME_KEY": "${{ inputs.workspace-name }}"
              }
            }
          }
        }
        EOF
        
        echo "✅ MCP configuration created at: $MCP_CONFIG_PATH"
        echo "mcp-config-path=$MCP_CONFIG_PATH" >> $GITHUB_OUTPUT

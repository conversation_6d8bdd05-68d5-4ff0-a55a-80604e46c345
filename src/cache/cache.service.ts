/* eslint-disable no-await-in-loop */
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Inject, Injectable } from '@nestjs/common';
import { CachedEntityWrapper } from 'cache/types/cached-entity-wrapper.type';
import { ScanDeleteOptions } from 'cache/types/scan-delete-options.type';
import { plainToInstance } from 'class-transformer';
import config from 'config';
import tracer from 'dd-trace';
import { Cluster, Redis } from 'ioredis';
import { chunk, isNil } from 'lodash';
import { ClsServiceManager } from 'nestjs-cls';
import { PolloMessage } from 'pollo-logger/classes/pollo.message';
import { PolloLogger } from 'pollo-logger/pollo.logger';

const BUFFERED_CHUNK_SIZE = 50;

const CIRCUIT_BREAKER_TIMEOUT_MS = 1000;
const HEALTH_CHECK_INITIAL_DELAY_MS = 5000;
const HEALTH_CHECK_MAX_DELAY_MS = 30000;
const HEALTH_CHECK_BACKOFF_MULTIPLIER = 1.5;

/**
 * Entity registry that maps class names to their constructors
 * Built from TypeORM entity configurations
 */
const ENTITY_REGISTRY = new Map<string, new (...args: unknown[]) => unknown>();

// Initialize entity registry with all TypeORM entities
async function initializeEntityRegistry(): Promise<void> {
    if (ENTITY_REGISTRY.size > 0) {
        return; // Already initialized
    }

    // Lazy-load TypeORM configs to avoid circular dependencies
    const { globalDatabaseConfig, tenantDatabaseConfig } = await import(
        'commons/configs/typeorm.config'
    );

    // Add global database entities
    globalDatabaseConfig.entities.forEach((EntityClass: new (...args: unknown[]) => unknown) => {
        ENTITY_REGISTRY.set(EntityClass.name, EntityClass);
    });

    // Add tenant database entities
    tenantDatabaseConfig.entities.forEach((EntityClass: new (...args: unknown[]) => unknown) => {
        ENTITY_REGISTRY.set(EntityClass.name, EntityClass);
    });
}

const DEFAULT_TTL_SECONDS = 3600;
const REGISTRY_TTL_BUFFER_SECONDS = 3600;

const CACHE_SERVICE_CONTEXT = 'CacheService';

/**
 * Extract Redis Cluster hash tag (content inside {...}) from a key, if present.
 * Keys that share the same hash tag will hash to the same slot.
 */
export function extractHashTag(key: string): string | null {
    const match = key.match(/\{([^}]+)\}/);
    return match ? match[1] : null;
}

/**
 * Compute the registry key for a given normalized cache key.
 * - Preserves any existing Redis hash tag so the registry shares the same slot
 *   as the original key (avoiding CROSSSLOT issues when batching ops per account/workspace).
 * - For non-prefixed keys, groups by the common underscore prefix.
 * - Falls back to a global registry with an explicit {global} tag.
 */
export function computeRegistryKey(normKey: string, globalPrefix?: string): string | null {
    const gp =
        globalPrefix || config.get<string>('cache.globalPrefix')?.toLowerCase() || 'globalstore';
    if (!normKey) return null;

    if (normKey.startsWith(gp + ':')) {
        // Prefixed key – create registry at namespace level (drop final data part)
        const keyParts = normKey.split(':');
        if (keyParts.length < 2) {
            return null;
        }
        const namespaceParts = keyParts.slice(0, -1);
        return `${namespaceParts.join(':')}:_registry`;
    }

    // Non-prefixed key – derive a registry based on naming convention
    const keyParts = normKey.split('_');
    if (keyParts.length >= 2) {
        const registryNamespace = keyParts.slice(0, -1).join('_');
        return `${registryNamespace}:_registry`;
    }

    // Fallback – global registry with explicit global hash tag
    return `${gp}:{global}:_registry`;
}

/**
 * Validate that a non-lock cache key contains a proper Redis hash tag and expected structure.
 * Throws errors for invalid/missing tag; lock keys ({lock}) are exempt.
 * Callers should try/catch and log.
 */
export type CacheOpType = 'get' | 'set' | 'del' | 'unlink' | 'multiDel';
export function validateKeyForNonLock(normKey: string, operation: CacheOpType): void {
    const gp = (config.get<string>('cache.globalPrefix') || 'globalstore').toLowerCase();

    const tag = extractHashTag(normKey);
    if (!tag) {
        throw new Error(
            `[CACHE KEY VALIDATION] Missing hash tag {…} for ${operation} => ${normKey}`,
        );
    }
    if (tag === 'lock') {
        // Exempt lock keys
        return;
    }
    if (
        !(
            /^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/.test(
                tag,
            ) || tag === 'global'
        )
    ) {
        throw new Error(
            `[CACHE KEY VALIDATION] Invalid hash tag '${tag}' for ${operation} => ${normKey}; expected 'global' or account UUID`,
        );
    }

    // Structural check: enforce standard structure for non-registry keys
    // Standard: globalPrefix:{tag}:namespace:keyname (exactly 4 segments)
    // Only exception that may exceed 4 is registry keys ending with "_registry" (legacy 5-segment registries)
    const parts = normKey.split(':');
    if (parts[0] !== gp) {
        throw new Error(
            `[CACHE KEY VALIDATION] Key should start with '${gp}:' for ${operation} => ${normKey}`,
        );
    }
    if (parts[1] !== `{${tag}}`) {
        throw new Error(
            `[CACHE KEY VALIDATION] Expected hash tag segment to be second part for ${operation} => ${normKey}`,
        );
    }
    const isRegistryKey = parts[parts.length - 1] === '_registry';
    if (!isRegistryKey && parts.length !== 4) {
        throw new Error(
            `[CACHE KEY VALIDATION] Expected 4 segments '${gp}:{global|uuid}:namespace:keyname' for ${operation} => ${normKey} (got ${parts.length})`,
        );
    }
    if (isRegistryKey && parts.length > 5) {
        throw new Error(
            `[CACHE KEY VALIDATION] Registry key should not exceed 5 segments for ${operation} => ${normKey} (got ${parts.length})`,
        );
    }
}

/**
 * By default, the Nest Cache decorators do not normalize keys.
 * Because application usage is inconsistent, we unify them:
 *
 * 1) remove double & single quotes
 * 2) toLowerCase
 */
function decoratorNormalizeKey(raw: string): string {
    let newKey: string;
    try {
        newKey = raw.replace(/["']/g, '');
        newKey = newKey.toLowerCase();
    } catch (error) {
        PolloLogger.logger('CacheService').log(
            PolloMessage.msg(`Error normalizing key => ${error.message}`).setError(error),
        );
        // If normalization fails, just keep the original key
        newKey = raw;
    }
    return newKey;
}

/**
 * The CacheService is our direct injection point for using the
 * ioredis.
 *
 * With added logs and key normalization for consistency.
 */
@Injectable()
export class CacheService {
    private logger = PolloLogger.logger('CacheService');

    // Service-local cache suspension flag for circuit breaker
    private cacheIsSuspended = false;

    // Health check timer to prevent concurrent health checks
    private healthCheckTimer: ReturnType<typeof setTimeout> | null = null;

    constructor(
        @Inject(CACHE_MANAGER)
        private cache: Redis | Cluster,
    ) {}

    /**
     * Performs a health check on Redis to determine if it's responsive
     */
    private async performHealthCheck(): Promise<boolean> {
        try {
            // Use a simple PING command with timeout to test Redis health
            const result = await this.withTimeout(
                this.cache.ping(),
                CIRCUIT_BREAKER_TIMEOUT_MS,
                'get',
            );
            return result === 'PONG';
        } catch (error) {
            return false;
        }
    }

    /**
     * Determines if an error is timeout-related and should trigger the circuit breaker
     * Uses proper error properties instead of string matching for reliability
     */
    private isTimeoutError(
        error: Error & { code?: string; errno?: string; name?: string },
    ): boolean {
        // Our circuit breaker timeout error
        if (error?.message === 'Redis timeout') {
            return true;
        }

        // ioredis ETIMEDOUT connection timeout
        if (error?.code === 'ETIMEDOUT' || error?.errno === 'ETIMEDOUT') {
            return true;
        }

        // ioredis MaxRetriesPerRequestError when max reconnection attempts reached
        if (error?.name === 'MaxRetriesPerRequestError') {
            return true;
        }

        // ioredis commandTimeout (when configured)
        if (error?.name === 'TimeoutError') {
            return true;
        }

        return false;
    }

    /**
     * Schedules a health check to re-enable cache when Redis becomes responsive
     */
    private scheduleHealthCheck(delayMs: number = HEALTH_CHECK_INITIAL_DELAY_MS): void {
        // Clear any existing health check timer to prevent concurrent checks
        if (this.healthCheckTimer) {
            clearTimeout(this.healthCheckTimer);
            this.healthCheckTimer = null;
        }

        this.healthCheckTimer = setTimeout(() => {
            // Clear the timer reference since we're running now
            this.healthCheckTimer = null;

            this.performHealthCheck()
                .then(isHealthy => {
                    if (isHealthy) {
                        this.logger.log(
                            PolloMessage.msg(
                                'Redis health check passed, re-enabling cache',
                            ).setContext('CacheService'),
                        );
                        this.cacheIsSuspended = false;
                    } else {
                        // Schedule another health check with exponential backoff (max 30 seconds)
                        const nextDelay = Math.min(
                            delayMs * HEALTH_CHECK_BACKOFF_MULTIPLIER,
                            HEALTH_CHECK_MAX_DELAY_MS,
                        );
                        this.scheduleHealthCheck(nextDelay);
                    }
                })
                .catch(error => {
                    this.logHint(`Redis health check failed: ${error.message}`, 'CacheService');
                    // Schedule another health check on error
                    const nextDelay = Math.min(
                        delayMs * HEALTH_CHECK_BACKOFF_MULTIPLIER,
                        HEALTH_CHECK_MAX_DELAY_MS,
                    );
                    this.scheduleHealthCheck(nextDelay);
                });
        }, delayMs);
    }

    /**
     * Wraps Redis operations with a timeout to prevent blocking operations
     * Returns null on timeout for GET operations, silently fails for SET/DEL/UNLINK operations.
     */
    private async withTimeout<T>(
        operation: Promise<T>,
        timeoutMs: number = CIRCUIT_BREAKER_TIMEOUT_MS,
        operationType: 'get' | 'set' | 'del' | 'unlink' = 'get',
    ): Promise<T | null> {
        // Fast-fail if cache is already suspended
        if (this.cacheIsSuspended) {
            return null;
        }

        // Prevent unhandled rejections: if operation loses the race and rejects later,
        // this catch handler will suppress the unhandled rejection
        operation.catch(() => {
            // Intentionally empty - error is handled by the race's catch block if operation wins
            // This only fires if operation loses the race and rejects afterward. Which we dont
            // care about since the race has already been lost.
        });

        try {
            return await Promise.race([
                operation,
                new Promise<never>((_, reject) =>
                    setTimeout(() => reject(new Error('Redis timeout')), timeoutMs),
                ),
            ]);
        } catch (error) {
            // Determine if this is a timeout-related error that should trigger circuit breaker
            const isTimeoutError = this.isTimeoutError(error);

            if (isTimeoutError) {
                this.logHint(
                    `Redis ${operationType} operation timed out after ${timeoutMs}ms (${error.message})`,
                    'CacheService',
                );

                // Suspend cache temporarily on timeouts to prevent cascade failures
                const wasAlreadySuspended = this.cacheIsSuspended;
                this.cacheIsSuspended = true;

                if (!wasAlreadySuspended) {
                    this.scheduleHealthCheck();
                }
            } else {
                this.logger.error(
                    PolloMessage.msg(`Redis ${operationType} operation failed: ${error.message}`)
                        .setContext('CacheService')
                        .setError(error),
                );
            }

            // Return null for GET operations (triggers cache miss fallback)
            // Silent failure for SET/DEL operations
            return null;
        }
    }

    /**
     * GET => logs key + normalizes the key
     */
    async get<T>(key: string, type?: new (...args: unknown[]) => T): Promise<T | null> {
        let result: T | null = null;
        try {
            const normKey = decoratorNormalizeKey(key);
            this.logHint(
                `CacheService GET => rawKey="${key}" => normKey="${normKey}"`,
                'CacheService',
            );
            // Validate key structure (non-lock)
            try {
                validateKeyForNonLock(normKey, 'get');
            } catch (error) {
                this.logger.warn(
                    PolloMessage.msg(String((error as Error).message))
                        .setContext(CACHE_SERVICE_CONTEXT)
                        .setSubContext('validateKey'),
                );
            }

            // Check CLS request cache first
            const requestCache = this.getRequestCache();
            if (requestCache && requestCache.has(normKey)) {
                this.logHint(`[CLS Cache HIT] ${normKey}`, 'CacheService');
                return requestCache.get(normKey) as T;
            }

            const raw = await this.withTimeout(
                this.cache.get(normKey),
                CIRCUIT_BREAKER_TIMEOUT_MS,
                'get',
            );
            if (isNil(raw)) {
                result = null;
            } else {
                const parsed = JSON.parse(raw);

                // Check if it's wrapped
                if (parsed && typeof parsed === 'object' && 'data' in parsed) {
                    const wrapper = parsed as CachedEntityWrapper<T>;
                    const data = wrapper.data;

                    if (isNil(data)) {
                        result = null;
                    } else if (type) {
                        result = plainToInstance(type, data);
                    } else if (wrapper.entityClassName && wrapper.entityClassName !== 'object') {
                        // Initialize entity registry on first use
                        await initializeEntityRegistry();

                        const EntityClass = ENTITY_REGISTRY.get(wrapper.entityClassName);
                        if (EntityClass) {
                            result = plainToInstance(
                                EntityClass as new (...args: unknown[]) => T,
                                data,
                            );
                        } else {
                            // No hydration possible, return data as-is
                            result = data;
                        }
                    } else {
                        // No hydration possible, return data as-is
                        result = data;
                    }
                } else {
                    // Legacy unwrapped value - hydrate if type provided
                    if (type) {
                        result = plainToInstance(type, parsed);
                    } else {
                        result = parsed as T;
                    }
                }
            }
        } catch (error) {
            this.logger.warn(
                PolloMessage.msg(`Error in GET => ${error.message}`)
                    .setContext('CacheService')
                    .setError(error),
            );
            result = null;
        }

        // Store successful results in CLS request cache for subsequent requests within the same request
        const requestCache = this.getRequestCache();
        if (requestCache && result !== null) {
            const normKey = decoratorNormalizeKey(key);
            requestCache.set(normKey, result);
        }

        return result;
    }

    /**
     * SET => logs key + optional TTL, then normalizes the key
     * Automatically adds prefixed cache keys to registry for fast invalidation
     */
    async set<T>(
        key: string,
        value: T,
        ttlOrTtlOption?: number | { ttl: number },
        type?: new (...args: unknown[]) => T,
    ): Promise<T> {
        const normKey = decoratorNormalizeKey(key);
        this.logHint(
            `CacheService SET => rawKey="${key}" => normKey="${normKey}" ttl="${
                typeof ttlOrTtlOption === 'number' ? ttlOrTtlOption : ttlOrTtlOption?.ttl || 'N/A'
            }"`,
            'CacheService',
        );
        // Validate key structure (non-lock)
        try {
            validateKeyForNonLock(normKey, 'set');
        } catch (error) {
            this.logger.warn(
                PolloMessage.msg(String((error as Error).message))
                    .setContext(CACHE_SERVICE_CONTEXT)
                    .setSubContext('validateKey'),
            );
        }

        try {
            // Determine className: prefer type parameter, fallback to constructor.name
            let className = 'object';
            if (type) {
                // Use provided type parameter (most reliable)
                className = type.name;
            } else if (
                value &&
                typeof value === 'object' &&
                value.constructor &&
                value.constructor.name
            ) {
                // Fallback to auto-detection from constructor
                className = value.constructor.name;
                // Skip generic names that aren't useful for hydration
                if (className === 'Object' || className === 'Array') {
                    className = 'object';
                }
            }

            // Create wrapper with metadata
            const wrapper: CachedEntityWrapper<T> = {
                data: value,
                entityClassName: className,
                relationships: [],
                reverseDependencies: { lists: [], entities: [] },
                lookupKeys: {},
            };

            const serialized = JSON.stringify(wrapper);
            const ttl =
                typeof ttlOrTtlOption === 'number'
                    ? ttlOrTtlOption
                    : ttlOrTtlOption?.ttl || DEFAULT_TTL_SECONDS;

            await this.withTimeout(
                this.cache.setex(normKey, ttl, serialized),
                CIRCUIT_BREAKER_TIMEOUT_MS,
                'set',
            );

            // Store in CLS request cache for immediate access within the same request
            const requestCache = this.getRequestCache();
            if (requestCache) {
                requestCache.set(normKey, value);
            }

            // Auto-register prefixed cache keys for fast invalidation
            await this.autoRegisterCacheKey(normKey, ttl);
        } catch (error) {
            this.logger.warn(
                PolloMessage.msg(`Error in SET => ${error.message}`)
                    .setContext('CacheService')
                    .setError(error),
            );
            // Return the value even if registration fails
        }
        return value;
    }

    /**
     * DEL => logs key + normalizes + auto-removes from registry
     */
    async del(key: string): Promise<void> {
        try {
            const normKey = decoratorNormalizeKey(key);
            this.logHint(
                `CacheService DEL => rawKey="${key}" => normKey="${normKey}"`,
                'CacheService',
            );
            try {
                validateKeyForNonLock(normKey, 'del');
            } catch (error) {
                this.logger.warn(
                    PolloMessage.msg(String((error as Error).message))
                        .setContext(CACHE_SERVICE_CONTEXT)
                        .setSubContext('validateKey'),
                );
            }

            await this.withTimeout(this.cache.del(normKey), CIRCUIT_BREAKER_TIMEOUT_MS, 'del');

            // Remove from CLS request cache as well
            const requestCache = this.getRequestCache();
            if (requestCache) {
                requestCache.delete(normKey);
            }

            // Auto-remove from registry if it's a prefixed key
            await this.autoUnregisterCacheKey(normKey);
        } catch (error) {
            this.logger.warn(
                PolloMessage.msg(`Error in DEL => ${error.message}`)
                    .setContext('CacheService')
                    .setError(error),
            );
        }
    }

    /**
     * UNLINK => logs key + normalizes + auto-removes from registry
     *
     * This command is very similar to DEL: it removes the specified keys.
     * However the command performs the actual memory reclaiming in a different thread,
     * so it is not blocking, while DEL is. The actual removal will happen later asynchronously.
     */
    async unlink(key: string): Promise<void> {
        try {
            const normKey = decoratorNormalizeKey(key);
            this.logHint(
                `CacheService UNLINK => rawKey="${key}" => normKey="${normKey}"`,
                'CacheService',
            );
            try {
                validateKeyForNonLock(normKey, 'unlink');
            } catch (error) {
                this.logger.warn(
                    PolloMessage.msg(String((error as Error).message))
                        .setContext(CACHE_SERVICE_CONTEXT)
                        .setSubContext('validateKey'),
                );
            }
            await this.withTimeout(
                this.cache.unlink(normKey),
                CIRCUIT_BREAKER_TIMEOUT_MS,
                'unlink',
            );

            // Remove from CLS request cache as well
            const requestCache = this.getRequestCache();
            if (requestCache) {
                requestCache.delete(normKey);
            }

            await this.autoUnregisterCacheKey(normKey);
        } catch (error) {
            this.logger.warn(
                PolloMessage.msg(`Error in UNLINK => ${error.message}`)
                    .setContext('CacheService')
                    .setError(error),
            );
        }
    }

    async multiDel(keys: string[]): Promise<void> {
        try {
            this.logHint(`CacheService MULTIDEL`, this.constructor.name, this.multiDel.name);

            if (keys.length === 0) {
                return;
            }

            const normKeys = keys.map(key => decoratorNormalizeKey(key));
            for (const normKey of normKeys) {
                try {
                    validateKeyForNonLock(normKey, 'multiDel');
                } catch (error) {
                    this.logger.warn(
                        PolloMessage.msg(`${String((error as Error).message)} (key: ${normKey})`)
                            .setContext(CACHE_SERVICE_CONTEXT)
                            .setSubContext('validateKey'),
                    );
                }
            }

            // Group keys by Redis hash tag to avoid CROSSSLOT errors when using multi-key DEL
            const keysByTag = new Map<string, string[]>();
            for (const k of normKeys) {
                const tag = extractHashTag(k);
                if (tag) {
                    if (!keysByTag.has(tag)) {
                        keysByTag.set(tag, []);
                    }

                    keysByTag.get(tag)?.push(k);
                } else {
                    // No hashtag present - delete individually to avoid CROSSSLOT
                    await this.withTimeout(this.cache.del(k), CIRCUIT_BREAKER_TIMEOUT_MS, 'del');
                }
            }
            // For keys that share a hashtag, we can safely MDEL per tag group
            for (const [, group] of keysByTag) {
                if (group.length === 1) {
                    await this.withTimeout(
                        this.cache.del(group[0]),
                        CIRCUIT_BREAKER_TIMEOUT_MS,
                        'del',
                    );
                } else {
                    await this.withTimeout(
                        this.cache.del(...group),
                        CIRCUIT_BREAKER_TIMEOUT_MS,
                        'del',
                    );
                }
            }

            // Remove from CLS request cache as well
            const requestCache = this.getRequestCache();
            if (requestCache) {
                for (const normKey of normKeys) {
                    requestCache.delete(normKey);
                }
            }

            // Auto-remove from registry in batch
            await this.batchUnregisterCacheKeys(normKeys);
        } catch (error) {
            this.logger.warn(
                PolloMessage.msg(`Error in multiDel => ${error.message}`)
                    .setContext(this.constructor.name)
                    .setSubContext(this.multiDel.name)
                    .setError(error),
            );
        }
    }

    async scanDel(options: ScanDeleteOptions): Promise<void> {
        try {
            // cleanup
            const match = decoratorNormalizeKey(options.match);
            this.logHint(`CacheService SCANDEL`, this.constructor.name, this.scanDel.name);

            // Send Datadog metric for SCAN operation
            tracer.dogstatsd.increment('cache.scan.operation', 1, {
                pattern: match.split(':').slice(0, 3).join(':'), // Extract pattern prefix for grouping
                operation: 'scanDel',
            });

            /*** PREVIOUS BEHAVIOR
             * Relied on the master node explicitly - we should use the interface
             *
            const nodes = (this.cache as Cluster).nodes('master');
            for (const node of nodes) {
                await node.scanStream(options).on('data', async (keys: string[]) => {
                    await node.del(...keys);
                });
            }
            */

            let cursor: string = '0';
            do {
                const [newCursor, keys] = (await this.withTimeout(
                    this.cache.scanBuffer(cursor, 'MATCH', match, 'COUNT', options.count),
                    CIRCUIT_BREAKER_TIMEOUT_MS,
                    'del',
                )) ?? ['0', []];
                cursor = newCursor.toString();
                if (keys.length > 0) {
                    // Since keys are not yet colocated, we need to delete them one by one
                    for (const key of keys) {
                        await this.withTimeout(
                            this.cache.unlink(key.toString()),
                            CIRCUIT_BREAKER_TIMEOUT_MS,
                            'del',
                        );
                    }
                }
            } while (cursor !== '0');
        } catch (error) {
            this.logger.warn(
                PolloMessage.msg(`Error in scanDel => ${error.message}`)
                    .setContext(this.constructor.name)
                    .setSubContext(this.scanDel.name)
                    .setError(error),
            );
        }
    }

    /**
     * KEYS => logs pattern, but typically we do NOT normalize the wildcard.
     * If your decorators were normalizing patterns, you'd do it here too.
     * For now, we keep the raw pattern since wildcard usage might break if forced to lowerCase, etc.
     */
    async keys(pattern = '*'): Promise<any[]> {
        let results: any[] = [];
        try {
            this.logHint(`CacheService KEYS => pattern="${pattern}"`, 'CacheService');

            results =
                (await this.withTimeout(
                    this.cache.keys(pattern),
                    CIRCUIT_BREAKER_TIMEOUT_MS,
                    'get',
                )) || [];
        } catch (error) {
            this.logger.error(
                PolloMessage.msg(`Error in KEYS => ${error.message}`)
                    .setContext('CacheService')
                    .setError(error),
            );
        }
        return results;
    }

    /**
     * @deprecated use acquireLock() instead
     *
     * setNx => example concurrency lock usage if needed
     *  - "NX" means only set if the key does not already exist
     */
    async setNx<T>(
        key: string,
        value: T,
        ttlOrTtlOption?: number | { ttl: number },
    ): Promise<string | null> {
        let result: string | null = null;
        try {
            const normKey = decoratorNormalizeKey(key);
            const numericTtl =
                typeof ttlOrTtlOption === 'object' &&
                typeof ttlOrTtlOption?.ttl === 'number' &&
                ttlOrTtlOption.ttl > 0
                    ? ttlOrTtlOption.ttl
                    : typeof ttlOrTtlOption === 'number' && ttlOrTtlOption > 0
                      ? ttlOrTtlOption
                      : DEFAULT_TTL_SECONDS;
            const toStore = typeof value === 'object' ? JSON.stringify(value) : String(value);

            this.logHint(
                `CacheService SETNX => rawKey="${key}" => normKey="${normKey}" ttl="${numericTtl}"`,
                'CacheService',
            );

            result = await this.withTimeout(
                this.cache.set(normKey, toStore, 'EX', numericTtl, 'NX'),
                CIRCUIT_BREAKER_TIMEOUT_MS,
                'set',
            );
        } catch (error) {
            this.logger.error(
                PolloMessage.msg(`Error in SETNX => ${error.message}`)
                    .setContext('CacheService')
                    .setError(error),
            );
        }
        return result;
    }

    /**
     * SADD => Add members to a Redis SET
     */
    async sadd(key: string, ...members: string[]): Promise<number> {
        let result = 0;
        try {
            const normKey = decoratorNormalizeKey(key);
            this.logHint(
                `CacheService SADD => rawKey="${key}" => normKey="${normKey}" members="${members.length}"`,
                'CacheService',
            );

            // "Chunk" the members array into chunks of BUFFERED_CHUNK_SIZE to give
            // redis some breathing room
            const chunks = chunk(members, BUFFERED_CHUNK_SIZE);
            for (const ch of chunks) {
                result +=
                    (await this.withTimeout(
                        this.cache.sadd(normKey, ...ch),
                        CIRCUIT_BREAKER_TIMEOUT_MS,
                        'set',
                    )) || 0;
            }
        } catch (error) {
            this.logger.error(
                PolloMessage.msg(`Error in SADD => ${error.message}`)
                    .setContext('CacheService')
                    .setError(error),
            );
        }
        return result;
    }

    /**
     * SMEMBERS => Get all members of a Redis SET using non-blocking SSCAN
     */
    async smembers(key: string): Promise<string[]> {
        let result: string[] = [];
        try {
            const normKey = decoratorNormalizeKey(key);
            this.logHint(
                `CacheService SMEMBERS => rawKey="${key}" => normKey="${normKey}"`,
                'CacheService',
            );

            let cursor: string = '0';
            do {
                const [newCursor, members] = (await this.withTimeout(
                    this.cache.sscan(normKey, cursor, 'COUNT', BUFFERED_CHUNK_SIZE),
                    CIRCUIT_BREAKER_TIMEOUT_MS,
                    'get',
                )) ?? ['0', []];
                cursor = newCursor;
                result = result.concat(members);
            } while (cursor !== '0');
        } catch (error) {
            this.logger.error(
                PolloMessage.msg(`Error in SMEMBERS => ${error.message}`)
                    .setContext('CacheService')
                    .setError(error),
            );
        }
        return result;
    }

    /**
     * EXPIRE => Set TTL on a key
     */
    async expire(key: string, seconds: number): Promise<number> {
        let result = 0;
        try {
            const normKey = decoratorNormalizeKey(key);
            this.logHint(
                `CacheService EXPIRE => rawKey="${key}" => normKey="${normKey}" ttl="${seconds}"`,
                'CacheService',
            );

            result =
                (await this.withTimeout(
                    this.cache.expire(normKey, seconds),
                    CIRCUIT_BREAKER_TIMEOUT_MS,
                    'set',
                )) || 0;
        } catch (error) {
            this.logger.error(
                PolloMessage.msg(`Error in EXPIRE => ${error.message}`)
                    .setContext('CacheService')
                    .setError(error),
            );
        }
        return result;
    }

    /**
     * TTL => Get remaining time to live for a key
     */
    async ttl(key: string): Promise<number> {
        let result = -1;
        try {
            const normKey = decoratorNormalizeKey(key);
            this.logHint(
                `CacheService TTL => rawKey="${key}" => normKey="${normKey}"`,
                'CacheService',
            );

            result =
                (await this.withTimeout(
                    this.cache.ttl(normKey),
                    CIRCUIT_BREAKER_TIMEOUT_MS,
                    'get',
                )) || -1;
        } catch (error) {
            this.logger.error(
                PolloMessage.msg(`Error in TTL => ${error.message}`)
                    .setContext('CacheService')
                    .setError(error),
            );
        }
        return result;
    }

    /**
     * Automatically register cache keys for fast invalidation.
     * Creates a single registry at the actual namespace level provided.
     *
     * For key: globalPrefix:accountId:store:workspace:data
     * Creates registry: globalPrefix:accountId:store:workspace:_registry
     *
     * For key: globalPrefix:accountId:store:data
     * Creates registry: globalPrefix:accountId:store:_registry
     *
     * For non-prefixed keys (e.g., simple cache keys):
     * Creates registry based on the pattern
     */
    private async autoRegisterCacheKey(normKey: string, ttl?: number): Promise<void> {
        try {
            const globalPrefix =
                config.get<string>('cache.globalPrefix')?.toLowerCase() || 'globalstore';

            const registryKey = computeRegistryKey(normKey, globalPrefix);
            if (!registryKey) {
                return;
            }

            // Register the cache key in the single appropriate registry
            const registryTtl = (ttl || DEFAULT_TTL_SECONDS) + REGISTRY_TTL_BUFFER_SECONDS; // +1 hour

            // Always update the registry expiration to ensure it outlives all cache entries
            // This is important because new cache entries might have longer TTLs than existing ones
            try {
                // Use pipeline for atomic registry operations
                const pipeline = this.cache.pipeline();

                // Add key to registry and get current TTL in one round trip
                pipeline.sadd(registryKey, normKey);
                pipeline.ttl(registryKey);

                const results = await this.withTimeout(
                    pipeline.exec(),
                    CIRCUIT_BREAKER_TIMEOUT_MS,
                    'set',
                );

                if (results) {
                    const [, ttlResult] = results;
                    const currentTtl =
                        ttlResult && !ttlResult[0] && typeof ttlResult[1] === 'number'
                            ? ttlResult[1]
                            : -1;
                    // Update expiration if:
                    // - Registry has no expiration (TTL = -1)
                    // - Registry is about to expire (TTL = -2, key doesn't exist)
                    // - New TTL is longer than current TTL
                    if (currentTtl === -1 || currentTtl === -2 || currentTtl < registryTtl) {
                        await this.withTimeout(
                            this.cache.expire(registryKey, registryTtl),
                            CIRCUIT_BREAKER_TIMEOUT_MS,
                            'set',
                        );

                        this.logHint(
                            `Updated registry TTL: ${registryKey} => ${registryTtl}s (was: ${currentTtl}s)`,
                            'CacheService',
                            'autoRegister',
                        );
                    }
                }
            } catch (ttlError) {
                // If TTL check fails, just set the expiration anyway
                await this.withTimeout(
                    this.cache.expire(registryKey, registryTtl),
                    CIRCUIT_BREAKER_TIMEOUT_MS,
                    'set',
                );

                this.logHint(
                    `Set registry TTL (fallback): ${registryKey} => ${registryTtl}s`,
                    'CacheService',
                    'autoRegister',
                );
            }

            this.logHint(
                `Auto-registered cache key in registry: ${registryKey}`,
                'CacheService',
                'autoRegister',
            );
        } catch (error) {
            // Registry failure shouldn't break caching
            this.logger.warn(
                PolloMessage.msg(`Auto-registration failed for key: ${normKey} => ${error.message}`)
                    .setContext('CacheService')
                    .setError(error),
            );
        }
    }

    /**
     * Automatically remove cache keys from registry when they're deleted.
     * Removes from the single registry where the key was registered.
     */
    private async autoUnregisterCacheKey(normKey: string): Promise<void> {
        try {
            const globalPrefix =
                config.get<string>('cache.globalPrefix')?.toLowerCase() || 'globalstore';

            const registryKey = computeRegistryKey(normKey, globalPrefix);
            if (!registryKey) {
                return;
            }

            // Remove the cache key from the registry
            await this.withTimeout(
                this.cache.srem(registryKey, normKey),
                CIRCUIT_BREAKER_TIMEOUT_MS,
                'del',
            );

            this.logHint(
                `Auto-unregistered cache key from registry: ${registryKey}`,
                'CacheService',
                'autoUnregister',
            );
        } catch (error) {
            // Registry failure shouldn't break deletion
            this.logger.warn(
                PolloMessage.msg(
                    `Auto-unregistration failed for key: ${normKey} => ${error.message}`,
                )
                    .setContext('CacheService')
                    .setError(error),
            );
        }
    }

    /**
     * Batch unregister cache keys from registry using pipeline for efficiency
     */
    private async batchUnregisterCacheKeys(normKeys: string[]): Promise<void> {
        if (normKeys.length === 0) {
            return;
        }

        try {
            const globalPrefix =
                config.get<string>('cache.globalPrefix')?.toLowerCase() || 'globalstore';

            const keysByRegistry = new Map<string, string[]>();

            for (const normKey of normKeys) {
                const registryKey = computeRegistryKey(normKey, globalPrefix);
                if (!registryKey) {
                    continue;
                }

                if (!keysByRegistry.has(registryKey)) {
                    keysByRegistry.set(registryKey, []);
                }
                keysByRegistry.get(registryKey)?.push(normKey);
            }

            // Execute SREM operations
            for (const [registryKey, keys] of keysByRegistry) {
                await this.withTimeout(
                    this.cache.srem(registryKey, ...keys),
                    CIRCUIT_BREAKER_TIMEOUT_MS,
                    'del',
                );
            }

            this.logHint(
                `Batch unregistered ${normKeys.length} keys from ${keysByRegistry.size} registries`,
                'CacheService',
                'batchUnregister',
            );
        } catch (error) {
            // Registry failure shouldn't break deletion
            this.logger.warn(
                PolloMessage.msg(`Batch unregistration failed: ${error.message}`)
                    .setContext('CacheService')
                    .setError(error),
            );
        }
    }

    /**
     * Get or create the request-scoped cache map from CLS
     * Returns null if CLS context is not available (e.g., middleware running before ClsModule)
     */
    private getRequestCache(): Map<string, any> | null {
        try {
            const cls = ClsServiceManager.getClsService();
            if (!cls) {
                return null;
            }

            let requestCache = cls.get('cacheRequestCache');
            if (!requestCache) {
                requestCache = new Map<string, any>();
                cls.set('cacheRequestCache', requestCache);
            }
            return requestCache;
        } catch (error) {
            // CLS context not available (e.g., middleware running before ClsModule's middleware)
            // This is expected and not an error - gracefully degrade to Redis-only caching
            return null;
        }
    }

    private logHint(message: string, context: string, subcontext?: string): void {
        if (!config.get<boolean>('cache.log.enabled')) {
            return;
        }

        const msg = PolloMessage.msg(message).setContext(context);

        if (!isNil(subcontext)) {
            msg.setSubContext(subcontext);
        }

        this.logger.log(msg);
    }

    /**
     * Check if a resource is currently locked
     * Uses direct Redis EXISTS for maximum performance
     * @param lockKey The lock identifier (will be prefixed with 'lock:')
     * @returns true if locked, false if available
     */
    async isLocked(lockKey: string): Promise<boolean> {
        const prefixedKey = `{lock}:${lockKey}`;
        const normKey = decoratorNormalizeKey(prefixedKey);

        this.logHint(
            `CacheService isLocked => key="${lockKey}" => normKey="${normKey}"`,
            CACHE_SERVICE_CONTEXT,
        );

        try {
            // Direct Redis call for performance - bypass wrappers
            const exists = await this.withTimeout(
                this.cache.exists(normKey),
                CIRCUIT_BREAKER_TIMEOUT_MS,
                'get',
            );
            return exists === 1;
        } catch (error) {
            this.logger.warn(
                PolloMessage.msg(`Error checking lock => ${error.message}`)
                    .setContext(CACHE_SERVICE_CONTEXT)
                    .setError(error),
            );
            return false;
        }
    }

    /**
     * Get the data stored in a lock
     * @param lockKey The lock identifier
     * @returns The lock data if locked, null otherwise
     */
    async getLockData<T>(lockKey: string): Promise<T | null> {
        const prefixedKey = `{lock}:${lockKey}`;
        const normKey = decoratorNormalizeKey(prefixedKey);

        this.logHint(
            `CacheService getLockData => key="${lockKey}" => normKey="${normKey}"`,
            CACHE_SERVICE_CONTEXT,
        );

        try {
            // Direct Redis call to get the lock data
            const data = await this.withTimeout(
                this.cache.get(normKey),
                CIRCUIT_BREAKER_TIMEOUT_MS,
                'get',
            );
            return data ? (JSON.parse(data) as T) : null;
        } catch (error) {
            this.logger.warn(
                PolloMessage.msg(`Error getting lock data => ${error.message}`)
                    .setContext(CACHE_SERVICE_CONTEXT)
                    .setError(error),
            );
            return null;
        }
    }

    /**
     * Acquire a distributed lock atomically
     * Uses Redis SET with NX (only if not exists) and EX (expiration)
     * @param lockKey The lock identifier (will be prefixed with 'lock:')
     * @param ttlSeconds Lock expiration in seconds (default: 60)
     * @param lockData Optional data to store (e.g., server ID, process ID)
     * @param lockValue Optional lock value (default: 'locked')
     * @returns true if lock acquired, false if already locked
     */
    async acquireLock(
        lockKey: string,
        ttlSeconds: number = 60,
        lockData?: unknown,
    ): Promise<boolean> {
        const prefixedKey = `{lock}:${lockKey}`;
        const normKey = decoratorNormalizeKey(prefixedKey);

        this.logHint(
            `CacheService acquireLock => key="${lockKey}" => normKey="${normKey}" ttl="${ttlSeconds}s"`,
            CACHE_SERVICE_CONTEXT,
        );

        try {
            // Direct Redis SET with NX (only if not exists) and EX (expiration in seconds)
            // This is an atomic operation - no race conditions possible
            const result = await this.withTimeout(
                this.cache.set(
                    normKey,
                    lockData ? JSON.stringify(lockData) : 'locked',
                    'EX',
                    ttlSeconds,
                    'NX',
                ),
                CIRCUIT_BREAKER_TIMEOUT_MS,
                'set',
            );
            return result === 'OK';
        } catch (error) {
            this.logger.warn(
                PolloMessage.msg(`Error setting lock => ${error.message}`)
                    .setContext(CACHE_SERVICE_CONTEXT)
                    .setError(error),
            );
            return false;
        }
    }

    /**
     * Release a distributed lock
     * @param lockKey The lock identifier (will be prefixed with 'lock:')
     */
    async releaseLock(lockKey: string): Promise<void> {
        const prefixedKey = `{lock}:${lockKey}`;
        const normKey = decoratorNormalizeKey(prefixedKey);

        this.logHint(
            `CacheService releaseLock => key="${lockKey}" => normKey="${normKey}"`,
            CACHE_SERVICE_CONTEXT,
        );

        try {
            // Direct Redis DEL for performance
            await this.withTimeout(this.cache.del(normKey), CIRCUIT_BREAKER_TIMEOUT_MS, 'del');
        } catch (error) {
            this.logger.warn(
                PolloMessage.msg(`Error releasing lock => ${error.message}`)
                    .setContext(CACHE_SERVICE_CONTEXT)
                    .setError(error),
            );
        }
    }

    /**
     * Execute a function with distributed locking to prevent race conditions
     * Migrated from execute-with-lock.helper.ts for centralization
     *
     * @param lockKey Unique identifier for the lock
     * @param processor The function to execute once lock is acquired
     * @param options Lock configuration options
     * @returns Result of the processor function
     * @throws Error if lock cannot be acquired after max retries
     */
    async executeWithLock<T>(
        lockKey: string,
        processor: () => Promise<T>,
        options: {
            maxRetries?: number; // Default: 30 (30 seconds max wait)
            retryDelay?: number; // Default: 1000ms between retries
            lockTtl?: number; // Default: 60 seconds lock expiration
            lockData?: unknown; // Optional lock value (e.g., server ID)
        } = {},
    ): Promise<T> {
        try {
            const { maxRetries = 30, retryDelay = 1000, lockTtl = 60, lockData } = options;

            // Try to acquire the lock atomically
            const lockAcquired = await this.acquireLock(lockKey, lockTtl, lockData);

            if (lockAcquired) {
                // We got the lock - process immediately
                try {
                    return await processor();
                } finally {
                    // Always release the lock
                    await this.releaseLock(lockKey);
                }
            }

            // Lock is held by another process - retry with backoff
            for (let attempt = 0; attempt < maxRetries; attempt++) {
                // Wait before retry
                await new Promise(resolve => setTimeout(resolve, retryDelay));

                // Try to acquire lock again
                const retryLockAcquired = await this.acquireLock(lockKey, lockTtl, lockData);

                if (retryLockAcquired) {
                    // Got the lock on retry
                    try {
                        return await processor();
                    } finally {
                        // Always release the lock
                        await this.releaseLock(lockKey);
                    }
                }
            }

            // Could not acquire lock after all retries
            throw new Error(
                `Failed to acquire lock for '${lockKey}' after ${maxRetries} attempts. ` +
                    `The operation may be taking longer than expected or may be stuck.`,
            );
        } catch (error) {
            // INFALLIBLE: If locking fails, execute without lock as fallback
            this.logger.warn(
                PolloMessage.msg(
                    `executeWithLock failed for ${lockKey}, proceeding without lock: ${error.message}`,
                )
                    .setError(error)
                    .setContext(CACHE_SERVICE_CONTEXT),
            );

            // Execute without lock as a fallback - better to risk race conditions than fail entirely
            try {
                return await processor();
            } catch (processorError) {
                this.logger.warn(
                    PolloMessage.msg(
                        `Processor also failed after lock failure for ${lockKey}: ${processorError.message}`,
                    )
                        .setError(processorError)
                        .setContext(CACHE_SERVICE_CONTEXT),
                );
                throw processorError; // Re-throw processor error, not lock error
            }
        }
    }

    /**
     * Helper to generate a lock key for account-scoped operations
     * Provides consistent key format for account-based locking
     * @param accountId The account ID
     * @param identifier Additional identifier for the lock
     * @returns Formatted lock key
     */
    getLockKey(accountId: string, identifier: string) {
        return `execute-with-lock:${accountId}:${identifier}`;
    }
}

import { ErrorCode } from '@drata/enums';
import {
    Body,
    Controller,
    Delete,
    Get,
    HttpCode,
    HttpStatus,
    Param,
    ParseUUIDPipe,
    Put,
    Query,
    Req,
    UnauthorizedException,
} from '@nestjs/common';
import { EventBus } from '@nestjs/cqrs';
import { JwtService } from '@nestjs/jwt';
import {
    ApiBadRequestResponse,
    ApiOkResponse,
    ApiOperation,
    ApiParam,
    ApiTags,
} from '@nestjs/swagger';
import { AuditApiKeysService } from 'audit-api-keys/audit-api-keys.service';
import { AuditApiKey } from 'auditors/entities/audit-api-key.entity';
import { AuthResponseDto } from 'auth/dtos/auth-response.dto';
import { AccountsCoreService } from 'auth/services/accounts-core.service';
import { AuthResponseType } from 'auth/types/auth-response-type';
import { BaseController } from 'commons/controllers/base.controller';
import { Dto } from 'commons/decorators/dto.decorator';
import { Area, ProductArea } from 'commons/decorators/product-area.decorator';
import { Roles } from 'commons/decorators/roles.decorator';
import { ExceptionResponseDto } from 'commons/dtos/exception-response.dto';
import { ApiResponse } from 'commons/enums/api-response.enum';
import { ClientType } from 'commons/enums/auth/client-type.enum';
import { AdminRole } from 'commons/enums/site-admin/admin-role.enum';
import { AuditLogEventType } from 'commons/enums/site-admin/audit-log-event-type.enum';
import { AuditLogTargetType } from 'commons/enums/site-admin/audit-log-target-type.enum';
import { AccountIdType } from 'commons/types/account-id.type';
import { PaginationType } from 'commons/types/pagination.type';
import config from 'config';
import { ToggleAccountEntitlementRequestDto } from 'entitlements/dtos/toggle-entitlement-request.dto';
import { Request } from 'express';
import { ExternalClientRouter } from 'external-client-router/entities/external-client-router.entity';
import { isNil } from 'lodash';
import { GetSiteAdmin } from 'site-admin/decorators/get-site-admin.decorator';
import { SiteAdminAuth } from 'site-admin/decorators/site-admin-auth.decorator';
import { AccountExternalClientRouterListResponseDto } from 'site-admin/dtos/account-external-client-router-list-response.dto';
import { AuditApiKeysResponseDto } from 'site-admin/dtos/audit-api-keys-response.dto';
import { SiteAdminDomainRequestDto } from 'site-admin/dtos/site-admin-domain-request.dto';
import { SiteAdminResponseDto } from 'site-admin/dtos/site-admin-response.dto';
import { SiteAdminsRequestDto } from 'site-admin/dtos/site-admins-request.dto';
import { SiteAdminsResponseDto } from 'site-admin/dtos/site-admins-response.dto';
import { SiteAdmin } from 'site-admin/entities/site-admin.entity';
import { AuditLogEvent } from 'site-admin/observables/events/audit-log.event';
import { SiteAdminCoreService } from 'site-admin/services/site-admin-core.service';
import { SiteAdminRefreshTokenService } from 'site-admin/services/site-admin-refresh-token.service';
import { SiteAdminService } from 'site-admin/services/site-admin.service';
import { SiteAdminRoute } from 'site-admin/site-admin.routes';
import { tenantWrapper } from 'tenancy/contexts/tenant-wrapper';

@ApiTags('Site Admin')
@SiteAdminAuth()
@Controller()
@ProductArea(Area.TBD)
export class SiteAdminController extends BaseController {
    constructor(
        private readonly siteAdminService: SiteAdminService,
        private readonly siteAdminCoreService: SiteAdminCoreService,
        private readonly accountsCoreService: AccountsCoreService,
        private readonly siteAdminRefreshTokenService: SiteAdminRefreshTokenService,
        private readonly auditApiKeysService: AuditApiKeysService,
        private readonly eventBus: EventBus,
        private readonly jwtService: JwtService,
    ) {
        super();
    }

    @ApiOperation({
        description:
            'Generates a new access token based on a given refresh token received via httponly cookie',
    })
    @ApiOkResponse({
        type: AuthResponseDto,
    })
    @Dto(AuthResponseDto)
    @Roles(AdminRole.EMPLOYEE)
    @Get(SiteAdminRoute.GET_REFRESH_TOKEN)
    async generateAccessToken(@Req() request: Request): Promise<AuthResponseType> {
        // does not need tenancy
        const refreshTokenCookie =
            request.cookies[config.get('jwt.cookie.siteAdminRefreshTokenKey')];

        if (!isNil(refreshTokenCookie)) {
            const siteAdmin =
                await this.siteAdminRefreshTokenService.getEntrybyRefreshToken(refreshTokenCookie);

            return {
                accessToken: this.jwtService.sign(
                    {
                        id: siteAdmin.id,
                    },
                    { expiresIn: config.get('jwt.siteAdminExpiresIn') },
                ),
            };
        } else {
            throw new UnauthorizedException(ErrorCode.BAD_ACCESS_TOKEN);
        }
    }

    @ApiOperation({
        description: `Get the current site admin record
        <br><br>
        Allowed Roles: [
            ${AdminRole[AdminRole.EMPLOYEE]}
        ]`,
    })
    @ApiOkResponse({
        type: SiteAdminResponseDto,
    })
    @Roles(AdminRole.EMPLOYEE)
    @Dto(SiteAdminResponseDto)
    @Get(SiteAdminRoute.GET_ADMINS_ME)
    getMySiteAdminRecord(@GetSiteAdmin() siteAdmin: SiteAdmin): Promise<SiteAdmin> {
        // does not need tenancy
        // call the service to get site admin record
        return this.siteAdminCoreService.findById(siteAdmin.id);
    }

    @ApiOperation({
        description: `Get site admin records with pagination
        <br><br>
        Allowed Roles: [
            ${AdminRole[AdminRole.EMPLOYEE]}
        ]`,
    })
    @ApiOkResponse({
        type: SiteAdminsResponseDto,
    })
    @ApiBadRequestResponse({
        description: ApiResponse.BAD_REQUEST,
        type: ExceptionResponseDto,
    })
    @Roles(AdminRole.EMPLOYEE)
    @Dto(SiteAdminsResponseDto)
    @Get(SiteAdminRoute.GET_ADMINS)
    getSiteAdmins(@Query() requestDto: SiteAdminsRequestDto): Promise<PaginationType<SiteAdmin>> {
        // does not need tenancy
        // get the paginated site admins
        return this.siteAdminCoreService.getSiteAdmins(requestDto);
    }

    @ApiOperation({
        description: `Domain Change
        <br><br>
        Allowed Roles: [
            ${AdminRole[AdminRole.ADMIN]},
            ${AdminRole[AdminRole.CSG_TECH]},
            ${AdminRole[AdminRole.BUS_DEV]},
        ]f`,
    })
    @ApiOkResponse({
        description: ApiResponse.OK,
    })
    @ApiBadRequestResponse({
        description: ApiResponse.BAD_REQUEST,
        type: ExceptionResponseDto,
    })
    @ApiParam({
        name: 'id',
        description: 'Account ID',
        type: 'string',
        format: 'uuid',
    })
    @Roles(AdminRole.ADMIN, AdminRole.CSG_TECH, AdminRole.BUS_DEV)
    @Put(SiteAdminRoute.PUT_UPDATE_DOMAIN)
    async changeDomain(
        @GetSiteAdmin() currentAdmin: SiteAdmin,
        @Param('id', ParseUUIDPipe) id: AccountIdType,
        @Body() requestDto: SiteAdminDomainRequestDto,
    ): Promise<void> {
        const { email } = requestDto;

        const account = await this.accountsCoreService.getAccountById(id);

        return tenantWrapper(account, () =>
            this.siteAdminService.updateTenantDomain(account, currentAdmin, email),
        );
    }

    @ApiOperation({
        description: `Toggle an account entitlement
        <br><br>
        Allowed Roles: [
            ${AdminRole[AdminRole.ADMIN]},
            ${AdminRole[AdminRole.CSG_TECH]},
            ${AdminRole[AdminRole.CSG]},
            ${AdminRole[AdminRole.BUS_DEV]}
        ]`,
    })
    @ApiOkResponse({
        description: ApiResponse.OK,
    })
    @ApiParam({
        name: 'id',
        description: 'Account ID',
        type: 'string',
        format: 'uuid',
    })
    @Roles(AdminRole.ADMIN, AdminRole.CSG_TECH, AdminRole.CSG, AdminRole.BUS_DEV)
    @Put(SiteAdminRoute.PUT_TOGGLE_ENTITLEMENT)
    @HttpCode(HttpStatus.OK)
    async toggleAccountEntitlement(
        @GetSiteAdmin() currentAdmin: SiteAdmin,
        @Param('id', ParseUUIDPipe) accountId: AccountIdType,
        @Body() requestDto: ToggleAccountEntitlementRequestDto,
    ): Promise<void> {
        const account = await this.accountsCoreService.getAccountById(accountId);
        return tenantWrapper(account, () =>
            this.siteAdminService.toggleAccountEntitlement(account, currentAdmin, requestDto),
        );
    }

    /**
     * Endpoint to get the AUDITOR API KEY, that allows an Auditor to access to Drata Auditor API
     * This endpoint does not have an IU implementation right now and and is only accessible through a customer service request.
     * Original ticket for more info: https://drata.atlassian.net/browse/ENG-34792
     * @param accountId
     * @returns
     */
    @ApiOperation({
        description: `Get decrypted Auditor API keys
        <br><br>
        Allowed Roles: [
            ${AdminRole[AdminRole.ADMIN]},
            ${AdminRole[AdminRole.ENGINEER]}
        ]`,
    })
    @ApiOkResponse({
        type: AuditApiKeysResponseDto,
    })
    @Roles(AdminRole.ENGINEER, AdminRole.ADMIN)
    @Dto(AuditApiKeysResponseDto)
    @Get(SiteAdminRoute.GET_AUDIT_KEY)
    async getAuditorApiKey(
        @GetSiteAdmin() currentAdmin: SiteAdmin,
        @Param('auditorId', ParseUUIDPipe) auditorId: string,
    ): Promise<PaginationType<AuditApiKey>> {
        const apiKeys = this.auditApiKeysService.getAuditorApiKeysByAuditorId(auditorId);

        // site-admin audit log
        this.eventBus.publish(
            new AuditLogEvent(
                currentAdmin,
                AuditLogEventType.AUDITOR_API_KEYS_FETCHED,
                AuditLogTargetType.ACCOUNT,
                auditorId,
            ),
        );

        return apiKeys;
    }

    @ApiOperation({
        description: `Delete the an external client router entry for a given account.
        <br><br>
        Allowed Roles: [
            ${AdminRole[AdminRole.ADMIN]},
            ${AdminRole[AdminRole.CSG_TECH]},
            ${AdminRole[AdminRole.CSG]},
            ${AdminRole[AdminRole.BUS_DEV]}
        ]`,
    })
    @ApiOkResponse({
        description: ApiResponse.OK,
    })
    @ApiParam({
        name: 'id',
        description: 'Account ID',
        type: 'string',
        format: 'uuid',
    })
    @Roles(AdminRole.ADMIN, AdminRole.CSG_TECH, AdminRole.CSG, AdminRole.BUS_DEV)
    @Delete(SiteAdminRoute.DELETE_EXTERNAL_CLIENT_ROUTE)
    @HttpCode(HttpStatus.OK)
    deleteExternalClientRouter(
        @Param('id', ParseUUIDPipe) accountId: AccountIdType,
        @Param('clientType') clientType: string,
    ): Promise<void> {
        // does not need tenancy
        const clientTypeEnum: ClientType = ClientType[clientType as keyof ClientType];
        return this.siteAdminCoreService.deleteExternalClientRouter(accountId, clientTypeEnum);
    }

    @ApiOperation({
        description: `Retrieve the external client router entries for a given account.
        <br><br>
        Allowed Roles: [
            ${AdminRole[AdminRole.ADMIN]},
            ${AdminRole[AdminRole.CSG_TECH]},
            ${AdminRole[AdminRole.CSG]},
            ${AdminRole[AdminRole.BUS_DEV]}
        ]`,
    })
    @ApiOkResponse({
        description: ApiResponse.OK,
    })
    @ApiParam({
        name: 'id',
        description: 'Account ID',
        type: 'string',
        format: 'uuid',
    })
    @Roles(AdminRole.ADMIN, AdminRole.CSG_TECH, AdminRole.CSG, AdminRole.BUS_DEV)
    @Get(SiteAdminRoute.GET_EXTERNAL_CLIENT_ROUTE)
    @Dto(AccountExternalClientRouterListResponseDto)
    @HttpCode(HttpStatus.OK)
    getExternalClientRouter(
        @Param('id', ParseUUIDPipe) accountId: AccountIdType,
    ): Promise<ExternalClientRouter[]> {
        // does not need tenancy
        return this.siteAdminCoreService.getExternalClientRouters(accountId);
    }

    @ApiOperation({
        description: `Delete the an external client router entry for a given account.
        <br><br>
        Allowed Roles: [
            ${AdminRole[AdminRole.ADMIN]},
            ${AdminRole[AdminRole.CSG_TECH]},
            ${AdminRole[AdminRole.CSG]},
            ${AdminRole[AdminRole.BUS_DEV]}
        ]`,
    })
    @ApiOkResponse({
        description: ApiResponse.OK,
    })
    @Roles(AdminRole.ADMIN, AdminRole.CSG_TECH, AdminRole.CSG, AdminRole.BUS_DEV)
    @Delete(SiteAdminRoute.DELETE_SLACK_WELCOME_MESSAGE_ROUTE)
    @HttpCode(HttpStatus.OK)
    removeSlackWelcomeMessageUserCache(@Param('slackUserId') slackUserId: string): Promise<void> {
        // does not need tenancy
        return this.siteAdminService.removeSlackWelcomeMessageUserCache(slackUserId);
    }
}

import { ErrorCode } from '@drata/enums';
import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { WorkspacesCoreService } from 'app/companies/products/services/workspaces-core.service';
import { FrameworkRepository } from 'app/frameworks/repositories/framework.repository';
import { User } from 'app/users/entities/user.entity';
import { UserRepository } from 'app/users/repositories/user.repository';
import { AuditorClient } from 'auditors/entities/auditor-client.entity';
import { AuditorFrameworkAuditors } from 'auditors/entities/auditor-framework-auditors.entity';
import { Auditor } from 'auditors/entities/auditor.entity';
import { AuditorClientRepository } from 'auditors/repositories/auditor-client.repository';
import { AuditorRepository } from 'auditors/repositories/auditor.repository';
import { Audit } from 'audits/entities/audit.entity';
import { Account } from 'auth/entities/account.entity';
import { Entry } from 'auth/entities/entry.entity';
import { EntryRepository } from 'auth/repositories/entry.repository';
import { PolloAdapter } from 'commons/adapters/pollo.adapter';
import { ConflictException } from 'commons/exceptions/conflict.exception';
import { ForbiddenException } from 'commons/exceptions/forbidden.exception';
import { NotFoundException as NotFoundExceptionWithErrorCode } from 'commons/exceptions/not-found.exception';
import { PreconditionFailedException } from 'commons/exceptions/precondition-failed.exception';
import { ValidationException } from 'commons/exceptions/validation.exception';
import { getDomainFromEmail, isExcludedDomain, isSameDomain } from 'commons/helpers/domain.helper';
import { isSupportUser } from 'commons/helpers/user.helper';
import { AppService } from 'commons/services/app.service';
import { AccountIdType } from 'commons/types/account-id.type';
import { chain, isEmpty, isNil, uniq } from 'lodash';
import { ServiceUserClientRepository } from 'service-user/repositories/service-user-client.repository';
import { ServiceUserRepository } from 'service-user/repositories/service-user.repository';
import { Repository } from 'typeorm';

@Injectable()
export class AuditHubValidationService extends AppService {
    constructor(
        private readonly entryRepository: EntryRepository,
        private readonly auditorRepository: AuditorRepository,
        private readonly auditorClientRepository: AuditorClientRepository,
        private readonly serviceUserClientRepository: ServiceUserClientRepository,
        private readonly serviceUserRepository: ServiceUserRepository,
        @InjectRepository(AuditorFrameworkAuditors)
        private readonly auditorFrameworkAuditorsRepository: Repository<AuditorFrameworkAuditors>,
        private readonly workspacesCoreService: WorkspacesCoreService,
    ) {
        super();
    }

    /**
     * # validateAuditorAccessToAuditOrFail
     *
     * This method does a three steps check
     * 1. is the user an auditor
     * 2. does the auditor has access to the tenant
     * 3. does the auditor has access to the audit
     */
    async validateAuditorAccessToAuditOrFail(
        entryId: string,
        auditId: string,
        accountId: AccountIdType,
    ): Promise<void> {
        const auditor = await this.getAuditorForUser(entryId);

        // find a service user for this entry and account
        const serviceUser = await this.serviceUserRepository.findOne({
            where: { entry: { id: entryId }, accounts: { id: accountId } },
            loadEagerRelations: false,
        });

        // if the user performing this action is both a service user AND an auditor
        if (!isNil(serviceUser) && !isNil(auditor)) {
            // service user access overrides auditor access
            // proceed as an assumed Admin with full access because the user is a service user
            return;
        }

        /**
         * If the user is an auditor, we need to make a few validations,
         * otherwise we assume that we are dealing with a tenant user whose
         * permissions were checked in the controller layer.
         */
        if (!isNil(auditor)) {
            const auditorClient = await this.getAuditorClientForTenantOrFail(auditor, accountId);

            await this.isAuditorAllowedInAuditOrFail(auditId, auditorClient);
        }
    }

    /**
     * # validateEmailForAuditorUserOrFail
     *
     * Assess whether or not a given email can be used by and Auditor user.
     *
     * The actual spec of these assessments is hard to determine from
     * the code, and we don't have a clear feature description to follow,
     * so, we are going to treat this method as a "black box" taken from the
     * deprecated service `src/auditors/services/auditors.service.ts`.
     */
    async validateEmailForAuditorUserOrFail(
        email: string,
        user: User,
        account: Account,
    ): Promise<void> {
        if (isSupportUser(email)) {
            throw new PreconditionFailedException(ErrorCode.SUPPORT_USER_NOT_SUPPORTED);
        }

        if (isExcludedDomain(getDomainFromEmail(email))) {
            throw new ConflictException(
                // 'This domain is in the list of public domains to exclude',
                'Only corporate email addresses are allowed',
                ErrorCode.SERVICE_PROVIDER_NOT_ALLOWED_DOMAIN,
            );
        }

        const entry = await this.getEntryByEmailNoFail(email, true);

        const userFound = await this.userRepository.findOne({
            where: {
                email,
            },
        });

        if (isNil(userFound) && isSameDomain(account.domain, email)) {
            throw new ConflictException(
                "Users within your company's domain cannot be auditors",
                ErrorCode.EMPLOYEE_AUDITOR_CONFLICT,
            );
        }

        if (isNil(entry)) {
            // new user, all ok!
            return;
        }

        const auditor = await this.auditorRepository.findOne({
            where: { entry: { id: entry.id } },
        });

        const serviceUserInThisAccount = await this.serviceUserClientRepository.findOne({
            where: {
                entry: { id: entry.id },
                account: { id: account.id },
            },
        });

        const serviceUser = await this.serviceUserRepository.findOne({
            where: {
                entry: {
                    id: entry.id,
                },
            },
        });

        if (!isNil(serviceUser) && isNil(serviceUserInThisAccount)) {
            // user is able to be service user and auditor at the same time
            return;
        }

        if (!isNil(auditor) && isNil(serviceUserInThisAccount)) {
            // user is an auditor already
            return;
        }

        if (isSameDomain(account.domain, entry.email))
            // user is not an auditor already, can't cross role
            throw new ConflictException(
                'User cannot be auditor',
                ErrorCode.EMPLOYEE_AUDITOR_CONFLICT,
            );
    }

    /**
     * # getAuditorForUser
     *
     * Given any tenant user, get back its corresponding `Auditor` entry, if one exists.
     *
     * If a tenant user exists in the `auditors` table it means that it was created
     * as the corresponding tenant user for and auditor.
     *
     * ⚠️ Warning: getting a record back determines that the user is acting as an auditor,
     * but it doesn't mean that the auditor has any sort of access granted.
     */
    private async getAuditorForUser(entryId: string): Promise<Auditor | null> {
        return this.auditorRepository.findOne({
            where: { entry: { id: entryId } },
        });
    }

    /**
     * # getAuditorClientForTenantOrFail
     *
     * Given an auditor, get an `AuditorClient` or fail with a `403` error code,
     * since if an entry doesn't exists it means that the auditor
     * doesn't have access to the tenant.
     *
     * ⚠️ Warning: getting a record back doesn't mean the auditor has access to any `Audit`,
     * it only means that it has access to the tenant.
     */
    async getAuditorClientForTenantOrFail(
        auditor: Auditor,
        accountId: string,
    ): Promise<AuditorClient> {
        const auditorClient = await this.auditorClientRepository
            .createQueryBuilder('AuditorClient')
            .select('AuditorClient.id')
            .where('AuditorClient.fk_entry_id = :entryId', { entryId: auditor.entry.id })
            .andWhere('AuditorClient.fk_account_id = :accountId', { accountId })
            .andWhere('AuditorClient.deleted_at IS NULL')
            .getOne();

        if (auditorClient === null) {
            throw new ForbiddenException(
                'Auditor not allowed in tenant.',
                ErrorCode.TENANT_AUDITOR_FORBIDDEN,
            );
        }

        return auditorClient;
    }

    /**
     * # isAuditorAllowedInAuditOrFail
     *
     * Given an auditor and an `Audit`,
     * determine if such auditor has access to the `Audit`.
     *
     * This method will fail with a `403` error code if there
     * is no entry binding these entities in the table `auditor_framework_auditors`.
     */
    private async isAuditorAllowedInAuditOrFail(
        auditId: string,
        auditorClient: AuditorClient,
    ): Promise<void> {
        const auditToAuditorMapping = await this.auditorFrameworkAuditorsRepository.findOne({
            where: {
                auditorFramework: { id: auditId },
                auditorClient: { id: auditorClient.id },
            },
        });

        if (isNil(auditToAuditorMapping)) {
            throw new ForbiddenException(
                'Auditor cannot access audit.',
                ErrorCode.AUDIT_AUDITOR_FORBIDDEN,
            );
        }
    }

    /**
     *
     * @param account The account for which the request has been made
     * @param audit The audit (auditorFramework) which has to be validated. It must contain
     *              the account relation in order for the validation to be executed correctly
     *              otherwise it will throw an error.
     *
     * This function will validate that the audit passed as parameter belongs to the account
     * also passed as parameter. It logs whether validation is passed or not.
     * It is used to avoid an admin accessing an audit belonging to a different tenant.
     */
    validateAuditForAccountOrFail(account: Account, audit: Audit): void {
        if (isNil(audit.account)) {
            throw new NotFoundException(
                "The audit's related account has not been correctly set up.",
            );
        }

        if (account.id === audit.account.id) {
            this.logger.log(
                PolloAdapter.acct(
                    `Account with id ${account.id} properly validated to access audit with id ${audit.id}`,
                    account,
                ),
            );
        } else {
            this.logger.error(
                PolloAdapter.acct(
                    `Account with id ${account.id} unauthorized access to audit with ${audit.id}`,
                    account,
                ),
            );

            throw new NotFoundExceptionWithErrorCode(ErrorCode.AUDIT_ACCOUNT_MISMATCH);
        }
    }

    /**
     *
     * @param account The tenant on which the operation is being executed
     * @param audit The audit to be validated. It requires the following relations to be
     *              loaded: Audit.auditorFrameworkAuditors.auditorClient.entry
     * @param metadata Additional information to be logged.
     *
     * The method will validate if all the auditor User entities assigned to an audit have
     * an associated Auditor entity. In case any of them are missing the auditor entity the
     * method will throw a Validation Exception.
     */
    async validateAuditHasAllValidAuditorsOrFail(
        account: Account,
        audit: Audit,
        metadata?: unknown,
    ): Promise<void> {
        const logMessage = PolloAdapter.acct('Validating Auditor information', account);
        if (!isNil(metadata)) {
            logMessage.setIdentifier(metadata);
        }
        this.logger.log(logMessage);

        const auditorEntryIds = audit.auditorFrameworkAuditors.map(
            auditAuditor => auditAuditor.auditorClient.entry.id,
        );

        const duplicatedEntryIds = chain(auditorEntryIds)
            .countBy()
            .pickBy(n => n > 1)
            .keys()
            .value();

        if (!isEmpty(duplicatedEntryIds)) {
            this.warn(`Audit with id ${audit.id} has duplicated auditor entries`, account, {
                duplicatedEntryIds,
            });
        }

        const uniqueAuditorEntryIds = uniq(auditorEntryIds);

        const auditors = await this.auditorRepository.getAuditorsByEntryIds(uniqueAuditorEntryIds);

        if (auditors?.length !== uniqueAuditorEntryIds.length) {
            throw new ValidationException(
                `Could not generate the control evidence package as some Auditors are not valid`,
                ErrorCode.AUDITOR_INVALID_DATA,
            );
        }
    }

    async getEntryByEmailNoFail(email: string, isAuditor = false): Promise<Entry | null> {
        const entry = await this.entryRepository.findOneBy({ email });

        if (!isNil(entry) && (isAuditor || entry.accounts.length)) {
            return entry;
        }

        return null;
    }

    private get userRepository(): UserRepository {
        return this.getCustomTenantRepository(UserRepository);
    }

    private get frameworkRepository(): FrameworkRepository {
        return this.getCustomTenantRepository(FrameworkRepository);
    }
}

import { CheckResultStatus, CheckStatus, TestSource } from '@drata/enums';
import { Provider } from '@drata/types/dist/recipe/recipe.type';
import { Injectable } from '@nestjs/common';
import { AutopilotRecipeInstance } from 'app/autopilot/entities/autopilot-recipe-instance.entity';
import { ConnectionEntity } from 'app/companies/connections/entities/connection.entity';
import { ConnectionsRepository } from 'app/companies/connections/repositories/connections.repository';
import { Product } from 'app/companies/products/entities/product.entity';
import { ProductRepository } from 'app/companies/products/repositories/product.repository';
import { ComplianceCheckExclusion } from 'app/compliance-check-exclusions/entities/compliance-check-exclusion.entity';
import { createExtendedComplianceCheckExclusion } from 'app/compliance-check-exclusions/helpers/compliance-check-exclusion.helper';
import { ControlTestInstanceTestIdComplianceCheckTypes } from 'app/compliance-check-exclusions/maps/control-test-instance-test-id-compliance-check-types.map';
import { ComplianceCheckExclusionRepository } from 'app/compliance-check-exclusions/repositories/compliance-check-exclusion.repository';
import { ExtendedComplianceCheckExclusion } from 'app/compliance-check-exclusions/types/extended-compliance-check-exclusion.type';
import { FindingExclusion } from 'app/iac-scanning/entities/finding-exclusion.entity';
import { FindingExclusionRepository } from 'app/iac-scanning/repositories/finding-exclusion.repository';
import { MonitoringSummaryIndexingConstants } from 'app/monitors/constants/monitoring-summary-indexing.constants';
import { ControlTestInstance } from 'app/monitors/entities/control-test-instance.entity';
import { MonitorInstanceExclusion } from 'app/monitors/entities/monitor-instance-exclusion.entity';
import { MonitorResultSummary } from 'app/monitors/entities/monitor-result-summary.opensearch.entity';
import { MonitorV2TestTypeOptions } from 'app/monitors/enums/monitor-v2-test-type-options.enum';
import { getMonitorFindingItems } from 'app/monitors/helpers/monitor-findings.helper';
import { logifyTestIds } from 'app/monitors/helpers/monitoring-summary-indexing.helper';
import { ControlTestInstanceRepository } from 'app/monitors/repositories/control-test-instance.repository';
import { MonitorInstanceExclusionRepository } from 'app/monitors/repositories/monitor-instance-exclusion.repository';
import {
    AvailableConnection,
    ControlTestInstanceDetails,
} from 'app/monitors/types/control-test-instance-details.type';
import { ControlTestInstanceWorkspaceTicketMapping } from 'app/monitors/types/control-test-instance-ticket-mapping.type';
import { ControlTestInstanceTicketingSummary } from 'app/monitors/types/control-test-instance-ticketing-summary.type';
import { MonitoringIndexingOptions } from 'app/monitors/types/monitor-indexing-options.type';
import { MonitorResultsSummaryConnections } from 'app/monitors/types/monitor-result-summary-connections.type';
import { MonitorResultsSummaryControlsAndFrameworks } from 'app/monitors/types/monitor-result-summary-controls-and-frameworks.type';
import { Personnel } from 'app/users/personnel/entities/personnel.entity';
import { PersonnelRepository } from 'app/users/personnel/repositories/personnel.repository';
import { VulnerabilityMonitoringConstants } from 'app/vulnerability/constants/vulnerability-monitoring.constants';
import { Account } from 'auth/entities/account.entity';
import { PolloAdapter } from 'commons/adapters/pollo.adapter';
import { Benchmark } from 'commons/benchmark/benchmark';
import { ClientType } from 'commons/enums/auth/client-type.enum';
import { CheckType } from 'commons/enums/check-type.enum';
import { ComplianceCheckExclusionTargetType } from 'commons/enums/compliance-check-exclusions/compliance-check-exclusion-target-type.enum';
import { EmploymentStatusOptionsFilter } from 'commons/enums/personnel/employment-status-options-filter.enum';
import { RunMode } from 'commons/enums/run-mode.enum';
import { TaskManagementStatus } from 'commons/enums/tickets/task-management-status-enum';
import { asyncForOf } from 'commons/helpers/array.helper';
import { getEnabledClientTypesByAutopilotTaskType } from 'commons/helpers/monitor.helper';
import { EmploymentStatusOptionsFilterMap } from 'commons/helpers/personnel.helper';
import { promiseAllSettledInBatches } from 'commons/helpers/promise.helper';
import { AppService } from 'commons/services/app.service';
import { FeatureFlagCategory } from 'feature-flags/enums/feature-flag-category.enum';
import { FeatureFlag } from 'feature-flags/feature-flag.enum';
import { FeatureFlagService } from 'feature-flags/feature-flag.service';
import { chunk, find, get, isEmpty, isNil, map, omit, uniq } from 'lodash';
import { Span } from 'nestjs-ddtrace';
import { In, SelectQueryBuilder } from 'typeorm';

@Injectable()
export class MonitoringSummaryResultSourceDataService extends AppService {
    constructor(private readonly featureFlagService: FeatureFlagService) {
        super();
    }

    private static readonly sortPriority = {
        TestingNew: 1,
        Testing: 2,
        ErrorNew: 3,
        Error: 4,
        FailedNew: 5,
        Failed: 6,
        PassedNew: 7,
        Passed: 8,
        ReadyNew: 9,
        Ready: 10,
        PreauditNew: 11,
        Preaudit: 12,
        DisabledNew: 13,
        Disabled: 14,
        UnusedNew: 15,
        Unused: 16,
        Unknown: 999,
    };

    protected get controlTestInstanceRepository(): ControlTestInstanceRepository {
        return this.getCustomTenantRepository(ControlTestInstanceRepository);
    }

    protected get findingExclusionRepository(): FindingExclusionRepository {
        return this.getCustomTenantRepository(FindingExclusionRepository);
    }

    protected get monitorInstanceExclusionRepository(): MonitorInstanceExclusionRepository {
        return this.getCustomTenantRepository(MonitorInstanceExclusionRepository);
    }

    protected get complianceCheckExclusionRepository(): ComplianceCheckExclusionRepository {
        return this.getCustomTenantRepository(ComplianceCheckExclusionRepository);
    }

    protected get connectionRepository(): ConnectionsRepository {
        return this.getCustomTenantRepository(ConnectionsRepository);
    }

    protected get personnelRepository(): PersonnelRepository {
        return this.getCustomTenantRepository(PersonnelRepository);
    }

    protected get productRepository(): ProductRepository {
        return this.getCustomTenantRepository(ProductRepository);
    }

    @Span()
    async getMonitoringSummaryResultsFromSourceDatabase(
        account: Account,
        indexingOptions: MonitoringIndexingOptions,
        workspaceId?: number,
        testIds?: number[],
    ): Promise<MonitorResultSummary[]> {
        let results: MonitorResultSummary[] = [];

        this.logger.debug(
            PolloAdapter.acct('indexingOptions', account)
                .setIdentifier({
                    workspaceId,
                    testIds: logifyTestIds(testIds ?? []),
                    testIdsCount: testIds?.length ?? 0,
                    indexingOptions: {
                        ...omit(indexingOptions, ['originalMonitorResultSummaries']),
                        originalMonitorResultSummariesCount:
                            indexingOptions?.originalMonitorResultSummaries?.length ?? 0,
                    },
                })
                .setSubContext(this.getMonitoringSummaryResultsFromSourceDatabase.name),
        );

        try {
            const controlTestInstances = await this.getControlTestInstances(
                account,
                workspaceId,
                testIds,
                indexingOptions,
            );

            if (isEmpty(controlTestInstances)) {
                this.logger.log(
                    PolloAdapter.acct(
                        'There were not any tests/monitors found in the source database given the query parameters. indexing will be skipped.',
                        account,
                    )
                        .setIdentifier({
                            workspaceId,
                            testIds: logifyTestIds(testIds ?? []),
                            testIdsCount: testIds?.length ?? 0,
                        })
                        .setSubContext(this.getMonitoringSummaryResultsFromSourceDatabase.name),
                );
                return [];
            }

            let isPersonnelExclusionsCountFeatureEnabled = false;
            try {
                isPersonnelExclusionsCountFeatureEnabled =
                    await this.featureFlagService.evaluateAsTenant(
                        {
                            category: FeatureFlagCategory.NONE,
                            name: FeatureFlag.RELEASE_PERSONNEL_EXCLUSIONS_COUNT,
                            defaultValue: false,
                        },
                        account,
                    );
            } catch (error) {
                this.logger.warn(
                    PolloAdapter.acct(
                        'Failed to evaluate personnel exclusions feature flag, using default',
                        account,
                    )
                        .setContext(this.constructor.name)
                        .setSubContext('IndexMonitorResultHandler')
                        .setIdentifier({ event }),
                );
            }

            const frameworks = await this.getFrameworks(workspaceId, testIds);

            const tickets: ControlTestInstanceTicketingSummary[] =
                await this.getMonitoringTicketingSummaries(account, workspaceId, testIds);

            let monitorInstanceExclusions: MonitorInstanceExclusion[] = [];
            let findingExclusions: FindingExclusion[] = [];
            let complianceCheckExclusions: Map<number, ExtendedComplianceCheckExclusion[]> =
                new Map<number, ExtendedComplianceCheckExclusion[]>();

            if (
                indexingOptions.calculateFindingsAndExclusionsCounts &&
                isNil(indexingOptions.exclusionsCountFromAutopilot)
            ) {
                monitorInstanceExclusions = await this.getMonitorInstanceExclusions(
                    account,
                    workspaceId,
                    testIds,
                );

                if (!isPersonnelExclusionsCountFeatureEnabled) {
                    complianceCheckExclusions = await this.getActiveComplianceCheckExclusions(
                        account,
                        workspaceId,
                        controlTestInstances,
                    );
                }

                findingExclusions = await this.getComplianceAsCodeFindingExclusions(
                    account,
                    workspaceId,
                    controlTestInstances,
                );
            }

            const activeConnections = await this.getActiveConnectionsSourceData(
                account,
                workspaceId,
            );

            if (!isEmpty(activeConnections)) {
                const activeConnectionIds = activeConnections.map(ac => ac.id);
                // Filter not active connection exclusions from monitor instance exclusions
                monitorInstanceExclusions = monitorInstanceExclusions.filter(
                    x => !x.connectionId || activeConnectionIds.includes(x.connectionId),
                );
            }

            const testsToTransformToResults = await this.transformAllToControlTestInstancesDetails(
                account,
                workspaceId,
                controlTestInstances,
                activeConnections,
                monitorInstanceExclusions,
                complianceCheckExclusions,
            );

            const batchResults = await promiseAllSettledInBatches(
                testsToTransformToResults,
                MonitoringSummaryIndexingConstants.CONCURRENT_BATCH_SIZE_TRANSFORM_SOURCE_DATA_TO_MONITOR_RESULT_SUMMARY,
                async cti => {
                    const frameworkResult = frameworks.find(
                        x => x.controlTestInstanceId === cti.id,
                    );
                    const frameworkNames = frameworkResult?.frameworkNames
                        ? frameworkResult.frameworkNames.split('|').map(name => name.trim())
                        : [];
                    return this.transformControlTestInstanceToMonitorResult(
                        cti,
                        account,
                        activeConnections,
                        tickets.filter(tm => tm.testId === cti.testId),
                        frameworkNames,
                        monitorInstanceExclusions.filter(
                            x => x.controlTestInstance.testId === cti.testId,
                        ),
                        complianceCheckExclusions.get(cti.testId) ?? [],
                        findingExclusions.filter(x => x.testId === cti.testId),
                        indexingOptions,
                    );
                },
            );

            if (batchResults.rejected && batchResults.rejected.count > 0) {
                // log rejections
                this.logger.error(
                    PolloAdapter.acct(
                        'transformControlTestInstanceToMonitorResult rejections',
                        account,
                    )
                        .setIdentifier({
                            workspaceId,
                            testIds: logifyTestIds(testIds ?? []),
                            testIdsCount: testIds?.length ?? 0,
                            rejectedCount: batchResults.rejected.count,
                            rejectedReasons: batchResults.rejected.reasons,
                        })
                        .setSubContext(this.getMonitoringSummaryResultsFromSourceDatabase.name),
                    batchResults.rejected.reasons.join(),
                );
            }

            results = batchResults.fulfilled;
        } catch (error) {
            this.error(error, account);
        }

        return results;
    }

    @Span()
    private async transformAllToControlTestInstancesDetails(
        account: Account,
        workspaceId: number | undefined,
        controlTestInstances: ControlTestInstance[],
        activeConnections: ConnectionEntity[],
        monitorInstanceExclusions: MonitorInstanceExclusion[],
        complianceCheckExclusions: Map<number, ExtendedComplianceCheckExclusion[]>,
    ): Promise<ControlTestInstanceDetails[]> {
        const batchedResults = await promiseAllSettledInBatches(
            controlTestInstances,
            MonitoringSummaryIndexingConstants.CONCURRENT_BATCH_SIZE_TRANSFORM_CTI_TO_CTI_DETAILS,
            async cti => {
                return this.transformCTIToControlTestInstanceDetails(
                    cti,
                    activeConnections,
                    monitorInstanceExclusions,
                    complianceCheckExclusions.get(cti.testId) ?? [],
                );
            },
        );

        if (batchedResults.rejected && batchedResults.rejected.count > 0) {
            this.logger.error(
                PolloAdapter.acct(
                    `There were ${batchedResults.rejected.count} errors generating the control test instance details`,
                    account,
                )
                    .setContext(this.constructor.name)
                    .setSubContext(this.transformAllToControlTestInstancesDetails.name)
                    .setIdentifier({
                        workspaceId,
                        rejectedCount: batchedResults.rejected.count,
                        rejectedReasons: batchedResults.rejected.reasons,
                    }),
                batchedResults.rejected.reasons.join(),
            );
        }

        return batchedResults.fulfilled;
    }

    @Span()
    private async getTicketsSourceData(
        account: Account,
        workspaceId: number | undefined,
        testIds: number[] | undefined,
    ): Promise<ControlTestInstanceWorkspaceTicketMapping[]> {
        const ticketsQuery = this.controlTestInstanceRepository
            .createQueryBuilder('controlTestInstance')
            .leftJoin('controlTestInstance.products', 'product')
            .innerJoin('controlTestInstance.monitorInstances', 'monitorInstance')
            .innerJoin('monitorInstance.tickets', 'ticket')
            .select([
                'controlTestInstance.id as controlTestInstanceId',
                'controlTestInstance.testId as testId',
                'monitorInstance.id as monitorInstanceId',
                'product.id as workspaceId',
                'product.name as workspaceName',
                'ticket.id as ticketId',
                'ticket.isDone as ticketIsDone',
                'ticket.deletedAt as ticketDeletedAt',
            ]);

        if (workspaceId) {
            ticketsQuery.andWhere('product.id = :workspaceId', { workspaceId });
        }

        if (testIds && testIds.length > 0) {
            ticketsQuery.andWhere('controlTestInstance.test_id IN (:...testIds)', {
                testIds,
            });
        }
        const [firstPageResults, totalCount] = await Promise.all([
            ticketsQuery
                .take(MonitoringSummaryIndexingConstants.PAGE_SIZE_GET_TICKETS_SOURCE_DATA)
                .getRawMany(),
            ticketsQuery.getCount(),
        ]);

        const remainingPages = Math.ceil(
            (totalCount - MonitoringSummaryIndexingConstants.PAGE_SIZE_GET_TICKETS_SOURCE_DATA) /
                MonitoringSummaryIndexingConstants.PAGE_SIZE_GET_TICKETS_SOURCE_DATA,
        );
        const pages = Array.from({ length: remainingPages }, (_, i) => i + 1);
        let allTickets: ControlTestInstanceWorkspaceTicketMapping[] = [...firstPageResults];

        if (pages.length > 0) {
            const { fulfilled, rejected } = await promiseAllSettledInBatches(
                pages,
                MonitoringSummaryIndexingConstants.CONCURRENT_BATCH_SIZE_GET_TICKETS_SOURCE_DATA,
                async (page: number) => {
                    return ticketsQuery
                        .skip(
                            page *
                                MonitoringSummaryIndexingConstants.PAGE_SIZE_GET_TICKETS_SOURCE_DATA,
                        )
                        .take(MonitoringSummaryIndexingConstants.PAGE_SIZE_GET_TICKETS_SOURCE_DATA)
                        .getRawMany();
                },
            );

            if (rejected.count > 0) {
                this.logger.error(
                    PolloAdapter.acct(`Failed to fetch ${rejected.count} pages of tickets`, account)
                        .setContext(this.constructor.name)
                        .setSubContext(this.getTicketsSourceData.name)
                        .setIdentifier({
                            workspaceId,
                            failedPages: rejected.count,
                            reasons: rejected.reasons,
                        }),
                );
            }

            allTickets = [...allTickets, ...fulfilled.flat()];
        }

        return allTickets;
    }

    @Span()
    async getMonitoringTicketingSummaries(
        account: Account,
        workspaceId: number | undefined,
        testIds: number[] | undefined,
    ): Promise<ControlTestInstanceTicketingSummary[]> {
        const ticketsData: ControlTestInstanceWorkspaceTicketMapping[] =
            await this.getTicketsSourceData(account, workspaceId, testIds);

        const ticketsByTest = ticketsData.reduce((accMap, ticket) => {
            const existing = accMap.get(ticket.testId) || [];
            accMap.set(ticket.testId, [...existing, ticket]);
            return accMap;
        }, new Map<number, ControlTestInstanceWorkspaceTicketMapping[]>());

        const ticketingSummaries: ControlTestInstanceTicketingSummary[] = [];

        for (const [testId, tickets] of ticketsByTest.entries()) {
            let aggregateStatus = '';
            const filteredTickets = tickets.filter(
                ticket => ticket.testId === testId && !ticket.ticketDeletedAt,
            );
            if (filteredTickets.length > 0) {
                const doneTicketsCount = filteredTickets.filter(
                    ticket => ticket.ticketIsDone,
                ).length;
                const nonDoneTicketsCount = filteredTickets.filter(
                    ticket => !ticket.ticketIsDone,
                ).length;

                if (nonDoneTicketsCount > 0) {
                    aggregateStatus = this.mapEnumValue(
                        TaskManagementStatus.IN_PROGRESS,
                        TaskManagementStatus,
                    );
                } else if (doneTicketsCount > 0 && nonDoneTicketsCount === 0) {
                    aggregateStatus = this.mapEnumValue(
                        TaskManagementStatus.ARCHIVED,
                        TaskManagementStatus,
                    );
                }
            }

            ticketingSummaries.push({
                testId,
                aggregateStatus,
            });
        }

        return ticketingSummaries;
    }

    @Span()
    private async getActiveConnectionsSourceData(
        account: Account,
        workspaceId: number | undefined,
    ): Promise<ConnectionEntity[]> {
        const activeConnectionsScopedToWorkspace =
            await this.connectionRepository.getActiveConnectionsByProductId(workspaceId ?? 1);
        const activeConnectionsScopedToTenant =
            await this.connectionRepository.getOrganizationalActiveConnections();

        return [...activeConnectionsScopedToWorkspace, ...activeConnectionsScopedToTenant];
    }

    @Span()
    async getMonitoringConnectionSummaries(
        account: Account,
        workspaceId: number | undefined,
        testIds: number[] | undefined,
        indexingOptions: MonitoringIndexingOptions,
    ): Promise<MonitorResultsSummaryConnections[]> {
        const connectionSummaries: MonitorResultsSummaryConnections[] = [];
        const [controlTestInstances, activeConnections] = await Promise.all([
            this.getControlTestInstances(account, workspaceId, testIds, indexingOptions),
            this.getActiveConnectionsSourceData(account, workspaceId),
        ]);

        await asyncForOf(controlTestInstances, async controlTestInstance => {
            const clientTypes = getEnabledClientTypesByAutopilotTaskType(
                controlTestInstance?.monitorInstance?.autopilotTaskType,
            );

            const connections = await this.getActivelyConnectedConnections(
                controlTestInstance,
                activeConnections,
                clientTypes,
            );

            connectionSummaries.push({
                testId: controlTestInstance.testId,
                workspaceId: controlTestInstance.workspaceId,
                checkStatus: this.mapEnumValue(controlTestInstance.checkStatus, CheckStatus),
                connections,
                possibleConnections: uniq([
                    ...clientTypes.map(clientType => ClientType[clientType]),
                    ...MonitoringSummaryResultSourceDataService.getVulnerabilityClientTypes(
                        controlTestInstance.testId,
                    ).map(clientType => ClientType[clientType]),
                ]),
            });
        });

        return connectionSummaries;
    }

    async getMonitoringControlsAndFrameworksSummaries(
        account: Account,
        workspaceId: number | undefined,
        testIds: number[] | undefined,
        indexingOptions: MonitoringIndexingOptions,
    ): Promise<MonitorResultsSummaryControlsAndFrameworks[]> {
        const summaries: MonitorResultsSummaryControlsAndFrameworks[] = [];
        const [controlTestInstances, frameworks] = await Promise.all([
            this.getControlTestInstances(account, workspaceId, testIds, indexingOptions),
            this.getFrameworks(workspaceId, testIds),
        ]);

        await asyncForOf(controlTestInstances, async controlTestInstance => {
            const testFrameworks = frameworks
                .find(x => x.controlTestInstanceId === controlTestInstance.id)
                ?.frameworkNames.split('|')
                .map(name => name.trim());

            summaries.push({
                testId: controlTestInstance.testId,
                workspaceId: controlTestInstance.workspaceId,
                controls: controlTestInstance.controls?.map(control => control.code) ?? [],
                frameworks: testFrameworks ?? [],
            });
        });

        return summaries;
    }

    @Span()
    private async getControlTestInstances(
        account: Account,
        workspaceId: number | undefined,
        testIds: number[] | undefined,
        indexingOptions: MonitoringIndexingOptions,
    ) {
        const ctiQuery: SelectQueryBuilder<ControlTestInstance> =
            this.controlTestInstanceRepository.createQueryBuilder('controlTestInstance');

        ctiQuery
            .leftJoinAndSelect('controlTestInstance.products', 'product')
            .leftJoin('controlTestInstance.monitorInstances', 'monitorInstance')
            .addSelect([
                'monitorInstance.id',
                'monitorInstance.monitorTemplateId',
                'monitorInstance.checkResultStatus',
                'monitorInstance.checkFrequency',
                'monitorInstance.autopilotTaskType',
                'monitorInstance.requestDescriptions',
                'monitorInstance.failedTestDescription',
                'monitorInstance.evidenceCollectionDescription',
                'monitorInstance.remedyDescription',
                'monitorInstance.url',
                'monitorInstance.enabled',
                'monitorInstance.expiresAt',
                'monitorInstance.createdAt',
                'monitorInstance.updatedAt',
            ])
            .leftJoinAndSelect('monitorInstance.monitorInstanceCheckTypes', 'mict')
            .leftJoinAndSelect('controlTestInstance.controls', 'control')
            .leftJoinAndSelect('controlTestInstance.recipes', 'AutopilotRecipeInstance');

        if (
            indexingOptions.calculateFindingsAndExclusionsCounts &&
            isNil(indexingOptions.findingsCountFromAutopilot)
        ) {
            ctiQuery.addSelect('monitorInstance.metadata');
        }

        if (workspaceId) {
            ctiQuery.andWhere('product.id = :workspaceId', { workspaceId });
        }

        if (testIds && testIds.length > 0) {
            ctiQuery.andWhere('controlTestInstance.test_id IN (:...testIds)', { testIds });
        }

        ctiQuery
            .andWhere('controlTestInstance.runMode != :runModeAP2Shadow', {
                runModeAP2Shadow: RunMode.AP2_SHADOW_TEST,
            })
            .andWhere(
                'NOT (controlTestInstance.runMode = :runMode AND controlTestInstance.releasedAt IS NOT NULL)',
                { runMode: 1 },
            );

        const { results: allResults, batchResults } = await this.paginateQuery({
            query: ctiQuery,
            pageSize: MonitoringSummaryIndexingConstants.PAGE_SIZE_GET_CONTROL_TEST_INSTANCES_DATA,
            batchSize:
                MonitoringSummaryIndexingConstants.CONCURRENT_BATCH_SIZE_GET_CONTROL_TEST_INSTANCES_DATA,
            benchmarkMsg: 'getControlTestInstances',
        });

        if (batchResults && batchResults.rejected.count > 0) {
            this.logger.error(
                PolloAdapter.acct(
                    `Failed to fetch ${batchResults.rejected.count} pages of control test instances`,
                    account,
                )
                    .setContext(this.constructor.name)
                    .setSubContext(this.getControlTestInstances.name)
                    .setIdentifier({
                        workspaceId,
                        failedPages: batchResults.rejected.count,
                        reasons: batchResults.rejected.reasons,
                    }),
            );
        }

        return allResults;
    }

    @Span()
    private async getFrameworks(
        workspaceId: number | undefined,
        testIds: number[] | undefined,
    ): Promise<{ controlTestInstanceId: number; frameworkNames: string }[]> {
        const query = this.controlTestInstanceRepository.manager
            .createQueryBuilder()
            .select('cti.id', 'controlTestInstanceId')
            .addSelect("GROUP_CONCAT(DISTINCT framework.name SEPARATOR '|')", 'frameworkNames')
            .from('control_test_instance', 'cti')
            .innerJoin(
                'controls_control_test_instances_v2_map',
                'ctiMap',
                'ctiMap.fk_control_test_instance_id = cti.id',
            )
            .innerJoin('control', 'control', 'control.id = ctiMap.fk_control_id')
            .innerJoin('controls_requirements_map', 'crMap', 'crMap.fk_control_id = control.id')
            .innerJoin('requirement', 'req', 'req.id = crMap.fk_requirement_id')
            .innerJoin('requirement_index', 'reqIndex', 'reqIndex.fk_requirement_id = req.id')
            .innerJoin('framework', 'framework', 'framework.id = reqIndex.fk_framework_id')
            .innerJoin(
                'products_control_test_instances_map',
                'pctim',
                'pctim.fk_control_test_instance_id = cti.id',
            );
        if (workspaceId) {
            query.andWhere('pctim.fk_product_id = :workspaceId', { workspaceId });
        }

        if (testIds && testIds.length > 0) {
            query.andWhere('cti.test_id IN (:...testIds)', { testIds });
        }
        query.groupBy('cti.id, pctim.fk_product_id');
        return query.getRawMany();
    }

    @Span()
    private async getMonitorInstanceExclusions(
        account: Account,
        workspaceId: number | undefined,
        testIds: number[] | undefined,
    ): Promise<MonitorInstanceExclusion[]> {
        const monitorInstanceExclusionsQuery = this.monitorInstanceExclusionRepository
            .createQueryBuilder('MonitorInstanceExclusion')
            .innerJoinAndSelect(
                'MonitorInstanceExclusion.controlTestInstance',
                'controlTestInstance',
            )
            .leftJoin('controlTestInstance.products', 'product');

        if (workspaceId) {
            monitorInstanceExclusionsQuery.andWhere('product.id = :workspaceId', {
                workspaceId,
            });
        }

        if (testIds && testIds.length > 0) {
            monitorInstanceExclusionsQuery.andWhere(
                'controlTestInstance.test_id IN (:...testIds)',
                {
                    testIds,
                },
            );
        }

        const { results: allResults, batchResults } = await this.paginateQuery({
            query: monitorInstanceExclusionsQuery,
            pageSize:
                MonitoringSummaryIndexingConstants.PAGE_SIZE_GET_MONITOR_INSTANCE_EXCLUSIONS_DATA,
            batchSize:
                MonitoringSummaryIndexingConstants.CONCURRENT_BATCH_SIZE_GET_MONITOR_INSTANCE_EXCLUSIONS_DATA,
            benchmarkMsg: 'getMonitorInstanceExclusions',
        });

        if (batchResults && batchResults.rejected.count > 0) {
            this.logger.error(
                PolloAdapter.acct(
                    `Failed to fetch ${batchResults.rejected.count} pages of monitor instance exclusions`,
                    account,
                )
                    .setContext(this.constructor.name)
                    .setSubContext(this.getMonitorInstanceExclusions.name)
                    .setIdentifier({
                        workspaceId,
                        failedPages: batchResults.rejected.count,
                        reasons: batchResults.rejected.reasons,
                    }),
            );
        }

        return allResults;
    }

    @Span()
    private async getComplianceAsCodeFindingExclusions(
        account: Account,
        workspaceId: number | undefined,
        controlTestInstances: ControlTestInstance[],
    ): Promise<FindingExclusion[]> {
        if (isEmpty(controlTestInstances)) {
            return [];
        }

        const cacTestIds = map(
            controlTestInstances.filter(t => t.source === TestSource.ACORN),
            'testId',
        );

        if (isEmpty(cacTestIds)) {
            return [];
        }

        const benchmark = new Benchmark();
        benchmark.start();

        const findingExclusionsQuery =
            this.findingExclusionRepository.createQueryBuilder('FindingExclusion');

        findingExclusionsQuery.andWhere('FindingExclusion.test_id IN (:...testIds)', {
            testIds: cacTestIds,
        });

        const { results: allResults, batchResults } = await this.paginateQuery({
            query: findingExclusionsQuery,
            pageSize:
                MonitoringSummaryIndexingConstants.PAGE_SIZE_GET_COMPLIANCE_AS_CODE_FINDING_EXCLUSIONS_DATA,
            batchSize:
                MonitoringSummaryIndexingConstants.CONCURRENT_BATCH_SIZE_GET_COMPLIANCE_AS_CODE_FINDING_EXCLUSIONS_DATA,
            benchmarkMsg: 'getComplianceAsCodeFindingExclusions',
        });

        benchmark.end();

        if (batchResults && batchResults.rejected.count > 0) {
            this.logger.error(
                PolloAdapter.acct(
                    `Failed to fetch ${batchResults.rejected.count} pages of finding exclusions`,
                    account,
                )
                    .setContext(this.constructor.name)
                    .setSubContext(this.getComplianceAsCodeFindingExclusions.name)
                    .setIdentifier({
                        workspaceId,
                        failedPages: batchResults.rejected.count,
                        reasons: batchResults.rejected.reasons,
                    })
                    .setExecutionTime(benchmark.time()),
            );
        }

        return allResults;
    }

    @Span()
    private async transformControlTestInstanceToMonitorResult(
        controlTestInstance: ControlTestInstanceDetails,
        account: Account,
        activeConnections: ConnectionEntity[],
        tickets: ControlTestInstanceTicketingSummary[],
        frameworkNames: string[],
        monitorInstanceExclusions: MonitorInstanceExclusion[],
        complianceCheckExclusions: ExtendedComplianceCheckExclusion[],
        findingExclusions: FindingExclusion[],
        indexingOptions: MonitoringIndexingOptions,
    ): Promise<MonitorResultSummary> {
        const monitorResult = new MonitorResultSummary(
            account.id,
            controlTestInstance.products?.[0]?.id || 1,
        );
        try {
            monitorResult.testId = controlTestInstance.testId;
            monitorResult.testName = controlTestInstance.name;

            monitorResult.checkResultStatus = this.mapEnumValue(
                controlTestInstance.checkResultStatus,
                CheckResultStatus,
            );
            monitorResult.checkStatus = this.mapEnumValue(
                controlTestInstance.checkStatus,
                CheckStatus,
            );
            monitorResult.category = this.mapEnumValue(
                controlTestInstance.monitorInstances?.[0]?.monitorInstanceCheckTypes?.[0]
                    ?.checkType,
                CheckType,
            );
            monitorResult.controls = controlTestInstance.controls?.map(control => control.code);
            monitorResult.frameworks = frameworkNames;

            if (!isEmpty(tickets)) {
                monitorResult.ticketStatus = tickets[0].aggregateStatus;
            } else {
                monitorResult.ticketStatus = '';
            }

            monitorResult.testType = this.determineTestType(
                controlTestInstance.source,
                controlTestInstance.draft,
            );

            const monitorInstance = controlTestInstance.monitorInstances?.[0];

            monitorResult.connections = [];
            monitorResult.possibleConnections = [];

            if (!isNil(monitorInstance)) {
                const clientTypes = getEnabledClientTypesByAutopilotTaskType(
                    monitorInstance.autopilotTaskType,
                );
                monitorResult.connections = this.getActivelyConnectedConnections(
                    controlTestInstance,
                    activeConnections,
                    clientTypes,
                );
                monitorResult.canManageExclusions = controlTestInstance.canManageExclusions;
                if (
                    [CheckStatus.DISABLED, CheckStatus.UNUSED].includes(
                        controlTestInstance.checkStatus,
                    )
                ) {
                    monitorResult.findingsCount = 0;
                    monitorResult.exclusionsCount = 0;
                } else if (!indexingOptions.calculateFindingsAndExclusionsCounts) {
                    if (!isEmpty(indexingOptions.originalMonitorResultSummaries)) {
                        const originalMonitorResultSummary =
                            indexingOptions?.originalMonitorResultSummaries?.find(
                                monitorResultSummary =>
                                    monitorResultSummary.testId === controlTestInstance.testId,
                            );
                        if (originalMonitorResultSummary) {
                            monitorResult.findingsCount =
                                originalMonitorResultSummary.findingsCount;
                            monitorResult.exclusionsCount =
                                originalMonitorResultSummary.exclusionsCount;
                        }
                    } else {
                        monitorResult.findingsCount = 0;
                        monitorResult.exclusionsCount = 0;
                    }
                } else if (
                    indexingOptions.calculateFindingsAndExclusionsCounts &&
                    !isNil(indexingOptions.findingsCountFromAutopilot)
                ) {
                    monitorResult.findingsCount = indexingOptions.findingsCountFromAutopilot;
                    if (!isNil(indexingOptions.exclusionsCountFromAutopilot)) {
                        monitorResult.exclusionsCount =
                            indexingOptions.exclusionsCountFromAutopilot;
                    } else {
                        const exclusionsCount = await this.calculateExclusionsCount(
                            controlTestInstance,
                            monitorInstanceExclusions,
                            complianceCheckExclusions,
                            findingExclusions,
                        );

                        monitorResult.exclusionsCount = exclusionsCount;
                    }
                } else {
                    const monitorFindings = await getMonitorFindingItems(
                        account,
                        controlTestInstance.testId,
                        controlTestInstance,
                        activeConnections,
                        findingExclusions,
                    );

                    monitorResult.findingsCount = monitorFindings
                        ? monitorFindings.monitorFindingItems.length
                        : 0;

                    const exclusionsCount = await this.calculateExclusionsCount(
                        controlTestInstance,
                        monitorInstanceExclusions,
                        complianceCheckExclusions,
                        findingExclusions,
                    );

                    monitorResult.exclusionsCount = exclusionsCount;
                }

                monitorResult.possibleConnections = uniq([
                    ...clientTypes.map(clientType => ClientType[clientType]),
                    ...MonitoringSummaryResultSourceDataService.getVulnerabilityClientTypes(
                        controlTestInstance.testId,
                    ).map(clientType => ClientType[clientType]),
                ]);
            }

            monitorResult.hasExclusions = monitorResult.exclusionsCount > 0;

            monitorResult.updatedAt =
                controlTestInstance.lastCheck ?? controlTestInstance.updatedAt;

            if (!isNil(controlTestInstance.releasedAt)) {
                monitorResult.isNew = this.isNew(controlTestInstance.releasedAt);
            }

            monitorResult.sortPriority =
                MonitoringSummaryResultSourceDataService.determineSortOrder(monitorResult);
        } catch (error) {
            this.error(error, account);
        }

        return monitorResult;
    }

    private mapEnumValue<T>(value: any, enumType: T): string {
        if (value === undefined) {
            return '';
        }
        const numericValue = Number(value);
        return !isNaN(numericValue) ? enumType[numericValue] : '';
    }

    private determineTestType(testType: TestSource, draft: boolean): string {
        switch (testType) {
            case TestSource.DRATA:
                return MonitorV2TestTypeOptions[MonitorV2TestTypeOptions.DRATA];
            case TestSource.CUSTOM:
                return draft
                    ? MonitorV2TestTypeOptions[MonitorV2TestTypeOptions.CUSTOM_DRAFT]
                    : MonitorV2TestTypeOptions[MonitorV2TestTypeOptions.CUSTOM_PUBLISHED];
            case TestSource.ACORN:
                return MonitorV2TestTypeOptions[MonitorV2TestTypeOptions.COMPLIANCE_AS_CODE];
            case TestSource.DRATA_LIBRARY:
                return draft
                    ? MonitorV2TestTypeOptions[MonitorV2TestTypeOptions.DRATA_CUSTOM_DRAFT]
                    : MonitorV2TestTypeOptions[MonitorV2TestTypeOptions.DRATA_CUSTOM_PUBLISHED];
            default:
                return '';
        }
    }

    private getActivelyConnectedConnections(
        controlTestInstance: ControlTestInstanceDetails,
        activeConnections: ConnectionEntity[],
        enabledClientTypes: ClientType[],
    ): string[] {
        if (controlTestInstance.checkStatus === CheckStatus.UNUSED) {
            return [];
        }

        if (!controlTestInstance.isCustom()) {
            const activeConnectionsClientType = activeConnections.map(ac => ac.clientType);
            const activeEnabledClientTypes = activeConnectionsClientType.filter(ac =>
                enabledClientTypes.includes(ac),
            );

            const vulnerabilityClientTypes =
                MonitoringSummaryResultSourceDataService.getVulnerabilityClientTypes(
                    controlTestInstance.testId,
                );

            return uniq([
                ...vulnerabilityClientTypes.map(ac => ClientType[ac]),
                ...activeEnabledClientTypes.map(ac => ClientType[ac]),
            ]);
        } else if (
            controlTestInstance.isCustom() ||
            [RunMode.AP2_SHADOW_TEST, RunMode.AP2].includes(controlTestInstance.runMode)
        ) {
            return (
                controlTestInstance.recipes
                    ?.flatMap((recipe: AutopilotRecipeInstance) => recipe.recipe.providers)
                    ?.map((provider: Provider) => provider.provider) ?? []
            );
        }

        // No active connections could be determined for this monitor instance
        return [];
    }

    private async calculateExclusionsCount(
        controlTestInstance: ControlTestInstanceDetails,
        monitorInstanceExclusions: MonitorInstanceExclusion[],
        complianceCheckExclusions: ExtendedComplianceCheckExclusion[],
        findingExclusions: FindingExclusion[],
    ): Promise<number> {
        if (controlTestInstance.source === TestSource.ACORN) {
            return this.filterFindingsExclusionsForTest(
                findingExclusions,
                controlTestInstance.testId,
            ).length;
        } else {
            let complianceCheckExclusionCount = 0;
            if (
                !isNil(controlTestInstance.complianceCheckExclusions) &&
                !isEmpty(controlTestInstance.complianceCheckExclusions)
            ) {
                complianceCheckExclusionCount =
                    controlTestInstance.complianceCheckExclusions.reduce(
                        (acc, exclusion) => acc + exclusion.personnel.length,
                        0,
                    );
            } else {
                complianceCheckExclusionCount = (
                    await this.filterComplianceCheckExclusionsByTest(
                        complianceCheckExclusions,
                        controlTestInstance.testId,
                    )
                ).length;
            }

            let monitorExclusionCount = 0;
            if (
                !isNil(controlTestInstance.monitorInstanceExclusions) &&
                !isEmpty(controlTestInstance.monitorInstanceExclusions)
            ) {
                monitorExclusionCount = controlTestInstance.monitorInstanceExclusions.length;
            } else {
                monitorExclusionCount = this.filterMonitorInstanceExclusionsForTest(
                    monitorInstanceExclusions,
                    controlTestInstance.testId,
                ).length;
            }

            return monitorExclusionCount + complianceCheckExclusionCount;
        }
    }

    private filterMonitorInstanceExclusionsForTest(
        monitorInstanceExclusions: MonitorInstanceExclusion[],
        testId: number,
    ): MonitorInstanceExclusion[] {
        return monitorInstanceExclusions.filter(x => x.controlTestInstance?.testId === testId);
    }

    private filterFindingsExclusionsForTest(
        findingExclusions: FindingExclusion[],
        testId: number,
    ): FindingExclusion[] {
        return findingExclusions.filter(x => x.testId === testId);
    }

    private async filterComplianceCheckExclusionsByTest(
        complianceCheckExclusions: ExtendedComplianceCheckExclusion[],
        testId: number,
    ): Promise<ExtendedComplianceCheckExclusion[]> {
        const complianceCheckType = ControlTestInstanceTestIdComplianceCheckTypes.get(testId);
        if (isNil(complianceCheckType)) {
            return [];
        }
        const testSpecificExclusions = complianceCheckExclusions.filter(x =>
            x.complianceCheckTypes
                .map(cct => cct.type)
                .includes(complianceCheckType.complianceCheckType),
        );

        if (isEmpty(testSpecificExclusions)) {
            return [];
        }

        return testSpecificExclusions;
    }

    private isNew(releasedAt: Date): boolean {
        if (!releasedAt) {
            return false;
        }
        const amountOfDaysForNew = 45;
        const releaseDatePlusAmount = new Date(releasedAt);
        releaseDatePlusAmount.setDate(releasedAt.getDate() + amountOfDaysForNew);
        return new Date() < releaseDatePlusAmount;
    }

    @Span()
    async getMonitoringSummaryResultsForTests(
        account: Account,
        indexingOptions: MonitoringIndexingOptions,
        workspaceId?: number,
        testIds?: number[],
    ): Promise<MonitorResultSummary[]> {
        let results: MonitorResultSummary[] = [];

        try {
            results = await this.getMonitoringSummaryResultsFromSourceDatabase(
                account,
                indexingOptions,
                workspaceId,
                testIds,
            );
        } catch (error) {
            this.logger.error(
                PolloAdapter.acct(
                    'error while reading source data to derive monitoring summary results for open search indexing',
                    account,
                    this.constructor.name,
                )
                    .setError(error)
                    .setIdentifier({
                        workspaceId,
                        testIds: logifyTestIds(testIds ?? []),
                        testIdsCount: testIds?.length ?? 0,
                    })
                    .setSubContext(this.getMonitoringSummaryResultsFromSourceDatabase.name),
            );
        }

        return results;
    }

    @Span()
    public async listWorkspaces(workspaceIds?: number[]): Promise<Product[]> {
        return this.productRepository.find({
            where: { id: workspaceIds?.length ? In(workspaceIds) : undefined },
        });
    }

    async getAllTestIdsForAccountWorkspace(
        account: Account,
        workspaceId: number,
    ): Promise<number[]> {
        return this.controlTestInstanceRepository.getTestIdsByWorkspaceId(workspaceId);
    }

    private static determineSortOrder(monitorResult: MonitorResultSummary): number {
        const { checkStatus, checkResultStatus, isNew } = monitorResult;

        if (checkStatus === CheckStatus[CheckStatus.TESTING]) {
            return isNew ? this.sortPriority.TestingNew : this.sortPriority.Testing;
        }

        if (checkStatus === CheckStatus[CheckStatus.ENABLED]) {
            if (checkResultStatus === CheckResultStatus[CheckResultStatus.ERROR]) {
                return isNew ? this.sortPriority.ErrorNew : this.sortPriority.Error;
            }

            if (checkResultStatus === CheckResultStatus[CheckResultStatus.FAILED]) {
                return isNew ? this.sortPriority.FailedNew : this.sortPriority.Failed;
            }

            if (checkResultStatus === CheckResultStatus[CheckResultStatus.PASSED]) {
                return isNew ? this.sortPriority.PassedNew : this.sortPriority.Passed;
            }

            if (checkResultStatus === CheckResultStatus[CheckResultStatus.READY]) {
                return isNew ? this.sortPriority.ReadyNew : this.sortPriority.Ready;
            }

            if (checkResultStatus === CheckResultStatus[CheckResultStatus.PREAUDIT]) {
                return isNew ? this.sortPriority.PreauditNew : this.sortPriority.Preaudit;
            }
        }

        if (checkStatus === CheckStatus[CheckStatus.DISABLED]) {
            return isNew ? this.sortPriority.DisabledNew : this.sortPriority.Disabled;
        }

        if (checkStatus === CheckStatus[CheckStatus.UNUSED]) {
            return isNew ? this.sortPriority.UnusedNew : this.sortPriority.Unused;
        }

        return this.sortPriority.Unknown;
    }

    private static getVulnerabilityClientTypes(testId: number): string[] {
        //vulnerability tests are an edge-case. They are not mapped to autopilotTaskType
        return Object.keys(VulnerabilityMonitoringConstants.TEST_IDS).filter(t =>
            VulnerabilityMonitoringConstants.TEST_IDS[t]?.includes(testId),
        );
    }

    @Span()
    private async getActiveComplianceCheckExclusions(
        account: Account,
        workspaceId: number | undefined,
        controlTestInstances: ControlTestInstance[],
    ): Promise<Map<number, ExtendedComplianceCheckExclusion[]>> {
        const exclusionsByTestId = new Map<number, ExtendedComplianceCheckExclusion[]>();

        for (const controlTestInstance of controlTestInstances) {
            const { complianceCheckType } =
                ControlTestInstanceTestIdComplianceCheckTypes.get(controlTestInstance.testId) ?? {};

            if (isNil(complianceCheckType)) {
                continue;
            }

            // eslint-disable-next-line no-await-in-loop
            const testExclusions = await this.getComplianceCheckExclusionsPersonnel(
                controlTestInstance.testId,
            );

            if (!isNil(testExclusions)) {
                exclusionsByTestId.set(controlTestInstance.testId, testExclusions);
            }
        }
        return exclusionsByTestId;
    }

    private async transformCTIToControlTestInstanceDetails(
        controlTestInstance: ControlTestInstance,
        activeConnections: ConnectionEntity[],
        monitorInstanceExclusions: MonitorInstanceExclusion[],
        complianceCheckExclusions: ExtendedComplianceCheckExclusion[],
    ): Promise<ControlTestInstanceDetails> {
        const testLevelMonitorInstanceExclusions = monitorInstanceExclusions.filter(
            x => x.controlTestInstance.testId === controlTestInstance.testId,
        );

        const testLevelComplianceCheckExclusions = await this.filterComplianceCheckExclusionsByTest(
            complianceCheckExclusions,
            controlTestInstance.testId,
        );

        const canManageExclusions =
            isNil(testLevelComplianceCheckExclusions) ||
            isEmpty(testLevelComplianceCheckExclusions);

        const availableConnections: AvailableConnection[] = activeConnections.map(connection => ({
            clientType: ClientType[connection.clientType],
        }));

        return Object.assign(controlTestInstance, {
            canManageExclusions,
            complianceCheckExclusions: isNil(testLevelComplianceCheckExclusions)
                ? []
                : testLevelComplianceCheckExclusions,
            monitorInstanceExclusions: testLevelMonitorInstanceExclusions,
            parentTestId: get(controlTestInstance.parent, 'testId', null),
            availableConnections,
        }) as ControlTestInstanceDetails;
    }

    /**
     *
     * @param {number} testId
     * @returns {Promise<ExtendedComplianceCheckExclusion[] | null>}
     */
    @Span()
    async getComplianceCheckExclusionsPersonnel(
        testId: number,
    ): Promise<ExtendedComplianceCheckExclusion[] | null> {
        const { complianceCheckType, employmentStatuses: testEmploymentStatuses } =
            ControlTestInstanceTestIdComplianceCheckTypes.get(testId) ?? {};

        if (isNil(complianceCheckType)) {
            return null;
        }

        const complianceCheckExclusions =
            await this.complianceCheckExclusionRepository.findByComplianceCheckType(
                complianceCheckType,
            );

        let exclusionsWithPersonnel: ExtendedComplianceCheckExclusion[] = [];
        const complianceCheckExclusionForPersonnel: ComplianceCheckExclusion[] = [];

        for (const complianceCheckExclusion of complianceCheckExclusions) {
            const { targetType, targetId } = complianceCheckExclusion;
            switch (targetType) {
                case ComplianceCheckExclusionTargetType.PERSONNEL:
                    complianceCheckExclusionForPersonnel.push(complianceCheckExclusion);
                    break;
                case ComplianceCheckExclusionTargetType.GROUP:
                    // eslint-disable-next-line no-await-in-loop
                    const personnelIds = await this.personnelRepository.getPersonnelIdsByGroupIds([
                        Number(targetId),
                    ]);

                    const personnelByGroupIds: Personnel[] = [];
                    const chunkIds = chunk(
                        personnelIds,
                        MonitoringSummaryIndexingConstants.PAGE_SIZE_GET_PERSONNEL_DATA,
                    );
                    for (const chunkId of chunkIds) {
                        const chunkedPersonnelByGroupIds =
                            // eslint-disable-next-line no-await-in-loop
                            await this.personnelRepository.findByIdsWithOptions(chunkId, {
                                setDevices: true,
                                setGroups: false,
                            });
                        personnelByGroupIds.push(...chunkedPersonnelByGroupIds);
                    }

                    exclusionsWithPersonnel.push(
                        createExtendedComplianceCheckExclusion(
                            complianceCheckExclusion,
                            personnelByGroupIds,
                        ),
                    );
                    break;
                case ComplianceCheckExclusionTargetType.EMPLOYMENT_STATUS:
                    const personnelByEmploymentStatus =
                        // eslint-disable-next-line no-await-in-loop
                        await this.personnelRepository.findByEmploymentStatuses([Number(targetId)]);
                    exclusionsWithPersonnel.push(
                        createExtendedComplianceCheckExclusion(
                            complianceCheckExclusion,
                            personnelByEmploymentStatus,
                        ),
                    );
                    break;
                case ComplianceCheckExclusionTargetType.COMPANY:
                    const allEmploymentStatuses =
                        EmploymentStatusOptionsFilterMap.get(
                            EmploymentStatusOptionsFilter.ALL_PERSONNEL,
                        ) ?? [];

                    const personnelByCompany =
                        // eslint-disable-next-line no-await-in-loop
                        await this.personnelRepository.findByEmploymentStatuses(
                            allEmploymentStatuses,
                        );
                    exclusionsWithPersonnel.push(
                        createExtendedComplianceCheckExclusion(
                            complianceCheckExclusion,
                            personnelByCompany,
                        ),
                    );
                    break;
            }
        }

        if (!isEmpty(complianceCheckExclusionForPersonnel)) {
            const personnelIds = complianceCheckExclusionForPersonnel.map(({ targetId }) =>
                Number(targetId),
            );

            const chunkIds = chunk(
                personnelIds,
                MonitoringSummaryIndexingConstants.PAGE_SIZE_GET_PERSONNEL_DATA,
            );
            for (const chunkId of chunkIds) {
                // eslint-disable-next-line no-await-in-loop
                const chunkedPersonnel = await this.personnelRepository.find({
                    where: {
                        id: In(chunkId),
                    },
                    select: {
                        id: true,
                        employmentStatus: true,
                        user: {
                            firstName: true,
                            lastName: true,
                            email: true,
                            id: true,
                        },
                        devices: {
                            id: true,
                            alias: true,
                            asset: {
                                id: true,
                                name: true,
                            },
                            agentAssignation: {
                                id: true,
                                agent: {
                                    id: true,
                                    serialNumber: true,
                                    operationalState: true,
                                },
                            },
                        },
                    },
                    relations:
                        testId === 64
                            ? [
                                  'devices',
                                  'user',
                                  'devices.agentAssignation',
                                  'devices.agentAssignation.agent',
                                  'devices.asset',
                              ]
                            : ['user'],
                    loadEagerRelations: false,
                });

                if (!isEmpty(chunkedPersonnel)) {
                    for (const p of chunkedPersonnel) {
                        const personnelCompliance = find(
                            complianceCheckExclusions,
                            ({ targetType, targetId }) =>
                                targetType === ComplianceCheckExclusionTargetType.PERSONNEL &&
                                Number(targetId) === p.id,
                        );

                        if (isNil(personnelCompliance)) {
                            continue;
                        }
                        exclusionsWithPersonnel.push(
                            createExtendedComplianceCheckExclusion(personnelCompliance, [p]),
                        );
                    }
                }
            }
        }

        // filter by employmentStatus if test is restricted by one
        if (testEmploymentStatuses && !isEmpty(testEmploymentStatuses)) {
            exclusionsWithPersonnel = exclusionsWithPersonnel.filter(exclusion => {
                exclusion.personnel = exclusion.personnel.filter(
                    pers => testEmploymentStatuses?.includes(pers.employmentStatus) ?? false,
                );
                return !isEmpty(exclusion.personnel);
            });
        }

        return exclusionsWithPersonnel;
    }

    private async paginateQuery<T>(params: {
        query: SelectQueryBuilder<T>;
        pageSize: number;
        batchSize: number;
        benchmarkMsg?: string;
    }): Promise<{
        results: T[];
        batchResults?: {
            fulfilled: T[][];
            rejected: { count: number; reasons: string[] };
        };
    }> {
        const { query, pageSize, batchSize } = params;
        const [firstPageResults, totalCount] = await query.take(pageSize).getManyAndCount();
        const remainingPages = Math.ceil((totalCount - pageSize) / pageSize);
        const pages = Array.from({ length: remainingPages }, (_, i) => i + 1);
        let allResults = [...firstPageResults];

        if (pages.length > 0) {
            const batchResults = await promiseAllSettledInBatches(
                pages,
                batchSize,
                async (page: number) => {
                    return query
                        .skip(page * pageSize)
                        .take(pageSize)
                        .getMany();
                },
            );

            allResults = [...allResults, ...batchResults.fulfilled.flat()];
            return { results: allResults, batchResults };
        }

        return { results: allResults };
    }
}

import { TestSource } from '@drata/enums';
import { MonitorData } from 'app/autopilot/classes/monitor-data.class';
import { resultExclusionFuzzyMatch } from 'app/autopilot/helper/fuzzy-matching.helper';
import { ConnectionEntity } from 'app/companies/connections/entities/connection.entity';
import { FindingExclusion } from 'app/iac-scanning/entities/finding-exclusion.entity';
import { isAwsResource } from 'app/monitors/dtos/csv/factory/findings-csv.helper';
import { MonitorInstanceExclusion } from 'app/monitors/entities/monitor-instance-exclusion.entity';
import { MonitorInstance } from 'app/monitors/entities/monitor-instance.entity';
import { ControlTestInstanceDetails } from 'app/monitors/types/control-test-instance-details.type';
import { MonitorFindingItem } from 'app/monitors/types/monitor-finding-item.type';
import { MonitorInstanceExclusionFailResultMap } from 'app/monitors/types/monitor-instance-exclusion-result-failure-map.type';
import { Account } from 'auth/entities/account.entity';
import { ClientType } from 'commons/enums/auth/client-type.enum';
import { ConnectionState } from 'commons/enums/auth/state-type.enum';
import { AutopilotTaskType } from 'commons/enums/autopilot/autopilot-task-type.enum';
import { CheckType } from 'commons/enums/check-type.enum';
import { Severity } from 'commons/enums/iac-scanning/severity.enum';
import { OperationalState } from 'commons/enums/operational-state-status.enum';
import * as crypto from 'crypto';
import { chain, isEmpty, isNil } from 'lodash';
import { PolloMessage } from 'pollo-logger/classes/pollo.message';
import { PolloLogger } from 'pollo-logger/pollo.logger';

export async function getMonitorFindingItems(
    account: Account,
    testId: number,
    controlTestInstanceDetails: ControlTestInstanceDetails,
    connections: ConnectionEntity[],
    cacExclusions: FindingExclusion[] = [],
): Promise<{
    category: CheckType;
    autopilotTaskType: AutopilotTaskType;
    monitorFindingItems: MonitorFindingItem[];
    testSource: TestSource;
}> {
    const category =
        controlTestInstanceDetails.monitorInstances?.[0]?.monitorInstanceCheckTypes?.[0]?.checkType;
    const autopilotTaskType = controlTestInstanceDetails.monitorInstances?.[0]?.autopilotTaskType;

    const monitorInstanceMetadata =
        controlTestInstanceDetails.monitorInstances?.[0]?.getMetadata() ?? new Map();

    const testSource = controlTestInstanceDetails.source;

    const connectionMap = new Map(connections.map(conn => [conn.id, conn]));
    const cacExcludedFindingIds = new Set(cacExclusions.map(e => e.findingId));
    const [fuzzyExcludedMonitorDataIds, excludedByTargetIds] = getFuzzyExcludedMonitorDataIds(
        controlTestInstanceDetails.monitorInstances,
        controlTestInstanceDetails.monitorInstanceExclusions,
    );
    const excludedUserIds = new Set(
        controlTestInstanceDetails.complianceCheckExclusions?.flatMap(e =>
            e.personnel.map(p => p.userId),
        ) ?? [],
    );

    const monitorFindingItems = Array.from(monitorInstanceMetadata.entries()).flatMap(
        ([connectionId, monitorInstanceMetadataItem]) => {
            const connection = connectionMap.get(connectionId);

            // Only if the connection is present we check if it is connected and active
            // If the connection is not present, we still include the results since they might not be connection based
            if (
                !isNil(connection) &&
                (!connection.connectedAt || connection.state !== ConnectionState.ACTIVE)
            ) {
                return [];
            }

            return monitorInstanceMetadataItem.monitorResult.fail
                .filter(monitorData =>
                    shouldIncludeFinding(
                        monitorData,
                        controlTestInstanceDetails,
                        fuzzyExcludedMonitorDataIds,
                        excludedUserIds,
                        cacExcludedFindingIds,
                        excludedByTargetIds,
                    ),
                )
                .map(monitorData =>
                    mapMonitorDataToFindingItem(
                        monitorData,
                        controlTestInstanceDetails,
                        connection,
                        category,
                        account.getCurrentProduct() ? account.getCurrentProduct().id : 1,
                        testId,
                        connectionId,
                        autopilotTaskType,
                    ),
                );
        },
    );

    if (
        autopilotTaskType === AutopilotTaskType.AGENT_ANTI_VIRUS_APPLICATION &&
        monitorFindingItems.length === 1 &&
        isEmpty(monitorFindingItems[0].name) &&
        isEmpty(monitorFindingItems[0].id)
    ) {
        return {
            category,
            autopilotTaskType,
            monitorFindingItems: [],
            testSource,
        };
    }

    return { category, autopilotTaskType, monitorFindingItems, testSource };
}

export function shouldIncludeFinding(
    monitorData: MonitorData,
    controlTestInstanceDetails: ControlTestInstanceDetails,
    fuzzyExcludedMonitorDataIds: Set<string>,
    excludedUserIds: Set<number>,
    cacExcludedFindingIds: Set<string>,
    excludedByTargetIds: Set<string>,
): boolean {
    const { canManageExclusions, source } = controlTestInstanceDetails;
    if (canManageExclusions) {
        if (source === TestSource.ACORN) {
            try {
                const parsedRaw: any = monitorData.raw;
                const findingId = parsedRaw?.findingId;
                return isNil(findingId) || !cacExcludedFindingIds.has(findingId);
            } catch {
                return true;
            }
        }

        if (
            !isEmpty(monitorData.findingTargetId) &&
            !excludedByTargetIds.has(monitorData.findingTargetId ?? '')
        ) {
            return true;
        }

        if (fuzzyExcludedMonitorDataIds.has(monitorData.id)) {
            return false;
        }

        return true;
    } else {
        if (excludedUserIds.has(+monitorData.id)) {
            return false;
        }
    }

    return true;
}

export function getFuzzyExcludedMonitorDataIds(
    monitorInstances: MonitorInstance[],
    exclusions: MonitorInstanceExclusion[],
): [Set<string>, Set<string>] {
    const excludedIds = new Set<string>();
    const excludedByTargetIds = new Set<string>();

    const allFailResults = chain(monitorInstances)
        .flatMap(monitorInstance => Array.from(monitorInstance.getMetadata().values()))
        .map(metadataItem => metadataItem.monitorResult)
        .flatMap(results =>
            results.excluded ? results.fail.concat(results.excluded) : results.fail,
        )
        .value();

    for (const exclusion of exclusions) {
        for (const failResult of allFailResults) {
            if (resultExclusionFuzzyMatch(failResult, exclusion)) {
                excludedIds.add(failResult.id);
                if (!isEmpty(failResult.findingTargetId)) {
                    excludedByTargetIds.add(failResult.findingTargetId);
                }
                break;
            }
        }
    }

    return [excludedIds, excludedByTargetIds];
}

export function matchExclusionsToFailures(
    monitorInstances: MonitorInstance[],
    exclusions: MonitorInstanceExclusion[],
): MonitorInstanceExclusionFailResultMap[] {
    const results: MonitorInstanceExclusionFailResultMap[] = [];
    // Extract all FailResults from monitorInstances
    const allFailResults: any[] = [];
    for (const monitorInstance of monitorInstances) {
        const instanceMetadata = monitorInstance.getMetadata();
        for (const metadata of instanceMetadata) {
            const metadataItem = metadata[1];
            allFailResults.push(...metadataItem.monitorResult.fail);
            if (metadataItem.monitorResult.excluded) {
                allFailResults.push(...metadataItem.monitorResult.excluded);
            }
        }
    }
    // Try to match exclusions to FailResults
    for (const exclusion of exclusions) {
        let currentFailResult = null;
        for (const failResult of allFailResults) {
            if (resultExclusionFuzzyMatch(failResult, exclusion)) {
                currentFailResult = failResult;
                break;
            }
        }
        results.push({
            exclusion,
            failResult: currentFailResult,
        });
    }
    return results;
}

export function mapMonitorDataToFindingItem(
    monitorData: MonitorData,
    controlTestInstance: ControlTestInstanceDetails,
    connection: ConnectionEntity | undefined,
    category: CheckType,
    workspaceId: number,
    testId: number,
    connectionId: number,
    autopilotTaskType: AutopilotTaskType,
): MonitorFindingItem {
    let parsedRaw: any = monitorData.raw;
    const clientType = connection?.clientType;
    if (typeof parsedRaw === 'string') {
        try {
            parsedRaw = JSON.parse(monitorData.raw);
        } catch (error) {
            PolloLogger.logger().error(
                PolloMessage.msg('Failed to parse raw JSON:').setError(error),
            );
            parsedRaw = {};
        }
    }

    const isAwsConnection = connection?.clientType && isAwsResource(connection.clientType);

    const findingItem: MonitorFindingItem = {
        accountId: monitorData.accountId ?? '',
        category: CheckType[category],
        connection: isNil(connection?.clientType) ? '' : ClientType[connection.clientType],
        connectionAlias: connection?.clientAlias ?? connection?.clientId ?? '',
        connectionId: connectionId,
        device: monitorData.displayName ?? '',
        email: monitorData.email ?? '',
        eventId: '', // Placeholder
        findingId: monitorData.id ?? '',
        id: monitorData.id,
        name: monitorData.name ?? monitorData.displayName ?? '',
        property: parsedRaw?.property ?? '',
        region: monitorData.region ?? '',
        resourceName: monitorData.displayName ?? '',
        severity: '',
        testId,
        workspaceId,
        drataDevice: parsedRaw?.drataDevice?.deviceName ?? '',
        personnel: parsedRaw?.personnel ?? '',
        serialNumber: parsedRaw?.drataDevice?.serialNumber ?? '',
        preventionPolicy: '',
        operationalState: '',
        issueId: monitorData?.displayName ?? '',
        issueTitle: monitorData?.accountName ?? '',
        entityName: monitorData?.name ?? '',
        resourceType: parsedRaw?.resourceType ?? monitorData?.resourceType ?? '',
        repository: parsedRaw?.repository ?? '',
        vulnerabilityTitle: monitorData?.displayName ?? '',
        resourceId: isAwsConnection
            ? (monitorData?.resourceArn ?? '')
            : (monitorData?.finding ?? monitorData?.id ?? ''),
        dueDate: monitorData.dueAt ? String(monitorData.dueAt) : '',
        ticketDescription: monitorData.name ?? '',
        link: monitorData?.url ?? '',
        organizationalUnitId: monitorData.organizationalUnitId ?? '',
    };
    if (controlTestInstance.source === TestSource.ACORN) {
        findingItem.findingId = parsedRaw?.findingId;
        findingItem.repository = parsedRaw?.repositoryName;
        findingItem.severity = Severity[parsedRaw?.severity];
        findingItem.resourceType = parsedRaw?.resourceMetadata?.resourceType;
        findingItem.resourceName = parsedRaw?.resourceMetadata?.resourceName;
        findingItem.property = parsedRaw?.configId;
    }

    const resourceOrganizationPath = monitorData.resourceOrganizationPath ?? '';
    if (resourceOrganizationPath !== '') {
        findingItem.resourceName = monitorData?.name;
    }

    switch (autopilotTaskType) {
        case AutopilotTaskType.INTERNAL_INFRASTRUCTURE_IDENTITIES_UNIQUE: //test 98
        case AutopilotTaskType.INFRASTRUCTURE_ACLS_PUBLIC_REMOTE_ADMIN_ACCESS_RESTRICTED: //test 227
        case AutopilotTaskType.ONLY_AUTHORIZED_USERS_CAN_ACCESS_LOG_SINKS: //test 110
        case AutopilotTaskType.COMPANY_INFRASTRUCTURE_MFA_ENABLED: //test 88
            findingItem.resourceName = monitorData.name ?? '';
            break;
        default:
            break;
    }

    //get operational state for CrowdStrike and SentinelOne
    const operationalStateRaw = parsedRaw?.agentData?.operationalState ?? '';
    if (!isEmpty(operationalStateRaw)) {
        if (clientType === ClientType.SENTINEL_ONE) {
            findingItem.operationalState =
                operationalStateRaw === OperationalState.NOT_DISABLED
                    ? 'Healthy'
                    : operationalStateRaw;
        } else if (clientType === ClientType.CROWDSTRIKE) {
            findingItem.operationalState =
                operationalStateRaw === OperationalState.CS_PREVENTION_APPLIED ||
                OperationalState.NOT_DISABLED
                    ? 'Enabled'
                    : 'Disabled';
        }
        if (clientType === ClientType.WIZ) {
            findingItem.severity = monitorData?.type ?? '';
        }
    }

    if (controlTestInstance.source !== TestSource.ACORN) {
        findingItem.findingId = createFindingHash(findingItem);
    }

    return findingItem;
}

function getMd5HashString(input: string): string {
    const hash = crypto.createHash('MD5');
    hash.update(input);
    return hash.digest('hex');
}

function createFindingHash(item: MonitorFindingItem): string {
    // Include all properties from MonitorFindingItem in a stable order
    const stableObject = {
        accountId: item.accountId,
        category: item.category,
        connection: item.connection,
        connectionAlias: item.connectionAlias,
        connectionId: item.connectionId,
        device: item.device,
        drataDevice: item.drataDevice,
        dueDate: item.dueDate,
        email: item.email,
        entityName: item.entityName,
        eventId: item.eventId,
        id: item.id,
        issueId: item.issueId,
        issueTitle: item.issueTitle,
        link: item.link,
        name: item.name,
        operationalState: item.operationalState,
        organizationalUnitId: item.organizationalUnitId,
        personnel: item.personnel,
        preventionPolicy: item.preventionPolicy,
        property: item.property,
        region: item.region,
        repository: item.repository,
        resourceId: item.resourceId,
        resourceName: item.resourceName,
        resourceType: item.resourceType,
        serialNumber: item.serialNumber,
        severity: item.severity,
        testId: item.testId,
        ticketDescription: item.ticketDescription,
        vulnerabilityTitle: item.vulnerabilityTitle,
        workspaceId: item.workspaceId,
    };

    // Create a deterministic string representation
    const stringToHash = Object.entries(stableObject)
        .sort(([keyA], [keyB]) => keyA.localeCompare(keyB))
        .map(([key, value]) => `${key}:${value ?? ''}`)
        .join('|');

    return getMd5HashString(stringToHash);
}

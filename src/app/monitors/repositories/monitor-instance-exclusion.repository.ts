import { SortDir, SortType } from '@drata/enums';
import { MonitorExclusionsConnectionClientTypesRequestDto } from 'app/monitors/dtos/monitor-exclusions-connection-client-types-request.dto';
import { MonitorExclusionsRequestDto } from 'app/monitors/dtos/monitor-exclusions-request.dto';
import { MonitorExclusionsResourcesRequestDto } from 'app/monitors/dtos/monitor-exclusions-resources-request.dto';
import { MonitorInstanceExclusion as Exclusion } from 'app/monitors/entities/monitor-instance-exclusion.entity';
import { MonitorExclusionExpand } from 'app/monitors/enums/monitor-exclusion-expand.enum';
import { ConnectionClientAlias } from 'app/monitors/types/connection-client-alias.type';
import { MonitorExclusionsRequestType } from 'app/monitors/types/monitor-exclusions-request.type';
import { Account } from 'auth/entities/account.entity';
import { PaginationExclusionRequestDto } from 'autopilot2/dtos/pagination-exclusion-request.dto';
import { ClientType } from 'commons/enums/auth/client-type.enum';
import {
    applyCursorFilters,
    getCursorPageAutoIncrementId,
    setupCursorQueryAutoIncrementId,
} from 'commons/helpers/cursor-repository.helper';
import { getEnumValuesByPartialMatch } from 'commons/helpers/enum.helper';
import { getSkip } from 'commons/helpers/pagination.helper';
import { addProductClause, getProductId } from 'commons/helpers/products.helper';
import { getSortDir } from 'commons/helpers/sort.helper';
import { BaseRepository } from 'commons/repositories/base.repository';
import { CursorPage } from 'commons/types/cursor-page.type';
import { PaginationType } from 'commons/types/pagination.type';
import config from 'config';
import { CustomRepository } from 'database/typeorm/typeorm.extensions.decorator';
import { isEmpty, isNil } from 'lodash';
import { Brackets, FindOptionsOrderValue, ILike, SelectQueryBuilder } from 'typeorm';

@CustomRepository(Exclusion)
export class MonitorInstanceExclusionRepository extends BaseRepository<Exclusion> {
    /**
     *
     * @param account
     * @param testId
     * @param connectionId
     * @returns
     */
    async getMonitorExclusionsByTestIdAndConnectionId(
        account: Account,
        testId: number,
        connectionId: number | null,
    ): Promise<Exclusion[]> {
        const test = 'MonitorInstanceExclusion.controlTestInstance';
        const user = 'MonitorInstanceExclusion.exclusionDesignator';

        const query = this.createQueryBuilder('MonitorInstanceExclusion')
            .innerJoinAndSelect(`${test}`, 'ControlTestInstance')
            .innerJoinAndSelect(`${user}`, 'ExclusionDesignator')
            .leftJoinAndSelect('ExclusionDesignator.roles', 'ExclusionDesignatorRole')
            .where('ControlTestInstance.testId = :testId', {
                testId,
            });

        if (!isNil(connectionId)) {
            query.andWhere('MonitorInstanceExclusion.fk_connection_id = :connectionId', {
                connectionId,
            });
        } else {
            query.andWhere('MonitorInstanceExclusion.fk_connection_id IS NULL');
        }

        addProductClause(query, 'ControlTestInstance', getProductId(account));

        return query.getMany();
    }

    /**
     *
     * @param {Account} account
     * @param {number} testId
     * @returns {Promise<Exclusion[]>}
     */
    async findByTestId(account: Account, testId: number): Promise<Exclusion[]> {
        const query = this.createQueryBuilder('MonitorInstanceExclusion')
            .innerJoinAndSelect(
                'MonitorInstanceExclusion.controlTestInstance',
                'ControlTestInstance',
            )
            .innerJoinAndSelect(
                'MonitorInstanceExclusion.exclusionDesignator',
                'ExclusionDesignator',
            )
            .leftJoinAndSelect('ExclusionDesignator.roles', 'ExclusionDesignatorRole')
            .where('ControlTestInstance.testId = :testId', {
                testId,
            });

        addProductClause(query, 'ControlTestInstance', getProductId(account));

        return query.getMany();
    }

    /**
     *
     * @param account
     * @param targetId
     * @param testId
     * @param connectionId
     * @returns
     */
    async getMonitorExclusionByTargetIdAndTestId(
        account: Account,
        targetId: string,
        testId: number,
        connectionId: number | null,
    ): Promise<Exclusion> {
        const query = this.createQueryBuilder('MonitorInstanceExclusion')
            .innerJoinAndSelect(
                'MonitorInstanceExclusion.controlTestInstance',
                'ControlTestInstance',
            )
            .innerJoinAndSelect(
                'MonitorInstanceExclusion.exclusionDesignator',
                'ExclusionDesignator',
            )
            .leftJoinAndSelect('ExclusionDesignator.roles', 'ExclusionDesignatorRole')
            .where(`MonitorInstanceExclusion.targetId = :targetId`, {
                targetId,
            })
            .andWhere('ControlTestInstance.testId = :testId', {
                testId,
            });

        if (!isNil(connectionId)) {
            query.andWhere('MonitorInstanceExclusion.fk_connection_id = :connectionId', {
                connectionId,
            });
        } else {
            query.andWhere('MonitorInstanceExclusion.fk_connection_id IS NULL');
        }

        addProductClause(query, 'ControlTestInstance', getProductId(account));

        return query.getOne();
    }

    /**
     *
     * @param dto
     * @returns
     */
    async getAllMonitorInstanceExclusion(
        dto: PaginationExclusionRequestDto,
    ): Promise<PaginationType<Exclusion>> {
        const { page, limit, testId, connectionId, workspaceId } = dto;

        const skip = getSkip(page, limit);
        const query = this.createQueryBuilder('MonitorInstanceExclusion')
            .innerJoin('MonitorInstanceExclusion.controlTestInstance', 'ControlTestInstance')
            .addSelect('ControlTestInstance.testId')
            .skip(skip)
            .take(limit);

        if (!isNil(workspaceId)) {
            query
                .leftJoin('ControlTestInstance.products', 'Product')
                .where('Product.id = :workspaceId', { workspaceId });
        }

        if (!isNil(testId)) {
            query.andWhere('ControlTestInstance.testId = :testId', {
                testId,
            });
        }

        if (!isNil(connectionId)) {
            query.andWhere('MonitorInstanceExclusion.fk_connection_id = :connectionId', {
                connectionId,
            });
        }

        const [exclusions, total] = await query.getManyAndCount();

        const paginationData: PaginationType<Exclusion> = {
            data: exclusions,
            page,
            limit,
            total,
        };

        return paginationData;
    }

    /**
     *
     * @param account
     * @param targetIds
     * @param testIds
     * @returns
     */
    getMonitorExclusionsByTargetIdsAndTestIds(
        account: Account,
        targetIds: string[],
        testIds: number[],
    ): Promise<Exclusion[]> {
        if (isEmpty(targetIds)) {
            return Promise.resolve([]);
        }

        const query = this.createQueryBuilder('MonitorInstanceExclusion')
            .innerJoin(
                'MonitorInstanceExclusion.controlTestInstance',
                'ControlTestInstance',
                'ControlTestInstance.testId IN (:...testIds)',
                {
                    testIds,
                },
            )
            .where(`MonitorInstanceExclusion.targetId IN (:...targetIds)`, {
                targetIds,
            });

        addProductClause(query, 'ControlTestInstance', getProductId(account));

        return query.getMany();
    }

    /**
     *
     * @param controlTestInstanceId
     * @returns
     */
    getExclusionsByControlTestInstance(controlTestInstanceId: number): Promise<Exclusion[]> {
        const test = 'MonitorInstanceExclusion.controlTestInstance';
        const user = 'MonitorInstanceExclusion.exclusionDesignator';
        const query = this.createQueryBuilder('MonitorInstanceExclusion')
            .innerJoinAndSelect(`${test}`, 'ControlTestInstance')
            .innerJoinAndSelect(`${user}`, 'ExclusionDesignator')
            .leftJoinAndSelect('ExclusionDesignator.roles', 'ExclusionDesignatorRole')
            .where(
                'MonitorInstanceExclusion.fk_control_test_instance_id = :controlTestInstanceId',
                {
                    controlTestInstanceId,
                },
            );

        return query.getMany();
    }

    getExclusionsByControlTestInstanceId(controlTestInstanceId: number): Promise<Exclusion[]> {
        return this.createQueryBuilder('MonitorInstanceExclusion')
            .select([
                'MonitorInstanceExclusion.exclusionReason',
                'MonitorInstanceExclusion.targetName',
                'MonitorInstanceExclusion.createdAt',
                'MonitorInstanceExclusion.deletedAt',
                'MonitorInstanceExclusionConnection.id',
                'ExclusionDesignator.firstName',
                'ExclusionDesignator.lastName',
            ])
            .leftJoin('MonitorInstanceExclusion.connection', 'MonitorInstanceExclusionConnection')
            .leftJoin('MonitorInstanceExclusion.exclusionDesignator', 'ExclusionDesignator')
            .where(
                'MonitorInstanceExclusion.fk_control_test_instance_id = :controlTestInstanceId',
                {
                    controlTestInstanceId,
                },
            )
            .withDeleted()
            .getMany();
    }

    async listByTestId(
        account: Account,
        testId: number,
        requestDto: MonitorExclusionsRequestDto,
    ): Promise<PaginationType<Exclusion>> {
        const {
            page = config.get('pagination.page'),
            limit = config.get('pagination.limit'),
            startDateFrom,
            startDateTo,
            createdBy: exclusionDesignatorId,
            q,
            targetName,
            connectionClientTypes: clientTypes = [],
            connections = [],
            sort,
            sortDir,
        } = requestDto;

        const query = this.createQueryBuilder('MonitorInstanceExclusion')
            .innerJoin(
                'MonitorInstanceExclusion.controlTestInstance',
                'ControlTestInstance',
                'ControlTestInstance.testId = :testId',
                {
                    testId,
                },
            )
            .innerJoin('ControlTestInstance.products', 'Product', 'Product.id = :productId', {
                productId: getProductId(account),
            })
            .leftJoinAndSelect('MonitorInstanceExclusion.connection', 'Connection')
            .leftJoinAndSelect(
                'MonitorInstanceExclusion.exclusionDesignator',
                'ExclusionDesignator',
            );

        // only connected connections (when connection exists)
        query.andWhere(
            new Brackets(qb => {
                qb.where('MonitorInstanceExclusion.fk_connection_id IS NULL') // No connection
                    .orWhere('Connection.connectedAt IS NOT NULL'); // Has connection and is connected
            }),
        );

        // created by
        if (!isNil(exclusionDesignatorId)) {
            query.andWhere('ExclusionDesignator.id = :exclusionDesignatorId', {
                exclusionDesignatorId,
            });
        }

        // target name
        if (!isNil(targetName)) {
            query.andWhere('MonitorInstanceExclusion.targetName = :targetName', { targetName });
        }

        // client types
        if (!isEmpty(clientTypes)) {
            query.andWhere('Connection.clientType IN (:...clientTypes)', { clientTypes });
        }

        // connections filters
        if (!isEmpty(connections)) {
            query.andWhere(
                new Brackets(qb => {
                    connections.forEach(({ clientAlias, clientId }, index) => {
                        if (!isNil(clientAlias) && !isNil(clientId)) {
                            qb.orWhere(
                                `Connection.clientAlias = :clientAlias${index} AND Connection.clientId = :clientId${index}`,
                                {
                                    [`clientAlias${index}`]: clientAlias,
                                    [`clientId${index}`]: clientId,
                                },
                            );
                        } else if (isNil(clientAlias)) {
                            qb.orWhere(
                                `Connection.clientAlias IS NULL AND Connection.clientId = :clientId${index}`,
                                {
                                    [`clientId${index}`]: clientId,
                                },
                            );
                        } else if (isNil(clientId)) {
                            qb.orWhere(
                                `Connection.clientAlias = :clientAlias${index} AND Connection.clientId IS NULL`,
                                {
                                    [`clientAlias${index}`]: clientAlias,
                                },
                            );
                        } else {
                            qb.orWhere(
                                'Connection.clientAlias IS NULL AND Connection.clientId IS NULL',
                            );
                        }
                    });
                }),
            );
        }

        // date range
        if (!isNil(startDateFrom) && !isNil(startDateTo)) {
            query.andWhere(
                'MonitorInstanceExclusion.createdAt BETWEEN :startDateFrom AND :startDateTo',
                {
                    startDateFrom,
                    startDateTo,
                },
            );
        } else if (!isNil(startDateFrom)) {
            query.andWhere('MonitorInstanceExclusion.createdAt >= :startDateFrom', {
                startDateFrom,
            });
        } else if (!isNil(startDateTo)) {
            query.andWhere('MonitorInstanceExclusion.createdAt >= :startDateTo', {
                startDateTo,
            });
        }

        let qClientTypes: ClientType[] = [];

        if (!isNil(q)) {
            // get client types for q
            qClientTypes = getEnumValuesByPartialMatch(q, ClientType);

            query.andWhere(
                new Brackets(qb => {
                    qb.where('MonitorInstanceExclusion.exclusionReason LIKE :qReason', {
                        qReason: `%${q}%`,
                    });

                    qb.orWhere('ExclusionDesignator.firstName LIKE :qFirstName', {
                        qFirstName: `%${q}%`,
                    }).orWhere('ExclusionDesignator.lastName LIKE :qLastName', {
                        qLastName: `%${q}%`,
                    });

                    qb.orWhere('MonitorInstanceExclusion.targetName LIKE :qTargetName', {
                        qTargetName: `%${q}%`,
                    });

                    if (!isEmpty(qClientTypes)) {
                        qb.orWhere('Connection.clientType IN (:qClientTypes)', { qClientTypes });
                    }

                    qb.orWhere('Connection.clientAlias LIKE :qClientAlias', {
                        qClientAlias: `%${q}%`,
                    }).orWhere('Connection.clientId LIKE :qClientId', {
                        qClientId: `%${q}%`,
                    });
                }),
            );
        }

        // sort
        this.resolveOrderQueryBuilder(query, sort, sortDir);

        // pagination
        query.skip(getSkip(page, limit)).take(limit);

        const [exclusions, total] = await query.getManyAndCount();

        return {
            data: exclusions,
            page,
            limit,
            total,
        };
    }

    async getMonitorInstanceExclusionsWithCursor(
        testId: number,
        workspaceId: number,
        request: MonitorExclusionsRequestType,
    ): Promise<CursorPage<Exclusion>> {
        const { cursor, size, sort, sortDir, expand, targetId } = request;

        const query = this.createQueryBuilder('MonitorInstanceExclusion')
            .innerJoinAndSelect(
                'MonitorInstanceExclusion.controlTestInstance',
                'ControlTestInstance',
            )
            .where('ControlTestInstance.testId = :testId', { testId });

        addProductClause(query, 'ControlTestInstance', workspaceId);

        this.expandSubCollections(query, expand);

        if (targetId) {
            query.andWhere('MonitorInstanceExclusion.targetId LIKE :targetId', {
                targetId: `${targetId}%`,
            });
        }

        setupCursorQueryAutoIncrementId(query, sort, sortDir);
        applyCursorFilters(cursor, query, sort, sortDir);

        return getCursorPageAutoIncrementId(query, sort, size);
    }

    async listResourcesByTestId(
        account: Account,
        testId: number,
        requestDto: MonitorExclusionsResourcesRequestDto,
    ): Promise<PaginationType<Exclusion>> {
        const {
            page = config.get('pagination.page'),
            limit = config.get('pagination.limit'),
            q: targetName,
        } = requestDto;

        const productId = getProductId(account);

        const [exclusions, total] = await this.findAndCount({
            select: {
                id: true,
                targetName: true,
            },
            where: {
                controlTestInstance: {
                    testId,
                    ...(productId && {
                        products: {
                            id: productId,
                        },
                    }),
                },
                ...(targetName && { targetName: ILike(`%${targetName || ''}%`) }),
            },
            order: this.resolveOrder(SortType.TARGET_NAME, SortDir.ASC),
            skip: getSkip(page, limit),
            take: limit,
        });

        return {
            data: exclusions,
            page,
            limit,
            total,
        };
    }

    async findByClientTypes(
        account: Account,
        testId: number,
        requestDto: MonitorExclusionsConnectionClientTypesRequestDto,
    ): Promise<PaginationType<ClientType>> {
        const {
            page = config.get('pagination.page'),
            limit = config.get('pagination.limit'),
            q,
        } = requestDto;

        let qClientTypes: ClientType[] = [];

        if (!isNil(q)) {
            qClientTypes = getEnumValuesByPartialMatch(q, ClientType);

            if (isEmpty(qClientTypes)) {
                return {
                    data: [],
                    total: 0,
                    limit: 1,
                    page: 1,
                };
            }
        }

        const query = this.createQueryBuilder('MonitorInstanceExclusion')
            .innerJoin(
                'MonitorInstanceExclusion.controlTestInstance',
                'ControlTestInstance',
                'ControlTestInstance.testId = :testId',
                {
                    testId,
                },
            )
            .innerJoin('ControlTestInstance.products', 'Product', 'Product.id = :productId', {
                productId: getProductId(account),
            })
            .leftJoin('MonitorInstanceExclusion.connection', 'Connection');

        if (!isEmpty(qClientTypes)) {
            query.andWhere('Connection.clientType IN (:...clientTypes)', {
                clientTypes: qClientTypes,
            });
        }

        const { count: total } = await query
            .select('COUNT(DISTINCT Connection.clientType)', 'count')
            .getRawOne();

        query.offset(getSkip(page, limit)).limit(limit);

        const rawClientTypes = await query
            .select('DISTINCT Connection.clientType', 'clientType')
            .getRawMany();

        return {
            data: rawClientTypes,
            total: Number(total),
            limit,
            page,
        };
    }

    async findByClientAliases(
        account: Account,
        testId: number,
        requestDto: MonitorExclusionsConnectionClientTypesRequestDto,
    ): Promise<PaginationType<ConnectionClientAlias>> {
        const {
            page = config.get('pagination.page'),
            limit = config.get('pagination.limit'),
            q,
        } = requestDto;

        const query = this.createQueryBuilder('MonitorInstanceExclusion')
            .innerJoin(
                'MonitorInstanceExclusion.controlTestInstance',
                'ControlTestInstance',
                'ControlTestInstance.testId = :testId',
                {
                    testId,
                },
            )
            .innerJoin('ControlTestInstance.products', 'Product', 'Product.id = :productId', {
                productId: getProductId(account),
            })
            .leftJoin('MonitorInstanceExclusion.connection', 'Connection');

        if (!isEmpty(q)) {
            query
                .where('Connection.clientAlias LIKE :clientAlias', {
                    clientAlias: `%${q}%`,
                })
                .orWhere('Connection.clientId LIKE :clientId', {
                    clientId: `%${q}%`,
                });
        }

        const { count: total } = await query
            .select(
                'COUNT(DISTINCT CONCAT(COALESCE(Connection.clientId, ""), "-", COALESCE(Connection.clientAlias, "")))',
                'count',
            )
            .getRawOne();

        query.offset(getSkip(page, limit)).limit(limit);

        const rawClientAliases = await query
            .distinct(true)
            .select('Connection.clientAlias', 'clientAlias')
            .addSelect('Connection.clientId', 'clientId')
            .getRawMany();

        return {
            data: rawClientAliases,
            total: Number(total),
            limit,
            page,
        };
    }

    private resolveOrder(sort?: SortType, sortDir?: SortDir) {
        const direction = isNil(sortDir)
            ? (SortDir[SortDir.DESC] as FindOptionsOrderValue)
            : (SortDir[sortDir] as FindOptionsOrderValue);

        switch (sort) {
            case SortType.TARGET_NAME:
                return {
                    targetName: direction,
                };
            case SortType.EXCLUSION_REASON:
                return {
                    exclusionReason: direction,
                };
            case SortType.CREATED_BY:
                return {
                    exclusionDesignator: {
                        firstName: direction,
                    },
                };
            case SortType.CONNECTION_CLIENT_ALIAS:
                return {
                    connection: {
                        clientId: direction,
                        clientAlias: direction,
                    },
                };
            case SortType.START_DATE:
            default:
                return {
                    createdAt: direction,
                };
        }
    }

    private resolveOrderQueryBuilder(
        query: SelectQueryBuilder<Exclusion>,
        sort: SortType = SortType.CREATED_AT,
        sortDir: SortDir = SortDir.ASC,
    ) {
        const dir = getSortDir(sortDir);

        switch (sort) {
            case SortType.TARGET_NAME:
                query.orderBy('MonitorInstanceExclusion.targetName', dir);
                break;
            case SortType.EXCLUSION_REASON:
                query.orderBy('MonitorInstanceExclusion.exclusionReason', dir);
                break;
            case SortType.CREATED_BY:
                query.orderBy('ExclusionDesignator.firstName', dir);
                break;
            case SortType.CONNECTION_CLIENT_ALIAS:
                query.orderBy('Connection.clientAlias', dir).addOrderBy('Connection.clientId', dir);
                break;
            case SortType.START_DATE:
            default:
                query.orderBy('MonitorInstanceExclusion.createdAt', dir);
                break;
        }
    }

    private expandSubCollections(
        query: SelectQueryBuilder<Exclusion>,
        expand: MonitorExclusionExpand[] | undefined,
    ): void {
        const expandMapping = {
            [MonitorExclusionExpand.connection]: () =>
                query.leftJoinAndSelect('MonitorInstanceExclusion.connection', 'Connection'),
            [MonitorExclusionExpand.exclusionDesignator]: () =>
                query
                    .leftJoinAndSelect(
                        'MonitorInstanceExclusion.exclusionDesignator',
                        'ExclusionDesignator',
                    )
                    .leftJoinAndSelect('ExclusionDesignator.roles', 'ExclusionDesignatorRole'),
        };

        [...new Set(expand)].forEach(prop => expandMapping[prop]?.());
    }
}

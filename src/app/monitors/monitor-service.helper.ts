import { CheckResultStatus } from '@drata/enums';
import { AutopilotRecipeInstance } from 'app/autopilot/entities/autopilot-recipe-instance.entity';
import { Task } from 'app/autopilot/tasks/task';
import { MonitorInstanceMetadataItem } from 'app/autopilot/types/monitor-instance-metadata-item.type';
import { ConnectionEntity } from 'app/companies/connections/entities/connection.entity';
import { Company } from 'app/companies/entities/company.entity';
import { ControlTestInstance } from 'app/monitors/entities/control-test-instance.entity';
import { MonitorInstance } from 'app/monitors/entities/monitor-instance.entity';
import { AutoRunTestAllowedClientType } from 'app/monitors/enums/auto-run-tests-allowed-client-types';
import { ProviderCatalogService } from 'app/provider-catalog/provider-catalog.service';
import { Account } from 'auth/entities/account.entity';
import { PolloAdapter } from 'commons/adapters/pollo.adapter';
import { ClientType } from 'commons/enums/auth/client-type.enum';
import { ProviderType } from 'commons/enums/auth/provider-type.enum';
import { AutopilotTaskType } from 'commons/enums/autopilot/autopilot-task-type.enum';
import { RequestDescriptionType } from 'commons/enums/request-description-type.enum';
import { connectionHasProviderType } from 'commons/helpers/connection.helper';
import { subtractDaysToDate } from 'commons/helpers/date.helper';
import { getImplementedAutopilotTaskTypesByClientType } from 'commons/helpers/monitor.helper';
import { promiseAllInBatches } from 'commons/helpers/promise.helper';
import config from 'config';
import { chain, isEmpty, isNil, isUndefined } from 'lodash';
import { PolloLogger } from 'pollo-logger/pollo.logger';

/**
 *
 * @param {ConnectionEntity} connectionEntity
 * @param {ConnectionEntity[]} connections
 * @return {AutopilotTaskType[]}
 */
export function findAutopilotTaskTypesForConnections(
    connections: ConnectionEntity[],
): AutopilotTaskType[] {
    let union = [];

    for (const connection of connections) {
        const implementedAutopilotTaskTypes = getImplementedAutopilotTaskTypesByClientType(
            connection.clientType,
            connection.getMetadata(),
        );

        union = union.concat(implementedAutopilotTaskTypes.filter(att => !union.includes(att)));
    }

    return union;
}

/**
 *
 * @param monitorInstance
 * @param connections
 * @returns
 */
export function resolveMonitorInstanceCheckResultStatus(
    monitorInstance: MonitorInstance,
    connections: ConnectionEntity[],
    task: Task<any>, // TODO: remove after logging completed
): CheckResultStatus {
    let resolvedCheckResultStatus = CheckResultStatus.PASSED;

    if (
        findMonitorInstanceCheckResultStatus(monitorInstance, connections, CheckResultStatus.ERROR)
    ) {
        if (
            monitorInstance.autopilotTaskType === 56 &&
            (monitorInstance.checkFrequency === 2 || monitorInstance.checkFrequency === 4) &&
            monitorInstance.controlTestInstance.controlTestTemplateId === 26
        ) {
            const clientType: string | null = isNil(task.getConnection()?.clientType)
                ? null
                : ClientType[task.getConnection()?.clientType as number];

            /**
             * Log here to get the list of accounts that are being toggled to passing
             */
            PolloLogger.logger().log(
                PolloAdapter.acct('Test 26 Passed from Standby', task.getAccount()).setIdentifier({
                    clientType,
                    errors: task.getErrors(),
                    connections: connections.map(connection => {
                        return {
                            id: connection.id,
                            clientType: ClientType[connection.clientType],
                            products: connection.products,
                        };
                    }),
                }),
            );

            resolvedCheckResultStatus = CheckResultStatus.PASSED;
        } else {
            resolvedCheckResultStatus = CheckResultStatus.ERROR;
        }
    } else if (
        findMonitorInstanceCheckResultStatus(monitorInstance, connections, CheckResultStatus.FAILED)
    ) {
        resolvedCheckResultStatus = CheckResultStatus.FAILED;
    }

    return resolvedCheckResultStatus;
}

function findMonitorInstanceCheckResultStatus(
    monitorInstance: MonitorInstance,
    connections: ConnectionEntity[],
    checkResultStatus: CheckResultStatus,
): boolean {
    const metadata = monitorInstance.getMetadata();

    for (const [connectionId, monitorInstanceMetadataItem] of metadata) {
        if (connectionId !== MonitorInstance.defaultMetadataKey) {
            const connection = connections.find(conn => conn.id === connectionId);

            if (isNil(connection)) {
                continue;
            }
        }

        if (monitorInstanceMetadataItem.checkResultStatus === checkResultStatus) {
            return true;
        }
    }

    return false;
}

export function getCleanMetadata(
    monitorInstance: MonitorInstance,
): Map<number, MonitorInstanceMetadataItem> {
    let metadata = monitorInstance.getMetadata();
    if (!isNil(metadata)) {
        return metadata;
    }
    // temporary to prevent possible blip
    monitorInstance.metadata = null;
    // should be a new map now
    metadata = monitorInstance.getMetadata();
    return metadata;
}

export function gcMetadata(
    metadata: Map<number, MonitorInstanceMetadataItem>,
    activeConnections: ConnectionEntity[],
    company: Company,
): void {
    // do some cleanup to remove deleted connection data
    const { agentEnabled } = company;
    for (const connectionId of metadata.keys()) {
        if (connectionId === MonitorInstance.defaultMetadataKey) {
            gcMetadataHandleDefaultConnectionId(
                activeConnections,
                agentEnabled,
                metadata,
                connectionId,
            );
        } else {
            gcMetadataHandleConnectionId(activeConnections, connectionId, agentEnabled, metadata);
        }
    }
}

function gcMetadataHandleConnectionId(
    activeConnections: ConnectionEntity[],
    connectionId: number,
    agentEnabled: boolean,
    metadata: Map<number, MonitorInstanceMetadataItem>,
) {
    const activeConnection = activeConnections.find(v => v.id === connectionId);
    if (isUndefined(activeConnection)) {
        // no connection exists with that id, remove it
        metadata.delete(connectionId);
        return;
    }

    // If the connection is not connected, remove it
    if (!activeConnection.connectedAt) {
        metadata.delete(connectionId);
        return;
    }

    if (!connectionHasProviderType(activeConnection, ProviderType.MDM)) {
        return;
    }

    const enabledConnections = activeConnections.every(({ enabledAt }) => isNil(enabledAt));
    if (agentEnabled && enabledConnections) {
        // agent data takes precedence, that data will be looked at.
        // This data will be ignored, delete it
        metadata.delete(connectionId);
        return;
    }
    // agent is disabled
    if (!isNil(activeConnection.enabledAt)) {
        // mdm connection is enabled, this connection will be referenced and we need to deleted it
        metadata.delete(connectionId);
        return;
    }
    // mdm connection is disabled, delete it
    metadata.delete(connectionId);
}

/**
 * If looking at the default (agent) connection id, delete it
 *  if and only if there is active mdm connection and the agent is disabled
 *
 * We shouldn't land in this function for tests that are solely driven by connections
 */
function gcMetadataHandleDefaultConnectionId(
    activeConnections: ConnectionEntity[],
    agentEnabled: boolean,
    metadata: Map<number, MonitorInstanceMetadataItem>,
    defaultMetadataKey: number,
) {
    const activeMdmConnection = activeConnections.some(
        connection =>
            connectionHasProviderType(connection, ProviderType.MDM) && !isNil(connection.enabledAt),
    );

    if (activeMdmConnection && !agentEnabled) {
        metadata.delete(defaultMetadataKey);
    }
}

/**
 *
 * @param connection
 * @param requestDescriptionType
 * @returns
 */
export function resolveMonitorInstanceMetadataKey(
    connection: ConnectionEntity,
    requestDescriptionType: RequestDescriptionType,
): number {
    /**
     * Use connectionId as key if possible or set default key for map
     */
    return !isEmpty(connection) &&
        [RequestDescriptionType.CONNECTION, RequestDescriptionType.AGENT].includes(
            requestDescriptionType,
        )
        ? connection.id
        : MonitorInstance.defaultMetadataKey;
}

export function wasCustomTestManuallyDisabled(
    originalControlTestInstance: ControlTestInstance,
    isConnectionConnected: boolean,
) {
    return (
        originalControlTestInstance.disabledMessage &&
        originalControlTestInstance.isCustom() &&
        isConnectionConnected
    );
}

/**
 * Fetches and returns a unique list of required policy names based on current resources from provided recipes.
 *
 * @async
 * @function getRequiredPolicies
 * @param {string} workspaceId - The ID of the workspace associated with the request.
 * @param {Account} account - The user account for which the policies are being retrieved.
 * @param {Recipe[]} recipes - An array of recipe objects containing provider and resource information.
 * @param {ProviderCatalogService} providerCatalogService - A service to fetch resources and policies associated with providers.
 * @returns {Promise<string[]>} - A promise that resolves to a unique list of policy names required for the current resources.
 *
 * @description
 * This function performs the following steps:
 * 1. **Extract Unique Providers:** It flattens the list of providers from the recipes and extracts a unique set of provider names.
 * 2. **Extract Current Resources:** It flattens the list of resources across all recipes to obtain the list of currently used resources.
 * 3. **Fetch Policies for Each Provider:** For each provider, it calls `getProviderResources` to fetch resources, then filters these resources to match the current resources and extracts their associated policies.
 * 4. **Batch Promise Execution:** Using `promiseAllInBatches`, it runs the policy-fetching promises in batches to prevent overwhelming the system with parallel requests.
 * 5. **Return Unique Policies:** The results from all providers are flattened and deduplicated to return a list of unique policy names.
 *
 * @example
 * const policies = await requiredPolicies('workspace-123', userAccount, recipes, providerCatalogService);
 * console.log(policies); // ['policy1', 'policy2', 'policy3']
 */
export async function getRequiredPolicies(
    workspaceId: string,
    account: Account,
    recipes: AutopilotRecipeInstance[],
    providerCatalogService: ProviderCatalogService,
): Promise<string[]> {
    if (isEmpty(recipes)) {
        return [];
    }

    const providers = chain(recipes).flatMap('recipe.providers').map('provider').uniq().value();
    const isCustomConnectionTest = providers.some(provider => !ClientType[provider]);

    // Custom connection test do not require any policies from the user
    if (isCustomConnectionTest) {
        return [];
    }

    const currentResources = chain(recipes)
        .flatMap('recipe.providers')
        .flatMap('resources')
        .map('resource')
        .value();

    const requiredPoliciesByProvider = async (provider: string) => {
        const { resources } = await providerCatalogService.getProviderResources(
            provider,
            { workspaceId },
            account,
        );

        return chain(resources)
            .filter(({ name }) => currentResources.some(resource => resource === name))
            .flatMap('policies')
            .map('name')
            .value();
    };

    const response = await promiseAllInBatches(
        providers,
        providers.length,
        requiredPoliciesByProvider,
    );

    return chain(response).flatMap().uniq().filter(Boolean).value();
}

/**
 * Verifies if the provider type is in the allow list to run the tests by connection.
 *
 * @param providerType type value to evaluate
 * @returns boolean
 */
export const isProviderAllowedToAutoRunTests = (providerType: ProviderType) => {
    const providersAllowList = [
        ProviderType.INFRASTRUCTURE,
        ProviderType.VERSION_CONTROL,
        ProviderType.IDENTITY,
        ProviderType.TICKETING,
    ];

    return providersAllowList.includes(providerType);
};

/**
 *  Verifies if the provider type of the connection to run the tests.
 * @param connection connection to evaluate
 * @returns boolean
 */
export const isConnectionProviderAllowedToAutoRunTests = (connection: ConnectionEntity) =>
    isProviderAllowedToAutoRunTests(connection.providerType);

/**
 * Verifies if the client type is in the allow list to run the tests by connection.
 *
 * @param clientType type value to evaluate
 * @returns boolean
 */
export const isClientTypeAllowedToRunTests = (clientType: ClientType) => {
    return AutoRunTestAllowedClientType.includes(clientType);
};

/**
 * Verifies if a connection is allowed to run tests checking provider and client types.
 *
 * @param connection connection to evaluate
 * @returns boolean
 */
export const isConnectionAllowedToAutoRunTests = (connection: ConnectionEntity) =>
    isConnectionProviderAllowedToAutoRunTests(connection) &&
    isClientTypeAllowedToRunTests(connection.clientType);

export const isControlTestInstanceLabeledAsNew = (releasedAt: Date | null | undefined): boolean => {
    if (!releasedAt) {
        return false;
    }
    const thresholdDays = config.get('newLabel.amountOfDaysForControlTestNewStatus');
    const thresholdDate = subtractDaysToDate(thresholdDays, new Date());
    return thresholdDate.getTime() < releasedAt.getTime();
};

/**
 * Removes MDM metadata entries when an EDR connection is active
 * When EDR connection is active, test 64 does not need to evaluate MDM results, so we will delete it
 * https://drata.atlassian.net/browse/ENG-67380
 *
 * @param metadata
 * @param connections
 * @param autopilotTaskType
 */
export function clearMDMMetadataWhenEDRIsConnected(
    metadata: Map<number, MonitorInstanceMetadataItem>,
    connections: ConnectionEntity[],
    autopilotTaskType: AutopilotTaskType,
): void {
    if (autopilotTaskType !== AutopilotTaskType.AGENT_ANTI_VIRUS_APPLICATION) {
        return;
    }

    const hasActiveEdrConnection = connections.some(
        connection =>
            connection.providerType === ProviderType.EDR && !isNil(connection.connectedAt),
    );

    if (hasActiveEdrConnection) {
        metadata.delete(MonitorInstance.defaultMetadataKey);
    }
}

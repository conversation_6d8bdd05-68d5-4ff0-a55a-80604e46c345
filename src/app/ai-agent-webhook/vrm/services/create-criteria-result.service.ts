import { ErrorCode } from '@drata/enums';
import { Injectable } from '@nestjs/common';
import { AgentWorkflowStep } from 'app/agent-workflow/entities/agent-workflow-step.entity';
import { AgentWorkflow } from 'app/agent-workflow/entities/agent-workflow.entity';
import {
    AgentT<PERSON>,
    AgentWorkflowStatus,
    AgentWorkflowStepStatus,
    AgentWorkflowVendorCriteriaStepName,
    AgentWorkflowVendorType,
} from 'app/agent-workflow/enums';
import { AgentWorkflowOrchestratorService } from 'app/agent-workflow/services/agent-workflow-orchestrator.service';
import { CriteriaCreationProgressRequestDto } from 'app/ai-agent-webhook/vrm/dtos/criteria-creation-progress-request.dto';
import { Account } from 'auth/entities/account.entity';
import { AccountsCoreService } from 'auth/services/accounts-core.service';
import { NotFoundException } from 'commons/exceptions/not-found.exception';
import { BaseService } from 'commons/services/base.service';
import { toAccountIdType } from 'commons/types/account-id.type';
import { isNil } from 'lodash';
import { PolloMessage } from 'pollo-logger/classes/pollo.message';
import { PolloLogger } from 'pollo-logger/pollo.logger';
import { TenancyContext } from 'tenancy/contexts/tenancy.context';
import { tenantWrapper } from 'tenancy/contexts/tenant-wrapper';
import { In } from 'typeorm';

@Injectable()
export class CreateCriteriaResultService extends BaseService {
    protected logger = PolloLogger.logger(this.constructor.name);

    constructor(
        private readonly accountsCoreService: AccountsCoreService,
        private readonly agentWorkflowOrchestratorService: AgentWorkflowOrchestratorService,
        private readonly tenancyContext: TenancyContext,
    ) {
        super();
    }

    /**
     * Handles the processing questionnaires webhook payload
     * @param dto The criteria creation progress request DTO
     */
    async handleProcessingQuestionnaires(dto: CriteriaCreationProgressRequestDto): Promise<void> {
        const { tenantId, jobId } = dto;

        this.logger.log(
            PolloMessage.msg('Processing questionnaires progress').setMetadata({
                tenantId,
                jobId,
            }),
        );

        // Get the account by tenantId
        const account = await this.getAccountByTenantId(tenantId);

        // Use tenant wrapper to execute in the correct tenant context
        await tenantWrapper(account, async () => {
            // Get the agent workflow for create criteria using accountId as agentWorkflowProcessTypeId
            const agentWorkflow = await this.agentWorkflowRepository.findOneOrFail({
                where: {
                    agentType: AgentType.VENDOR_RISK_MANAGEMENT,
                    agentWorkflowType: AgentWorkflowVendorType.VENDORS_CRITERIA,
                    status: In([AgentWorkflowStatus.PENDING, AgentWorkflowStatus.IN_PROGRESS]),
                    workflowTypeId: account.id,
                },
                relations: {
                    steps: true,
                },
            });

            await this.completeStepAndTransition(
                account,
                agentWorkflow,
                AgentWorkflowVendorCriteriaStepName.SEND_CRITERIA_QUESTIONNAIRE_TO_AI,
                AgentWorkflowVendorCriteriaStepName.PROCESSING_QUESTIONNAIRES,
                jobId,
            );
        });
    }

    /**
     * Handles the processing questions webhook payload
     * @param dto The criteria creation progress request DTO
     */
    async handleProcessingQuestions(dto: CriteriaCreationProgressRequestDto): Promise<void> {
        const { tenantId, jobId } = dto;

        this.logger.log(
            PolloMessage.msg('Processing questions progress').setMetadata({
                tenantId,
                jobId,
            }),
        );

        // Get the account by tenantId
        const account = await this.getAccountByTenantId(tenantId);

        // Use tenant wrapper to execute in the correct tenant context
        await tenantWrapper(account, async () => {
            // Get the agent workflow for create criteria using accountId as agentWorkflowProcessTypeId
            const agentWorkflow = await this.agentWorkflowRepository.findOneOrFail({
                where: {
                    agentType: AgentType.VENDOR_RISK_MANAGEMENT,
                    agentWorkflowType: AgentWorkflowVendorType.VENDORS_CRITERIA,
                    status: In([AgentWorkflowStatus.PENDING, AgentWorkflowStatus.IN_PROGRESS]),
                    workflowTypeId: account.id,
                },
                relations: {
                    steps: true,
                },
            });

            await this.completeStepAndTransition(
                account,
                agentWorkflow,
                AgentWorkflowVendorCriteriaStepName.PROCESSING_QUESTIONNAIRES,
                AgentWorkflowVendorCriteriaStepName.PROCESSING_QUESTIONS,
                jobId,
            );
        });
    }

    /**
     * Handles the creating criteria webhook payload
     * @param dto The criteria creation progress request DTO
     */
    async handleCreatingCriteria(dto: CriteriaCreationProgressRequestDto): Promise<void> {
        const { tenantId, jobId, data } = dto;

        this.logger.log(
            PolloMessage.msg('Creating criteria progress').setMetadata({
                tenantId,
                jobId,
                data,
            }),
        );

        // Get the account by tenantId
        const account = await this.getAccountByTenantId(tenantId);

        // Use tenant wrapper to execute in the correct tenant context
        await tenantWrapper(account, async () => {
            // Get the agent workflow for create criteria using accountId as agentWorkflowProcessTypeId
            const agentWorkflow = await this.agentWorkflowRepository.findOneOrFail({
                where: {
                    agentType: AgentType.VENDOR_RISK_MANAGEMENT,
                    agentWorkflowType: AgentWorkflowVendorType.VENDORS_CRITERIA,
                    status: In([AgentWorkflowStatus.PENDING, AgentWorkflowStatus.IN_PROGRESS]),
                    workflowTypeId: account.id,
                },
                relations: {
                    steps: true,
                },
            });

            // Retrieve the SEND_CRITERIA_QUESTIONNAIRE_TO_AI step and update its resultData
            const sendQuestionnaireToAiStep = agentWorkflow.steps.find(
                step =>
                    step.stepName ===
                    AgentWorkflowVendorCriteriaStepName.SEND_CRITERIA_QUESTIONNAIRE_TO_AI,
            );

            if (!isNil(sendQuestionnaireToAiStep)) {
                // Parse existing resultData
                const existingResultData = sendQuestionnaireToAiStep.resultData
                    ? JSON.parse(sendQuestionnaireToAiStep.resultData)
                    : {};

                // Add the data property to the resultData
                const updatedResultData = {
                    ...existingResultData,
                    data,
                };

                // Update the step with the new resultData
                sendQuestionnaireToAiStep.resultData = JSON.stringify(updatedResultData);
                await this.agentWorkflowStepRepository.save(sendQuestionnaireToAiStep);

                this.logger.log(
                    PolloMessage.msg(
                        'Updated SEND_CRITERIA_QUESTIONNAIRE_TO_AI resultData',
                    ).setMetadata({
                        agentWorkflowId: agentWorkflow.id,
                        stepId: sendQuestionnaireToAiStep.id,
                        updatedResultData,
                    }),
                );
            } else {
                this.logger.warn(
                    PolloMessage.msg(
                        'SEND_CRITERIA_QUESTIONNAIRE_TO_AI step not found',
                    ).setMetadata({
                        agentWorkflowId: agentWorkflow.id,
                    }),
                );
            }

            await this.completeStepAndTransition(
                account,
                agentWorkflow,
                AgentWorkflowVendorCriteriaStepName.PROCESSING_QUESTIONS,
                AgentWorkflowVendorCriteriaStepName.CREATING_CRITERIA,
                jobId,
            );
        });
    }

    /**
     * Completes the current step and transitions to the next step
     * @param account - The account context
     * @param agentWorkflow - The agent workflow entity
     * @param currentStepName - The name of the current step to complete
     * @param nextStepName - The name of the next step to transition to
     * @param jobId - The job ID for logging purposes
     */
    private async completeStepAndTransition(
        account: Account,
        agentWorkflow: AgentWorkflow,
        currentStepName: AgentWorkflowVendorCriteriaStepName,
        nextStepName: AgentWorkflowVendorCriteriaStepName,
        jobId: string,
    ): Promise<void> {
        // Find the current step
        const currentStep = agentWorkflow.steps.find(step => step.stepName === currentStepName);

        if (isNil(currentStep)) {
            this.logger.error(
                PolloMessage.msg(`Step ${currentStepName} not found`).setMetadata({
                    agentWorkflowId: agentWorkflow.id,
                    currentStepName,
                }),
            );
            throw new NotFoundException(ErrorCode.ENTITY_NOT_FOUND);
        }

        // Set progress to 100 and change status to completed
        currentStep.progress = 100;
        currentStep.status = AgentWorkflowStepStatus.COMPLETED;
        await this.agentWorkflowStepRepository.save(currentStep);

        await this.agentWorkflowOrchestratorService.emitStepCompletedEvent(
            account,
            agentWorkflow,
            currentStep,
        );

        // Transition to the next step
        await this.agentWorkflowOrchestratorService.continueWorkflow(account, agentWorkflow.id, {
            stepName: nextStepName,
            resumeWorkflow: true,
        });

        this.logger.log(
            PolloMessage.msg(
                `Successfully transitioned from ${currentStepName} to ${nextStepName}`,
            ).setMetadata({
                agentWorkflowId: agentWorkflow.id,
                currentStepName,
                nextStepName,
                jobId,
            }),
        );
    }

    /**
     * Gets an account by tenant ID
     * @param tenantId The tenant ID (account ID)
     * @returns The account entity
     */
    private async getAccountByTenantId(tenantId: string): Promise<Account> {
        try {
            const accountId = toAccountIdType(tenantId);
            return await this.accountsCoreService.getAccountById(accountId);
        } catch (error) {
            this.logger.error(
                PolloMessage.msg('Failed to find account by tenant ID')
                    .setMetadata({ tenantId })
                    .setError(error),
            );
            throw new NotFoundException(ErrorCode.ACCOUNT_NOT_FOUND);
        }
    }

    private get agentWorkflowRepository() {
        return this.tenancyContext.getRepository(AgentWorkflow);
    }

    private get agentWorkflowStepRepository() {
        return this.tenancyContext.getRepository(AgentWorkflowStep);
    }
}

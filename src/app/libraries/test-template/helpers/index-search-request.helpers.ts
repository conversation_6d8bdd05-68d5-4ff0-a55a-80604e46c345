import { LibraryTestTemplateSearchRequestDto } from 'app/libraries/test-template/dtos/library-test-template-search-request.dto';

export function normalizeSearchRequestDto(
    dto: LibraryTestTemplateSearchRequestDto,
): LibraryTestTemplateSearchRequestDto {
    const normalizedDto = { ...dto };

    if (normalizedDto.categories?.length) {
        normalizedDto.categories = normalizedDto.categories.map(category =>
            fromTitleCase(category),
        );
    }

    if (normalizedDto.ratings?.length) {
        normalizedDto.ratings = normalizedDto.ratings.map(rating => {
            return fromTitleCase(rating);
        });
    }

    if (normalizedDto.connections?.length) {
        normalizedDto.connections = normalizedDto.connections.map(connection =>
            fromTitleCase(connection),
        );
    }

    if (normalizedDto.resources?.length) {
        normalizedDto.resources = normalizedDto.resources.map(resource => fromTitleCase(resource));
    }

    return normalizedDto;
}

export function getSortFieldName(sortField: string): string {
    const sortFieldMap: Record<string, string> = {
        name: 'name.keyword',
        description: 'description.keyword',
    };

    return sortFieldMap[sortField] || sortField;
}

function fromTitleCase(str: string): string {
    if (!str) return '';

    const specialCases: Record<string, string> = {
        DigitalOcean: 'DIGITAL_OCEAN',
        Targetprocess: 'TARGET_PROCESS',
        'Route 53 Domains': 'ROUTE53DOMAINS',
        'Route 53 Operations': 'ROUTE53OPERATIONS',
    };
    if (specialCases[str]) {
        return specialCases[str];
    }
    return str.toUpperCase().replace(/ /g, '_');
}

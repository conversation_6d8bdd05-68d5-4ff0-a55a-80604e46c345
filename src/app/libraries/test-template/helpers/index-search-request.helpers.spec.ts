import { LibraryTestTemplateSearchRequestDto } from 'app/libraries/test-template/dtos/library-test-template-search-request.dto';
import {
    getSortFieldName,
    normalizeSearchRequestDto,
} from 'app/libraries/test-template/helpers/index-search-request.helpers';

describe('index-search-request.helpers', () => {
    describe('getSortFieldName', () => {
        it('should return mapped field name for known fields', () => {
            expect(getSortFieldName('name')).toBe('name.keyword');
            expect(getSortFieldName('description')).toBe('description.keyword');
        });

        it('should return original field name for unknown fields', () => {
            expect(getSortFieldName('unknown')).toBe('unknown');
        });
    });

    describe('normalizeSearchRequestDto', () => {
        it('should normalize resources with Route 53 special cases', () => {
            const dto = new LibraryTestTemplateSearchRequestDto();
            dto.resources = [
                'DigitalOcean',
                'Targetprocess',
                'Route 53 Domains',
                'Route 53 Operations',
                'REGULAR_RESOURCE',
            ];

            const result = normalizeSearchRequestDto(dto);

            expect(result.resources).toEqual([
                'DIGITAL_OCEAN',
                'TARGET_PROCESS',
                'ROUTE53DOMAINS',
                'ROUTE53OPERATIONS',
                'REGULAR_RESOURCE',
            ]);
        });

        it('should handle empty arrays', () => {
            const dto = new LibraryTestTemplateSearchRequestDto();
            dto.categories = [];
            dto.ratings = [];
            dto.connections = [];
            dto.resources = [];

            const result = normalizeSearchRequestDto(dto);

            expect(result.categories).toEqual([]);
            expect(result.ratings).toEqual([]);
            expect(result.connections).toEqual([]);
            expect(result.resources).toEqual([]);
        });

        it('should handle undefined arrays', () => {
            const dto = new LibraryTestTemplateSearchRequestDto();

            const result = normalizeSearchRequestDto(dto);

            expect(result.categories).toBeUndefined();
            expect(result.ratings).toBeUndefined();
            expect(result.connections).toBeUndefined();
            expect(result.resources).toBeUndefined();
        });

        it('should handle mixed case and special characters', () => {
            const dto = new LibraryTestTemplateSearchRequestDto();
            dto.resources = ['Some Resource', 'ANOTHER_RESOURCE', 'mixedCase'];

            const result = normalizeSearchRequestDto(dto);

            expect(result.resources).toEqual(['SOME_RESOURCE', 'ANOTHER_RESOURCE', 'MIXEDCASE']);
        });
    });
});

import { Injectable, Logger } from '@nestjs/common';
import { IndicesIndexSettings, MappingTypeMapping } from '@opensearch-project/opensearch/api/types';
import { LibraryTestTemplateDocument } from 'app/libraries/test-template/entities/library-test-template.opensearch.entity';
import { getEnvironmentName } from 'commons/helpers/environment.helper';
import { PaginationType } from 'commons/types/pagination.type';
import {
    SearchFilterOptions,
    SearchFilterSortOrder,
} from 'commons/types/search-filter-options.type';
import config from 'config';
import { SearchEngine } from 'dependencies/search-engine/search-engine';
import { FacetInput } from 'dependencies/search-engine/types/facet.types';
import { format } from 'util';

@Injectable()
export class LibraryTestTemplateData {
    private readonly logger = new Logger(LibraryTestTemplateData.name);

    static readonly libraryTestTemplateIndexMapping: MappingTypeMapping = {
        properties: {
            templateId: { type: 'integer' },
            name: {
                type: 'text',
                fields: {
                    keyword: { type: 'keyword' },
                },
            },
            description: {
                type: 'text',
                fields: {
                    keyword: { type: 'keyword' },
                },
            },
            createdAt: { type: 'date', format: 'strict_date_time' },
            updatedAt: { type: 'date', format: 'strict_date_time' },
            frameworks: { type: 'keyword' },
            category: { type: 'keyword' },
            resources: { type: 'keyword' },
            connections: { type: 'keyword' },
            rating: { type: 'keyword' },
            lastIndexed: { type: 'date', format: 'strict_date_time' },
        },
    };

    static readonly libraryTestTemplateIndexSettings: IndicesIndexSettings = {
        number_of_shards: 1,
        number_of_replicas: 1,
    };

    static readonly defaultSortClause: { field: string; order: SearchFilterSortOrder }[] = [
        { field: 'createdAt', order: 'desc' },
        { field: 'rating', order: 'asc' },
    ];

    static readonly LIBRARY_TEST_TEMPLATE_TABLE_FILTERS: FacetInput = {
        termFields: ['frameworks', 'category'],
        subAggs: {
            connections: ['resources'],
        },
    };

    constructor(private readonly searchEngine: SearchEngine) {}

    static getLibraryTestTemplateIndexName(): string {
        return format(config.get('opensearch.libraryTestTemplates.index'), getEnvironmentName());
    }

    async createLibraryTestTemplateIndex(indexName: string): Promise<void> {
        return this.searchEngine.createIndices([
            {
                name: indexName,
                body: {
                    settings: LibraryTestTemplateData.libraryTestTemplateIndexSettings,
                    mappings: LibraryTestTemplateData.libraryTestTemplateIndexMapping,
                },
            },
        ]);
    }

    async filterLibraryTestTemplateDocuments(
        options: SearchFilterOptions,
    ): Promise<PaginationType<LibraryTestTemplateDocument>> {
        return this.searchEngine.filterGlobalDocuments(
            LibraryTestTemplateData.getLibraryTestTemplateIndexName(),
            options,
        );
    }

    async getLibraryTestTemplateFilterValues(): Promise<{
        frameworks: string[];
        categories: string[];
        connections: string[];
        connectionResourceMap: Record<string, string[]>;
    }> {
        try {
            const result = await this.searchEngine.getGlobalFacetsByFields(
                LibraryTestTemplateData.getLibraryTestTemplateIndexName(),
                LibraryTestTemplateData.LIBRARY_TEST_TEMPLATE_TABLE_FILTERS,
            );

            const connectionResourceMap: Record<string, string[]> = {};
            const connectionsFacet = result.connections || [];

            for (const connection of connectionsFacet) {
                const connectionId = connection.key;
                const resources = connection.resources ?? [];
                connectionResourceMap[connectionId] = resources;
            }

            return {
                frameworks: result.frameworks || [],
                categories: result.category || [],
                connections: connectionsFacet.map((b: any) => b.key) || [],
                connectionResourceMap,
            };
        } catch (error) {
            this.logger.error(`Error getting available filter values: ${error.message}`);
            return {
                frameworks: [],
                categories: [],
                connections: [],
                connectionResourceMap: {},
            };
        }
    }

    async getAllTemplateIds(): Promise<number[]> {
        const indexName = LibraryTestTemplateData.getLibraryTestTemplateIndexName();

        try {
            const result = await this.searchEngine.getGlobalFacetsByFields(
                indexName,
                { termFields: ['templateId'] },
                {},
                config.get<number>('opensearch.pagination.maxFacetSize'),
            );

            return (
                result.templateId?.map(bucket =>
                    typeof bucket === 'object' ? parseInt(bucket.key, 10) : parseInt(bucket, 10),
                ) || []
            );
        } catch (error) {
            this.logger.error(`Error getting all template IDs: ${error.message}`);
            return [];
        }
    }

    async getTotalDocumentsCount(): Promise<number> {
        const indexName = LibraryTestTemplateData.getLibraryTestTemplateIndexName();

        try {
            return await this.searchEngine.countGlobal(indexName);
        } catch (error) {
            this.logger.error(`Error getting total document count: ${error.message}`);

            return 0;
        }
    }
}

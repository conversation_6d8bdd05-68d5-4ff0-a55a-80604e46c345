import { ErrorCode, LibraryType, TestSource } from '@drata/enums';
import { Injectable } from '@nestjs/common';
import { TemplateAddedEvent } from 'app/analytics/observables/events/template-added.event';
import { AutopilotRecipeInstanceRepository } from 'app/autopilot/repositories/autopilot-recipe-instance.repository';
import { ConnectionEntity } from 'app/companies/connections/entities/connection.entity';
import { ConnectionsCoreService } from 'app/companies/connections/services/connections-core.service';
import { WorkspacesCoreService } from 'app/companies/products/services/workspaces-core.service';
import { ControlRepository } from 'app/grc/repositories/control.repository';
import { LibraryTemplateCoreService } from 'app/libraries/services/library-template-core.service';
import { LibraryTestTemplateBulkAddRequestDto } from 'app/libraries/test-template/dtos/library-test-template-bulk-add-request.dto';
import { LibraryTestTemplateBulkAddValidationResponseDto } from 'app/libraries/test-template/dtos/library-test-template-bulk-add-validation-response.dto';
import { LibraryTestTemplateCountResponseDto } from 'app/libraries/test-template/dtos/library-test-template-count-response.dto';
import { LibraryTestTemplateFiltersResponseDto } from 'app/libraries/test-template/dtos/library-test-template-filters-response.dto';
import { LibraryTestTemplateIdsResponseDto } from 'app/libraries/test-template/dtos/library-test-template-ids-response.dto';
import { LibraryTestTemplateImportRequestDto } from 'app/libraries/test-template/dtos/library-test-template-import-request.dto';
import { LibraryTestTemplateListResponseDto } from 'app/libraries/test-template/dtos/library-test-template-list-response.dto';
import { LibraryTestTemplateMappingsRequestDto } from 'app/libraries/test-template/dtos/library-test-template-mappings-request.dto';
import { LibraryTestTemplateSearchRequestDto } from 'app/libraries/test-template/dtos/library-test-template-search-request.dto';
import { LibraryTestTemplateDocument } from 'app/libraries/test-template/entities/library-test-template.opensearch.entity';
import { LibraryTestTemplateRating } from 'app/libraries/test-template/enums/library-test-template-rating.enum';
import { LibraryTestTemplateStatus } from 'app/libraries/test-template/enums/library-test-template-status.enum';
import { ExecuteBulkTestEvent } from 'app/libraries/test-template/events/test-run-requested.event';
import {
    createAutopilotRecipeInstanceFromTemplate,
    createControlTestInstanceFromControlTestTemplate,
    createMonitorInstanceFromMonitorTemplate,
} from 'app/libraries/test-template/helpers/create-control-tests.helpers';
import {
    getCategoryFromMonitorTemplate,
    getConnectionsFromMonitorTemplate,
    getDescriptionFromMonitorTemplate,
    getFrameworksFromRequirementIndex,
} from 'app/libraries/test-template/helpers/create-index-documents.helpers';
import {
    getSortFieldName,
    normalizeSearchRequestDto,
} from 'app/libraries/test-template/helpers/index-search-request.helpers';
import {
    convertTemplatesToResponseDtos,
    createEmptyResponseDto,
    toTitleCase,
} from 'app/libraries/test-template/helpers/index-search-response.helpers';
import { LibraryTestTemplateData } from 'app/libraries/test-template/repositories/library-test-template.data';
import { LibraryTestTemplateConnectionWithStatus } from 'app/libraries/test-template/types/library-test-template-active-connections.type';
import { LibraryTestTemplateDetails } from 'app/libraries/test-template/types/library-test-template-details.type';
import {
    LibraryTestTemplateError,
    LibraryTestTemplateErrorResultType,
    LibraryTestTemplateErrorType,
} from 'app/libraries/test-template/types/library-test-template-errors.type';
import { ControlTestInstance } from 'app/monitors/entities/control-test-instance.entity';
import { MonitorInstanceCheckType } from 'app/monitors/entities/monitor-instance-check-type.entity';
import { MonitorInstance } from 'app/monitors/entities/monitor-instance.entity';
import { ControlTestInstanceRepository } from 'app/monitors/repositories/control-test-instance.repository';
import { MonitorInstanceCheckTypeRepository } from 'app/monitors/repositories/monitor-instance-check-type.repository';
import { MonitorInstanceRepository } from 'app/monitors/repositories/monitor-instance.repository';
import { User } from 'app/users/entities/user.entity';
import { bulkAddTestTemplatesWorkflowV1 } from 'app/worker/workflows/library-test-template/bulk-add-test-templates.v1.workflow';
import { Account } from 'auth/entities/account.entity';
import { CacheService } from 'cache/cache.service';
import { PolloAdapter } from 'commons/adapters/pollo.adapter';
import { TenancyTransaction } from 'commons/decorators/transaction';
import { ClientType } from 'commons/enums/auth/client-type.enum';
import { ConnectionState } from 'commons/enums/auth/state-type.enum';
import { RunMode } from 'commons/enums/run-mode.enum';
import { ConflictException } from 'commons/exceptions/conflict.exception';
import { NotFoundException } from 'commons/exceptions/not-found.exception';
import { safeFormatCompanyName } from 'commons/helpers/event.helper';
import { getSkip } from 'commons/helpers/pagination.helper';
import { isMultiProductEnabled, productLevelProviderTypes } from 'commons/helpers/products.helper';
import { promiseAllInBatches } from 'commons/helpers/promise.helper';
import { getTemporalClient } from 'commons/helpers/temporal/client';
import { AppService } from 'commons/services/app.service';
import { ListType } from 'commons/types/list.type';
import { PaginationType } from 'commons/types/pagination.type';
import {
    SearchFilterOptions,
    SearchFilterSortOrder,
} from 'commons/types/search-filter-options.type';
import config from 'config';
import { SearchEngine } from 'dependencies/search-engine/search-engine';
import { isNil } from 'lodash';
import { ControlTemplate } from 'site-admin/entities/control-template.entity';
import { ControlTestTemplate } from 'site-admin/entities/control-test-template.entity';
import { FrameworkTemplate } from 'site-admin/entities/framework-template.entity';
import { MonitorTemplate } from 'site-admin/entities/monitor-template.entity';
import { FindOptionsWhere, In, IsNull } from 'typeorm';

@Injectable()
export class LibraryTestTemplateService extends AppService {
    private readonly ONE_MINUTE_IN_SECONDS = config.get<number>('cache.ttl.min');
    private readonly CUSTOM_TEST_BASE_ID = config.get<number>('autopilot2.customTestBaseId');
    private readonly SEQUENTIAL_BATCH_SIZE = 1;
    private readonly MONITOR_BATCH_SIZE = 10;
    private readonly TEST_TEMPLATE_BATCH_SIZE = 5;
    private readonly DEFAULT_LIMIT = config.get('pagination.limit');
    private readonly DEFAULT_PAGE = config.get('pagination.page');

    static readonly defaultSortClause: { field: string; order: SearchFilterSortOrder }[] = [
        { field: 'createdAt', order: 'desc' },
        { field: 'rating', order: 'asc' },
    ];

    constructor(
        private readonly libraryTestTemplateData: LibraryTestTemplateData,
        private readonly connectionsCoreService: ConnectionsCoreService,
        private readonly templateCoreService: LibraryTemplateCoreService,
        private readonly searchEngine: SearchEngine,
        private readonly workspacesCoreService: WorkspacesCoreService,
        private readonly cacheService: CacheService,
    ) {
        super();
    }

    /**
     * TEMPLATE SEARCH
     */
    async searchTestTemplates(
        account: Account,
        dto: LibraryTestTemplateSearchRequestDto,
    ): Promise<LibraryTestTemplateListResponseDto> {
        try {
            const normalizedDto = normalizeSearchRequestDto(dto);
            const { filterTemplateIds, installedTemplateIdsSet, installedTemplateMap } =
                await this.filterTemplateIdsByStatus(normalizedDto?.statuses || []);

            const indexSearchResults = await this.getTemplatesFromIndex(
                normalizedDto,
                filterTemplateIds || undefined,
            );
            const totalTemplates = await this.libraryTestTemplateData.getTotalDocumentsCount();
            const searchTotal = indexSearchResults.total ?? 0;

            const { filters, counts } = await this.getFiltersAndCountsFromIndex(
                account,
                totalTemplates,
                searchTotal,
                installedTemplateIdsSet,
                normalizedDto,
            );

            const templateDtos = convertTemplatesToResponseDtos(
                indexSearchResults.data,
                installedTemplateIdsSet,
                installedTemplateMap,
                account.companyName,
            );

            return new LibraryTestTemplateListResponseDto().build(templateDtos, filters, counts);
        } catch (error) {
            this.error(error, undefined, {
                event: 'searchTestTemplates',
                message: 'Error searching test templates',
            });

            return createEmptyResponseDto();
        }
    }

    private async getTemplatesFromIndex(
        dto: LibraryTestTemplateSearchRequestDto,
        filteredTemplateIds?: number[],
    ): Promise<PaginationType<LibraryTestTemplateDocument>> {
        const filters: Record<string, any> = {};

        if (dto.frameworks?.length) {
            filters['frameworks'] = dto.frameworks;
        }

        if (dto.categories?.length) {
            filters['category'] = dto.categories;
        }

        if (dto.resources?.length) {
            filters['resources'] = dto.resources;
        }

        if (dto.connections?.length) {
            filters['connections'] = dto.connections;
        }

        if (dto.ratings?.length) {
            filters['rating'] = dto.ratings;
        }

        if (filteredTemplateIds) {
            filters['templateId'] = filteredTemplateIds;
        }

        const sortField = dto.sort ? getSortFieldName(dto.sort) : undefined;

        const searchOptions: SearchFilterOptions = {
            page: dto.page || config.get('pagination.page'),
            size: dto.limit || config.get('pagination.limit'),
            sort:
                sortField && dto.sortDir
                    ? [
                          {
                              field: sortField,
                              order: dto.sortDir.toLowerCase() === 'asc' ? 'asc' : 'desc',
                          },
                      ]
                    : LibraryTestTemplateService.defaultSortClause,
            filters: filters,
        };

        if (dto.statuses?.includes(LibraryTestTemplateStatus.NEW)) {
            const amountOfDaysForNew = 45;
            const thresholdDate = new Date();
            thresholdDate.setDate(thresholdDate.getDate() - amountOfDaysForNew);

            searchOptions.rangeFilters = {
                createdAt: {
                    gte: thresholdDate.toISOString(),
                },
            };
        }

        if (!isNil(dto.q) && dto.q !== '') {
            searchOptions.searchQuery = dto.q;
            searchOptions.searchFields = ['name', 'description'];
        }

        return this.libraryTestTemplateData.filterLibraryTestTemplateDocuments(searchOptions);
    }

    private async getFiltersAndCountsFromIndex(
        account: Account,
        totalTemplates: number,
        searchTotal: number,
        installedTemplateIdsSet: Set<number>,
        dto: LibraryTestTemplateSearchRequestDto,
    ): Promise<{
        filters: LibraryTestTemplateFiltersResponseDto;
        counts: LibraryTestTemplateCountResponseDto;
    }> {
        const activeConnections =
            await this.connectionsCoreService.getActiveConnectionsByProductId(account);

        const availableFilters =
            await this.libraryTestTemplateData.getLibraryTestTemplateFilterValues();

        const connectionsWithStatus: LibraryTestTemplateConnectionWithStatus[] =
            availableFilters.connections.map(connectionType => {
                const isActive = activeConnections.some(
                    conn => conn.clientType === ClientType[connectionType],
                );
                const connectionName = toTitleCase(connectionType);

                return {
                    connectionType: connectionName,
                    active: isActive,
                };
            });

        const activeCount = installedTemplateIdsSet.size;
        const totalCount = totalTemplates;
        const availableCount = totalCount - activeCount;

        let availableResources;
        if (dto.connections?.length === 1) {
            const resources = availableFilters.connectionResourceMap[dto.connections[0]];
            if (resources?.length) {
                availableResources = resources
                    .filter(resource => resource && resource.trim() !== '')
                    .map(resource => toTitleCase(resource));
            }
        }

        const normalizedCategories = availableFilters.categories
            .filter(category => category && category.trim() !== '')
            .map(category => toTitleCase(category));

        const filters = new LibraryTestTemplateFiltersResponseDto().build(
            [...availableFilters.frameworks].sort((a, b) => a.localeCompare(b)),
            [...normalizedCategories].sort((a, b) => a.localeCompare(b)),
            [...connectionsWithStatus].sort((a, b) =>
                a.connectionType.localeCompare(b.connectionType),
            ),
            Object.values(LibraryTestTemplateRating),
            Object.values(LibraryTestTemplateStatus),
            availableResources
                ? [...availableResources].sort((a, b) => a.localeCompare(b))
                : undefined,
        );
        const counts = new LibraryTestTemplateCountResponseDto().build(
            totalCount,
            activeCount,
            availableCount,
            searchTotal,
        );

        return { filters, counts };
    }

    private async filterTemplateIdsByStatus(statuses: string[]): Promise<{
        filterTemplateIds: number[] | null;
        installedTemplateIdsSet: Set<number>;
        installedTemplateMap: Map<number, ControlTestInstance[]>;
    }> {
        const result = {
            filterTemplateIds: null as number[] | null,
            installedTemplateIdsSet: new Set<number>(),
            installedTemplateMap: new Map<number, ControlTestInstance[]>(),
        };

        const allTemplateIds = await this.libraryTestTemplateData.getAllTemplateIds();
        const testInstances = await this.controlTestInstanceRepository.find({
            where: { controlTestTemplateId: In(allTemplateIds) },
            relations: { products: true },
        });

        const activeTemplateIds = testInstances
            .map(instance => instance.controlTestTemplateId)
            .filter((id): id is number => id !== null && id !== undefined);
        const installedTemplateIdsSet = new Set(activeTemplateIds);
        result.installedTemplateIdsSet = installedTemplateIdsSet;

        testInstances.forEach(instance => {
            if (instance.controlTestTemplateId) {
                if (!result.installedTemplateMap.has(instance.controlTestTemplateId)) {
                    result.installedTemplateMap.set(instance.controlTestTemplateId, [instance]);
                } else {
                    result.installedTemplateMap.get(instance.controlTestTemplateId)?.push(instance);
                }
            }
        });

        if (!statuses?.length) {
            return result;
        }

        if (
            statuses.includes(LibraryTestTemplateStatus.IN_USE) &&
            !statuses.includes(LibraryTestTemplateStatus.NOT_IN_USE)
        ) {
            result.filterTemplateIds = activeTemplateIds;
        } else if (
            statuses.includes(LibraryTestTemplateStatus.NOT_IN_USE) &&
            !statuses.includes(LibraryTestTemplateStatus.IN_USE)
        ) {
            result.filterTemplateIds = allTemplateIds.filter(id => !activeTemplateIds.includes(id));
        }

        return result;
    }

    /**
     * OPENSEARCH INDEX OPERATIONS
     */
    async indexAllTestTemplates(): Promise<void> {
        try {
            const allTests = await this.templateCoreService.getTestTemplateIndexData();
            const testTemplateLibraryDocs =
                await this.convertTestTemplatesToIndexDocuments(allTests);

            await this.searchEngine.upsertDocumentsInBulk(
                LibraryTestTemplateData.getLibraryTestTemplateIndexName(),
                testTemplateLibraryDocs.map(document => ({
                    ...document,
                    id: `${document.testId}`,
                })),
            );

            this.log(
                `Successfully indexed ${testTemplateLibraryDocs.length} test templates to library`,
            );
        } catch (error) {
            this.error(error, undefined, {
                event: 'indexAllTestTemplates',
                message: 'Error indexing test templates to library',
            });
        }
    }

    async indexTestTemplatesByTestId(
        testIds: number | number[],
    ): Promise<{ successful: number[]; failed: number[] }> {
        const idsArray = Array.isArray(testIds) ? testIds : [testIds];
        const result: { successful: number[]; failed: number[] } = { successful: [], failed: [] };

        if (!idsArray.length) {
            return result;
        }

        try {
            const testTemplates = await this.templateCoreService.getTestTemplateByTestIds(idsArray);

            if (!testTemplates?.length) {
                this.log(`No test templates found for IDs: ${idsArray.join(', ')}`);
                result.failed = idsArray;
                return result;
            }

            const testTemplateDocs = await this.convertTestTemplatesToIndexDocuments(testTemplates);

            await this.searchEngine.upsertDocumentsInBulk(
                LibraryTestTemplateData.getLibraryTestTemplateIndexName(),
                testTemplateDocs.map(document => ({
                    ...document,
                    id: `${document.testId}`,
                })),
            );

            const successfulIds = testTemplates.map(template => template.testId);
            const failedIds = idsArray.filter(id => !successfulIds.includes(id));

            result.successful = successfulIds;
            result.failed = failedIds;

            this.log(
                `Successfully indexed ${successfulIds.length} test templates to library. Failed: ${failedIds.length}`,
            );
        } catch (error) {
            this.error(error, undefined, {
                event: 'indexTestTemplatesByTestIds',
                message: `Error indexing test templates by IDs: ${idsArray.join(', ')}`,
            });
            result.failed = idsArray;
        }

        return result;
    }

    async removeTestTemplateFromIndex(templateId: number): Promise<boolean> {
        try {
            await this.searchEngine.deleteDocument(
                LibraryTestTemplateData.getLibraryTestTemplateIndexName(),
                `${templateId}`,
            );

            this.log(`Successfully removed test template ID ${templateId} from library index`);
            return true;
        } catch (error) {
            this.error(error, undefined, {
                event: 'removeTestTemplateFromIndex',
                message: `Error removing test template ID ${templateId} from library index`,
            });
            return false;
        }
    }

    async convertTestTemplatesToIndexDocuments(
        tests: ControlTestTemplate[],
    ): Promise<LibraryTestTemplateDocument[]> {
        const validTests = tests.filter(
            test => test.monitorTemplates?.length > 0 && test.controlTemplates?.length > 0,
        );

        this.log(
            `Filtering ${tests.length} tests to ${validTests.length} valid tests with monitor templates and control mappings`,
        );

        return Promise.all(
            validTests.map(async test => {
                const frameworks = getFrameworksFromRequirementIndex(test);
                const category = getCategoryFromMonitorTemplate(test);
                const resources =
                    test.runMode === RunMode.AP2
                        ? await this.templateCoreService.getResourcesFromRecipeTemplate(test)
                        : [];
                const connections = getConnectionsFromMonitorTemplate(test);

                const libraryDoc = new LibraryTestTemplateDocument();
                libraryDoc.templateId = test.id;
                libraryDoc.testId = test.testId;
                libraryDoc.name = test.name;
                libraryDoc.description = getDescriptionFromMonitorTemplate(test);
                libraryDoc.createdAt = test.createdAt;
                libraryDoc.updatedAt = test.updatedAt;
                libraryDoc.frameworks = frameworks;
                libraryDoc.category = category;
                libraryDoc.resources = resources;
                libraryDoc.connections = connections;
                libraryDoc.rating = test.rating;
                libraryDoc.lastIndexed = new Date();

                return libraryDoc;
            }),
        );
    }

    async getTestTemplateDetails(
        testId: number,
        account: Account,
    ): Promise<LibraryTestTemplateDetails> {
        const libraryTestTemplateDetails =
            await this.templateCoreService.getTestTemplateDetails(testId);
        libraryTestTemplateDetails.description = safeFormatCompanyName(
            libraryTestTemplateDetails.description,
            account.companyName,
        );
        const autopilotRecipeTemplates =
            await this.templateCoreService.getAutopilotRecipeTemplate(testId);

        const activeConnections =
            await this.connectionsCoreService.getActiveConnectionsByProductId(account);
        const activeConnectionTypes = activeConnections.map(conn => ClientType[conn.clientType]);

        return Object.assign(libraryTestTemplateDetails, {
            testTemplate: libraryTestTemplateDetails,
            autopilotRecipeTemplates,
            activeConnections: activeConnectionTypes,
        }) as LibraryTestTemplateDetails;
    }

    async getTestTemplateMappings(
        templateTestId: number,
        requestDto: LibraryTestTemplateMappingsRequestDto,
    ): Promise<PaginationType<ControlTemplate>> {
        const { sort, sortDir, frameworkTemplateIds, page = 1, limit = 10 } = requestDto;
        return this.templateCoreService.findControlTemplatesRelatedToControlTestId(
            limit,
            page,
            { templateTestId, frameworkTemplateIds },
            sort,
            sortDir,
        );
    }

    async getUniqueFrameworkTemplateForControlTestTemplate(
        testId: number,
    ): Promise<ListType<FrameworkTemplate>> {
        const frameworks =
            await this.templateCoreService.getUniqueFrameworkTemplateForControlTestTemplate(testId);
        return { data: frameworks };
    }

    async getControlTestInstancesByName(name: string): Promise<ControlTestInstance[]> {
        return this.controlTestInstanceRepository.find({
            where: { name },
            relations: ['products'],
        });
    }

    async getActiveTestsAcrossWorkspaces(
        testId: number,
        account: Account,
        page = this.DEFAULT_PAGE,
        limit = this.DEFAULT_LIMIT,
    ): Promise<PaginationType<ControlTestInstance>> {
        const multipleWorkspacesEnabled = isMultiProductEnabled(account);
        const controlTestTemplate =
            await this.templateCoreService.getTestTemplateIdByTestIds(testId);
        if (isNil(controlTestTemplate)) {
            throw new NotFoundException(ErrorCode.CONTROL_TEST_TEMPLATE_NOT_FOUND);
        }
        const baseWhereStatement: FindOptionsWhere<ControlTestInstance> = {
            controlTestTemplateId: controlTestTemplate?.id,
            deletedAt: IsNull(),
        };

        const whereStatement: FindOptionsWhere<ControlTestInstance> = !multipleWorkspacesEnabled
            ? {
                  ...baseWhereStatement,
                  products: { primary: true },
              }
            : baseWhereStatement;

        const [activeTests, total] = await this.controlTestInstanceRepository.findAndCount({
            where: whereStatement,
            relations: { products: true },
            skip: getSkip(page, limit),
            take: limit,
        });

        return {
            data: activeTests,
            total,
            page,
            limit,
        };
    }

    async getTestTemplateIds(
        requestDto: LibraryTestTemplateSearchRequestDto,
    ): Promise<LibraryTestTemplateIdsResponseDto> {
        const normalizedDto = normalizeSearchRequestDto(requestDto);
        const { filterTemplateIds } = await this.filterTemplateIdsByStatus(
            normalizedDto?.statuses || [],
        );

        const indexSearchResults = await this.getTemplatesFromIndex(
            { ...normalizedDto, page: 1, limit: 10000 },
            filterTemplateIds || undefined,
        );

        return new LibraryTestTemplateIdsResponseDto().build(
            indexSearchResults.data.map(template => template.templateId),
        );
    }

    async validateBulkAddTestTemplates(
        requestDto: LibraryTestTemplateBulkAddRequestDto,
    ): Promise<LibraryTestTemplateBulkAddValidationResponseDto> {
        const testTemplatesIdsToAdd: number[] = [];
        const errors: LibraryTestTemplateErrorType[] = [];
        const { testTemplateIds, productIds } = requestDto;

        const allTemplates = await this.templateCoreService.getTestTemplatesByIds(testTemplateIds);

        const activeConnections =
            await this.connectionsCoreService.getActiveConnectionsWithProducts();

        for (const template of allTemplates) {
            const runModeError = this.validateImportTestTemplateRunMode(template);
            if (runModeError) {
                errors.push(runModeError);
                continue;
            }

            let allProductsHaveConnection = true;

            for (const product of productIds) {
                const activeConnectionsPerProduct = activeConnections.filter(
                    connection =>
                        connection.state === ConnectionState.ACTIVE &&
                        (connection.products.some(p => p.id === product) ||
                            !productLevelProviderTypes.includes(connection.providerType)),
                );

                const connectionError = this.validateImportTestTemplateConnection(
                    template,
                    activeConnectionsPerProduct,
                );

                if (connectionError) {
                    errors.push(connectionError);
                    allProductsHaveConnection = false;
                    break;
                }
            }

            if (allProductsHaveConnection) {
                testTemplatesIdsToAdd.push(template.id);
            }
        }
        return new LibraryTestTemplateBulkAddValidationResponseDto().build(
            testTemplatesIdsToAdd,
            errors,
        );
    }

    async validateImportTestTemplateViaTestId(
        testId: number,
    ): Promise<LibraryTestTemplateErrorResultType> {
        const controlTestTemplate = await this.templateCoreService.getTestTemplateByTestId(testId);
        if (!controlTestTemplate) {
            throw new NotFoundException(ErrorCode.CONTROL_TEST_TEMPLATE_NOT_FOUND);
        }

        const activeConnections =
            await this.connectionsCoreService.getActiveConnectionsWithProducts();

        const runModeError = this.validateImportTestTemplateRunMode(controlTestTemplate);
        if (runModeError) {
            return { error: runModeError };
        }

        return {
            error: this.validateImportTestTemplateConnection(
                controlTestTemplate,
                activeConnections,
            ),
        };
    }

    validateImportTestTemplateRunMode(
        controlTestTemplate: ControlTestTemplate,
    ): LibraryTestTemplateErrorType | null {
        if (controlTestTemplate.runMode === RunMode.AP1) {
            return {
                testTemplateId: controlTestTemplate.id,
                name: controlTestTemplate.name,
                testId: controlTestTemplate.testId,
                errorType: LibraryTestTemplateError.NOT_SUPPORTED,
            };
        }
        return null;
    }

    validateImportTestTemplateConnection(
        controlTestTemplate: ControlTestTemplate,
        activeConnections: ConnectionEntity[],
    ): LibraryTestTemplateErrorType | null {
        const templateConnections = getConnectionsFromMonitorTemplate(controlTestTemplate);
        if (!templateConnections.length) {
            return null;
        }

        const hasSomeRequiredConnection = activeConnections
            ?.map(connection => connection.clientType)
            .some(clientType => templateConnections.includes(ClientType[clientType]));

        if (!hasSomeRequiredConnection) {
            return {
                testTemplateId: controlTestTemplate.id,
                name: controlTestTemplate.name,
                testId: controlTestTemplate.testId,
                errorType: LibraryTestTemplateError.MISSING_CONNECTION,
            };
        }
        return null;
    }

    /**
     * @param requestDto
     */
    async bulkAddTestTemplates(
        account: Account,
        user: User,
        requestDto: LibraryTestTemplateBulkAddRequestDto,
    ): Promise<number> {
        try {
            const temporalClient = await getTemporalClient(account.domain);

            const result = await temporalClient.executeWorkflow(bulkAddTestTemplatesWorkflowV1, {
                taskQueue: config.get('temporal.taskQueues.temporal-default'),
                args: [
                    {
                        account,
                        user,
                        requestDto,
                    },
                ],
                memo: { accountId: account.id, domain: account.domain },
            });

            this._eventBus.publish(new ExecuteBulkTestEvent(account, result.testsByWorkspace));

            return result.newTestsCount;
        } catch (error) {
            this.error(error, undefined, {
                event: 'bulkAddTestTemplates',
                message: 'Error executing bulk add test templates workflow',
            });
            throw error;
        }
    }

    @TenancyTransaction()
    async importTestTemplateById(
        account: Account,
        user: User,
        templateId: number,
        requestDto: LibraryTestTemplateImportRequestDto,
    ): Promise<ControlTestInstance[]> {
        const { name, description, draft, workspaceIds } = requestDto;
        const workspaces = await this.workspacesCoreService.selectValidWorkspacesOrFail(
            account,
            workspaceIds,
        );
        const controlTestTemplate = await this.templateCoreService.getTestTemplateById(templateId);
        if (isNil(controlTestTemplate)) {
            throw new NotFoundException(ErrorCode.CONTROL_TEST_TEMPLATE_NOT_FOUND);
        }

        const controlTestNameExists = await this.controlTestInstanceRepository.findOneBy({
            name,
        });

        if (controlTestNameExists) {
            throw new ConflictException(
                'A control test with this name already exists',
                ErrorCode.CONTROL_TEST_INSTANCE_NAME_EXISTS,
            );
        }

        const autopilotRecipeTemplate = await this.templateCoreService.getAutopilotRecipeTemplate(
            controlTestTemplate.testId,
        );

        const controlTemplateIds = controlTestTemplate.controlTemplates.map(ct => ct.id);

        const controlTestInstances = await promiseAllInBatches(
            workspaces,
            this.SEQUENTIAL_BATCH_SIZE,
            async workspace => {
                const controls = controlTemplateIds?.length
                    ? await this.controlRepository.getControlsByTemplateIdsAndWorkspaceId(
                          controlTemplateIds,
                          workspace.id,
                      )
                    : [];
                const latestTestId = await this.getNextControlTestId(account);
                return createControlTestInstanceFromControlTestTemplate(
                    controlTestTemplate,
                    workspace,
                    {
                        name,
                        description,
                        draft,
                        controls,
                        testId: latestTestId,
                        source: TestSource.DRATA_LIBRARY,
                    },
                );
            },
        );

        const savedControlTestInstances =
            await this.controlTestInstanceRepository.save(controlTestInstances);

        this.logger.log(
            PolloAdapter.acct(
                `Created ${savedControlTestInstances.length} test instances from template ${templateId} for workspaces ${workspaces.map(
                    workspace => workspace.id,
                )}`,
                account,
            )
                .setMetadata({
                    templateId,
                    workspaceIds,
                })
                .setIdentifier({ templateId }),
        );

        if (autopilotRecipeTemplate) {
            const autopilotRecipeInstances = await promiseAllInBatches(
                savedControlTestInstances,
                this.TEST_TEMPLATE_BATCH_SIZE,
                async savedControlTestInstance => {
                    const recipeInstance = createAutopilotRecipeInstanceFromTemplate(
                        autopilotRecipeTemplate,
                        savedControlTestInstance,
                        user,
                    );
                    return this.autopilotRecipeInstanceRepository.save(recipeInstance);
                },
            );

            savedControlTestInstances.forEach((instance, index) => {
                instance.recipes = [autopilotRecipeInstances[index]];
            });

            await this.controlTestInstanceRepository.save(savedControlTestInstances);
        }

        const finalInstances = await promiseAllInBatches(
            savedControlTestInstances,
            this.TEST_TEMPLATE_BATCH_SIZE,
            async savedControlTestInstance => {
                const monitorInstances = await this.createAndSaveMonitorInstances(
                    account,
                    controlTestTemplate,
                    savedControlTestInstance,
                );
                savedControlTestInstance.monitorInstances = monitorInstances;

                return savedControlTestInstance;
            },
        );

        this._eventBus.publish(
            new TemplateAddedEvent(
                account,
                user,
                controlTestTemplate.testId,
                workspaces,
                draft,
                LibraryType.TEST_TEMPLATE,
            ),
        );

        const testIdsByWorkspace: Record<number, number[]> = {};
        for (const instance of finalInstances) {
            const workspaceId = instance.products[0]?.id;
            if (workspaceId) {
                if (!testIdsByWorkspace[workspaceId]) {
                    testIdsByWorkspace[workspaceId] = [];
                }
                testIdsByWorkspace[workspaceId].push(instance.testId);
            }
        }

        this._eventBus.publish(new ExecuteBulkTestEvent(account, testIdsByWorkspace));

        return finalInstances;
    }

    async getNextControlTestId(account: Account): Promise<number> {
        const globalPrefix = config.get<string>('cache.globalPrefix') || 'globalstore';
        const cacheKey = `${globalPrefix}:{${account.id}}:library-test-template:latest-control-test-id`;

        let newLatestNumber = await this.cacheService.get<number>(cacheKey);

        if (isNil(newLatestNumber)) {
            newLatestNumber =
                await this.controlTestInstanceRepository.getControlTestInstanceGreatestTestIdWithDeletedTests();
        }

        const latestNumber =
            newLatestNumber < this.CUSTOM_TEST_BASE_ID
                ? this.CUSTOM_TEST_BASE_ID
                : newLatestNumber + 1;

        await this.cacheService.set<number>(cacheKey, latestNumber, {
            ttl: this.ONE_MINUTE_IN_SECONDS,
        });

        return latestNumber;
    }

    async createAndSaveMonitorInstances(
        account: Account,
        template: ControlTestTemplate,
        controlTestInstance: ControlTestInstance,
    ): Promise<MonitorInstance[]> {
        return promiseAllInBatches(
            template.monitorTemplates,
            this.MONITOR_BATCH_SIZE,
            async monitorTemplate => {
                const monitorInstance = createMonitorInstanceFromMonitorTemplate(
                    monitorTemplate,
                    controlTestInstance,
                );
                monitorInstance.controlTestInstance = controlTestInstance;
                this.logger.log(
                    PolloAdapter.acct(
                        `Creating monitor instance for test id ${controlTestInstance.testId}`,
                        account,
                    )
                        .setMetadata({
                            testId: controlTestInstance.testId,
                            monitorTemplateId: monitorTemplate.id,
                        })
                        .setIdentifier({ monitorTemplateId: monitorTemplate.id }),
                );

                const savedMonitorInstance =
                    await this.monitorInstanceRepository.save(monitorInstance);

                const monitorInstanceCheckTypes = await this.createAndSaveMonitorCheckTypes(
                    account,
                    monitorTemplate,
                    savedMonitorInstance,
                );
                savedMonitorInstance.monitorInstanceCheckTypes = monitorInstanceCheckTypes;

                return savedMonitorInstance;
            },
        );
    }

    async createAndSaveMonitorCheckTypes(
        account: Account,
        template: MonitorTemplate,
        monitorInstance: MonitorInstance,
    ): Promise<MonitorInstanceCheckType[]> {
        const monitorInstanceCheckTypes = template.monitorTemplateCheckTypes.map(
            monitorTemplateCheckType => {
                const monitorInstanceCheckType = new MonitorInstanceCheckType();
                monitorInstanceCheckType.checkType = monitorTemplateCheckType.checkType;
                monitorInstanceCheckType.monitorInstance = monitorInstance;
                return monitorInstanceCheckType;
            },
        );

        this.logger.log(
            PolloAdapter.acct(
                `Creating monitor instance check types for monitor instance ${monitorInstance.id}`,
                account,
            )
                .setMetadata({
                    testId: monitorInstance.controlTestInstance?.testId,
                    templateId: template.id,
                    monitorInstanceId: monitorInstance.id,
                })
                .setIdentifier({ monitorInstanceId: monitorInstance.id }),
        );

        return this.monitorInstanceCheckTypeRepository.save(monitorInstanceCheckTypes);
    }

    private get monitorInstanceRepository(): MonitorInstanceRepository {
        return this.getCustomTenantRepository(MonitorInstanceRepository);
    }

    private get monitorInstanceCheckTypeRepository(): MonitorInstanceCheckTypeRepository {
        return this.getCustomTenantRepository(MonitorInstanceCheckTypeRepository);
    }

    private get controlRepository(): ControlRepository {
        return this.getCustomTenantRepository(ControlRepository);
    }

    private get controlTestInstanceRepository(): ControlTestInstanceRepository {
        return this.getCustomTenantRepository(ControlTestInstanceRepository);
    }

    private get autopilotRecipeInstanceRepository(): AutopilotRecipeInstanceRepository {
        return this.getCustomTenantRepository(AutopilotRecipeInstanceRepository);
    }
}

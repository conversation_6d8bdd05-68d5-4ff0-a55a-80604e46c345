import { ComplianceCheckStatus, ComplianceCheckType, <PERSON><PERSON>r<PERSON><PERSON>, SocketEvent } from '@drata/enums';
import { Injectable, InternalServerErrorException } from '@nestjs/common';
import { CompanyRepository } from 'app/ai/knowledge-setup/repositories/company.repository';
import { SecurityTrainingData } from 'app/apis/classes/security-training/security-training-data.class';
import { ConnectionEntity } from 'app/companies/connections/entities/connection.entity';
import { Company } from 'app/companies/entities/company.entity';
import { TrainingType } from 'app/companies/entities/training-type.enum';
import { CompaniesCoreService } from 'app/companies/services/companies-core.service';
import { ComplianceCheckExclusion } from 'app/compliance-check-exclusions/entities/compliance-check-exclusion.entity';
import { AutopilotComplianceCheckExclusionsArchivedEvent } from 'app/compliance-check-exclusions/observables/events/autopilot-compliance-check-exclusions-archived.event';
import { ComplianceCheckExclusionRepository } from 'app/compliance-check-exclusions/repositories/compliance-check-exclusion.repository';
import { ComplianceCheckExclusionsCoreService } from 'app/compliance-check-exclusions/services/compliance-check-exclusions-core.service';
import { PersonnelExclusions } from 'app/compliance-check-exclusions/types/personnel-exclusions.type';
import { TrainingCampaign } from 'app/security-training/entities/training-campaign.entity';
import { ComplianceCheckSettingTypeMap } from 'app/security-training/enums/compliance-check-setting-type.map';
import { TrainingComplianceCheckRepository } from 'app/security-training/repositories/training-compliance-check.repository';
import { SecurityTrainingComplianceChecksCoreService } from 'app/security-training/services/security-training-compliance-checks-core.service';
import { Setting } from 'app/settings/entities/setting.entity';
import { SettingsCoreService } from 'app/settings/services/settings-core.service';
import { AutoResetConfig } from 'app/settings/types/auto-reset-config.type';
import { UserDocumentEnrichedWithDownloadUrl } from 'app/users/entities/user-document-enriched-with-download-url.entity';
import { UserDocumentTypeByTrainingCampaignType } from 'app/users/entities/user-document-training-campaign-type.map';
import { UserDocumentTypeToComplianceCheckType } from 'app/users/entities/user-document-type-to-compliance-check-type.map';
import { UserDocument } from 'app/users/entities/user-document.entity';
import { User } from 'app/users/entities/user.entity';
import { UserDocumentExpand } from 'app/users/enums/user-document-expand.enum';
import { UserDocumentDeletedEvent } from 'app/users/observables/events/user-document-deleted.event';
import { UserDocumentUploadedEvent } from 'app/users/observables/events/user-document-uploaded.event';
import { ComplianceCheck } from 'app/users/personnel/entities/compliance-check.entity';
import { Personnel } from 'app/users/personnel/entities/personnel.entity';
import {
    filterAllPersonnelComplianceCheckExclusionsByPersonnel,
    getLatestTrainingCompletionDate,
} from 'app/users/personnel/helpers/personnel.helper';
import { HipaaTrainingUpdatedEvent } from 'app/users/personnel/observables/events/hipaa-training-updated.event';
import { NistAiTrainingUpdatedEvent } from 'app/users/personnel/observables/events/nist-ai-training-updated.event';
import { SecurityTrainingUpdatedEvent } from 'app/users/personnel/observables/events/security-training-updated.event';
import { ComplianceCheckRepository } from 'app/users/personnel/repositories/compliance-check.repository';
import { PersonnelRepository } from 'app/users/personnel/repositories/personnel.repository';
import { ComplianceChecksCoreService } from 'app/users/personnel/services/compliance-checks-core.service';
import { PersonnelCoreService } from 'app/users/personnel/services/personnel-core.service';
import { PersonnelTicketsOrchestrationService } from 'app/users/personnel/services/personnel-tickets-orchestration.service';
import { ComplianceCheckWithExclusion } from 'app/users/personnel/types/compliance-check-with-exclusion.type';
import { ExtendedPersonnel } from 'app/users/personnel/types/extended-personnel.type';
import { FailComplianceCheckOptions } from 'app/users/personnel/types/fail-compliance-check-options.type';
import { UserPolicyVersion } from 'app/users/policies/entities/user-policy-version.entity';
import { UserPolicyAcceptedEvent } from 'app/users/policies/observables/events/user-policy-accepted.event';
import { PolicyRepository } from 'app/users/policies/repositories/policy.repository';
import { UserPolicyVersionRepository } from 'app/users/policies/repositories/user-policy-version.repository';
import { UserDocumentRepository } from 'app/users/repositories/user-document.repository';
import { UserRepository } from 'app/users/repositories/user.repository';
import { UsersCoreService } from 'app/users/services/users-core.service';
import { UserDocumentRequestType } from 'app/users/types/user-document.request.type';
import { UserDocumentsRequestType } from 'app/users/types/user-documents-request.type';
import { Account } from 'auth/entities/account.entity';
import {
    mapComplianceCheckTypeClientType,
    mapComplianceCheckTypeTrainingCampaignType,
    mapHipaaTrainingToClientType,
    mapNistAiTrainingToClientType,
    mapSecurityTrainingToClientType,
} from 'auth/helpers/provider-type.helper';
import { StackOneSecurityTrainingTypes } from 'auth/helpers/stack-one/stack-one-provider-type.helper';
import { PolloAdapter } from 'commons/adapters/pollo.adapter';
import { Benchmark } from 'commons/benchmark/benchmark';
import { ComplianceCheckExclusionStatus } from 'commons/enums/compliance-check-exclusions/compliance-check-exclusion-status.enum';
import { ComplianceCheckExclusionTargetType } from 'commons/enums/compliance-check-exclusions/compliance-check-exclusion-target-type.enum';
import { HipaaTrainingType } from 'commons/enums/hipaa-training-type.enum';
import { NistAITrainingType } from 'commons/enums/nistai-training-type.enum';
import { EmploymentStatusOptionsFilter } from 'commons/enums/personnel/employment-status-options-filter.enum';
import { SecurityTrainingType } from 'commons/enums/security-training-type.enum';
import { AutoResetType } from 'commons/enums/security-training/auto-reset-type.enum';
import { TrainingCampaignType } from 'commons/enums/training-campaign-type.enum';
import { UploadType } from 'commons/enums/upload-type.enum';
import { UserDocumentType } from 'commons/enums/users/user-document-type.enum';
import { NotFoundException } from 'commons/exceptions/not-found.exception';
import { checkFrequencyNextExpiration } from 'commons/helpers/check-frequency.helper';
import {
    areUserDocumentsCompliant,
    isDocumentValidAfterDate,
} from 'commons/helpers/compliance-check.helper';
import { hasExpired } from 'commons/helpers/date.helper';
import { getNumericEnumValues } from 'commons/helpers/enum.helper';
import {
    allStrictFormerStatuses,
    EmploymentStatusOptionsFilterMap,
} from 'commons/helpers/personnel.helper';
import { checkIsDocumentOrImage } from 'commons/helpers/upload.helper';
import { AppService } from 'commons/services/app.service';
import { CreateUserDocumentRequestType } from 'commons/types/create-user-document-request.type';
import { CursorPage } from 'commons/types/cursor-page.type';
import { UploadedFileType } from 'commons/types/uploaded-file.type';
import { Downloader } from 'dependencies/downloader/downloader';
import { Socket } from 'dependencies/socket/socket';
import { UploaderPayloadType } from 'dependencies/uploader/types/uploader-payload.type';
import { Uploader } from 'dependencies/uploader/uploader';
import { FeatureFlagService } from 'feature-flags/feature-flag.service';
import { compact, isEmpty, isNil, uniq, uniqBy } from 'lodash';
import moment from 'moment';
import { PolloMessage } from 'pollo-logger/classes/pollo.message';
import { tenantWrapper } from 'tenancy/contexts/tenant-wrapper';
import { IsNull } from 'typeorm';

@Injectable()
export class ComplianceChecksOrchestrationService extends AppService {
    constructor(
        protected readonly complianceCheckExclusionsCoreService: ComplianceCheckExclusionsCoreService,
        protected readonly complianceChecksCoreService: ComplianceChecksCoreService,
        protected readonly featureFlagService: FeatureFlagService,
        protected readonly companiesCoreService: CompaniesCoreService,
        protected readonly personnelCoreService: PersonnelCoreService,
        protected readonly securityTrainingComplianceChecksCoreService: SecurityTrainingComplianceChecksCoreService,
        protected readonly settingsCoreService: SettingsCoreService,
        protected readonly usersCoreService: UsersCoreService,
        protected readonly uploader: Uploader,
        protected readonly downloader: Downloader,
        protected readonly personnelTicketsOrchestrationService: PersonnelTicketsOrchestrationService,
        protected readonly socket: Socket,
    ) {
        super();
    }

    async uploadUserDocumentFromBuffer(
        account: Account,
        user: User,
        data: Buffer,
        type: UserDocumentType,
        fileName: string,
        mimetype: string,
    ): Promise<UserDocument> {
        // upload the file from the buffer and return the path to it
        const uploadedFile = await this.uploader.uploadPrivateFileFromBuffer(
            account.id,
            UploadType.USER_DOCUMENT,
            data,
            fileName,
            mimetype,
        );
        // create the user document record from the uploaded file
        return this.createUserDocument(account, user, uploadedFile, type);
    }

    async acceptUserPolicyVersion(
        account: Account,
        user: User,
        policyId: number,
        acceptedAt?: Date,
    ): Promise<UserPolicyVersion> {
        this.logger.log(
            PolloAdapter.acct('Begin acceptUserPolicyVersion method', account)
                .setDomain(account.domain)
                .setAccountId(account.id)
                .setIdentifier({
                    user: user.id,
                    policy: policyId,
                })
                .setSubContext('acceptUserPolicyVersion'),
        );

        const policy =
            await this.policyRepository.getPolicyWithCurrentPublishedVersionByPolicyIdOrFail(
                policyId,
            );
        const currentPublishedVersion = policy.currentPublishedVersion();
        if (isNil(currentPublishedVersion)) {
            // no versioned published yet, cannot process any further
            throw new NotFoundException(ErrorCode.POLICY_VERSION_NOT_FOUND);
        }
        // run the accepted logic
        const userPolicyVersion = await this.userPolicyVersionRepository.getUserPolicyVersion(
            user.id,
            policyId,
            currentPublishedVersion.id,
        );
        if (isNil(userPolicyVersion.acceptedAt)) {
            const currentVersion = userPolicyVersion.policyVersion;
            userPolicyVersion.acceptedAt = acceptedAt || new Date();
            await this.userPolicyVersionRepository.save(userPolicyVersion);
            // check if all polices are accepted
            const allCurrentPoliciesAccepted =
                await this.userPolicyVersionRepository.areAllCurrentPoliciesAccepted(user, account);
            // they are, mark this user as compliant
            if (allCurrentPoliciesAccepted.allPoliciesAccepted) {
                // update the personnel accepted policies check
                await this.passComplianceCheck(
                    user,
                    ComplianceCheckType.ACCEPTED_POLICIES,
                    userPolicyVersion.acceptedAt,
                );
            }
            // emit the PolicyVersionApprovedEvent event here
            this._eventBus.publish(
                new UserPolicyAcceptedEvent(account, user, currentVersion.pdf, policy),
            );
        }
        this.logger.log(
            PolloAdapter.acct('Finish acceptUserPolicyVersion method', account)
                .setDomain(account.domain)
                .setAccountId(account.id)
                .setSubContext('acceptUserPolicyVersion'),
        );
        return userPolicyVersion;
    }

    async updateNistAiTrainingComplianceCheck(
        account: Account,
        connection: ConnectionEntity,
        personnel: Personnel,
        nistAiTraining: NistAITrainingType,
    ): Promise<void> {
        const nistAiTrainingCheck = personnel.complianceChecks.find(
            complianceCheck => complianceCheck.type === ComplianceCheckType.NIST_AI_TRAINING,
        );

        const clientType = mapNistAiTrainingToClientType(nistAiTraining);
        if (isNil(clientType)) {
            this.log(`NistAI Training Fail Compliance Check ${personnel.user.email}`, account);

            return;
        }

        const personnelNistAITrainingComplianceChecks =
            await this.trainingComplianceCheckRepository.getAssignmentChecksByPersonnel(
                personnel.id,
                { campaignType: TrainingCampaignType.NIST_AI_TRAINING },
            );

        if (isEmpty(personnelNistAITrainingComplianceChecks)) {
            this.log(
                `No NIST_AI trainings where found for ${personnel.user.email}, skipping...`,
                account,
            );
            return;
        }

        const isNistAiTrainingFullyCompliant =
            await this.securityTrainingComplianceChecksCoreService.hasPersonnelAllCompletedAssignments(
                personnel.id,
                TrainingCampaignType.NIST_AI_TRAINING,
                clientType,
            );

        if (isNistAiTrainingFullyCompliant) {
            const usersDocuments = await this.userDocumentRepository.getAllDocumentsOfTypeManyUsers(
                UserDocumentType.NIST_AI_TRAINING_EVIDENCE,
                [personnel.user?.id] as number[],
            );
            const latestCompletionDate = getLatestTrainingCompletionDate(
                personnelNistAITrainingComplianceChecks,
                usersDocuments,
            );

            if (
                nistAiTrainingCheck.status === ComplianceCheckStatus.PASS &&
                latestCompletionDate < nistAiTrainingCheck?.completionDate
            ) {
                this.log(
                    `The NistAI Training Compliance has already been passed, ${personnel.user.email}`,
                    account,
                );

                return;
            }

            await this.passComplianceCheck(
                personnel.user,
                ComplianceCheckType.NIST_AI_TRAINING,
                latestCompletionDate,
                true,
            );

            this.log(`NistAI Training Pass Compliance Check ${personnel.user.email}`, account);
        } else {
            await this.failComplianceCheck(personnel.user, ComplianceCheckType.NIST_AI_TRAINING, {
                account,
            });

            this.log(`NistAI Training Fail Compliance Check ${personnel.user.email}`, account);
        }
        // emit the event
        this._eventBus.publish(new NistAiTrainingUpdatedEvent(account, personnel, connection));
    }

    async updateHipaaTrainingComplianceCheck(
        account: Account,
        connection: ConnectionEntity,
        personnel: Personnel,
        hipaaTraining: HipaaTrainingType,
    ): Promise<void> {
        const hipaaTrainingCheck = personnel.complianceChecks.find(
            complianceCheck => complianceCheck.type === ComplianceCheckType.HIPAA_TRAINING,
        );

        const clientType = mapHipaaTrainingToClientType(hipaaTraining);
        if (isNil(clientType)) {
            this.log(`Hipaa Training Fail Compliance Check ${personnel.user.email}`, account);

            return;
        }

        const personnelHipaaTrainingComplianceChecks =
            await this.trainingComplianceCheckRepository.getAssignmentChecksByPersonnel(
                personnel.id,
                { campaignType: TrainingCampaignType.HIPAA_TRAINING },
            );

        if (isEmpty(personnelHipaaTrainingComplianceChecks)) {
            this.log(
                `No HIPAA trainings where found for ${personnel.user.email}, skipping...`,
                account,
            );
            return;
        }

        const isHipaaTrainingFullyCompliant =
            await this.securityTrainingComplianceChecksCoreService.hasPersonnelAllCompletedAssignments(
                personnel.id,
                TrainingCampaignType.HIPAA_TRAINING,
                clientType,
            );

        if (isHipaaTrainingFullyCompliant) {
            const usersDocuments = await this.userDocumentRepository.getAllDocumentsOfTypeManyUsers(
                UserDocumentType.HIPAA_TRAINING_EVIDENCE,
                [personnel.user?.id] as number[],
            );
            const latestCompletionDate = getLatestTrainingCompletionDate(
                personnelHipaaTrainingComplianceChecks,
                usersDocuments,
            );

            if (
                hipaaTrainingCheck.status === ComplianceCheckStatus.PASS &&
                latestCompletionDate < hipaaTrainingCheck?.completionDate
            ) {
                this.log(
                    `The Hipaa Training Compliance has already been passed, ${personnel.user.email}`,
                    account,
                );

                return;
            }

            await this.passComplianceCheck(
                personnel.user,
                ComplianceCheckType.HIPAA_TRAINING,
                latestCompletionDate,
                true,
            );

            this.log(`Hipaa Training Pass Compliance Check ${personnel.user.email}`, account);
        } else {
            await this.failComplianceCheck(personnel.user, ComplianceCheckType.HIPAA_TRAINING, {
                account,
            });

            this.log(`Hipaa Training Fail Compliance Check ${personnel.user.email}`, account);
        }
        // emit the event
        this._eventBus.publish(new HipaaTrainingUpdatedEvent(account, personnel, connection));
    }

    async updateSecurityTrainingComplianceCheck(
        account: Account,
        connection: ConnectionEntity,
        personnel: Personnel,
        securityTraining: SecurityTrainingType,
    ): Promise<void> {
        const securityTrainingCheck = personnel.complianceChecks.find(
            complianceCheck => complianceCheck.type === ComplianceCheckType.SECURITY_TRAINING,
        );

        const clientType = mapSecurityTrainingToClientType(securityTraining);
        if (isNil(clientType)) {
            this.log(`Security Training Fail Compliance Check ${personnel.user.email}`, account);

            return;
        }

        const personnelSecurityTrainingComplianceChecks =
            await this.trainingComplianceCheckRepository.getAssignmentChecksByPersonnel(
                personnel.id,
                { campaignType: TrainingCampaignType.SECURITY_TRAINING },
            );

        if (isEmpty(personnelSecurityTrainingComplianceChecks)) {
            this.log(
                `No SECURITY trainings where found for ${personnel.user.email}, skipping...`,
                account,
            );
            return;
        }

        const isSecurityTrainingFullyCompliant =
            await this.securityTrainingComplianceChecksCoreService.hasPersonnelAllCompletedAssignments(
                personnel.id,
                TrainingCampaignType.SECURITY_TRAINING,
                clientType,
            );

        if (isSecurityTrainingFullyCompliant) {
            const usersDocuments = await this.userDocumentRepository.getAllDocumentsOfTypeManyUsers(
                UserDocumentType.SEC_TRAINING,
                [personnel.user?.id] as number[],
            );
            const latestCompletionDate = getLatestTrainingCompletionDate(
                personnelSecurityTrainingComplianceChecks,
                usersDocuments,
            );

            if (
                securityTrainingCheck?.status === ComplianceCheckStatus.PASS &&
                latestCompletionDate <= securityTrainingCheck?.completionDate
            ) {
                this.log(
                    `The Security Training Compliance has already been passed, ${personnel.user.email}`,
                    account,
                );
                return;
            }

            await this.passComplianceCheck(
                personnel.user,
                ComplianceCheckType.SECURITY_TRAINING,
                latestCompletionDate,
                true,
            );

            this.log(`Security Training Pass Compliance Check ${personnel.user.email}`, account);
        } else {
            await this.failComplianceCheck(personnel.user, ComplianceCheckType.SECURITY_TRAINING, {
                account,
            });

            this.log(`Security Training Fail Compliance Check ${personnel.user.email}`, account);
        }
        // emit the event
        this._eventBus.publish(new SecurityTrainingUpdatedEvent(account, personnel, connection));
    }

    async saveSecurityTrainingData(
        personnel: Personnel,
        account: Account,
        trainingCampaign: TrainingCampaign,
        data: SecurityTrainingData,
        fileName: string,
        fileNameWithoutExtension: string,
        company: Company,
        isInitialSync?: boolean,
    ): Promise<void> {
        const { isCompliance, url } = data;

        await this.securityTrainingComplianceChecksCoreService.saveTrainingComplianceCheckByPersonnel(
            account,
            trainingCampaign,
            personnel,
            data,
            fileNameWithoutExtension,
        );

        const { securityTraining, hipaaTraining, nistaiTraining } = company;
        const isSecurityTraining =
            trainingCampaign.type === TrainingCampaignType.SECURITY_TRAINING &&
            [
                SecurityTrainingType.KNOWBE4,
                SecurityTrainingType.CURRICULA,
                ...StackOneSecurityTrainingTypes,
            ].includes(securityTraining ?? 0);
        const isHipaaTraining =
            trainingCampaign.type === TrainingCampaignType.HIPAA_TRAINING &&
            [HipaaTrainingType.KNOWBE4].includes(hipaaTraining);
        const isNistAiTraining =
            trainingCampaign.type === TrainingCampaignType.NIST_AI_TRAINING &&
            [NistAITrainingType.KNOWBE4].includes(nistaiTraining);

        const canUploadUserDocumentFromUrl = isCompliance && !isNil(url);

        const shouldUploadUserDocumentFromUrl = canUploadUserDocumentFromUrl
            ? isSecurityTraining || isHipaaTraining || isNistAiTraining
            : false;

        if (shouldUploadUserDocumentFromUrl) {
            const userDocumentType = UserDocumentTypeByTrainingCampaignType.get(
                trainingCampaign.type,
            );

            /**
             * verify if document should be saved locally
             */
            const userHasValidTrainingDocument = await this.userHasValidTrainingDocument(
                trainingCampaign.type,
                personnel.user,
                fileNameWithoutExtension,
            );

            if (!userHasValidTrainingDocument || isInitialSync) {
                const benchmark = new Benchmark();
                await this.uploadUserDocumentFromUrl(
                    url,
                    fileName,
                    userDocumentType,
                    account,
                    personnel.user,
                    data,
                );
                benchmark.end();

                this.logSaveAgentOperation(
                    account,
                    `Upload user document operation benchmark`,
                    {
                        personnelId: personnel.id,
                    },
                    benchmark,
                );
            }
        }
    }

    async uploadUserDocumentFromUrl(
        url: string,
        fileName: string,
        type: UserDocumentType,
        account: Account,
        user: User,
        data: SecurityTrainingData,
    ): Promise<UserDocument> {
        const uploadedFile = await this.uploader.uploadPrivateFileFromUrl(
            url,
            fileName,
            account.id,
            UploadType.USER_DOCUMENT,
            null,
            true,
        );

        const completionDate = data.completedAt ? new Date(data.completedAt) : null;
        // create the user document record from the uploaded file
        return this.createUserDocument(
            account,
            user,
            uploadedFile,
            type,
            null, // uploaderUser
            completionDate,
            false,
        );
    }

    async uploadDocumentForUser(
        userId: number,
        account: Account,
        file: UploadedFileType,
        createUserDocumentRequestType: CreateUserDocumentRequestType,
        adminUser: User,
    ): Promise<UserDocument> {
        const userForDocument = await this.userRepository.findOneByUserIdOrFail(userId);

        return this.uploadUserDocument(
            account,
            userForDocument,
            file,
            createUserDocumentRequestType,
            adminUser,
        );
    }

    async uploadUserDocument(
        account: Account,
        user: User,
        file: UploadedFileType,
        createUserDocumentRequestType: CreateUserDocumentRequestType,
        adminUser?: User,
    ): Promise<UserDocument> {
        try {
            const uploadedFile = await this.uploadUserDocumentAndValidate(file, account.id);

            return await this.createUserDocument(
                account,
                user,
                uploadedFile,
                createUserDocumentRequestType.type,
                adminUser,
                createUserDocumentRequestType.completionDate,
            );
        } catch (error) {
            this.logger.logConditionalWarningOrError(
                PolloAdapter.acct(error, account).setError(error),
            );
            throw error;
        }
    }

    async uploadUserDocumentAndValidate(
        file: UploadedFileType,
        accountId: string,
    ): Promise<UploaderPayloadType> {
        await checkIsDocumentOrImage(file);
        return this.uploader.uploadPrivateFile(file, UploadType.USER_DOCUMENT, accountId);
    }

    async getUserDocumentsByUserIdWithCursor(
        userId: number,
        userDocumentsPublicRequest: UserDocumentsRequestType,
    ) {
        const page: CursorPage<UserDocumentEnrichedWithDownloadUrl> =
            await this.userDocumentRepository.findUserDocumentsByIdWithCursor(
                userId,
                userDocumentsPublicRequest,
            );

        if (userDocumentsPublicRequest.expand?.includes(UserDocumentExpand.downloadUrl)) {
            page.data = await Promise.all(
                page.data.map(async userDocument => {
                    userDocument.downloadUrl = await this.downloader.getDownloadUrl(
                        userDocument.file,
                    );
                    return userDocument;
                }),
            );
        }

        return page;
    }

    async getUserDocument(
        userId: number,
        documentId: number,
        userDocumentPublicRequest: UserDocumentRequestType,
    ): Promise<UserDocumentEnrichedWithDownloadUrl> {
        const userDocument: UserDocumentEnrichedWithDownloadUrl =
            await this.userDocumentRepository.findOneByOrFail({
                id: documentId,
                user: {
                    id: userId,
                },
            });

        if (userDocumentPublicRequest.expand?.includes(UserDocumentExpand.downloadUrl)) {
            userDocument.downloadUrl = await this.downloader.getDownloadUrl(userDocument.file);
        }

        return userDocument;
    }

    async deleteDocumentForUser(
        account: Account,
        docId: number,
        userId: number,
        deleterUser?: User,
    ): Promise<void> {
        const user = await this.userRepository.findOneByUserIdOrFail(userId);
        return this.deleteUserDocument(account, user, docId, deleterUser);
    }

    async deleteUserDocument(
        account: Account,
        user: User,
        id: number,
        deleterUser?: User,
    ): Promise<void> {
        const userDocument = await this.userDocumentRepository.findOneByOrFail({
            id,
            user: {
                id: user.id,
            },
        });

        await this.userDocumentRepository.softDelete(id);
        await this.updateComplianceStatusOnDocumentDeletion(userDocument, user);

        this._eventBus.publish(
            new UserDocumentDeletedEvent(account, user, userDocument, deleterUser),
        );
    }

    private async updateComplianceStatusOnDocumentDeletion(
        deletedDocument: UserDocument,
        user: User,
    ): Promise<void> {
        const complianceCheckType = UserDocumentTypeToComplianceCheckType.get(deletedDocument.type);

        const remainingDocuments = await this.userDocumentRepository.findBy({
            type: deletedDocument.type,
            user: {
                id: user.id,
            },
            deletedAt: IsNull(),
        });

        if (complianceCheckType === ComplianceCheckType.SECURITY_TRAINING) {
            await this.updateSecurityTrainingStatusOnDelete(user, isEmpty(remainingDocuments));
            return;
        }

        if (!isNil(complianceCheckType) && isEmpty(remainingDocuments)) {
            if (complianceCheckType === ComplianceCheckType.OFFBOARDING) {
                await this.personnelTicketsOrchestrationService.updateOffboardingComplianceStatusOnDocumentDeletion(
                    user,
                    remainingDocuments,
                );
            } else {
                await this.failComplianceCheck(user, complianceCheckType);
            }
        }
    }

    private async createUserDocument(
        account: Account,
        user: User,
        uploadedFile: UploaderPayloadType,
        type: UserDocumentType,
        uploaderUser?: User,
        completionDate?: Date,
        updateComplianceCheck = true,
    ): Promise<UserDocument> {
        const userDocument = await this.usersCoreService.saveUserDocument(
            user,
            uploadedFile,
            type,
            completionDate,
        );

        if (updateComplianceCheck) {
            await this.updateComplianceStatusOnDocumentUpload(userDocument, user, completionDate);
        }

        this._eventBus.publish(
            new UserDocumentUploadedEvent(account, user, userDocument, uploaderUser),
        );

        return userDocument;
    }

    /**
     * If the document type is used as proof of compliance for a ComplianceCheckType,
     * that ComplianceCheckType should be passed
     * @param deletedDocument
     * @param user
     */
    async updateComplianceStatusOnDocumentUpload(
        userDocument: UserDocument,
        user: User,
        completionDate?: Date,
    ): Promise<void> {
        // if uploading a UserDocument as compliance evidence
        // pass the compliance check
        const complianceCheckType = UserDocumentTypeToComplianceCheckType.get(userDocument.type);
        if (!isNil(complianceCheckType)) {
            return this.passComplianceCheck(user, complianceCheckType, completionDate);
        }
    }

    /**
     * logic that decides if a user already have a document we want
     * to upload or/and create locally.
     * @param trainingCampaignType
     * @param user
     * @param filename
     * @returns
     */
    async userHasValidTrainingDocument(
        trainingCampaignType: TrainingCampaignType,
        user: User,
        filename: string,
    ): Promise<boolean> {
        /**
         * find and compare if incoming certificate should be added
         */
        const userDocumentType = UserDocumentTypeByTrainingCampaignType.get(trainingCampaignType);
        const trainingDocuments = await this.usersCoreService.listAllDocumentsOfType(
            user.id,
            userDocumentType,
        );

        /**
         * First, check for exact filename match for efficiency
         */
        const exactMatchDocument = trainingDocuments.find(
            (doc: UserDocument) => doc.name === filename,
        );

        if (!isEmpty(exactMatchDocument)) {
            return true;
        }

        /**
         * If no exact match found, check if user has any valid (non-expired) training document
         * for this training type to avoid regenerating documents when connection changes
         * but user already has valid training evidence
         */
        const validTrainingDocument = trainingDocuments.find((doc: UserDocument) => {
            // Check if document is not deleted and not expired
            return (
                isNil(doc.deletedAt) &&
                !isNil(doc.renewalDate) &&
                !hasExpired(new Date(doc.renewalDate))
            );
        });

        return !isEmpty(validTrainingDocument);
    }

    /**
     * Archive expired exclusions or group exclusions whose groups are going to be deleted from the IdP
     * @param {Account} account
     * @param {number[]} groupIds Group ids that were deleted from the IdP
     * @returns {Promise<Personnel[]>}
     */
    async archiveExpiredOrObsoleteExclusions(
        account: Account,
        groupIds?: number[],
    ): Promise<Personnel[]> {
        let expiredOrObsoleteExclusions: ComplianceCheckExclusion[] = [];
        if (groupIds) {
            expiredOrObsoleteExclusions =
                await this.complianceCheckExclusionRepository.findByOptions([
                    {
                        targetIds: groupIds.map(g => String(g)),
                        targetType: ComplianceCheckExclusionTargetType.GROUP,
                        status: ComplianceCheckExclusionStatus.ACTIVE,
                    },
                ]);
        } else {
            expiredOrObsoleteExclusions =
                await this.complianceCheckExclusionRepository.findExpired();
        }
        let uniqueApplicablePersonnel: Personnel[] = [];

        if (!isEmpty(expiredOrObsoleteExclusions)) {
            for (const expiredOrObsoleteExclusion of expiredOrObsoleteExclusions) {
                expiredOrObsoleteExclusion.status = ComplianceCheckExclusionStatus.ARCHIVED;

                this._eventBus.publish(
                    new AutopilotComplianceCheckExclusionsArchivedEvent(
                        account,
                        expiredOrObsoleteExclusion,
                    ),
                );
            }
            const applicablePersonnel =
                await this.personnelCoreService.getApplicablePersonnelForExclusions(
                    expiredOrObsoleteExclusions,
                );

            await this.complianceCheckExclusionRepository.save(expiredOrObsoleteExclusions);

            uniqueApplicablePersonnel = uniqBy(applicablePersonnel, 'id');

            await this.computeComplianceChecksForPersonnel(account, uniqueApplicablePersonnel);
        }

        return uniqueApplicablePersonnel;
    }

    async resetTrainingForMultiple(
        account: Account,
        user: User,
        complianceCheckType: ComplianceCheckType,
    ): Promise<void> {
        const personnel =
            await this.personnelRepository.getCurrentPersonnelFullyCompliance(complianceCheckType);

        await this.updateComplianceForTrainingForMultiple(account, personnel, complianceCheckType);

        this.personnelCoreService.sendTrainingEvent(account, user, {} as User, complianceCheckType);
    }

    async updateComplianceForTrainingForMultiple(
        account: Account,
        multiplePersonnel: Personnel[],
        complianceCheckType: ComplianceCheckType,
    ): Promise<void> {
        const personnelIds: number[] = [];
        const users: User[] = [];

        for (const personnel of multiplePersonnel) {
            const { id, user } = personnel;
            personnelIds.push(id);
            users.push(user);
        }

        await Promise.all([
            this.complianceChecksCoreService.failComplianceCheckForUsers(
                users,
                complianceCheckType,
            ),
            this.securityTrainingComplianceChecksCoreService.resetTrainingChecksForManyPersonnel(
                personnelIds,
                complianceCheckType,
            ),
            this.personnelCoreService.resetTrainingDocumentsForUsers(
                account,
                complianceCheckType,
                users.map(user => user.id),
            ),
        ]);
    }

    async resetTraining(
        account: Account,
        user: User,
        complianceCheckType: ComplianceCheckType,
        personnelId: number,
    ): Promise<ExtendedPersonnel> {
        const personnel = await this.personnelCoreService.getPersonnelByIdWithUser(personnelId);

        await this.updateComplianceForTraining(account, personnel, complianceCheckType);

        this.personnelCoreService.sendTrainingEvent(
            account,
            user,
            personnel.user,
            complianceCheckType,
        );

        return this.getPersonnelWithComplianceTests(account, personnelId); // get updated data
    }

    async updateComplianceForTraining(
        account: Account,
        personnel: Personnel,
        complianceCheckType: ComplianceCheckType,
    ): Promise<void> {
        const { user, id: personnelId } = personnel;

        await Promise.all([
            this.failComplianceCheck(user, complianceCheckType, { account }),
            this.personnelCoreService.resetTrainingDocuments(account, complianceCheckType, user),
            this.securityTrainingComplianceChecksCoreService.resetTrainingsChecks(
                personnelId,
                complianceCheckType,
            ),
        ]);
    }

    async getPersonnelWithComplianceTests(
        account: Account,
        id: number,
    ): Promise<ExtendedPersonnel> {
        const { securityTraining, hipaaTraining, nistaiTraining } =
            await this.companyRepository.findOneOrFail({
                where: { accountId: account.id },
                loadEagerRelations: false,
            });
        const personnel: ExtendedPersonnel =
            await this.personnelCoreService.getPersonnelByIdForComplianceTests(id);
        personnel.complianceTests = await this.personnelCoreService.getPersonnelComplianceTests();

        personnel.complianceChecksWithExclusions =
            await this.getComplianceChecksWithExclusionsForPersonnel(personnel);

        if (!isNil(securityTraining)) {
            personnel.securityTrainingComplianceChecks =
                await this.personnelCoreService.getPersonnelTrainingComplianceChecks(
                    personnel.id,
                    ComplianceCheckType.SECURITY_TRAINING,
                    securityTraining,
                );
        }

        if (!isNil(hipaaTraining)) {
            personnel.hipaaTrainingComplianceChecks =
                await this.personnelCoreService.getPersonnelTrainingComplianceChecks(
                    personnel.id,
                    ComplianceCheckType.HIPAA_TRAINING,
                    hipaaTraining,
                );
        }

        if (!isNil(nistaiTraining)) {
            personnel.nistAiTrainingComplianceChecks =
                await this.personnelCoreService.getPersonnelTrainingComplianceChecks(
                    personnel.id,
                    ComplianceCheckType.NIST_AI_TRAINING,
                    nistaiTraining,
                );
        }

        await this.personnelCoreService.addDocumentsAndEdrAgentToSinglePersonnel(
            personnel,
            account,
        );

        return personnel;
    }

    async getComplianceChecksWithExclusionsForPersonnel(
        personnel: Personnel,
    ): Promise<ComplianceCheckWithExclusion[]> {
        let { complianceChecks } = personnel;

        if (isNil(complianceChecks)) {
            complianceChecks =
                await this.complianceCheckRepository.getComplianceChecksByPersonnel(personnel);
        }

        const company = await this.companyRepository.findOneBy({});

        const complianceCheckExclusions =
            await this.complianceCheckExclusionsCoreService.getCurrentExclusionsForPersonnel(
                company.id,
                [personnel],
            );

        return this.complianceChecksCoreService.getComplianceChecksWithExclusions(
            complianceCheckExclusions,
            complianceChecks,
        );
    }

    async getPersonnelByUserId(userId: number): Promise<ExtendedPersonnel> {
        const personnel: ExtendedPersonnel =
            await this.personnelRepository.findOneByUserIdOrFail(userId);

        const personnelComplianceCheckWithExclusions =
            await this.getComplianceChecksWithExclusionsForPersonnel(personnel);

        personnel.complianceChecksWithExclusions = personnelComplianceCheckWithExclusions;

        return personnel;
    }

    async passComplianceCheckForUsers(
        account: Account,
        users: User[],
        complianceCheckType: ComplianceCheckType,
    ): Promise<void> {
        const usersFiltered =
            await this.complianceChecksCoreService.filterUsersByExcludedComplianceCheck(
                users,
                complianceCheckType,
            );

        await this.complianceCheckRepository.passComplianceCheckForUsers(
            usersFiltered,
            complianceCheckType,
        );

        await this.updateFullComplianceForUsers(account, usersFiltered);
    }
    async updateFullComplianceForUsers(
        account: Account,
        users: User[],
        addOffboardingCompliance?: boolean,
    ): Promise<void> {
        const additionalComplianceChecks: ComplianceCheckType[] = [];

        // if tenant has the HIPAA framework enabled, an extra compliance check must be made.
        const isHipaaFrameworkEnabled = await this.companiesCoreService.getIsTrainingEnabled(
            TrainingType.HIPAA_TRAINING,
        );

        if (isHipaaFrameworkEnabled) {
            additionalComplianceChecks.push(ComplianceCheckType.HIPAA_TRAINING);
        }

        const isNistAiFrameworkEnabled = await this.companiesCoreService.getIsTrainingEnabled(
            TrainingType.NIST_AI_TRAINING,
        );

        if (isNistAiFrameworkEnabled) {
            additionalComplianceChecks.push(ComplianceCheckType.NIST_AI_TRAINING);
        }

        if (addOffboardingCompliance) {
            additionalComplianceChecks.push(ComplianceCheckType.OFFBOARDING);
        }

        // Verify if there is personnel with missing compliance checks
        const userIds = users.map(user => user.id);

        const personnelWithMissingComplianceChecks =
            await this.complianceCheckRepository.getPersonnelMissingChecksByUserIds(userIds);

        if (!isNil(personnelWithMissingComplianceChecks)) {
            const complianceChecks = getNumericEnumValues(ComplianceCheckType);

            // Create missing compliance checks for each personnel found
            for (const personnelWithChecks of personnelWithMissingComplianceChecks) {
                const personnelChecks = personnelWithChecks.checks
                    .split(',')
                    .map(id => parseInt(id));

                const missingComplianceChecks = complianceChecks.filter(
                    check => !personnelChecks.includes(check),
                );

                // eslint-disable-next-line no-await-in-loop
                const personnel = await this.personnelRepository.findOneOrFail({
                    where: { id: personnelWithChecks.personnelId },
                });

                // eslint-disable-next-line no-await-in-loop
                await this.complianceChecksCoreService.createComplianceChecks(
                    missingComplianceChecks,
                    personnel,
                );
            }
        }

        const types = this.complianceChecksCoreService.isFullyComplianceSetup(
            additionalComplianceChecks,
        );

        // has the user become fully compliant
        const complianceChecksNotCompliant = await this.complianceCheckRepository.areFullyCompliant(
            users,
            types,
        );

        const usersFullyCompliant = [];
        const usersNotFullyCompliant = [];

        for (const user of users) {
            const isFullyCompliant = isEmpty(
                complianceChecksNotCompliant.filter(cc => cc.personnel.user?.id === user.id),
            );

            if (isFullyCompliant) {
                usersFullyCompliant.push(user);
            } else {
                usersNotFullyCompliant.push(user);
            }
        }

        // update to be fully compliant
        await this.complianceCheckRepository.passComplianceCheckForUsers(
            usersFullyCompliant,
            ComplianceCheckType.FULL_COMPLIANCE,
        );

        // update to be not fully compliant
        await this.complianceCheckRepository.failComplianceCheckForUsers(
            usersNotFullyCompliant,
            ComplianceCheckType.FULL_COMPLIANCE,
        );
    }

    async hasPersonnelAllCompletedAssignments(
        account: Account,
        personnel: Personnel,
        company: Company,
        complianceCheckType: ComplianceCheckType,
    ): Promise<boolean> {
        let isFullyCompliance: boolean;
        try {
            const trainingCampaignType =
                mapComplianceCheckTypeTrainingCampaignType(complianceCheckType);

            const clientType = mapComplianceCheckTypeClientType(complianceCheckType, company);

            isFullyCompliance = await tenantWrapper(account, () => {
                return this.securityTrainingComplianceChecksCoreService.hasPersonnelAllCompletedAssignments(
                    personnel.id,
                    trainingCampaignType,
                    clientType,
                );
            });
        } catch {
            isFullyCompliance = false;
        }

        return isFullyCompliance;
    }

    async computeTrainingComplianceCheck(
        account: Account,
        personnel: Personnel[],
        userDocumentType: UserDocumentType,
        complianceCheckType: ComplianceCheckType,
        isCLIRequest: boolean,
    ): Promise<void> {
        if (isEmpty(personnel)) {
            return;
        }

        const company = await this.companiesCoreService.getCompanyByAccountId(account.id);

        const users = personnel.map(p => p.user);
        const userIds = users.map(u => u.id);

        const usersDocuments = await this.userDocumentRepository.getAllDocumentsOfTypeManyUsers(
            userDocumentType,
            userIds,
        );

        /**
         * get the auto reset settings based on the training type
         */
        const settingType = ComplianceCheckSettingTypeMap.get(complianceCheckType);

        let settings: Setting;
        try {
            settings = await tenantWrapper(account, () => {
                return this.settingsCoreService.getSettingBySettingType(settingType);
            });
        } catch {
            settings = null;
        }

        if (!isNil(settings) && !isNil(settings.enabledAt)) {
            const autoResetConfig = settings?.configObject as AutoResetConfig;

            const { usersToFail, usersToPass } = await this.evaluateTrainingCompliance(
                users,
                personnel,
                usersDocuments,
                autoResetConfig,
                account,
                company,
                complianceCheckType,
                isCLIRequest,
            );

            if (!isEmpty(usersToFail)) {
                await this.complianceCheckRepository.failComplianceCheckForUsers(
                    usersToFail,
                    complianceCheckType,
                );
            }

            if (!isEmpty(usersToPass)) {
                await this.complianceCheckRepository.passComplianceCheckForUsers(
                    usersToPass,
                    complianceCheckType,
                );
            }
        }
    }

    private async evaluateTrainingCompliance(
        users: (User | null)[] | null,
        personnel: Personnel[],
        usersDocuments: UserDocument[],
        autoResetConfig: AutoResetConfig,
        account: Account,
        company: Company,
        complianceCheckType: ComplianceCheckType,
        isCLIRequest: boolean,
    ): Promise<{
        usersToFail: User[];
        usersToPass: User[];
    }> {
        const usersToFail: User[] = [];
        const usersToPass: User[] = [];

        if (isNil(users) || isEmpty(users) || users.every(user => isNil(user))) {
            return { usersToFail, usersToPass };
        }
        const usersNoNull = users.filter(user => !isNil(user));

        for (const user of usersNoNull) {
            const person = personnel.find(p => p?.user?.id === user?.id);

            const userDocuments = usersDocuments.filter(ud => ud.user.id === user.id);

            const shouldResetTrainingForPersonnel =
                isEmpty(autoResetConfig) || isNil(person)
                    ? false
                    : this.shouldResetTrainingForPersonnel(
                          autoResetConfig,
                          person,
                          complianceCheckType,
                          userDocuments,
                      );

            const isTrainingFullyCompliant = isNil(person)
                ? false
                : // eslint-disable-next-line no-await-in-loop
                  await this.hasPersonnelAllCompletedAssignments(
                      account,
                      person,
                      company,
                      complianceCheckType,
                  );

            const areDocumentsCompliant = areUserDocumentsCompliant(userDocuments, autoResetConfig);

            if (
                !areDocumentsCompliant ||
                shouldResetTrainingForPersonnel ||
                !isTrainingFullyCompliant ||
                /**
                 * we need to evaluate isDocumentValid only if re compute was requested from CLI.
                 * AP will be ignored.
                 */
                (isCLIRequest && !areDocumentsCompliant)
            ) {
                usersToFail.push(user);

                /**
                 * throw event if user fails because of the reset training
                 */
                if (shouldResetTrainingForPersonnel) {
                    this.personnelCoreService.sendTrainingEvent(
                        account,
                        {} as User,
                        user,
                        complianceCheckType,
                    );
                }
            } else {
                usersToPass.push(user);
            }
        }

        return { usersToFail, usersToPass };
    }
    async updateFullCompliance(user: User): Promise<void> {
        const additionalComplianceChecks: ComplianceCheckType[] = [];

        const personnel = await this.personnelRepository.findOneByOrFail({
            user: { id: user.id },
        });

        if (allStrictFormerStatuses.includes(personnel.employmentStatus)) {
            additionalComplianceChecks.push(ComplianceCheckType.OFFBOARDING);
        }

        const isHipaaFrameworkEnabled = await this.companiesCoreService.getIsTrainingEnabled(
            TrainingType.HIPAA_TRAINING,
        );

        if (isHipaaFrameworkEnabled) {
            additionalComplianceChecks.push(ComplianceCheckType.HIPAA_TRAINING);
        }

        const isNistAiFrameworkEnabled = await this.companiesCoreService.getIsTrainingEnabled(
            TrainingType.NIST_AI_TRAINING,
        );

        if (isNistAiFrameworkEnabled) {
            additionalComplianceChecks.push(ComplianceCheckType.NIST_AI_TRAINING);
        }

        // Verify if there is personnel with missing compliance checks
        const [personnelWithMissingComplianceChecks] =
            await this.complianceCheckRepository.getPersonnelMissingChecksByUserIds([user.id]);

        if (!isNil(personnelWithMissingComplianceChecks)) {
            const complianceChecks = getNumericEnumValues(ComplianceCheckType);

            const personnelChecks = personnelWithMissingComplianceChecks.checks
                .split(',')
                .map(id => parseInt(id));

            const missingComplianceChecks = complianceChecks.filter(
                check => !personnelChecks.includes(check),
            );

            // Create missing compliance checks with fail status for personnel
            await this.complianceChecksCoreService.createComplianceChecks(
                missingComplianceChecks,
                personnel,
            );
        }

        const types = this.complianceChecksCoreService.isFullyComplianceSetup(
            additionalComplianceChecks,
        );

        const isFullyCompliant = await this.complianceCheckRepository.isFullyCompliant(user, types);

        if (isFullyCompliant) {
            await this.complianceCheckRepository.passComplianceCheck(
                user,
                ComplianceCheckType.FULL_COMPLIANCE,
                new Date(),
            );
        } else {
            await this.complianceCheckRepository.failComplianceCheck(
                user,
                ComplianceCheckType.FULL_COMPLIANCE,
            );
        }

        this.logger.log(
            PolloMessage.msg(`Finish compliance checks for userId: ${user.id}`)
                .setSubContext(this.updateFullCompliance.name)
                .setContext(this.constructor.name),
        );
    }

    async passComplianceCheck(
        user: User,
        complianceCheckType: ComplianceCheckType,
        completionDate?: Date,
        forceCompletionDateToUpdate?: boolean,
    ): Promise<void> {
        const complianceCheck = await this.complianceCheckRepository.getComplianceCheckByType(
            user,
            complianceCheckType,
        );

        if (isNil(complianceCheck)) {
            this.logger.warn(
                PolloMessage.msg(
                    `Compliance check ${complianceCheckType} not found for user ${user.id}. Cannot be updated`,
                )
                    .setContext(this.constructor.name)
                    .setSubContext('passComplianceCheck'),
            );

            return;
        }

        const { status: complianceCheckStatus } = complianceCheck;

        if (complianceCheckStatus === ComplianceCheckStatus.EXCLUDED) {
            return;
        }

        await this.complianceCheckRepository.passComplianceCheck(
            user,
            complianceCheckType,
            completionDate ? new Date(completionDate) : new Date(),
            forceCompletionDateToUpdate,
        );

        await this.updateFullCompliance(user);
    }

    async failComplianceCheck(
        user: User,
        complianceCheckType: ComplianceCheckType,
        options?: FailComplianceCheckOptions,
    ): Promise<void> {
        const { entryId } = user;
        const { account, skipFullCompliance = false } = options ?? {};

        if (complianceCheckType === ComplianceCheckType.ACCEPTED_POLICIES) {
            /**
             * We do not have the account object here and adding it would be a huge
             * regression - do the best we can here and put a log with the entry id.
             */
            if (isNil(account)) {
                this.logger.log(
                    PolloMessage.msg(`failComplianceCheck called for user with entryId: ${entryId}`)
                        .setContext(this.constructor.name)
                        .setSubContext(this.failComplianceCheck.name),
                );
            } else {
                this.logger.log(
                    PolloAdapter.acct(
                        `failComplianceCheck called for user with entryId: ${entryId}`,
                        account,
                    )
                        .setContext(this.constructor.name)
                        .setSubContext(this.failComplianceCheck.name),
                );
            }
        }

        const complianceCheck = await this.complianceCheckRepository.getComplianceCheckByType(
            user,
            complianceCheckType,
        );

        if (isNil(complianceCheck)) {
            this.logger.warn(
                PolloAdapter.acct(
                    `Compliance check ${complianceCheckType} not found for user ${user.id}. Cannot be updated`,
                    account,
                )
                    .setContext(this.constructor.name)
                    .setSubContext('failComplianceCheck'),
            );
            return;
        }

        const { status: complianceCheckStatus } = complianceCheck;

        if (complianceCheckStatus === ComplianceCheckStatus.EXCLUDED) {
            return;
        }

        await this.complianceCheckRepository.failComplianceCheck(user, complianceCheckType);

        if (!skipFullCompliance) {
            await this.complianceCheckRepository.failComplianceCheck(
                user,
                ComplianceCheckType.FULL_COMPLIANCE,
            );
        }
    }

    private shouldResetTrainingForPersonnel(
        autoResetConfig: AutoResetConfig,
        personnel: Personnel,
        complianceCheckType: ComplianceCheckType,
        userDocuments: UserDocument[],
    ) {
        let shouldResetTraining = false;
        /**
         * get the training compliance check that matches
         */
        const trainingComplianceCheck = personnel.complianceChecks.find(
            complianceCheck => complianceCheck.type === complianceCheckType,
        );

        if (isNil(trainingComplianceCheck?.completionDate)) {
            return shouldResetTraining;
        }

        switch (Number(autoResetConfig.type)) {
            case AutoResetType.GLOBAL_RESET:
                const { day, month } = autoResetConfig;

                /**
                 * build renewal date
                 */
                const resetDate = new Date(new Date().getFullYear(), month - 1, day);

                /**
                 * if today is renewal date we reset
                 */
                shouldResetTraining = moment(new Date()).isSame(resetDate, 'day');

                break;
            case AutoResetType.ROLLING_RESET:
                const completionDate = new Date(trainingComplianceCheck.completionDate);
                completionDate.setFullYear(completionDate.getFullYear() + 1);

                const oneYearAgoUtcDate = moment.utc(new Date()).subtract(1, 'year');

                /**
                 * completion date must be 1 year older and status must be passing
                 */
                const isCompletionDateExpired =
                    new Date() > completionDate &&
                    trainingComplianceCheck.status === ComplianceCheckStatus.PASS;

                /**
                 * check if documents are compliant
                 */
                const validDocuments = userDocuments
                    .filter(document =>
                        [
                            UserDocumentType.SEC_TRAINING,
                            UserDocumentType.HIPAA_TRAINING_EVIDENCE,
                            UserDocumentType.NIST_AI_TRAINING_EVIDENCE,
                        ].includes(document.type),
                    )
                    .filter(({ renewalDate, createdAt }) =>
                        isDocumentValidAfterDate(
                            new Date(renewalDate),
                            createdAt,
                            oneYearAgoUtcDate.toDate(),
                        ),
                    );
                shouldResetTraining = isCompletionDateExpired && isEmpty(validDocuments);
                break;
            case AutoResetType.NO_AUTOMATION:
                break;
            default:
                break;
        }

        return shouldResetTraining;
    }

    getPersonnelToComputeForComplianceCheckType(
        personnelExclusions: PersonnelExclusions[],
        complianceCheckType: ComplianceCheckType,
    ): Personnel[] {
        return personnelExclusions
            .filter(
                pe =>
                    !pe.exclusions.some(e =>
                        e.complianceCheckTypes.some(cct => cct.type === complianceCheckType),
                    ),
            )
            .map(pe => pe.personnel);
    }

    async recomputeComplianceChecksForPersonnel(
        account: Account,
        personnelExclusions: PersonnelExclusions[],
        complianceChecks: ComplianceCheckType[],
        isCLIRequest: boolean,
    ): Promise<void> {
        if (
            this.shouldComputeComplianceCheck(
                complianceChecks,
                ComplianceCheckType.ACCEPTED_POLICIES,
            )
        ) {
            const acceptedPoliciesPersonnel = this.getPersonnelToComputeForComplianceCheckType(
                personnelExclusions,
                ComplianceCheckType.ACCEPTED_POLICIES,
            );

            await this.complianceChecksCoreService.computePoliciesAcceptedComplianceCheck(
                account,
                acceptedPoliciesPersonnel,
            );
        }

        if (this.shouldComputeComplianceCheck(complianceChecks, ComplianceCheckType.IDENTITY_MFA)) {
            const identityMFAPersonnel = this.getPersonnelToComputeForComplianceCheckType(
                personnelExclusions,
                ComplianceCheckType.IDENTITY_MFA,
            );

            await this.complianceChecksCoreService.computeIdentityMFAComplianceCheck(
                identityMFAPersonnel,
                account,
            );
        }

        if (this.shouldComputeComplianceCheck(complianceChecks, ComplianceCheckType.BG_CHECK)) {
            const backgroundCheckPersonnel = this.getPersonnelToComputeForComplianceCheckType(
                personnelExclusions,
                ComplianceCheckType.BG_CHECK,
            );
            await this.complianceChecksCoreService.computeBackgroundCheckComplianceCheck(
                backgroundCheckPersonnel,
            );
        }

        if (
            this.shouldComputeComplianceCheck(
                complianceChecks,
                ComplianceCheckType.SECURITY_TRAINING,
            )
        ) {
            const securityTrainingPersonnel = this.getPersonnelToComputeForComplianceCheckType(
                personnelExclusions,
                ComplianceCheckType.SECURITY_TRAINING,
            );

            await this.computeTrainingComplianceCheck(
                account,
                securityTrainingPersonnel,
                UserDocumentType.SEC_TRAINING,
                ComplianceCheckType.SECURITY_TRAINING,
                isCLIRequest,
            );
        }
        if (
            this.shouldComputeComplianceCheck(complianceChecks, ComplianceCheckType.HIPAA_TRAINING)
        ) {
            const hipaaTrainingPersonnel = this.getPersonnelToComputeForComplianceCheckType(
                personnelExclusions,
                ComplianceCheckType.HIPAA_TRAINING,
            );

            await this.computeTrainingComplianceCheck(
                account,
                hipaaTrainingPersonnel,
                UserDocumentType.HIPAA_TRAINING_EVIDENCE,
                ComplianceCheckType.HIPAA_TRAINING,
                isCLIRequest,
            );
        }
        if (
            this.shouldComputeComplianceCheck(
                complianceChecks,
                ComplianceCheckType.NIST_AI_TRAINING,
            )
        ) {
            const nistAiTrainingPersonnel = this.getPersonnelToComputeForComplianceCheckType(
                personnelExclusions,
                ComplianceCheckType.NIST_AI_TRAINING,
            );

            await this.computeTrainingComplianceCheck(
                account,
                nistAiTrainingPersonnel,
                UserDocumentType.NIST_AI_TRAINING_EVIDENCE,
                ComplianceCheckType.NIST_AI_TRAINING,
                isCLIRequest,
            );
        }

        if (
            this.shouldComputeComplianceCheck(
                complianceChecks,
                ComplianceCheckType.PASSWORD_MANAGER,
            )
        ) {
            const passwordManagerPersonnel = this.getPersonnelToComputeForComplianceCheckType(
                personnelExclusions,
                ComplianceCheckType.PASSWORD_MANAGER,
            );

            await this.complianceChecksCoreService.computeDeviceRelatedComplianceCheck(
                ComplianceCheckType.PASSWORD_MANAGER,
                passwordManagerPersonnel,
            );
        }

        if (
            this.shouldComputeComplianceCheck(complianceChecks, ComplianceCheckType.HDD_ENCRYPTION)
        ) {
            const hddEncryptionPersonnel = this.getPersonnelToComputeForComplianceCheckType(
                personnelExclusions,
                ComplianceCheckType.HDD_ENCRYPTION,
            );

            await this.complianceChecksCoreService.computeDeviceRelatedComplianceCheck(
                ComplianceCheckType.HDD_ENCRYPTION,
                hddEncryptionPersonnel,
            );
        }

        if (this.shouldComputeComplianceCheck(complianceChecks, ComplianceCheckType.ANTIVIRUS)) {
            const antiVirusPersonnel = this.getPersonnelToComputeForComplianceCheckType(
                personnelExclusions,
                ComplianceCheckType.ANTIVIRUS,
            );

            await this.complianceChecksCoreService.computeDeviceRelatedComplianceCheck(
                ComplianceCheckType.ANTIVIRUS,
                antiVirusPersonnel,
            );
        }

        if (this.shouldComputeComplianceCheck(complianceChecks, ComplianceCheckType.AUTO_UPDATES)) {
            const autoUpdatesPersonnel = this.getPersonnelToComputeForComplianceCheckType(
                personnelExclusions,
                ComplianceCheckType.AUTO_UPDATES,
            );

            await this.complianceChecksCoreService.computeDeviceRelatedComplianceCheck(
                ComplianceCheckType.AUTO_UPDATES,
                autoUpdatesPersonnel,
            );
        }

        if (this.shouldComputeComplianceCheck(complianceChecks, ComplianceCheckType.LOCK_SCREEN)) {
            const lockScreenPersonnel = this.getPersonnelToComputeForComplianceCheckType(
                personnelExclusions,
                ComplianceCheckType.LOCK_SCREEN,
            );
            await this.complianceChecksCoreService.computeDeviceRelatedComplianceCheck(
                ComplianceCheckType.LOCK_SCREEN,
                lockScreenPersonnel,
            );
        }

        if (
            this.shouldComputeComplianceCheck(complianceChecks, ComplianceCheckType.AGENT_INSTALLED)
        ) {
            const agentInstalledPersonnel = this.getPersonnelToComputeForComplianceCheckType(
                personnelExclusions,
                ComplianceCheckType.AGENT_INSTALLED,
            );

            await this.complianceChecksCoreService.computeDeviceRelatedComplianceCheck(
                ComplianceCheckType.AGENT_INSTALLED,
                agentInstalledPersonnel,
            );
        }

        if (this.shouldComputeComplianceCheck(complianceChecks, ComplianceCheckType.OFFBOARDING)) {
            const offboardingPersonnel = this.getPersonnelToComputeForComplianceCheckType(
                personnelExclusions,
                ComplianceCheckType.OFFBOARDING,
            );

            await this.complianceChecksCoreService.computeOffboardingComplianceCheck(
                offboardingPersonnel,
            );
        }
    }

    getExclusionTypesForExclusions(exclusions: ComplianceCheckExclusion[]): ComplianceCheckType[] {
        return uniq(exclusions.map(e => e.complianceCheckTypes.map(cct => cct.type)).flat());
    }

    async excludeComplianceChecksForPersonnel(
        personnelExclusions: PersonnelExclusions[],
    ): Promise<void> {
        const complianceChecksToUpdate: ComplianceCheck[] = [];

        for (const currentPersonnel of personnelExclusions) {
            const { personnel, exclusions } = currentPersonnel;
            const excludedComplianceCheckTypes = this.getExclusionTypesForExclusions(exclusions);

            personnel.complianceChecks.forEach(cc => {
                if (
                    excludedComplianceCheckTypes.includes(cc.type) &&
                    cc.status !== ComplianceCheckStatus.EXCLUDED
                ) {
                    cc.status = ComplianceCheckStatus.EXCLUDED;
                    complianceChecksToUpdate.push(cc);
                }
            });
        }

        await this.complianceCheckRepository.save(complianceChecksToUpdate, {
            chunk: 200,
        });
    }

    async getPersonnelExclusionsType(personnel: Personnel[]): Promise<PersonnelExclusions[]> {
        const company = await this.companyRepository.findOneBy({});

        const complianceCheckExclusionsForApplicablePersonnel =
            await this.complianceCheckExclusionsCoreService.getCurrentExclusionsForPersonnel(
                company.id,
                personnel,
            );

        const personnelExclusions: PersonnelExclusions[] = [];

        for (const currentPersonnel of personnel) {
            const currentPersonnelExclusions =
                filterAllPersonnelComplianceCheckExclusionsByPersonnel(
                    complianceCheckExclusionsForApplicablePersonnel,
                    currentPersonnel,
                );

            personnelExclusions.push({
                personnel: currentPersonnel,
                exclusions: currentPersonnelExclusions,
            });
        }

        return personnelExclusions;
    }

    private async notifyOnComplianceCheckCompletion(
        notifyUser: boolean,
        account: Account,
        userId?: number,
    ): Promise<void> {
        if (!notifyUser) {
            return;
        }

        if (isNil(userId)) {
            this.logger.warn(
                PolloAdapter.acct(
                    'Cannot send notification: user is required for socket message',
                    account,
                )
                    .setContext(this.constructor.name)
                    .setSubContext('notifyOnComplianceCheckCompletion')
                    .setIdentifier({
                        notifyUser,
                    }),
            );
            return;
        }

        try {
            await this.socket.sendMessage(
                account.id,
                SocketEvent.PERSONNEL_COMPLIANCE_CHECKS_COMPLETED,
                {},
                userId,
            );
        } catch (error) {
            PolloAdapter.acct('Something went wrong sending message', account).setError(error);
        }
    }

    async computeComplianceChecksForPersonnel(
        account: Account,
        personnel: Personnel[],
        onlyExclusions = false,
        complianceChecks: ComplianceCheckType[] = [],
        isCLIRequest: boolean = false,
        notifyUser = false,
        userId?: number,
    ): Promise<void> {
        try {
            if (isEmpty(personnel)) {
                return;
            }

            const personnelExclusions = await this.getPersonnelExclusionsType(personnel);

            await this.excludeComplianceChecksForPersonnel(personnelExclusions);

            if (!onlyExclusions) {
                await this.recomputeComplianceChecksForPersonnel(
                    account,
                    personnelExclusions,
                    complianceChecks,
                    isCLIRequest,
                );
            }

            const users = compact(personnel.map(p => p.user));

            await this.updateFullComplianceForUsers(account, users);
            void this.notifyOnComplianceCheckCompletion(notifyUser, account, userId);
        } catch (error) {
            const errorMessage = 'Error while computing compliance checks for personnel';
            this.error(error, account, errorMessage);

            throw new InternalServerErrorException(errorMessage);
        }
    }

    async computeComplianceChecksForAllPersonnel(
        account: Account,
        complianceChecks: ComplianceCheckType[] = [],
    ): Promise<void> {
        const applicablePersonnel =
            await this.personnelCoreService.getPersonnelByEmploymentStatuses(
                EmploymentStatusOptionsFilterMap.get(EmploymentStatusOptionsFilter.ALL_PERSONNEL),
            );

        await this.computeComplianceChecksForPersonnel(
            account,
            applicablePersonnel,
            false,
            complianceChecks,
            true,
        );
    }

    async updateAllPersonnelFullyComplianceCheck(): Promise<void> {
        const allPersonnel = await this.personnelRepository.find();

        for (const personnel of allPersonnel) {
            // eslint-disable-next-line no-await-in-loop
            await this.updateFullCompliance(personnel.user);
        }
    }

    private async updateSecurityTrainingStatusOnDelete(
        user: User,
        mustFailSecurityComplianceChecks: boolean,
    ) {
        if (mustFailSecurityComplianceChecks) {
            await this.failComplianceCheck(user, ComplianceCheckType.SECURITY_TRAINING);

            await this.securityTrainingComplianceChecksCoreService.updateTrainingCampaignStatusForPersonnel(
                user.id,
                TrainingCampaignType.SECURITY_TRAINING,
                ComplianceCheckStatus.FAIL,
            );
        }
    }

    private shouldComputeComplianceCheck(
        complianceChecks: ComplianceCheckType[],
        complianceCheck: ComplianceCheckType,
    ) {
        if (isEmpty(complianceChecks)) {
            return true;
        }

        return complianceChecks.some(cc => cc === complianceCheck);
    }

    private logSaveAgentOperation(
        account: Account,
        message: string,
        identifier: unknown,
        benchmark: Benchmark,
    ): void {
        const pollogLog = PolloAdapter.acct(message, account, this.constructor.name).setSubContext(
            account.companyName,
        );
        if (!isNil(identifier)) {
            pollogLog.setIdentifier(identifier);
        }
        if (!isNil(benchmark)) {
            pollogLog.setExecutionTime(benchmark.time());
        }
        this.logger.log(pollogLog);
    }

    private get companyRepository(): CompanyRepository {
        return this.getCustomTenantRepository(CompanyRepository);
    }

    protected get complianceCheckRepository(): ComplianceCheckRepository {
        return this.getCustomTenantRepository(ComplianceCheckRepository);
    }

    private get personnelRepository(): PersonnelRepository {
        return this.getCustomTenantRepository(PersonnelRepository);
    }

    private get userDocumentRepository(): UserDocumentRepository {
        return this.getCustomTenantRepository(UserDocumentRepository);
    }

    private get complianceCheckExclusionRepository(): ComplianceCheckExclusionRepository {
        return this.getCustomTenantRepository(ComplianceCheckExclusionRepository);
    }

    private get trainingComplianceCheckRepository(): TrainingComplianceCheckRepository {
        return this.getCustomTenantRepository(TrainingComplianceCheckRepository);
    }

    private get policyRepository(): PolicyRepository {
        return this.getCustomTenantRepository(PolicyRepository);
    }

    private get userPolicyVersionRepository(): UserPolicyVersionRepository {
        return this.getCustomTenantRepository(UserPolicyVersionRepository);
    }

    private get userRepository(): UserRepository {
        return this.getCustomTenantRepository(UserRepository);
    }

    async cleanupStaleExclusionStatuses(account: Account, personnel?: Personnel[]): Promise<void> {
        try {
            let personnelToCheck: Personnel[] = [];

            if (personnel && !isEmpty(personnel)) {
                personnelToCheck = personnel.filter(person =>
                    person.complianceChecks?.some(
                        cc => cc.status === ComplianceCheckStatus.EXCLUDED,
                    ),
                );
            } else {
                const excludedPersonnelIds =
                    await this.complianceCheckExclusionRepository.findExcludedPersonnelIdsWithoutExclusion(
                        account,
                    );

                const excludedPersonnelIdsWithNoExclusion =
                    await this.complianceCheckExclusionRepository.findPersonnelWithExcludedChecksButNoExclusionRecords(
                        account,
                        excludedPersonnelIds,
                    );

                const allPersonnelIds = uniq([
                    ...excludedPersonnelIds,
                    ...excludedPersonnelIdsWithNoExclusion,
                ]);

                if (!isEmpty(allPersonnelIds)) {
                    personnelToCheck =
                        await this.personnelCoreService.getPersonnelByIds(allPersonnelIds);

                    this.log(
                        `Found ${excludedPersonnelIds.length} personnel with stale exclusions and ` +
                            `${excludedPersonnelIdsWithNoExclusion.length} personnel with excluded checks but no exclusion records`,
                        account,
                    );
                }
            }

            if (isEmpty(personnelToCheck)) {
                return;
            }

            const complianceChecksToReset: ComplianceCheck[] = [];

            const currentExclusions = await this.getPersonnelExclusionsType(personnelToCheck);
            for (const person of personnelToCheck) {
                if (!person.complianceChecks?.length) {
                    this.log(`No compliance checks found for personnel ${person.id}`, account);
                    continue;
                }

                const personnelExclusion =
                    currentExclusions.find(pe => pe.personnel.id === person.id) || null;
                const excludedTypes =
                    personnelExclusion && personnelExclusion.exclusions?.length > 0
                        ? this.getExclusionTypesForExclusions(personnelExclusion.exclusions || [])
                        : [];

                for (const cc of person.complianceChecks) {
                    if (
                        cc.status === ComplianceCheckStatus.EXCLUDED &&
                        !excludedTypes.includes(cc.type)
                    ) {
                        cc.status = ComplianceCheckStatus.FAIL;
                        cc.lastCheckedAt = new Date();
                        cc.expiresAt = checkFrequencyNextExpiration(cc.checkFrequency) || null;
                        complianceChecksToReset.push(cc);
                    }
                }
            }

            if (!isEmpty(complianceChecksToReset)) {
                await this.complianceCheckRepository.save(complianceChecksToReset, { chunk: 200 });

                this.log(
                    `Cleaned up ${complianceChecksToReset.length} stale exclusion statuses for ${personnelToCheck.length} personnel`,
                    account,
                );
            }
        } catch (error) {
            this.logger.error(
                PolloAdapter.acct(
                    `Failed to cleanup stale exclusion statuses: ${error.message}`,
                    account,
                )
                    .setContext(this.constructor.name)
                    .setSubContext('cleanupStaleExclusionStatuses')
                    .setError(error),
            );
        }
    }
}

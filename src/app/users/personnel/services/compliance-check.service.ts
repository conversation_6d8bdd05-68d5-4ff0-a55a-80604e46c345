/* eslint-disable no-await-in-loop */
import { ComplianceCheckStatus, ComplianceCheckType } from '@drata/enums';
import { Injectable } from '@nestjs/common';
import { BackgroundCheckRepository } from 'app/background-check/repositories/background-check.repository';
import { Company } from 'app/companies/entities/company.entity';
import { TrainingType } from 'app/companies/entities/training-type.enum';
import { CompaniesCoreService } from 'app/companies/services/companies-core.service';
import { ComplianceCheckSettingTypeMap } from 'app/security-training/enums/compliance-check-setting-type.map';
import { SecurityTrainingComplianceChecksCoreService } from 'app/security-training/services/security-training-compliance-checks-core.service';
import { Setting } from 'app/settings/entities/setting.entity';
import { SettingsCoreService } from 'app/settings/services/settings-core.service';
import { AutoResetConfig } from 'app/settings/types/auto-reset-config.type';
import { UserDocument } from 'app/users/entities/user-document.entity';
import { User } from 'app/users/entities/user.entity';
import { ComplianceCheck } from 'app/users/personnel/entities/compliance-check.entity';
import { Personnel } from 'app/users/personnel/entities/personnel.entity';
import { ComplianceCheckRepository } from 'app/users/personnel/repositories/compliance-check.repository';
import { PersonnelTicketRepository } from 'app/users/personnel/repositories/personnel-ticket.repository';
import { PersonnelRepository } from 'app/users/personnel/repositories/personnel.repository';
import { PersonnelCoreService } from 'app/users/personnel/services/personnel-core.service';
import { FailComplianceCheckOptions } from 'app/users/personnel/types/fail-compliance-check-options.type';
import { UserCompletionDate } from 'app/users/personnel/types/user-completion-date.type';
import { UserPolicyVersionRepository } from 'app/users/policies/repositories/user-policy-version.repository';
import { UserDocumentRepository } from 'app/users/repositories/user-document.repository';
import { UserIdentityRepository } from 'app/users/repositories/user-identity-repository';
import { Account } from 'auth/entities/account.entity';
import {
    mapComplianceCheckTypeClientType,
    mapComplianceCheckTypeTrainingCampaignType,
} from 'auth/helpers/provider-type.helper';
import { PolloAdapter } from 'commons/adapters/pollo.adapter';
import { BackgroundCheckStatus } from 'commons/enums/background-check-status.enum';
import { PersonnelTicketType } from 'commons/enums/personnel/personnel-ticket-type.enum';
import { AutoResetType } from 'commons/enums/security-training/auto-reset-type.enum';
import { UserDocumentType } from 'commons/enums/users/user-document-type.enum';
import {
    areUserDocumentsCompliant,
    complianceCheckFrequency,
} from 'commons/helpers/compliance-check.helper';
import { hasExpired } from 'commons/helpers/date.helper';
import { filterAgentDevices } from 'commons/helpers/device.helper';
import { getNumericEnumValues } from 'commons/helpers/enum.helper';
import { allStrictFormerStatuses, filterActiveDevices } from 'commons/helpers/personnel.helper';
import { AppService } from 'commons/services/app.service';
import { FeatureFlagService } from 'feature-flags/feature-flag.service';
import { isEmpty, isNil } from 'lodash';
import moment from 'moment';
import { PolloMessage } from 'pollo-logger/classes/pollo.message';
import { tenantWrapper } from 'tenancy/contexts/tenant-wrapper';
import { In } from 'typeorm';

@Injectable()
export class ComplianceCheckService extends AppService {
    constructor(
        private readonly companiesCoreService: CompaniesCoreService,
        private readonly personnelCoreService: PersonnelCoreService,
        private readonly featureFlagService: FeatureFlagService,
        private readonly securityTrainingComplianceChecksCoreService: SecurityTrainingComplianceChecksCoreService,
        private readonly settingsCoreService: SettingsCoreService,
    ) {
        super();
    }

    /**
     * @deprecated Use ComplianceChecksCoreService.getComplianceCheck
     *
     * @param {User} user
     * @param {ComplianceCheckType} type
     * @returns {Promise<ComplianceCheck>}
     */
    getComplianceCheck(user: User, type: ComplianceCheckType): Promise<ComplianceCheck> {
        return this.complianceCheckRepository.getComplianceCheckByType(user, type);
    }

    /**
     * @deprecated Use ComplianceChecksOrchestrationService.passComplianceCheck
     *
     * @param {User} user
     * @param {ComplianceCheckType} complianceCheckType
     * @param {Date} completionDate
     * @returns {Promise<void>}
     */
    async passComplianceCheck(
        user: User,
        complianceCheckType: ComplianceCheckType,
        completionDate?: Date,
        forceCompletionDateToUpdate?: boolean,
    ): Promise<void> {
        const complianceCheck = await this.complianceCheckRepository.getComplianceCheckByType(
            user,
            complianceCheckType,
        );

        if (isNil(complianceCheck)) {
            this.logger.warn(
                PolloMessage.msg(
                    `Compliance check ${complianceCheckType} not found for user ${user.id}. Cannot be updated`,
                )
                    .setContext(this.constructor.name)
                    .setSubContext('passComplianceCheck'),
            );
            return;
        }

        const { status: complianceCheckStatus } = complianceCheck;

        if (complianceCheckStatus === ComplianceCheckStatus.EXCLUDED) {
            return;
        }

        await this.complianceCheckRepository.passComplianceCheck(
            user,
            complianceCheckType,
            completionDate ? new Date(completionDate) : new Date(),
            forceCompletionDateToUpdate,
        );
        await this.updateFullCompliance(user);
    }

    /**
     * @deprecated Use ComplianceChecksOrchestrationService.passComplianceCheckForUsers
     *
     * @param {User[]} users
     * @param {ComplianceCheckType} complianceCheckType
     * @returns {Promise<void>}
     */
    async passComplianceCheckForUsers(
        account: Account,
        users: User[],
        complianceCheckType: ComplianceCheckType,
    ): Promise<void> {
        const usersFiltered = await this.filterUsersByExcludedComplianceCheck(
            users,
            complianceCheckType,
        );

        await this.complianceCheckRepository.passComplianceCheckForUsers(
            usersFiltered,
            complianceCheckType,
        );

        await this.updateFullComplianceForUsers(account, usersFiltered);
    }

    /**
     * @deprecated Use ComplianceChecksOrchestrationService.failComplianceCheck
     *
     * @param {User} user
     * @param {ComplianceCheckType} complianceCheckType
     * @param {ComplianceCheckType} options
     * @returns {Promise<void>}
     */
    async failComplianceCheck(
        user: User,
        complianceCheckType: ComplianceCheckType,
        options?: FailComplianceCheckOptions,
    ): Promise<void> {
        const { entryId } = user;
        const { account, skipFullCompliance = false } = options ?? {};

        if (complianceCheckType === ComplianceCheckType.ACCEPTED_POLICIES) {
            /**
             * We do not have the account object here and adding it would be a huge
             * regression - do the best we can here and put a log with the entry id.
             */
            if (isNil(account)) {
                this.logger.log(
                    PolloMessage.msg(`failComplianceCheck called for user with entryId: ${entryId}`)
                        .setContext(this.constructor.name)
                        .setSubContext(this.failComplianceCheck.name),
                );
            } else {
                this.logger.log(
                    PolloAdapter.acct(
                        `failComplianceCheck called for user with entryId: ${entryId}`,
                        account,
                    )
                        .setContext(this.constructor.name)
                        .setSubContext(this.failComplianceCheck.name),
                );
            }
        }

        const complianceCheck = await this.complianceCheckRepository.getComplianceCheckByType(
            user,
            complianceCheckType,
        );

        if (isNil(complianceCheck)) {
            this.logger.warn(
                PolloAdapter.acct(
                    `Compliance check ${complianceCheckType} not found for user ${user.id}. Cannot be updated`,
                    account,
                )
                    .setContext(this.constructor.name)
                    .setSubContext('failComplianceCheck'),
            );
            return;
        }

        const { status: complianceCheckStatus } = complianceCheck;

        if (complianceCheckStatus === ComplianceCheckStatus.EXCLUDED) {
            return;
        }

        await this.complianceCheckRepository.failComplianceCheck(user, complianceCheckType);

        if (!skipFullCompliance) {
            await this.complianceCheckRepository.failComplianceCheck(
                user,
                ComplianceCheckType.FULL_COMPLIANCE,
            );
        }
    }

    /**
     * @deprecated Use ComplianceChecksCoreService.failComplianceCheckForUsers
     *
     * @param {User[]} users
     * @param {ComplianceCheckType} complianceCheckType
     * @returns {Promise<void>}
     */
    async failComplianceCheckForUsers(
        users: User[],
        complianceCheckType: ComplianceCheckType,
    ): Promise<void> {
        if (isEmpty(users)) {
            return;
        }

        const usersFiltered = await this.filterUsersByExcludedComplianceCheck(
            users,
            complianceCheckType,
        );

        await this.complianceCheckRepository.failComplianceCheckForUsers(
            usersFiltered,
            complianceCheckType,
        );

        await this.complianceCheckRepository.failComplianceCheckForUsers(
            usersFiltered,
            ComplianceCheckType.FULL_COMPLIANCE,
        );
    }

    /**
     * @deprecated Use ComplianceChecksOrchestrationService.updateFullCompliance
     *
     * @param {User} user
     * @returns {Promise<void>}
     */
    async updateFullCompliance(user: User): Promise<void> {
        const additionalComplianceChecks: ComplianceCheckType[] = [];

        const personnel = await this.personnelRepository.findOneByOrFail({
            user: { id: user.id },
        });

        if (allStrictFormerStatuses.includes(personnel.employmentStatus)) {
            additionalComplianceChecks.push(ComplianceCheckType.OFFBOARDING);
        }
        const isHipaaFrameworkEnabled = await this.companiesCoreService.getIsTrainingEnabled(
            TrainingType.HIPAA_TRAINING,
        );

        if (isHipaaFrameworkEnabled) {
            additionalComplianceChecks.push(ComplianceCheckType.HIPAA_TRAINING);
        }
        const isNistAiFrameworkEnabled = await this.companiesCoreService.getIsTrainingEnabled(
            TrainingType.NIST_AI_TRAINING,
        );
        if (isNistAiFrameworkEnabled) {
            additionalComplianceChecks.push(ComplianceCheckType.NIST_AI_TRAINING);
        }

        // Verify if there is personnel with missing compliance checks
        const [personnelWithMissingComplianceChecks] =
            await this.complianceCheckRepository.getPersonnelMissingChecksByUserIds([user.id]);

        if (!isNil(personnelWithMissingComplianceChecks)) {
            const complianceChecks = getNumericEnumValues(ComplianceCheckType);

            const personnelChecks = personnelWithMissingComplianceChecks.checks
                .split(',')
                .map(id => parseInt(id));
            const missingComplianceChecks = complianceChecks.filter(
                check => !personnelChecks.includes(check),
            );

            // Create missing compliance checks with fail status for personnel
            await this.createComplianceChecks(missingComplianceChecks, personnel);
        }

        const types = await this.isFullyCompliaceSetup(additionalComplianceChecks);

        const isFullyCompliant = await this.complianceCheckRepository.isFullyCompliant(user, types);

        if (isFullyCompliant) {
            await this.complianceCheckRepository.passComplianceCheck(
                user,
                ComplianceCheckType.FULL_COMPLIANCE,
                new Date(),
            );
        } else {
            await this.complianceCheckRepository.failComplianceCheck(
                user,
                ComplianceCheckType.FULL_COMPLIANCE,
            );
        }

        this.logger.log(
            PolloMessage.msg(`Finish compliance checks for userId: ${user.id}`)
                .setSubContext(this.updateFullCompliance.name)
                .setContext(this.constructor.name),
        );
    }

    /**
     * @deprecated Use ComplianceChecksOrchestrationService.updateFullComplianceForUsers
     *
     * @param {User[]} users
     * @param {boolean} addOffboardingCompliance
     * @returns {Promise<void>}
     */
    async updateFullComplianceForUsers(
        account: Account,
        users: User[],
        addOffboardingCompliance?: boolean,
    ): Promise<void> {
        const additionalComplianceChecks: ComplianceCheckType[] = [];
        // if tenant has the HIPAA framework enabled, an extra compliance check must be made.
        const isHipaaFrameworkEnabled = await this.companiesCoreService.getIsTrainingEnabled(
            TrainingType.HIPAA_TRAINING,
        );

        if (isHipaaFrameworkEnabled) {
            additionalComplianceChecks.push(ComplianceCheckType.HIPAA_TRAINING);
        }
        const isNistAiFrameworkEnabled = await this.companiesCoreService.getIsTrainingEnabled(
            TrainingType.NIST_AI_TRAINING,
        );

        if (isNistAiFrameworkEnabled) {
            additionalComplianceChecks.push(ComplianceCheckType.NIST_AI_TRAINING);
        }

        if (addOffboardingCompliance) {
            additionalComplianceChecks.push(ComplianceCheckType.OFFBOARDING);
        }

        // Verify if there is personnel with missing compliance checks
        const userIds = users.map(user => user.id);
        const personnelWithMissingComplianceChecks =
            await this.complianceCheckRepository.getPersonnelMissingChecksByUserIds(userIds);
        if (!isNil(personnelWithMissingComplianceChecks)) {
            const complianceChecks = getNumericEnumValues(ComplianceCheckType);

            // Create missing compliance checks for each personnel found
            for (const personnelWithChecks of personnelWithMissingComplianceChecks) {
                const personnelChecks = personnelWithChecks.checks
                    .split(',')
                    .map(id => parseInt(id));

                const missingComplianceChecks = complianceChecks.filter(
                    check => !personnelChecks.includes(check),
                );

                const personnel = await this.personnelRepository.findOneOrFail({
                    where: { id: personnelWithChecks.personnelId },
                });

                await this.createComplianceChecks(missingComplianceChecks, personnel);
            }
        }

        const types = await this.isFullyCompliaceSetup(additionalComplianceChecks);

        // has the user become fully compliant
        const complianceChecksNotCompliant = await this.complianceCheckRepository.areFullyCompliant(
            users,
            types,
        );

        const usersFullyCompliant = [];
        const usersNotFullyCompliant = [];
        for (const user of users) {
            const isFullyCompliant = isEmpty(
                complianceChecksNotCompliant.filter(cc => cc.personnel.user?.id === user.id),
            );

            if (isFullyCompliant) {
                usersFullyCompliant.push(user);
            } else {
                usersNotFullyCompliant.push(user);
            }
        }

        // update to be fully compliant
        await this.complianceCheckRepository.passComplianceCheckForUsers(
            usersFullyCompliant,
            ComplianceCheckType.FULL_COMPLIANCE,
        );

        // update to be not fully compliant
        await this.complianceCheckRepository.failComplianceCheckForUsers(
            usersNotFullyCompliant,
            ComplianceCheckType.FULL_COMPLIANCE,
        );
    }

    /**
     *
     * @param {ComplianceCheck} check
     * @returns {Promise<ComplianceCheck>}
     */
    saveComplianceCheck(check: ComplianceCheck): Promise<ComplianceCheck> {
        return this.complianceCheckRepository.save(check);
    }

    /**
     * @deprecated Use ComplianceChecksCoreService.saveComplianceChecks
     *
     * @param {ComplianceCheck[]} checks
     * @return {Promise<ComplianceCheck[]>}
     */
    saveComplianceChecks(checks: ComplianceCheck[]): Promise<ComplianceCheck[]> {
        return this.complianceCheckRepository.save(checks);
    }

    /**
     * @deprecated Use ComplianceChecksCoreService.createComplianceChecks
     *
     * @param {number[]} neededComplianceTypes
     * @param {Personnel} personnel
     * @return {Promise<ComplianceCheck[]>}
     */
    createComplianceChecks(
        neededComplianceTypes: number[],
        personnel: Personnel,
    ): Promise<ComplianceCheck[]> {
        return this.saveComplianceChecks(
            neededComplianceTypes.map(complianceType => {
                const complianceCheck = new ComplianceCheck();
                complianceCheck.personnel = personnel;
                complianceCheck.type = complianceType;
                complianceCheck.checkFrequency = complianceCheckFrequency(complianceType);
                complianceCheck.status = ComplianceCheckStatus.FAIL;
                complianceCheck.expiresAt = new Date();
                complianceCheck.lastCheckedAt = new Date();
                return complianceCheck;
            }),
        );
    }

    /**
     * @deprecated Use ComplianceChecksCoreService.computePoliciesAcceptedComplianceCheck
     *
     * @param {Account} account
     * @param {Personnel} personnel
     * @returns {Promise<void>}
     */
    async computePoliciesAcceptedComplianceCheck(
        account: Account,
        personnel: Personnel[],
    ): Promise<void> {
        if (isEmpty(personnel)) {
            return;
        }

        const users = personnel.map(p => p.user).filter(user => !isNil(user));

        await this.computePoliciesAcceptedComplianceCheckViaUsers(users, account);
    }

    /**
     * @deprecated Use ComplianceChecksCoreService.computePoliciesAcceptedComplianceCheckViaUsers
     *
     * @param users
     * @param account
     */
    async computePoliciesAcceptedComplianceCheckViaUsers(
        users: User[],
        account: Account,
    ): Promise<void> {
        const usersToFail: User[] = [];
        const usersToPass: UserCompletionDate[] = [];

        for (const user of users) {
            const result = await this.userPolicyVersionRepository.areAllCurrentPoliciesAccepted(
                user,
                account,
            );

            if (result.allPoliciesAccepted) {
                usersToPass.push({
                    user,
                    completionDate: result.latestAcceptedPolicyDate,
                });
            } else {
                usersToFail.push(user);
            }
        }

        if (!isEmpty(usersToFail)) {
            await this.complianceCheckRepository.failComplianceCheckForUsers(
                usersToFail,
                ComplianceCheckType.ACCEPTED_POLICIES,
            );
        }

        if (!isEmpty(usersToPass)) {
            await this.complianceCheckRepository.passComplianceCheckForUsersWithCompletionDate(
                usersToPass,
                ComplianceCheckType.ACCEPTED_POLICIES,
            );
        }
    }

    /**
     * @deprecated Use ComplianceChecksCoreService.computeIdentityMFAComplianceCheck
     *
     * @param {Personnel} personnel
     * @returns {Promise<void>}
     */
    async computeIdentityMFAComplianceCheck(
        personnel: Personnel[],
        account: Account,
    ): Promise<void> {
        if (isEmpty(personnel)) {
            return;
        }

        const users = personnel.map(p => p.user);
        const userIds = users.map(u => u.id);

        const userIdentities =
            await this.userIdentityRepository.getUserIdentityWithMFAByUserIds(userIds);

        const userIdentitiesWithMfa = userIdentities.map(userIdentity => userIdentity.user.id);

        const usersDocuments = await this.userDocumentRepository.getAllDocumentsOfTypeManyUsers(
            UserDocumentType.MFA_EVIDENCE,
            userIds,
        );

        const usersToFail: User[] = [];
        const usersToPass: User[] = [];

        for (const user of users) {
            const { id: userId } = user;

            const userDocuments = usersDocuments.filter(ud => ud.user.id === user.id);

            const isSomeDocumentValid = userDocuments.some(
                ({ renewalDate }) => !hasExpired(new Date(renewalDate)),
            );

            if (userIdentitiesWithMfa.includes(userId) || isSomeDocumentValid) {
                usersToPass.push(user);
            } else {
                usersToFail.push(user);
            }
        }

        this.log(
            `Users to pass/fail identity MFA on ${this.computeIdentityMFAComplianceCheck.name}`,
            account,
            { usersToFail, usersToPass },
        );

        if (!isEmpty(usersToFail)) {
            await this.complianceCheckRepository.failComplianceCheckForUsers(
                usersToFail,
                ComplianceCheckType.IDENTITY_MFA,
            );
        }

        if (!isEmpty(usersToPass)) {
            await this.complianceCheckRepository.passComplianceCheckForUsers(
                usersToPass,
                ComplianceCheckType.IDENTITY_MFA,
            );
        }
    }

    /**
     * @deprecated Use ComplianceChecks.computeBackgroundCheckComplianceCheck
     *
     * @param {Personnel} personnel
     * @returns {Promise<void>}
     */
    async computeBackgroundCheckComplianceCheck(personnel: Personnel[]): Promise<void> {
        if (isEmpty(personnel)) {
            return;
        }

        const users = personnel.map(p => p.user);
        const userIds = users.map(u => u.id);

        const backgroundChecks = await this.backgroundCheckRepository.findBy({
            status: In([BackgroundCheckStatus.OK, BackgroundCheckStatus.ISSUE]),
            user: {
                id: In(userIds),
            },
        });

        const userIdsWithBackgroundChecks = backgroundChecks.map(
            backgroundCheck => backgroundCheck.user.id,
        );

        const usersToFail: User[] = [];
        const usersToPass: User[] = [];

        for (const user of users) {
            const { id: userId } = user;

            if (userIdsWithBackgroundChecks.includes(userId)) {
                usersToPass.push(user);
            } else {
                usersToFail.push(user);
            }
        }

        if (!isEmpty(usersToFail)) {
            await this.complianceCheckRepository.failComplianceCheckForUsers(
                usersToFail,
                ComplianceCheckType.BG_CHECK,
            );
        }

        if (!isEmpty(usersToPass)) {
            await this.complianceCheckRepository.passComplianceCheckForUsers(
                usersToPass,
                ComplianceCheckType.BG_CHECK,
            );
        }
    }

    /**
     * @deprecated Use ComplianceChecksOrchestrationService.hasPersonnelAllCompletedAssignments
     *
     * @param account
     * @param personnel
     * @param company
     * @param complianceCheckType
     * @returns
     */
    async hasPersonnelAllCompletedAssignments(
        account: Account,
        personnel: Personnel,
        company: Company,
        complianceCheckType: ComplianceCheckType,
    ): Promise<boolean> {
        let isFullyCompliance: boolean;
        try {
            const trainingCampaignType =
                mapComplianceCheckTypeTrainingCampaignType(complianceCheckType);

            const clientType = mapComplianceCheckTypeClientType(complianceCheckType, company);

            isFullyCompliance = await tenantWrapper(account, () => {
                return this.securityTrainingComplianceChecksCoreService.hasPersonnelAllCompletedAssignments(
                    personnel.id,
                    trainingCampaignType,
                    clientType,
                );
            });
        } catch {
            isFullyCompliance = false;
        }

        return isFullyCompliance;
    }

    /**
     * @deprecated Use ComplianceChecksOrchestrationService.computeTrainingComplianceCheck
     *
     * @param {Personnel} personnel
     * @param {UserDocumentType} userDocumentType
     * @param {ComplianceCheckType} complianceCheckType
     * @returns {Promise<void>}
     */
    async computeTrainingComplianceCheck(
        account: Account,
        personnel: Personnel[],
        userDocumentType: UserDocumentType,
        complianceCheckType: ComplianceCheckType,
        isCLIRequest: boolean,
    ): Promise<void> {
        if (isEmpty(personnel)) {
            return;
        }
        const company = await this.companiesCoreService.getCompanyByAccountId(account.id);

        const users = personnel.map(p => p.user);
        const userIds = users.map(u => u.id);

        const usersDocuments = await this.userDocumentRepository.getAllDocumentsOfTypeManyUsers(
            userDocumentType,
            userIds,
        );

        /**
         * get the auto reset settings based on the training type
         */
        const settingType = ComplianceCheckSettingTypeMap.get(complianceCheckType);

        let settings: Setting;
        try {
            settings = await tenantWrapper(account, () => {
                return this.settingsCoreService.getSettingBySettingType(settingType);
            });
        } catch {
            settings = null;
        }

        const autoResetConfig = settings?.configObject as AutoResetConfig;

        const { usersToFail, usersToPass } = await this.evaluateTrainingCompliance(
            users,
            personnel,
            usersDocuments,
            autoResetConfig,
            account,
            company,
            complianceCheckType,
            isCLIRequest,
        );

        if (!isEmpty(usersToFail)) {
            await this.complianceCheckRepository.failComplianceCheckForUsers(
                usersToFail,
                complianceCheckType,
            );
        }

        if (!isEmpty(usersToPass)) {
            await this.complianceCheckRepository.passComplianceCheckForUsers(
                usersToPass,
                complianceCheckType,
            );
        }
    }

    private async evaluateTrainingCompliance(
        users: (User | null)[] | null,
        personnel: Personnel[],
        usersDocuments: UserDocument[],
        autoResetConfig: AutoResetConfig,
        account: Account,
        company: Company,
        complianceCheckType: ComplianceCheckType,
        isCLIRequest: boolean,
    ): Promise<{
        usersToFail: User[];
        usersToPass: User[];
    }> {
        const usersToFail: User[] = [];
        const usersToPass: User[] = [];

        if (isNil(users) || isEmpty(users) || users.every(user => isNil(user))) {
            return { usersToFail, usersToPass };
        }
        const usersNoNull = users.filter(user => !isNil(user));

        for (const user of usersNoNull) {
            const person = personnel.find(p => p?.user?.id === user?.id);

            const userDocuments = usersDocuments.filter(ud => ud.user.id === user.id);

            const shouldResetTrainingForPersonnel =
                isEmpty(autoResetConfig) || isNil(person)
                    ? false
                    : this.shouldResetTrainingForPersonnel(
                          autoResetConfig,
                          person,
                          complianceCheckType,
                      );

            const isTrainingFullyCompliant = isNil(person)
                ? false
                : await this.hasPersonnelAllCompletedAssignments(
                      account,
                      person,
                      company,
                      complianceCheckType,
                  );

            const areDocumentsCompliant = areUserDocumentsCompliant(userDocuments, autoResetConfig);

            if (
                !areDocumentsCompliant ||
                shouldResetTrainingForPersonnel ||
                !isTrainingFullyCompliant ||
                /**
                 * we need to evaluate isDocumentValid only if re compute was requested from CLI.
                 * AP will be ignored.
                 */
                (isCLIRequest && !areDocumentsCompliant)
            ) {
                usersToFail.push(user);

                /**
                 * throw event if user fails because of the reset training
                 */
                if (shouldResetTrainingForPersonnel) {
                    this.personnelCoreService.sendTrainingEvent(
                        account,
                        {} as User,
                        user,
                        complianceCheckType,
                    );
                }
            } else {
                usersToPass.push(user);
            }
        }

        return { usersToFail, usersToPass };
    }

    private shouldResetTrainingForPersonnel(
        autoResetconfig: AutoResetConfig,
        personnel: Personnel,
        complianceCheckType: ComplianceCheckType,
    ) {
        let shouldResetTraining = false;

        switch (Number(autoResetconfig.type)) {
            case AutoResetType.GLOBAL_RESET:
                const { day, month } = autoResetconfig;

                /**
                 * build renewal date
                 */
                const renewalDate = new Date(new Date().getFullYear(), month - 1, day);

                /**
                 * if today is renewal date we reset
                 */
                shouldResetTraining = moment(new Date()).isSame(renewalDate, 'day');

                break;
            case AutoResetType.ROLLING_RESET:
                /**
                 * get the training compliance check that matches
                 */
                const trainingComplianceCheck = personnel.complianceChecks.find(
                    complianceCheck => complianceCheck.type === complianceCheckType,
                );

                const completionDate = new Date(trainingComplianceCheck.completionDate);

                completionDate.setFullYear(completionDate.getFullYear() + 1);

                /**
                 * completion date must be 1 year older and status must be passing
                 */
                shouldResetTraining =
                    new Date() > completionDate &&
                    trainingComplianceCheck.status === ComplianceCheckStatus.PASS;
                break;
            case AutoResetType.NO_AUTOMATION:
                break;
            default:
                break;
        }

        return shouldResetTraining;
    }

    /**
     * @deprecated Use ComplianceChecksCoreService.computeDeviceRelatedComplianceCheck
     *
     * @param {ComplianceCheckType} complianceCheckType
     * @param {Personnel[]} personnel
     * @returns {Promise<void>}
     */
    async computeDeviceRelatedComplianceCheck(
        complianceCheckType: ComplianceCheckType,
        personnel: Personnel[],
    ): Promise<void> {
        if (isEmpty(personnel)) {
            return;
        }

        const usersDevices = personnel.map(pers => {
            const { user } = pers;
            const devices = filterActiveDevices(pers);

            return {
                user,
                devices: filterAgentDevices(complianceCheckType, devices),
            };
        });

        const usersToFail: User[] = [];
        const usersToPass: User[] = [];

        for (const userDevice of usersDevices) {
            const { user, devices } = userDevice;
            const deviceComplianceChecks = devices
                .filter(d => d.complianceChecks.some(cc => cc.type === complianceCheckType))
                .map(d => d.complianceChecks.find(cc => cc.type === complianceCheckType));
            /*
                If there is no device, or all devices are missing the compliance check in the DB means there is no evidence
                therefore is a failure
            */
            if (isEmpty(deviceComplianceChecks)) {
                usersToFail.push(user);
            } else {
                const areAllDevicesCompliant = deviceComplianceChecks.every(
                    dcc => dcc.status !== ComplianceCheckStatus.FAIL,
                );
                if (areAllDevicesCompliant) {
                    usersToPass.push(user);
                } else {
                    usersToFail.push(user);
                }
            }
        }

        if (!isEmpty(usersToFail)) {
            await this.complianceCheckRepository.failComplianceCheckForUsers(
                usersToFail,
                complianceCheckType,
            );
        }

        if (!isEmpty(usersToPass)) {
            await this.complianceCheckRepository.passComplianceCheckForUsers(
                usersToPass,
                complianceCheckType,
            );
        }
    }

    /**
     * @deprecated Use ComplianceChecksCoreService.computeOffboardingComplianceCheck
     *
     * @param {Personnel} personnel
     * @returns {Promise<void>}
     */
    async computeOffboardingComplianceCheck(personnel: Personnel[]): Promise<void> {
        if (isEmpty(personnel)) {
            return;
        }

        const users = personnel.map(p => p.user);
        const userIds = users.map(u => u.id);
        const personnelIds = personnel.map(p => p.id);

        const personnelTickets =
            await this.personnelTicketRepository.getPersonnelTicketsDoneForPersonnelByType(
                personnelIds,
                PersonnelTicketType.OFFBOARDING,
            );

        const userDocuments = await this.userDocumentRepository.getAllDocumentsOfTypeManyUsers(
            UserDocumentType.OFFBOARDING_EVIDENCE,
            userIds,
        );

        const usersToFail: User[] = [];
        const usersToPass: User[] = [];

        for (const pers of personnel) {
            const { id: personnelId, user, complianceChecks = [] } = pers;
            const { id: userId } = user;

            const userTickets = personnelTickets.filter(t => t.personnel.id === personnelId);

            const userOffboardingEvidenceDocuments = userDocuments.find(
                ud => ud.user.id === userId,
            );

            const { status: offboardingComplianceCheckStatus } =
                complianceChecks.find(cc => cc.type === ComplianceCheckType.OFFBOARDING) ?? {};

            const hasEvidence = !isEmpty(userTickets) || !isEmpty(userOffboardingEvidenceDocuments);

            if (user?.email.endsWith('observeinc.com') || user?.email.endsWith('drata.com')) {
                this.logger.log(
                    PolloMessage.msg('Showing offboarding evidence for user').setIdentifier({
                        email: user.email,
                        tickets: userTickets.map(({ ticket }) => ({
                            id: ticket.externalTicketId,
                            isDone: ticket.isDone,
                            lastSyncedAt: ticket.lastSyncedAt,
                        })),
                        document: userOffboardingEvidenceDocuments?.name,
                        oldComplianceStatus: offboardingComplianceCheckStatus,
                        shouldPassCompliance: hasEvidence,
                    }),
                );
            }

            if (hasEvidence && offboardingComplianceCheckStatus !== ComplianceCheckStatus.PASS) {
                usersToPass.push(user);
            } else if (
                !hasEvidence &&
                offboardingComplianceCheckStatus !== ComplianceCheckStatus.FAIL
            ) {
                usersToFail.push(user);
            }
        }

        if (!isEmpty(usersToFail)) {
            await this.complianceCheckRepository.failComplianceCheckForUsers(
                usersToFail,
                ComplianceCheckType.OFFBOARDING,
            );
        }

        if (!isEmpty(usersToPass)) {
            await this.complianceCheckRepository.passComplianceCheckForUsers(
                usersToPass,
                ComplianceCheckType.OFFBOARDING,
            );
        }
    }

    /**
     * @deprecated Use ComplianceChecksCoreService.filterUsersByExcludedComplianceCheck
     *
     * @param {User[]} users
     * @param {ComplianceCheckType} complianceCheckType
     * @returns {Promise<User[]>}
     */
    private async filterUsersByExcludedComplianceCheck(
        users: User[],
        complianceCheckType: ComplianceCheckType,
    ): Promise<User[]> {
        const complianceChecks = await this.complianceCheckRepository.getComplianceChecksByType(
            users,
            complianceCheckType,
        );
        const complianceChecksWithoutExcludedStatus = complianceChecks.filter(
            complianceCheck => complianceCheck.status !== ComplianceCheckStatus.EXCLUDED,
        );
        return complianceChecksWithoutExcludedStatus.map(
            complianceCheck => complianceCheck.personnel.user,
        );
    }

    /**
     * @deprecated Use ComplianceChecksCoreService.isFullyComplianceSetup
     *
     * @param additionalComplianceChecks
     * @returns
     */
    public async isFullyCompliaceSetup(additionalComplianceChecks: ComplianceCheckType[] = []) {
        // set the types that would make someone FULLY compliant
        const types = [
            ComplianceCheckType.ACCEPTED_POLICIES,
            ComplianceCheckType.IDENTITY_MFA,
            ComplianceCheckType.BG_CHECK,
            ComplianceCheckType.PASSWORD_MANAGER,
            ComplianceCheckType.AUTO_UPDATES,
            // ComplianceCheckType.LOCATION_SERVICES,
            ComplianceCheckType.HDD_ENCRYPTION,
            ComplianceCheckType.ANTIVIRUS,
            ComplianceCheckType.LOCK_SCREEN,
            ComplianceCheckType.SECURITY_TRAINING,
        ];

        if (!isEmpty(additionalComplianceChecks)) {
            types.push(...additionalComplianceChecks);
        }

        return types;
    }

    private get complianceCheckRepository(): ComplianceCheckRepository {
        return this.getCustomTenantRepository(ComplianceCheckRepository);
    }

    private get personnelRepository(): PersonnelRepository {
        return this.getCustomTenantRepository(PersonnelRepository);
    }

    private get userIdentityRepository(): UserIdentityRepository {
        return this.getCustomTenantRepository(UserIdentityRepository);
    }

    private get backgroundCheckRepository(): BackgroundCheckRepository {
        return this.getCustomTenantRepository(BackgroundCheckRepository);
    }

    private get userDocumentRepository(): UserDocumentRepository {
        return this.getCustomTenantRepository(UserDocumentRepository);
    }

    private get personnelTicketRepository(): PersonnelTicketRepository {
        return this.getCustomTenantRepository(PersonnelTicketRepository);
    }

    private get userPolicyVersionRepository(): UserPolicyVersionRepository {
        return this.getCustomTenantRepository(UserPolicyVersionRepository);
    }
}

import { NotFoundException } from '@nestjs/common';
import { TestingModule } from '@nestjs/testing';
import { ComplianceCheckExclusionsCoreService } from 'app/compliance-check-exclusions/services/compliance-check-exclusions-core.service';
import { CustomFieldLocation } from 'app/custom-fields/entities/custom-field-location.entity';
import { CustomFieldsLocationsRepository } from 'app/custom-fields/repositories/custom-fields-locations.repository';
import { User } from 'app/users/entities/user.entity';
import { Personnel } from 'app/users/personnel/entities/personnel.entity';
import { PersonnelExpand } from 'app/users/personnel/enums/personnel-expand.enum';
import { PersonnelRepository } from 'app/users/personnel/repositories/personnel.repository';
import { ComplianceChecksCoreService } from 'app/users/personnel/services/compliance-checks-core.service';
import { PersonnelCoreService } from 'app/users/personnel/services/personnel-core.service';
import { PersonnelRequestType } from 'app/users/personnel/types/personnel-request.type';
import { PersonnelWithCustomFields } from 'app/users/personnel/types/personnel-with-custom-fields.type';
import { createAppTestingModule } from 'commons/helpers/app-testing-module.helper';
import { MockFactory } from 'commons/mocks/factories/mock.factory';
import { defaultRepositoryMock } from 'commons/mocks/repositories/default-repository.mock';
import { MockType } from 'commons/mocks/types/mock.type';
import * as databaseHelpers from 'database/typeorm/typeorm.extensions.helper';
import { FeatureFlagService } from 'feature-flags/feature-flag.service';
import { TenancyContext } from 'tenancy/contexts/tenancy.context';

describe('PersonnelCoreService', () => {
    let personnelCoreService: PersonnelCoreService;
    let personnelRepository: MockType<PersonnelRepository>;
    let customFieldsLocationsRepository: MockType<CustomFieldsLocationsRepository>;

    const personnelRepositoryMock = {
        ...defaultRepositoryMock,
        ...MockFactory.getMock(PersonnelRepository),
    };

    const customFieldsLocationsRepositoryMock = {
        ...defaultRepositoryMock,
        ...MockFactory.getMock(CustomFieldsLocationsRepository),
    };

    const connectionMock = {
        getRepository: jest.fn(() => defaultRepositoryMock),
        getCustomRepository: jest.fn((CustomRepository: any) => {
            switch (CustomRepository) {
                case PersonnelRepository:
                    return personnelRepositoryMock;
                case CustomFieldsLocationsRepository:
                    return customFieldsLocationsRepositoryMock;
                default:
                    return defaultRepositoryMock;
            }
        }),
    };

    beforeEach(async () => {
        jest.spyOn(databaseHelpers, 'getCustomRepository').mockImplementation(
            connectionMock.getCustomRepository,
        );

        const module: TestingModule = await createAppTestingModule({
            providers: [
                PersonnelCoreService,
                {
                    provide: TenancyContext,
                    useValue: connectionMock,
                },
                {
                    provide: FeatureFlagService,
                    useValue: MockFactory.getMock(FeatureFlagService),
                },
                {
                    provide: ComplianceChecksCoreService,
                    useValue: {
                        getComplianceChecksWithExclusions: jest.fn(),
                    },
                },
                {
                    provide: ComplianceCheckExclusionsCoreService,
                    useValue: {
                        getCurrentExclusionsForPersonnel: jest.fn(),
                    },
                },
            ],
        }).compile();

        personnelCoreService = await module.resolve<PersonnelCoreService>(PersonnelCoreService);
        personnelRepository = connectionMock.getCustomRepository(PersonnelRepository);
        customFieldsLocationsRepository = connectionMock.getCustomRepository(
            CustomFieldsLocationsRepository,
        );
    });

    describe('getPersonnelByEmailV2', () => {
        const testEmail = '<EMAIL>';
        let mockPersonnel: Personnel;
        let mockUser: User;

        beforeEach(() => {
            // Reset all mocks before each test
            jest.clearAllMocks();

            mockUser = new User();
            mockUser.id = 1;
            mockUser.email = testEmail;

            mockPersonnel = new Personnel();
            mockPersonnel.id = 1;
            mockPersonnel.user = mockUser;
        });

        it('should return personnel without custom fields when expand does not include customFields', async () => {
            const requestType: PersonnelRequestType = {
                expand: [PersonnelExpand.user],
            };

            personnelRepository.findByEmailOrFail?.mockResolvedValue(mockPersonnel);

            const result: PersonnelWithCustomFields =
                await personnelCoreService.getPersonnelByEmailV2(testEmail, requestType);

            expect(result).toEqual({
                personnel: mockPersonnel,
                customFields: undefined,
            });
            expect(personnelRepository.findByEmailOrFail).toHaveBeenCalledWith(testEmail, [
                PersonnelExpand.user,
            ]);
            expect(
                customFieldsLocationsRepository.getCustomFieldPersonnelSubmissions,
            ).not.toHaveBeenCalled();
        });

        it('should return personnel with custom fields when expand includes customFields', async () => {
            const requestType: PersonnelRequestType = {
                expand: [PersonnelExpand.customFields, PersonnelExpand.user],
            };

            const mockCustomFields: CustomFieldLocation[] = [
                new CustomFieldLocation(),
                new CustomFieldLocation(),
            ];

            personnelRepository.findByEmailOrFail?.mockResolvedValue(mockPersonnel);
            customFieldsLocationsRepository.getCustomFieldPersonnelSubmissions?.mockResolvedValue(
                mockCustomFields,
            );

            const result: PersonnelWithCustomFields =
                await personnelCoreService.getPersonnelByEmailV2(testEmail, requestType);

            expect(result).toEqual({
                personnel: mockPersonnel,
                customFields: mockCustomFields,
            });
            expect(personnelRepository.findByEmailOrFail).toHaveBeenCalledWith(testEmail, [
                PersonnelExpand.customFields,
                PersonnelExpand.user,
            ]);
            expect(
                customFieldsLocationsRepository.getCustomFieldPersonnelSubmissions,
            ).toHaveBeenCalledWith(mockPersonnel.id);
        });

        it('should return personnel without custom fields when expand is undefined', async () => {
            const requestType: PersonnelRequestType = {};

            personnelRepository.findByEmailOrFail?.mockResolvedValue(mockPersonnel);

            const result: PersonnelWithCustomFields =
                await personnelCoreService.getPersonnelByEmailV2(testEmail, requestType);

            expect(result).toEqual({
                personnel: mockPersonnel,
                customFields: undefined,
            });
            expect(personnelRepository.findByEmailOrFail).toHaveBeenCalledWith(testEmail, []);
            expect(
                customFieldsLocationsRepository.getCustomFieldPersonnelSubmissions,
            ).not.toHaveBeenCalled();
        });

        it('should return personnel without custom fields when expand is empty array', async () => {
            const requestType: PersonnelRequestType = {
                expand: [],
            };

            personnelRepository.findByEmailOrFail?.mockResolvedValue(mockPersonnel);

            const result: PersonnelWithCustomFields =
                await personnelCoreService.getPersonnelByEmailV2(testEmail, requestType);

            expect(result).toEqual({
                personnel: mockPersonnel,
                customFields: undefined,
            });
            expect(personnelRepository.findByEmailOrFail).toHaveBeenCalledWith(testEmail, []);
            expect(
                customFieldsLocationsRepository.getCustomFieldPersonnelSubmissions,
            ).not.toHaveBeenCalled();
        });

        it('should return personnel with empty custom fields array when custom fields repository returns empty array', async () => {
            const requestType: PersonnelRequestType = {
                expand: [PersonnelExpand.customFields],
            };

            const emptyCustomFields: CustomFieldLocation[] = [];

            personnelRepository.findByEmailOrFail?.mockResolvedValue(mockPersonnel);
            customFieldsLocationsRepository.getCustomFieldPersonnelSubmissions?.mockResolvedValue(
                emptyCustomFields,
            );

            const result: PersonnelWithCustomFields =
                await personnelCoreService.getPersonnelByEmailV2(testEmail, requestType);

            expect(result).toEqual({
                personnel: mockPersonnel,
                customFields: emptyCustomFields,
            });
            expect(personnelRepository.findByEmailOrFail).toHaveBeenCalledWith(testEmail, [
                PersonnelExpand.customFields,
            ]);
            expect(
                customFieldsLocationsRepository.getCustomFieldPersonnelSubmissions,
            ).toHaveBeenCalledWith(mockPersonnel.id);
        });

        it('should throw NotFoundException when personnel is not found', async () => {
            const requestType: PersonnelRequestType = {
                expand: [PersonnelExpand.user],
            };

            const notFoundError = new NotFoundException('Personnel not found');
            personnelRepository.findByEmailOrFail?.mockRejectedValue(notFoundError);

            await expect(
                personnelCoreService.getPersonnelByEmailV2(testEmail, requestType),
            ).rejects.toThrow(NotFoundException);

            expect(personnelRepository.findByEmailOrFail).toHaveBeenCalledWith(testEmail, [
                PersonnelExpand.user,
            ]);
            expect(
                customFieldsLocationsRepository.getCustomFieldPersonnelSubmissions,
            ).not.toHaveBeenCalled();
        });

        it('should throw error when custom fields repository fails', async () => {
            const requestType: PersonnelRequestType = {
                expand: [PersonnelExpand.customFields],
            };

            const customFieldsError = new Error('Custom fields repository error');

            personnelRepository.findByEmailOrFail?.mockResolvedValue(mockPersonnel);
            customFieldsLocationsRepository.getCustomFieldPersonnelSubmissions?.mockRejectedValue(
                customFieldsError,
            );

            await expect(
                personnelCoreService.getPersonnelByEmailV2(testEmail, requestType),
            ).rejects.toThrow('Custom fields repository error');

            expect(personnelRepository.findByEmailOrFail).toHaveBeenCalledWith(testEmail, [
                PersonnelExpand.customFields,
            ]);
            expect(
                customFieldsLocationsRepository.getCustomFieldPersonnelSubmissions,
            ).toHaveBeenCalledWith(mockPersonnel.id);
        });

        it('should handle multiple expand options correctly', async () => {
            const requestType: PersonnelRequestType = {
                expand: [
                    PersonnelExpand.customFields,
                    PersonnelExpand.user,
                    PersonnelExpand.complianceChecks,
                    PersonnelExpand.reasonProvider,
                ],
            };

            const mockCustomFields: CustomFieldLocation[] = [new CustomFieldLocation()];

            personnelRepository.findByEmailOrFail?.mockResolvedValue(mockPersonnel);
            customFieldsLocationsRepository.getCustomFieldPersonnelSubmissions?.mockResolvedValue(
                mockCustomFields,
            );

            const result: PersonnelWithCustomFields =
                await personnelCoreService.getPersonnelByEmailV2(testEmail, requestType);

            expect(result).toEqual({
                personnel: mockPersonnel,
                customFields: mockCustomFields,
            });
            expect(personnelRepository.findByEmailOrFail).toHaveBeenCalledWith(testEmail, [
                PersonnelExpand.customFields,
                PersonnelExpand.user,
                PersonnelExpand.complianceChecks,
                PersonnelExpand.reasonProvider,
            ]);
            expect(
                customFieldsLocationsRepository.getCustomFieldPersonnelSubmissions,
            ).toHaveBeenCalledWith(mockPersonnel.id);
        });
    });

    describe('getPersonnelByIdV2', () => {
        const testPersonnelId = 123;
        let mockPersonnel: Personnel;
        let mockUser: User;

        beforeEach(() => {
            // Reset all mocks before each test
            jest.clearAllMocks();

            mockUser = new User();
            mockUser.id = 1;
            mockUser.email = '<EMAIL>';

            mockPersonnel = new Personnel();
            mockPersonnel.id = testPersonnelId;
            mockPersonnel.user = mockUser;
        });

        it('should return personnel without custom fields when expand does not include customFields', async () => {
            const requestType: PersonnelRequestType = {
                expand: [PersonnelExpand.user],
            };

            personnelRepository.findByIdOrFail?.mockResolvedValue(mockPersonnel);

            const result: PersonnelWithCustomFields = await personnelCoreService.getPersonnelByIdV2(
                testPersonnelId,
                requestType,
            );

            expect(result).toEqual({
                personnel: mockPersonnel,
                customFields: undefined,
            });
            expect(personnelRepository.findByIdOrFail).toHaveBeenCalledWith(testPersonnelId, [
                PersonnelExpand.user,
            ]);
            expect(
                customFieldsLocationsRepository.getCustomFieldPersonnelSubmissions,
            ).not.toHaveBeenCalled();
        });

        it('should return personnel with custom fields when expand includes customFields', async () => {
            const requestType: PersonnelRequestType = {
                expand: [PersonnelExpand.customFields, PersonnelExpand.complianceChecks],
            };

            const mockCustomFields: CustomFieldLocation[] = [
                new CustomFieldLocation(),
                new CustomFieldLocation(),
            ];

            personnelRepository.findByIdOrFail?.mockResolvedValue(mockPersonnel);
            customFieldsLocationsRepository.getCustomFieldPersonnelSubmissions?.mockResolvedValue(
                mockCustomFields,
            );

            const result: PersonnelWithCustomFields = await personnelCoreService.getPersonnelByIdV2(
                testPersonnelId,
                requestType,
            );

            expect(result).toEqual({
                personnel: mockPersonnel,
                customFields: mockCustomFields,
            });
            expect(personnelRepository.findByIdOrFail).toHaveBeenCalledWith(testPersonnelId, [
                PersonnelExpand.customFields,
                PersonnelExpand.complianceChecks,
            ]);
            expect(
                customFieldsLocationsRepository.getCustomFieldPersonnelSubmissions,
            ).toHaveBeenCalledWith(testPersonnelId);
        });

        it('should return personnel without custom fields when expand is undefined', async () => {
            const requestType: PersonnelRequestType = {};

            personnelRepository.findByIdOrFail?.mockResolvedValue(mockPersonnel);

            const result: PersonnelWithCustomFields = await personnelCoreService.getPersonnelByIdV2(
                testPersonnelId,
                requestType,
            );

            expect(result).toEqual({
                personnel: mockPersonnel,
                customFields: undefined,
            });
            expect(personnelRepository.findByIdOrFail).toHaveBeenCalledWith(testPersonnelId, []);
            expect(
                customFieldsLocationsRepository.getCustomFieldPersonnelSubmissions,
            ).not.toHaveBeenCalled();
        });

        it('should return personnel without custom fields when expand is empty array', async () => {
            const requestType: PersonnelRequestType = {
                expand: [],
            };

            personnelRepository.findByIdOrFail?.mockResolvedValue(mockPersonnel);

            const result: PersonnelWithCustomFields = await personnelCoreService.getPersonnelByIdV2(
                testPersonnelId,
                requestType,
            );

            expect(result).toEqual({
                personnel: mockPersonnel,
                customFields: undefined,
            });
            expect(personnelRepository.findByIdOrFail).toHaveBeenCalledWith(testPersonnelId, []);
            expect(
                customFieldsLocationsRepository.getCustomFieldPersonnelSubmissions,
            ).not.toHaveBeenCalled();
        });

        it('should return personnel with empty custom fields array when custom fields repository returns empty array', async () => {
            const requestType: PersonnelRequestType = {
                expand: [PersonnelExpand.customFields],
            };

            const emptyCustomFields: CustomFieldLocation[] = [];

            personnelRepository.findByIdOrFail?.mockResolvedValue(mockPersonnel);
            customFieldsLocationsRepository.getCustomFieldPersonnelSubmissions?.mockResolvedValue(
                emptyCustomFields,
            );

            const result: PersonnelWithCustomFields = await personnelCoreService.getPersonnelByIdV2(
                testPersonnelId,
                requestType,
            );

            expect(result).toEqual({
                personnel: mockPersonnel,
                customFields: emptyCustomFields,
            });
            expect(personnelRepository.findByIdOrFail).toHaveBeenCalledWith(testPersonnelId, [
                PersonnelExpand.customFields,
            ]);
            expect(
                customFieldsLocationsRepository.getCustomFieldPersonnelSubmissions,
            ).toHaveBeenCalledWith(testPersonnelId);
        });

        it('should throw NotFoundException when personnel is not found', async () => {
            const requestType: PersonnelRequestType = {
                expand: [PersonnelExpand.user],
            };

            const notFoundError = new NotFoundException('Personnel not found');
            personnelRepository.findByIdOrFail?.mockRejectedValue(notFoundError);

            await expect(
                personnelCoreService.getPersonnelByIdV2(testPersonnelId, requestType),
            ).rejects.toThrow(NotFoundException);

            expect(personnelRepository.findByIdOrFail).toHaveBeenCalledWith(testPersonnelId, [
                PersonnelExpand.user,
            ]);
            expect(
                customFieldsLocationsRepository.getCustomFieldPersonnelSubmissions,
            ).not.toHaveBeenCalled();
        });

        it('should throw error when custom fields repository fails', async () => {
            const requestType: PersonnelRequestType = {
                expand: [PersonnelExpand.customFields],
            };

            const customFieldsError = new Error('Custom fields repository error');

            personnelRepository.findByIdOrFail?.mockResolvedValue(mockPersonnel);
            customFieldsLocationsRepository.getCustomFieldPersonnelSubmissions?.mockRejectedValue(
                customFieldsError,
            );

            await expect(
                personnelCoreService.getPersonnelByIdV2(testPersonnelId, requestType),
            ).rejects.toThrow('Custom fields repository error');

            expect(personnelRepository.findByIdOrFail).toHaveBeenCalledWith(testPersonnelId, [
                PersonnelExpand.customFields,
            ]);
            expect(
                customFieldsLocationsRepository.getCustomFieldPersonnelSubmissions,
            ).toHaveBeenCalledWith(testPersonnelId);
        });

        it('should handle multiple expand options correctly', async () => {
            const requestType: PersonnelRequestType = {
                expand: [
                    PersonnelExpand.customFields,
                    PersonnelExpand.user,
                    PersonnelExpand.complianceChecks,
                    PersonnelExpand.reasonProvider,
                ],
            };

            const mockCustomFields: CustomFieldLocation[] = [new CustomFieldLocation()];

            personnelRepository.findByIdOrFail?.mockResolvedValue(mockPersonnel);
            customFieldsLocationsRepository.getCustomFieldPersonnelSubmissions?.mockResolvedValue(
                mockCustomFields,
            );

            const result: PersonnelWithCustomFields = await personnelCoreService.getPersonnelByIdV2(
                testPersonnelId,
                requestType,
            );

            expect(result).toEqual({
                personnel: mockPersonnel,
                customFields: mockCustomFields,
            });
            expect(personnelRepository.findByIdOrFail).toHaveBeenCalledWith(testPersonnelId, [
                PersonnelExpand.customFields,
                PersonnelExpand.user,
                PersonnelExpand.complianceChecks,
                PersonnelExpand.reasonProvider,
            ]);
            expect(
                customFieldsLocationsRepository.getCustomFieldPersonnelSubmissions,
            ).toHaveBeenCalledWith(testPersonnelId);
        });

        it('should handle edge case with zero personnel ID', async () => {
            const zeroPersonnelId = 0;
            const requestType: PersonnelRequestType = {
                expand: [PersonnelExpand.user],
            };

            const mockPersonnelWithZeroId = new Personnel();
            mockPersonnelWithZeroId.id = zeroPersonnelId;
            mockPersonnelWithZeroId.user = mockUser;

            personnelRepository.findByIdOrFail?.mockResolvedValue(mockPersonnelWithZeroId);

            const result: PersonnelWithCustomFields = await personnelCoreService.getPersonnelByIdV2(
                zeroPersonnelId,
                requestType,
            );

            expect(result).toEqual({
                personnel: mockPersonnelWithZeroId,
                customFields: undefined,
            });
            expect(personnelRepository.findByIdOrFail).toHaveBeenCalledWith(zeroPersonnelId, [
                PersonnelExpand.user,
            ]);
            expect(
                customFieldsLocationsRepository.getCustomFieldPersonnelSubmissions,
            ).not.toHaveBeenCalled();
        });

        it('should handle large personnel ID numbers', async () => {
            const largePersonnelId = 999999999;
            const requestType: PersonnelRequestType = {
                expand: [PersonnelExpand.customFields],
            };

            const mockPersonnelWithLargeId = new Personnel();
            mockPersonnelWithLargeId.id = largePersonnelId;
            mockPersonnelWithLargeId.user = mockUser;

            const mockCustomFields: CustomFieldLocation[] = [new CustomFieldLocation()];

            personnelRepository.findByIdOrFail?.mockResolvedValue(mockPersonnelWithLargeId);
            customFieldsLocationsRepository.getCustomFieldPersonnelSubmissions?.mockResolvedValue(
                mockCustomFields,
            );

            const result: PersonnelWithCustomFields = await personnelCoreService.getPersonnelByIdV2(
                largePersonnelId,
                requestType,
            );

            expect(result).toEqual({
                personnel: mockPersonnelWithLargeId,
                customFields: mockCustomFields,
            });
            expect(personnelRepository.findByIdOrFail).toHaveBeenCalledWith(largePersonnelId, [
                PersonnelExpand.customFields,
            ]);
            expect(
                customFieldsLocationsRepository.getCustomFieldPersonnelSubmissions,
            ).toHaveBeenCalledWith(largePersonnelId);
        });
    });

    describe('Integration-style tests for both methods', () => {
        beforeEach(() => {
            jest.clearAllMocks();
        });

        it('should handle concurrent calls to both getPersonnelByEmailV2 and getPersonnelByIdV2', async () => {
            const testEmail = '<EMAIL>';
            const testPersonnelId = 456;

            const mockUser = new User();
            mockUser.id = 1;
            mockUser.email = testEmail;

            const mockPersonnelForEmail = new Personnel();
            mockPersonnelForEmail.id = testPersonnelId;
            mockPersonnelForEmail.user = mockUser;

            const mockPersonnelForId = new Personnel();
            mockPersonnelForId.id = testPersonnelId;
            mockPersonnelForId.user = mockUser;

            const requestType: PersonnelRequestType = {
                expand: [PersonnelExpand.customFields],
            };

            const mockCustomFields: CustomFieldLocation[] = [new CustomFieldLocation()];

            personnelRepository.findByEmailOrFail?.mockResolvedValue(mockPersonnelForEmail);
            personnelRepository.findByIdOrFail?.mockResolvedValue(mockPersonnelForId);
            customFieldsLocationsRepository.getCustomFieldPersonnelSubmissions?.mockResolvedValue(
                mockCustomFields,
            );

            const [emailResult, idResult] = await Promise.all([
                personnelCoreService.getPersonnelByEmailV2(testEmail, requestType),
                personnelCoreService.getPersonnelByIdV2(testPersonnelId, requestType),
            ]);

            expect(emailResult).toEqual({
                personnel: mockPersonnelForEmail,
                customFields: mockCustomFields,
            });

            expect(idResult).toEqual({
                personnel: mockPersonnelForId,
                customFields: mockCustomFields,
            });

            expect(personnelRepository.findByEmailOrFail).toHaveBeenCalledWith(testEmail, [
                PersonnelExpand.customFields,
            ]);
            expect(personnelRepository.findByIdOrFail).toHaveBeenCalledWith(testPersonnelId, [
                PersonnelExpand.customFields,
            ]);
            expect(
                customFieldsLocationsRepository.getCustomFieldPersonnelSubmissions,
            ).toHaveBeenCalledTimes(2);
        });

        it('should handle null/undefined custom fields gracefully', async () => {
            const testEmail = '<EMAIL>';
            const mockUser = new User();
            mockUser.id = 1;
            mockUser.email = testEmail;

            const mockPersonnel = new Personnel();
            mockPersonnel.id = 789;
            mockPersonnel.user = mockUser;

            const requestType: PersonnelRequestType = {
                expand: [PersonnelExpand.customFields],
            };

            personnelRepository.findByEmailOrFail?.mockResolvedValue(mockPersonnel);
            customFieldsLocationsRepository.getCustomFieldPersonnelSubmissions?.mockResolvedValue(
                null as any,
            );

            const result = await personnelCoreService.getPersonnelByEmailV2(testEmail, requestType);

            expect(result).toEqual({
                personnel: mockPersonnel,
                customFields: null,
            });
        });

        it('should handle custom fields with complex data structures', async () => {
            const testPersonnelId = 999;
            const mockUser = new User();
            mockUser.id = 1;
            mockUser.email = '<EMAIL>';

            const mockPersonnel = new Personnel();
            mockPersonnel.id = testPersonnelId;
            mockPersonnel.user = mockUser;

            const requestType: PersonnelRequestType = {
                expand: [PersonnelExpand.customFields],
            };

            // Create complex custom field data
            const complexCustomField1 = new CustomFieldLocation();
            complexCustomField1.id = 1;

            const complexCustomField2 = new CustomFieldLocation();
            complexCustomField2.id = 2;

            const mockCustomFields: CustomFieldLocation[] = [
                complexCustomField1,
                complexCustomField2,
            ];

            personnelRepository.findByIdOrFail?.mockResolvedValue(mockPersonnel);
            customFieldsLocationsRepository.getCustomFieldPersonnelSubmissions?.mockResolvedValue(
                mockCustomFields,
            );

            const result = await personnelCoreService.getPersonnelByIdV2(
                testPersonnelId,
                requestType,
            );

            expect(result).toEqual({
                personnel: mockPersonnel,
                customFields: mockCustomFields,
            });
            expect(result.customFields).toHaveLength(2);
            expect(result.customFields?.[0].id).toBe(1);
            expect(result.customFields?.[1].id).toBe(2);
        });

        it('should maintain method isolation when one fails', async () => {
            const testEmail = '<EMAIL>';
            const testPersonnelId = 111;

            const mockUser = new User();
            mockUser.id = 1;
            mockUser.email = testEmail;

            const mockPersonnel = new Personnel();
            mockPersonnel.id = testPersonnelId;
            mockPersonnel.user = mockUser;

            const requestType: PersonnelRequestType = {
                expand: [PersonnelExpand.customFields],
            };

            const mockCustomFields: CustomFieldLocation[] = [new CustomFieldLocation()];

            // Email method fails, ID method succeeds
            personnelRepository.findByEmailOrFail?.mockRejectedValue(
                new NotFoundException('Email not found'),
            );
            personnelRepository.findByIdOrFail?.mockResolvedValue(mockPersonnel);
            customFieldsLocationsRepository.getCustomFieldPersonnelSubmissions?.mockResolvedValue(
                mockCustomFields,
            );

            // Email method should fail
            await expect(
                personnelCoreService.getPersonnelByEmailV2(testEmail, requestType),
            ).rejects.toThrow(NotFoundException);

            // ID method should still succeed
            const idResult = await personnelCoreService.getPersonnelByIdV2(
                testPersonnelId,
                requestType,
            );

            expect(idResult).toEqual({
                personnel: mockPersonnel,
                customFields: mockCustomFields,
            });
        });
    });

    describe('Additional method coverage', () => {
        beforeEach(() => {
            jest.clearAllMocks();
        });

        it('should test service is properly initialized', () => {
            expect(personnelCoreService).toBeDefined();
            expect(personnelRepository).toBeDefined();
            expect(customFieldsLocationsRepository).toBeDefined();
        });

        it('should verify mock setup is working correctly', async () => {
            const mockPersonnel = new Personnel();
            mockPersonnel.id = 999;

            personnelRepository.findByEmailOrFail?.mockResolvedValue(mockPersonnel);

            const result = await personnelRepository.findByEmailOrFail('<EMAIL>', []);

            expect(result).toBe(mockPersonnel);
            expect(personnelRepository.findByEmailOrFail).toHaveBeenCalledWith(
                '<EMAIL>',
                [],
            );
        });

        it('should handle repository method calls with proper parameters', async () => {
            const mockCustomFields: CustomFieldLocation[] = [new CustomFieldLocation()];

            customFieldsLocationsRepository.getCustomFieldPersonnelSubmissions?.mockResolvedValue(
                mockCustomFields,
            );

            const result =
                await customFieldsLocationsRepository.getCustomFieldPersonnelSubmissions(123);

            expect(result).toBe(mockCustomFields);
            expect(
                customFieldsLocationsRepository.getCustomFieldPersonnelSubmissions,
            ).toHaveBeenCalledWith(123);
        });
    });
});

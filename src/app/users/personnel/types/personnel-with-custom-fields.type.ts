import { CustomFieldLocation } from 'app/custom-fields/entities/custom-field-location.entity';
import { Personnel } from 'app/users/personnel/entities/personnel.entity';
import { ComplianceCheckWithExclusion } from 'app/users/personnel/types/compliance-check-with-exclusion.type';

export type PersonnelWithCustomFields = {
    personnel: Personnel & {
        complianceChecks?: ComplianceCheckWithExclusion[];
    };
    customFields?: CustomFieldLocation[];
};

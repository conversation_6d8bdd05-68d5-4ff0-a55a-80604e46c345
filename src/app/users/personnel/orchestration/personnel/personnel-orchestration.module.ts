import { Module } from '@nestjs/common';
import { CompaniesCoreModule } from 'app/companies/companies-core.module';
import { ComplianceCheckExclusionsCoreModule } from 'app/compliance-check-exclusions/compliance-check-exclusions-core.module';
import { DevicesCoreModule } from 'app/devices/devices-core.module';
import { EdrCoreModule } from 'app/edr/edr-core.module';
import { TicketsCoreModule } from 'app/tickets/tickets-core.module';
import { ComplianceChecksCoreModule } from 'app/users/compliance-checks-core.module';
import { PersonnelTemplateCoreModule } from 'app/users/personnel/core/personnel-template/personnel-template.core.module';
import { PersonnelOrchestrationService } from 'app/users/personnel/orchestration/personnel/personnel-orchestration.service';
import { UserIdentitiesCoreModule } from 'app/users/user-identities-core.module';
import { EntryCoreModule } from 'auth/entry-core.module';
import { ModuleType, ModuleTypes } from 'commons/decorators/module-type.decorator';

@ModuleType(ModuleTypes.ORCHESTRATION)
@Module({
    imports: [
        CompaniesCoreModule,
        ComplianceCheckExclusionsCoreModule,
        ComplianceChecksCoreModule,
        DevicesCoreModule,
        EdrCoreModule,
        PersonnelTemplateCoreModule,
        TicketsCoreModule,
        EntryCoreModule,
        UserIdentitiesCoreModule,
    ],
    providers: [PersonnelOrchestrationService],
    exports: [PersonnelOrchestrationService],
})
export class PersonnelOrchestrationModule {}

import { ComplianceCheckType, EmploymentStatus } from '@drata/enums';
import { HttpService } from '@nestjs/axios';
import { CompanyRepository } from 'app/ai/knowledge-setup/repositories/company.repository';
import { ApiClientService } from 'app/api-client/api-client.service';
import { BackgroundCheck } from 'app/background-check/entities/background-check.entity';
import { Company } from 'app/companies/entities/company.entity';
import { CompaniesCoreService } from 'app/companies/services/companies-core.service';
import { ComplianceCheckExclusionRepository } from 'app/compliance-check-exclusions/repositories/compliance-check-exclusion.repository';
import { ComplianceCheckExclusionsCoreService } from 'app/compliance-check-exclusions/services/compliance-check-exclusions-core.service';
import { CustomFieldValueType } from 'app/custom-fields/types/custom-field-value.type';
import { DeviceDocument } from 'app/devices/entities/device-document.entity';
import { Device } from 'app/devices/entities/device.entity';
import { DevicesCoreService } from 'app/devices/services/devices-core.service';
import { EdrCoreService } from 'app/edr/services/edr-core.service';
import { Ticket } from 'app/tickets/entities/ticket.entity';
import { TicketsCoreService } from 'app/tickets/services/tickets-core.service';
import { UserDocument } from 'app/users/entities/user-document.entity';
import { User } from 'app/users/entities/user.entity';
import { GroupPersonnel } from 'app/users/groups/entities/group-personnel.entity';
import { Group } from 'app/users/groups/entities/group.entity';
import { PersonnelTemplateCoreService } from 'app/users/personnel/core/personnel-template/personnel-template.core.service';
import { ComplianceCheck } from 'app/users/personnel/entities/compliance-check.entity';
import { Personnel } from 'app/users/personnel/entities/personnel.entity';
import { personnelOrchestrationConnectionMock } from 'app/users/personnel/orchestration/personnel/mocks/personnel-orchestration-connection.mock';
import { PersonnelOrchestrationService } from 'app/users/personnel/orchestration/personnel/personnel-orchestration.service';
import { ComplianceChecksCoreService } from 'app/users/personnel/services/compliance-checks-core.service';
import { PersonnelDetailsPDFFormatterHelper } from 'app/users/personnel/services/personnel-details-pdf-formatter.helper';
import { PersonnelDetailsPDF } from 'app/users/personnel/types/personnel-detail-pdf.type';
import { UserIdentitiesCoreService } from 'app/users/services/user-identities-core.service';
import { Account } from 'auth/entities/account.entity';
import { EntryCoreService } from 'auth/services/entry-core.service';
import { AuditorPersonnelStatus } from 'commons/enums/auditors/auditor-personnel-status.enum';
import { ComplianceCheckExclusionStatus } from 'commons/enums/compliance-check-exclusions/compliance-check-exclusion-status.enum';
import { ComplianceCheckExclusionTargetType } from 'commons/enums/compliance-check-exclusions/compliance-check-exclusion-target-type.enum';
import { UserDocumentType } from 'commons/enums/users/user-document-type.enum';
import { createAppTestingModule } from 'commons/helpers/app-testing-module.helper';
import { MockFactory } from 'commons/mocks/factories/mock.factory';
import { MockType } from 'commons/mocks/types/mock.type';
import * as databaseHelpers from 'database/typeorm/typeorm.extensions.helper';
import { Downloader } from 'dependencies/downloader/downloader';
import { S3Downloader } from 'dependencies/downloader/s3/s3-downloader';
import { DocraptorHtmlToPdfConverter } from 'dependencies/html-to-pdf-converter/docraptor/docraptor-html-to-pdf-converter';
import { HtmlToPdfConverter } from 'dependencies/html-to-pdf-converter/html-to-pdf-converter';
import { TenancyContext } from 'tenancy/contexts/tenancy.context';

describe('PersonnelOrchestrationService', () => {
    let personnelOrchestrationService: PersonnelOrchestrationService;

    const companiesCoreServiceMock: MockType<CompaniesCoreService> =
        MockFactory.getMock(CompaniesCoreService);

    const complianceChecksCoreServiceMock: MockType<ComplianceChecksCoreService> =
        MockFactory.getMock(ComplianceChecksCoreService);

    const complianceCheckExclusionsCoreServiceMock: MockType<ComplianceCheckExclusionsCoreService> =
        MockFactory.getMock(ComplianceCheckExclusionsCoreService);

    const devicesCoreServiceMock: MockType<DevicesCoreService> =
        MockFactory.getMock(DevicesCoreService);

    const edrCoreServiceMock: MockType<EdrCoreService> = MockFactory.getMock(EdrCoreService);

    const personnelTemplateCoreServiceMock: MockType<PersonnelTemplateCoreService> =
        MockFactory.getMock(PersonnelTemplateCoreService);

    const htmlToPdfConverterMock: MockType<HtmlToPdfConverter> = MockFactory.getMock(
        DocraptorHtmlToPdfConverter,
    );

    const apiClientServiceMock: MockType<ApiClientService> = MockFactory.getMock(ApiClientService);

    const httpServiceMock: MockType<HttpService> = MockFactory.getMock(HttpService);

    const downloaderMock: MockType<Downloader> = MockFactory.getMock(S3Downloader);

    let companyRepositoryMock: MockType<CompanyRepository>;
    let complianceCheckExclusionRepositoryMock: MockType<ComplianceCheckExclusionRepository>;

    beforeEach(async () => {
        jest.spyOn(databaseHelpers, 'getCustomRepository').mockImplementation(
            personnelOrchestrationConnectionMock.getCustomRepository,
        );
        const module = await createAppTestingModule({
            providers: [
                PersonnelOrchestrationService,
                {
                    provide: TenancyContext,
                    useValue: personnelOrchestrationConnectionMock,
                },
                {
                    provide: CompaniesCoreService,
                    useValue: companiesCoreServiceMock,
                },
                {
                    provide: ComplianceChecksCoreService,
                    useValue: complianceChecksCoreServiceMock,
                },
                {
                    provide: ComplianceCheckExclusionsCoreService,
                    useValue: complianceCheckExclusionsCoreServiceMock,
                },
                {
                    provide: DevicesCoreService,
                    useValue: devicesCoreServiceMock,
                },
                {
                    provide: EdrCoreService,
                    useValue: edrCoreServiceMock,
                },
                {
                    provide: PersonnelTemplateCoreService,
                    useValue: personnelTemplateCoreServiceMock,
                },
                {
                    provide: HtmlToPdfConverter,
                    useValue: htmlToPdfConverterMock,
                },
                {
                    provide: ApiClientService,
                    useValue: apiClientServiceMock,
                },
                {
                    provide: HttpService,
                    useValue: httpServiceMock,
                },
                {
                    provide: Downloader,
                    useValue: downloaderMock,
                },
                {
                    provide: TicketsCoreService,
                    useValue: {},
                },
                {
                    provide: EntryCoreService,
                    useValue: {},
                },
                {
                    provide: UserIdentitiesCoreService,
                    useValue: {},
                },
            ],
        }).compile();

        personnelOrchestrationService = await module.resolve<PersonnelOrchestrationService>(
            PersonnelOrchestrationService,
        );
        companyRepositoryMock =
            personnelOrchestrationConnectionMock.getCustomRepository(CompanyRepository);
        complianceCheckExclusionRepositoryMock =
            personnelOrchestrationConnectionMock.getCustomRepository(
                ComplianceCheckExclusionRepository,
            );
    });

    it('Should be defined', () => {
        expect(personnelOrchestrationService).toBeDefined();
    });

    describe('getPersonnelDetailPdfFile', () => {
        describe('Given an account, a personnel, user documents, tickets, and custom field value types', () => {
            let account: Account;
            let personnel: Personnel;
            let user: User;
            let userDocuments: UserDocument[];
            let tickets: Ticket[];
            let customFieldValueTypes: CustomFieldValueType[];

            beforeEach(() => {
                account = new Account();
                personnel = new Personnel();
                personnel.id = 1;
                personnel.employmentStatus = EmploymentStatus.CURRENT_EMPLOYEE;
                personnel.startDate = new Date().toString();

                const backgroundCheckComplianceCheck = new ComplianceCheck();
                backgroundCheckComplianceCheck.type = ComplianceCheckType.BG_CHECK;
                const offboardingComplianceCheck = new ComplianceCheck();
                offboardingComplianceCheck.type = ComplianceCheckType.OFFBOARDING;
                const securityTrainingComplianceCheck = new ComplianceCheck();
                securityTrainingComplianceCheck.type = ComplianceCheckType.SECURITY_TRAINING;
                const acceptedPoliciesComplianceCheck = new ComplianceCheck();
                acceptedPoliciesComplianceCheck.type = ComplianceCheckType.ACCEPTED_POLICIES;
                const nistAiTrainingComplianceCheck = new ComplianceCheck();
                nistAiTrainingComplianceCheck.type = ComplianceCheckType.NIST_AI_TRAINING;

                const complianceChecks = [
                    backgroundCheckComplianceCheck,
                    offboardingComplianceCheck,
                    securityTrainingComplianceCheck,
                    acceptedPoliciesComplianceCheck,
                    nistAiTrainingComplianceCheck,
                ];

                personnel.complianceChecks = complianceChecks;

                user = new User();
                user.id = 1;
                user.firstName = 'John';
                user.lastName = 'Doe';
                const backgroundCheck = new BackgroundCheck();
                user.backgroundChecks = [backgroundCheck];
                personnel.user = user;

                const documentA = new UserDocument();
                documentA.id = 1;
                userDocuments = [documentA];

                const ticketA = new Ticket();
                ticketA.externalTicketId = 'external-id';
                tickets = [ticketA];

                customFieldValueTypes = [
                    {
                        name: 'Custom Field A',
                        submissionValue: 'Some value',
                    },
                ];

                const personnelDetailsPdfFullCompliance = {
                    type: ComplianceCheckType.FULL_COMPLIANCE,
                } as PersonnelDetailsPDF;

                const personnelDetailsPdfAcceptedPoliciesComplianceCheck = {
                    type: ComplianceCheckType.ACCEPTED_POLICIES,
                } as PersonnelDetailsPDF;
                const personnelDetailsPdfOffboardingComplianceCheck = {
                    type: ComplianceCheckType.OFFBOARDING,
                } as PersonnelDetailsPDF;
                const personnelDetailsPdfSecurityTrainingComplianceChecks = {
                    type: ComplianceCheckType.SECURITY_TRAINING,
                } as PersonnelDetailsPDF;

                const listOfPersonnelDetailsForPdf = [
                    personnelDetailsPdfFullCompliance,
                    personnelDetailsPdfAcceptedPoliciesComplianceCheck,
                    personnelDetailsPdfOffboardingComplianceCheck,
                    personnelDetailsPdfSecurityTrainingComplianceChecks,
                ];

                const company = new Company();
                company.id = 4;

                companyRepositoryMock.findOneBy?.mockResolvedValue(company);
                complianceCheckExclusionRepositoryMock.findByOptions?.mockResolvedValue([]);
                companiesCoreServiceMock.getIsTrainingEnabled?.mockResolvedValue(true);
                personnelTemplateCoreServiceMock.mapPersonnelComplianceChecks?.mockResolvedValue(
                    listOfPersonnelDetailsForPdf,
                );
                personnelTemplateCoreServiceMock.getPersonnelDetailsTemplateDelegate?.mockReturnValue(
                    jest.fn(() => `<div>Sample</div>`),
                );
                htmlToPdfConverterMock.convertToPdfBuffer?.mockResolvedValue({
                    data: Buffer.from([0]),
                });
            });

            it('Should return a file buffer type', async () => {
                const result = await personnelOrchestrationService.getPersonnelDetailPdfFile(
                    account,
                    personnel,
                    userDocuments,
                    tickets,
                    customFieldValueTypes,
                );

                expect(result).toEqual({
                    stream: Buffer.from([0]),
                    filename: PersonnelDetailsPDFFormatterHelper.getFilename(user),
                });
            });
        });
    });

    describe('getDocumentsToDownload', () => {
        describe('Given a personnel and a list of user non-device document types', () => {
            let personnel: Personnel;
            let nonDeviceDocTypes: UserDocumentType[] = [];
            let mockedUserDocuments: UserDocument[] = [];

            beforeEach(() => {
                personnel = new Personnel();
                const user = new User();
                user.id = 1;
                personnel.user = user;

                nonDeviceDocTypes = [
                    UserDocumentType.MFA_EVIDENCE,
                    UserDocumentType.SEC_TRAINING,
                    UserDocumentType.HIPAA_TRAINING_EVIDENCE,
                    UserDocumentType.NIST_AI_TRAINING_EVIDENCE,
                ];

                const documentA = new UserDocument();
                documentA.id = 1;
                mockedUserDocuments = [documentA];

                complianceChecksCoreServiceMock.getAllDocuments?.mockResolvedValue(
                    mockedUserDocuments,
                );
            });

            it('Should return a list of UserDocuments', async () => {
                const result = await personnelOrchestrationService.getDocumentsToDownload(
                    personnel,
                    nonDeviceDocTypes,
                );
                expect(result).toEqual(mockedUserDocuments);
            });
        });

        describe('Given a personnel and a list of user device document types', () => {
            let personnel: Personnel;
            let deviceDocTypes: UserDocumentType[] = [];
            let mockedDeviceDocuments: DeviceDocument[] = [];

            beforeEach(() => {
                personnel = new Personnel();
                const user = new User();
                user.id = 1;
                personnel.user = user;

                deviceDocTypes = [
                    UserDocumentType.PASSWORD_MANAGER_EVIDENCE,
                    UserDocumentType.AUTO_UPDATES_EVIDENCE,
                    UserDocumentType.HARD_DRIVE_ENCRYPTION_EVIDENCE,
                    UserDocumentType.ANTIVIRUS_EVIDENCE,
                    UserDocumentType.LOCK_SCREEN_EVIDENCE,
                ];
                const documentA = new DeviceDocument();
                documentA.id = 1;
                mockedDeviceDocuments = [documentA];

                devicesCoreServiceMock.getAllDevicesDocuments?.mockResolvedValue(
                    mockedDeviceDocuments,
                );
            });

            it('Should return a list of DeviceDocuments', async () => {
                const result = await personnelOrchestrationService.getDocumentsToDownload(
                    personnel,
                    deviceDocTypes,
                );
                expect(result).toEqual(mockedDeviceDocuments);
            });
        });

        describe('Given a personnel and a list of user device and non-device document types', () => {
            let personnel: Personnel;
            let docTypes: UserDocumentType[] = [];
            let mockedDeviceDocuments: DeviceDocument[] = [];
            let mockedUserDocuments: UserDocument[] = [];

            beforeEach(() => {
                personnel = new Personnel();
                const user = new User();
                user.id = 1;
                personnel.user = user;

                docTypes = [
                    UserDocumentType.PASSWORD_MANAGER_EVIDENCE,
                    UserDocumentType.AUTO_UPDATES_EVIDENCE,
                    UserDocumentType.HARD_DRIVE_ENCRYPTION_EVIDENCE,
                    UserDocumentType.ANTIVIRUS_EVIDENCE,
                    UserDocumentType.LOCK_SCREEN_EVIDENCE,
                    UserDocumentType.MFA_EVIDENCE,
                    UserDocumentType.SEC_TRAINING,
                    UserDocumentType.HIPAA_TRAINING_EVIDENCE,
                    UserDocumentType.NIST_AI_TRAINING_EVIDENCE,
                ];
                const deviceDocumentA = new DeviceDocument();
                deviceDocumentA.id = 1;
                mockedDeviceDocuments = [deviceDocumentA];

                const userDocumentA = new UserDocument();
                userDocumentA.id = 1;
                mockedUserDocuments = [userDocumentA];

                devicesCoreServiceMock.getAllDevicesDocuments?.mockResolvedValue(
                    mockedDeviceDocuments,
                );
                complianceChecksCoreServiceMock.getAllDocuments?.mockResolvedValue(
                    mockedUserDocuments,
                );
            });

            it('Should return a list of DeviceDocuments', async () => {
                const result = await personnelOrchestrationService.getDocumentsToDownload(
                    personnel,
                    docTypes,
                );
                expect(result).toEqual([...mockedUserDocuments, ...mockedDeviceDocuments]);
            });
        });
    });

    describe('filterUnusedTypes', () => {
        describe('Given a list of compliance checks', () => {
            let complianceChecks: ComplianceCheck[] = [];

            let backgroundCheckComplianceCheck: ComplianceCheck;
            let offboardingComplianceCheck: ComplianceCheck;
            let agentInstalledComplianceCheck: ComplianceCheck;
            let locationServicesComplianceCheck: ComplianceCheck;

            beforeEach(() => {
                backgroundCheckComplianceCheck = new ComplianceCheck();
                backgroundCheckComplianceCheck.type = ComplianceCheckType.BG_CHECK;
                offboardingComplianceCheck = new ComplianceCheck();
                offboardingComplianceCheck.type = ComplianceCheckType.OFFBOARDING;
                agentInstalledComplianceCheck = new ComplianceCheck();
                agentInstalledComplianceCheck.type = ComplianceCheckType.AGENT_INSTALLED;
                locationServicesComplianceCheck = new ComplianceCheck();
                locationServicesComplianceCheck.type = ComplianceCheckType.LOCATION_SERVICES;

                complianceChecks = [
                    backgroundCheckComplianceCheck,
                    offboardingComplianceCheck,
                    agentInstalledComplianceCheck,
                    locationServicesComplianceCheck,
                ];
            });

            it('Should return all compliance checks except LOCATION_SERVICES, and AGENT_INSTALLED', () => {
                const result = personnelOrchestrationService.filterUnusedTypes(complianceChecks);
                expect(result).toEqual([
                    backgroundCheckComplianceCheck,
                    offboardingComplianceCheck,
                ]);
            });
        });
    });

    describe('filterOffboarding', () => {
        describe('Given a list of compliance checks, and a flag isFormerPersonnel', () => {
            let complianceChecks: ComplianceCheck[] = [];

            let backgroundCheckComplianceCheck: ComplianceCheck;
            let offboardingComplianceCheck: ComplianceCheck;
            let securityTrainingComplianceCheck: ComplianceCheck;
            let acceptedPoliciesComplianceCheck: ComplianceCheck;

            beforeEach(() => {
                backgroundCheckComplianceCheck = new ComplianceCheck();
                backgroundCheckComplianceCheck.type = ComplianceCheckType.BG_CHECK;
                offboardingComplianceCheck = new ComplianceCheck();
                offboardingComplianceCheck.type = ComplianceCheckType.OFFBOARDING;
                securityTrainingComplianceCheck = new ComplianceCheck();
                securityTrainingComplianceCheck.type = ComplianceCheckType.SECURITY_TRAINING;
                acceptedPoliciesComplianceCheck = new ComplianceCheck();
                acceptedPoliciesComplianceCheck.type = ComplianceCheckType.ACCEPTED_POLICIES;

                complianceChecks = [
                    backgroundCheckComplianceCheck,
                    offboardingComplianceCheck,
                    securityTrainingComplianceCheck,
                    acceptedPoliciesComplianceCheck,
                ];
            });

            describe('When isFormerPersonnel flag is false', () => {
                const isFormerPersonnel = false;

                it('Should return all compliance checks except OFFBOARDING', () => {
                    const result = personnelOrchestrationService.filterOffboarding(
                        complianceChecks,
                        isFormerPersonnel,
                    );
                    expect(result).toEqual([
                        backgroundCheckComplianceCheck,
                        securityTrainingComplianceCheck,
                        acceptedPoliciesComplianceCheck,
                    ]);
                });
            });

            describe('When isFormerPersonnel flag is true', () => {
                const isFormerPersonnel = true;

                it('Should return all of compliance checks', () => {
                    const result = personnelOrchestrationService.filterOffboarding(
                        complianceChecks,
                        isFormerPersonnel,
                    );
                    expect(result).toEqual(complianceChecks);
                });
            });
        });
    });

    describe('filterHipaaTraining', () => {
        describe('Given a list of compliance checks, and a flag isHipaaEnabled', () => {
            let complianceChecks: ComplianceCheck[] = [];

            let backgroundCheckComplianceCheck: ComplianceCheck;
            let offboardingComplianceCheck: ComplianceCheck;
            let securityTrainingComplianceCheck: ComplianceCheck;
            let acceptedPoliciesComplianceCheck: ComplianceCheck;
            let hipaaTrainingComplianceCheck: ComplianceCheck;

            beforeEach(() => {
                backgroundCheckComplianceCheck = new ComplianceCheck();
                backgroundCheckComplianceCheck.type = ComplianceCheckType.BG_CHECK;
                offboardingComplianceCheck = new ComplianceCheck();
                offboardingComplianceCheck.type = ComplianceCheckType.OFFBOARDING;
                securityTrainingComplianceCheck = new ComplianceCheck();
                securityTrainingComplianceCheck.type = ComplianceCheckType.SECURITY_TRAINING;
                acceptedPoliciesComplianceCheck = new ComplianceCheck();
                acceptedPoliciesComplianceCheck.type = ComplianceCheckType.ACCEPTED_POLICIES;
                hipaaTrainingComplianceCheck = new ComplianceCheck();
                hipaaTrainingComplianceCheck.type = ComplianceCheckType.HIPAA_TRAINING;

                complianceChecks = [
                    backgroundCheckComplianceCheck,
                    offboardingComplianceCheck,
                    securityTrainingComplianceCheck,
                    acceptedPoliciesComplianceCheck,
                    hipaaTrainingComplianceCheck,
                ];
            });

            describe('When isHipaaEnabled flag is false', () => {
                const isHipaaEnabled = false;

                it('Should return all compliance checks except HIPAA_TRAINING', () => {
                    const result = personnelOrchestrationService.filterHipaaTraining(
                        complianceChecks,
                        isHipaaEnabled,
                    );
                    expect(result).toEqual([
                        backgroundCheckComplianceCheck,
                        offboardingComplianceCheck,
                        securityTrainingComplianceCheck,
                        acceptedPoliciesComplianceCheck,
                    ]);
                });
            });

            describe('When isHipaaEnabled flag is true', () => {
                const isHipaaEnabled = true;

                it('Should return all of compliance checks', () => {
                    const result = personnelOrchestrationService.filterHipaaTraining(
                        complianceChecks,
                        isHipaaEnabled,
                    );
                    expect(result).toEqual(complianceChecks);
                });
            });
        });
    });

    describe('filterNistAiTraining', () => {
        describe('Given a list of compliance checks, and a flag isNistEnabled', () => {
            let complianceChecks: ComplianceCheck[] = [];

            let backgroundCheckComplianceCheck: ComplianceCheck;
            let offboardingComplianceCheck: ComplianceCheck;
            let securityTrainingComplianceCheck: ComplianceCheck;
            let acceptedPoliciesComplianceCheck: ComplianceCheck;
            let nistAiTrainingComplianceCheck: ComplianceCheck;

            beforeEach(() => {
                backgroundCheckComplianceCheck = new ComplianceCheck();
                backgroundCheckComplianceCheck.type = ComplianceCheckType.BG_CHECK;
                offboardingComplianceCheck = new ComplianceCheck();
                offboardingComplianceCheck.type = ComplianceCheckType.OFFBOARDING;
                securityTrainingComplianceCheck = new ComplianceCheck();
                securityTrainingComplianceCheck.type = ComplianceCheckType.SECURITY_TRAINING;
                acceptedPoliciesComplianceCheck = new ComplianceCheck();
                acceptedPoliciesComplianceCheck.type = ComplianceCheckType.ACCEPTED_POLICIES;
                nistAiTrainingComplianceCheck = new ComplianceCheck();
                nistAiTrainingComplianceCheck.type = ComplianceCheckType.NIST_AI_TRAINING;

                complianceChecks = [
                    backgroundCheckComplianceCheck,
                    offboardingComplianceCheck,
                    securityTrainingComplianceCheck,
                    acceptedPoliciesComplianceCheck,
                    nistAiTrainingComplianceCheck,
                ];
            });

            describe('When isNistEnabled flag is false', () => {
                const isNistEnabled = false;

                it('Should return all compliance checks except NIST_AI_TRAINING', () => {
                    const result = personnelOrchestrationService.filterNistAiTraining(
                        complianceChecks,
                        isNistEnabled,
                    );
                    expect(result).toEqual([
                        backgroundCheckComplianceCheck,
                        offboardingComplianceCheck,
                        securityTrainingComplianceCheck,
                        acceptedPoliciesComplianceCheck,
                    ]);
                });
            });

            describe('When isNistEnabled flag is true', () => {
                const isNistEnabled = true;

                it('Should return all of compliance checks', () => {
                    const result = personnelOrchestrationService.filterNistAiTraining(
                        complianceChecks,
                        isNistEnabled,
                    );
                    expect(result).toEqual(complianceChecks);
                });
            });
        });
    });

    describe('filterIdentityMFA', () => {
        describe('Given a list of compliance checks, and a flag for out of scope employment status', () => {
            let complianceChecks: ComplianceCheck[] = [];

            let backgroundCheckComplianceCheck: ComplianceCheck;
            let offboardingComplianceCheck: ComplianceCheck;
            let securityTrainingComplianceCheck: ComplianceCheck;
            let acceptedPoliciesComplianceCheck: ComplianceCheck;
            let identityMfaComplianceCheck: ComplianceCheck;

            beforeEach(() => {
                backgroundCheckComplianceCheck = new ComplianceCheck();
                backgroundCheckComplianceCheck.type = ComplianceCheckType.BG_CHECK;
                offboardingComplianceCheck = new ComplianceCheck();
                offboardingComplianceCheck.type = ComplianceCheckType.OFFBOARDING;
                securityTrainingComplianceCheck = new ComplianceCheck();
                securityTrainingComplianceCheck.type = ComplianceCheckType.SECURITY_TRAINING;
                acceptedPoliciesComplianceCheck = new ComplianceCheck();
                acceptedPoliciesComplianceCheck.type = ComplianceCheckType.ACCEPTED_POLICIES;
                identityMfaComplianceCheck = new ComplianceCheck();
                identityMfaComplianceCheck.type = ComplianceCheckType.IDENTITY_MFA;

                complianceChecks = [
                    backgroundCheckComplianceCheck,
                    offboardingComplianceCheck,
                    securityTrainingComplianceCheck,
                    acceptedPoliciesComplianceCheck,
                    identityMfaComplianceCheck,
                ];
            });

            describe('When isEmploymentStatusOutOfScope flag is false', () => {
                const isEmploymentStatusOutOfScope = false;

                it('Should return all compliance checks', () => {
                    const result = personnelOrchestrationService.filterIdentityMFA(
                        complianceChecks,
                        isEmploymentStatusOutOfScope,
                    );
                    expect(result).toEqual(complianceChecks);
                });
            });

            describe('When isEmploymentStatusOutOfScope flag is true', () => {
                const isEmploymentStatusOutOfScope = true;

                it('Should return a list of compliance checks that does not include IDENTITY_MFA', () => {
                    const result = personnelOrchestrationService.filterIdentityMFA(
                        complianceChecks,
                        isEmploymentStatusOutOfScope,
                    );
                    expect(result).toEqual([
                        backgroundCheckComplianceCheck,
                        offboardingComplianceCheck,
                        securityTrainingComplianceCheck,
                        acceptedPoliciesComplianceCheck,
                    ]);
                });
            });
        });
    });

    describe('filterByAuditorPersonnelStatus', () => {
        describe('Given a list of compliance checks, and an auditor personnel status', () => {
            let complianceChecks: ComplianceCheck[] = [];

            let backgroundCheckComplianceCheck: ComplianceCheck;
            let offboardingComplianceCheck: ComplianceCheck;
            let securityTrainingComplianceCheck: ComplianceCheck;
            let acceptedPoliciesComplianceCheck: ComplianceCheck;

            beforeEach(() => {
                backgroundCheckComplianceCheck = new ComplianceCheck();
                backgroundCheckComplianceCheck.type = ComplianceCheckType.BG_CHECK;
                offboardingComplianceCheck = new ComplianceCheck();
                offboardingComplianceCheck.type = ComplianceCheckType.OFFBOARDING;
                securityTrainingComplianceCheck = new ComplianceCheck();
                securityTrainingComplianceCheck.type = ComplianceCheckType.SECURITY_TRAINING;
                acceptedPoliciesComplianceCheck = new ComplianceCheck();
                acceptedPoliciesComplianceCheck.type = ComplianceCheckType.ACCEPTED_POLICIES;

                complianceChecks = [
                    backgroundCheckComplianceCheck,
                    offboardingComplianceCheck,
                    securityTrainingComplianceCheck,
                    acceptedPoliciesComplianceCheck,
                ];
            });

            describe('Given an auditor personnel status of CURRENT', () => {
                const auditorPersonnelStatus = AuditorPersonnelStatus.CURRENT;

                it('Should return a list of compliance checks that does not include BG_CHECK, and OFFBOARDING', () => {
                    const result = personnelOrchestrationService.filterByAuditorPersonnelStatus(
                        complianceChecks,
                        auditorPersonnelStatus,
                    );

                    expect(result).toEqual([
                        securityTrainingComplianceCheck,
                        acceptedPoliciesComplianceCheck,
                    ]);
                });
            });

            describe('Given an auditor personnel status of HIRED', () => {
                const auditorPersonnelStatus = AuditorPersonnelStatus.HIRED;

                it('Should return a list of compliance checks that includes BG_CHECK, SECURITY_TRAINING, and ACCEPTED_POLICIES', () => {
                    const result = personnelOrchestrationService.filterByAuditorPersonnelStatus(
                        complianceChecks,
                        auditorPersonnelStatus,
                    );

                    expect(result).toEqual([
                        backgroundCheckComplianceCheck,
                        securityTrainingComplianceCheck,
                        acceptedPoliciesComplianceCheck,
                    ]);
                });
            });

            describe('Given an auditor personnel status of FORMER', () => {
                const auditorPersonnelStatus = AuditorPersonnelStatus.FORMER;

                it('Should return a list of compliance checks with type of OFFBOARDING', () => {
                    const result = personnelOrchestrationService.filterByAuditorPersonnelStatus(
                        complianceChecks,
                        auditorPersonnelStatus,
                    );

                    expect(result).toEqual([offboardingComplianceCheck]);
                });
            });

            describe('Given an auditor personnel status of undefined', () => {
                const auditorPersonnelStatus = undefined;

                it('Should return all compliance checks', () => {
                    const result = personnelOrchestrationService.filterByAuditorPersonnelStatus(
                        complianceChecks,
                        auditorPersonnelStatus,
                    );

                    expect(result).toEqual(complianceChecks);
                });
            });
        });
    });

    describe('groupComplianceChecksBySection', () => {
        describe('Given a list of personnel details pdf', () => {
            let personnelDetailsPdfFullCompliance: PersonnelDetailsPDF;
            let personnelDetailsPdfAcceptedPoliciesComplianceCheck: PersonnelDetailsPDF;
            let personnelDetailsPdfOffboardingComplianceCheck: PersonnelDetailsPDF;
            let personnelDetailsPdfSecurityTrainingComplianceChecks: PersonnelDetailsPDF;

            let listOfPersonnelDetailsForPdf: PersonnelDetailsPDF[] = [];

            beforeEach(() => {
                personnelDetailsPdfFullCompliance = {
                    type: ComplianceCheckType.FULL_COMPLIANCE,
                } as PersonnelDetailsPDF;

                personnelDetailsPdfAcceptedPoliciesComplianceCheck = {
                    type: ComplianceCheckType.ACCEPTED_POLICIES,
                } as PersonnelDetailsPDF;
                personnelDetailsPdfOffboardingComplianceCheck = {
                    type: ComplianceCheckType.OFFBOARDING,
                } as PersonnelDetailsPDF;
                personnelDetailsPdfSecurityTrainingComplianceChecks = {
                    type: ComplianceCheckType.SECURITY_TRAINING,
                } as PersonnelDetailsPDF;

                listOfPersonnelDetailsForPdf = [
                    personnelDetailsPdfFullCompliance,
                    personnelDetailsPdfAcceptedPoliciesComplianceCheck,
                    personnelDetailsPdfOffboardingComplianceCheck,
                    personnelDetailsPdfSecurityTrainingComplianceChecks,
                ];
            });

            it('Should return an object that groups the pdfs by compliance check type', () => {
                const result = personnelOrchestrationService.groupComplianceChecksBySection(
                    listOfPersonnelDetailsForPdf,
                );
                expect(result).toEqual({
                    fullComplianceCheck: [personnelDetailsPdfFullCompliance],
                    acceptedPoliciesComplianceCheck: [
                        personnelDetailsPdfAcceptedPoliciesComplianceCheck,
                    ],
                    offboardingComplianceCheck: [personnelDetailsPdfOffboardingComplianceCheck],
                    securityTrainingComplianceChecks: [
                        personnelDetailsPdfSecurityTrainingComplianceChecks,
                    ],
                });
            });
        });
    });

    describe('getCurrentExclusionsForPersonnel', () => {
        describe('Given a list of personnel', () => {
            let personnels: Personnel[] = [];
            let personnelA: Personnel;
            let company: Company;
            let group: Group;

            beforeEach(() => {
                personnelA = new Personnel();
                personnelA.id = 1;
                personnelA.employmentStatus = EmploymentStatus.CURRENT_EMPLOYEE;
                const groupPersonnel = new GroupPersonnel();
                groupPersonnel.id = 2;
                group = new Group();
                group.id = 3;
                groupPersonnel.group = group;
                personnelA.groups = [groupPersonnel];
                personnels = [personnelA];

                company = new Company();
                company.id = 4;
                companyRepositoryMock.findOneBy?.mockResolvedValue(company);
            });

            it('Should return pass in the options to filter a list of compliance check exclusions', async () => {
                complianceCheckExclusionRepositoryMock.findByOptions?.mockImplementation(
                    input => input,
                );
                const options =
                    await personnelOrchestrationService.getCurrentExclusionsForPersonnel(
                        personnels,
                    );
                expect(options).toEqual([
                    {
                        isCurrent: true,
                        status: ComplianceCheckExclusionStatus.ACTIVE,
                        targetIds: [company.id.toString()],
                        targetType: ComplianceCheckExclusionTargetType.COMPANY,
                    },
                    {
                        isCurrent: true,
                        status: ComplianceCheckExclusionStatus.ACTIVE,
                        targetIds: [group.id.toString()],
                        targetType: ComplianceCheckExclusionTargetType.GROUP,
                    },
                    {
                        isCurrent: true,
                        status: ComplianceCheckExclusionStatus.ACTIVE,
                        targetIds: [EmploymentStatus.CURRENT_EMPLOYEE],
                        targetType: ComplianceCheckExclusionTargetType.EMPLOYMENT_STATUS,
                    },
                    {
                        isCurrent: true,
                        status: ComplianceCheckExclusionStatus.ACTIVE,
                        targetIds: [personnelA.id.toString()],
                        targetType: ComplianceCheckExclusionTargetType.PERSONNEL,
                    },
                ]);
            });
        });
    });

    describe('getPersonnelDevices', () => {
        describe('Given a personnel with an active EDR Connection', () => {
            let personnel: Personnel;
            const isEDRConnectionActive = true;
            let devices: Device[];

            beforeEach(() => {
                const deviceA = new Device();
                devices = [deviceA];

                personnel = new Personnel();

                devicesCoreServiceMock.getDevicesByPersonnel?.mockResolvedValue(devices);
                edrCoreServiceMock.addDeviceAgents?.mockResolvedValue(devices);
            });

            it('Should return a list of devices', async () => {
                const result = await personnelOrchestrationService.getPersonnelDevices(
                    personnel,
                    isEDRConnectionActive,
                );
                expect(result).toEqual(devices);
            });
        });

        describe('Given a personnel with an inactive EDR Connection', () => {
            let personnel: Personnel;
            const isEDRConnectionActive = true;
            let devices: Device[];

            beforeEach(() => {
                const deviceA = new Device();
                devices = [deviceA];

                personnel = new Personnel();

                devicesCoreServiceMock.getDevicesByPersonnel?.mockResolvedValue(devices);
            });

            it('Should return a list of devices', async () => {
                const result = await personnelOrchestrationService.getPersonnelDevices(
                    personnel,
                    isEDRConnectionActive,
                );
                expect(result).toEqual(devices);
            });
        });
    });
});

import {
    ComplianceCheckType,
    CustomFieldsSection,
    EmploymentStatus,
    ErrorCode,
} from '@drata/enums';
import { HttpService } from '@nestjs/axios';
import {
    BadRequestException,
    ConflictException,
    HttpStatus,
    Injectable,
    ServiceUnavailableException,
} from '@nestjs/common';
import { CompanyRepository } from 'app/ai/knowledge-setup/repositories/company.repository';
import { TicketingIssueType } from 'app/apis/types/ticketing/ticketing-issue.type';
import { PERSONNEL_STATUS_FILE_SUFFIX_MAP } from 'app/audit-hub/constants/audit-packages.constants';
import { ConnectionEntity } from 'app/companies/connections/entities/connection.entity';
import { ConnectionsRepository } from 'app/companies/connections/repositories/connections.repository';
import { TrainingType } from 'app/companies/entities/training-type.enum';
import { CompaniesCoreService } from 'app/companies/services/companies-core.service';
import { ComplianceCheckExclusion } from 'app/compliance-check-exclusions/entities/compliance-check-exclusion.entity';
import { getExclusionFiltersForPersonnel } from 'app/compliance-check-exclusions/helpers/compliance-check-exclusion.helper';
import { ComplianceCheckExclusionRepository } from 'app/compliance-check-exclusions/repositories/compliance-check-exclusion.repository';
import { ComplianceCheckExclusionsCoreService } from 'app/compliance-check-exclusions/services/compliance-check-exclusions-core.service';
import {
    getFormatedValue,
    isCustomFieldsEnabled,
} from 'app/custom-fields/helpers/custom-fields.helper';
import { CustomFieldsLocationsRepository } from 'app/custom-fields/repositories/custom-fields-locations.repository';
import { CustomFieldValueType } from 'app/custom-fields/types/custom-field-value.type';
import { DeviceDocument } from 'app/devices/entities/device-document.entity';
import { Device } from 'app/devices/entities/device.entity';
import { DevicesCoreService } from 'app/devices/services/devices-core.service';
import { EDRConnectionRepository } from 'app/edr/repositories/edr-connection.repository';
import { EdrCoreService } from 'app/edr/services/edr-core.service';
import { Ticket } from 'app/tickets/entities/ticket.entity';
import { TicketConnectionError } from 'app/tickets/errors/ticket-connection.error';
import { TicketsCoreService } from 'app/tickets/services/tickets-core.service';
import { TicketDataTemplate } from 'app/tickets/types/ticket-data-template.type';
import { TicketType } from 'app/tickets/types/ticket.type';
import { DocumentTypePrefix } from 'app/users/entities/user-document-type-prefix.map';
import { UserDocument } from 'app/users/entities/user-document.entity';
import { User } from 'app/users/entities/user.entity';
import { UserDocumentDownloadedEvent } from 'app/users/observables/events/user-document-downloaded.event';
import { PersonnelTemplateCoreService } from 'app/users/personnel/core/personnel-template/personnel-template.core.service';
import { PersonnelRequestDto } from 'app/users/personnel/dtos/personnel-request.dto';
import { ComplianceCheck } from 'app/users/personnel/entities/compliance-check.entity';
import { Personnel } from 'app/users/personnel/entities/personnel.entity';
import { ComplianceCheckRepository } from 'app/users/personnel/repositories/compliance-check.repository';
import { PersonnelTicketRepository } from 'app/users/personnel/repositories/personnel-ticket.repository';
import { PersonnelRepository } from 'app/users/personnel/repositories/personnel.repository';
import { ComplianceChecksCoreService } from 'app/users/personnel/services/compliance-checks-core.service';
import { PersonnelDetailsPDFFormatterHelper } from 'app/users/personnel/services/personnel-details-pdf-formatter.helper';
import { ComplianceCheckWithExclusion } from 'app/users/personnel/types/compliance-check-with-exclusion.type';
import { PersonnelDetailsPDF } from 'app/users/personnel/types/personnel-detail-pdf.type';
import { PolicyRepository } from 'app/users/policies/repositories/policy.repository';
import { UserIdentitiesCoreService } from 'app/users/services/user-identities-core.service';
import { BaseUserDataType } from 'app/users/types/base-user-data.type';
import { Account } from 'auth/entities/account.entity';
import { Entry } from 'auth/entities/entry.entity';
import { getProviderTypeClientTypeMap } from 'auth/helpers/provider-type.helper';
import { EntryCoreService } from 'auth/services/entry-core.service';
import { PolloAdapter } from 'commons/adapters/pollo.adapter';
import { AuditorPersonnelStatus } from 'commons/enums/auditors/auditor-personnel-status.enum';
import { ClientType } from 'commons/enums/auth/client-type.enum';
import {
    ProviderType,
    ProviderType as ProviderTypeEnum,
} from 'commons/enums/auth/provider-type.enum';
import { PersonnelTicketType } from 'commons/enums/personnel/personnel-ticket-type.enum';
import { TrainingCampaignType } from 'commons/enums/training-campaign-type.enum';
import { UserDocumentType } from 'commons/enums/users/user-document-type.enum';
import { ErrorCodeException } from 'commons/exceptions/error-code.exception';
import { isEmptyArray } from 'commons/helpers/array.helper';
import { isWithinHours, normalizeDate } from 'commons/helpers/date.helper';
import { getFilenameFromDirectory } from 'commons/helpers/download.helper';
import { shouldEmitEvent } from 'commons/helpers/event.helper';
import {
    allStrictFormerStatuses,
    getEmployeeStatusInformation,
    isOutOfScope,
} from 'commons/helpers/personnel.helper';
import { sanitizeFileName } from 'commons/helpers/upload.helper';
import { AppService } from 'commons/services/app.service';
import { CsvDataSetType } from 'commons/types/csv-data-set.type';
import { FileBufferTicketType, FileBufferType } from 'commons/types/file-buffer.type';
import { Downloader } from 'dependencies/downloader/downloader';
import { HtmlToPdfConverter } from 'dependencies/html-to-pdf-converter/html-to-pdf-converter';
import {
    chain,
    filter,
    flatMap,
    flatten,
    flow,
    get,
    groupBy,
    isEmpty,
    isEqual,
    isNil,
    keyBy,
    map,
    omit,
    reduce,
    remove,
} from 'lodash';
import { TenancyContext } from 'tenancy/contexts/tenancy.context';
import { EntityNotFoundError } from 'typeorm';

type PromiseSettled<T> = {
    status: 'rejected' | 'fulfilled';
    reason: any;
    value: T[];
};

@Injectable()
export class PersonnelOrchestrationService extends AppService {
    private readonly OUT_OF_SCOPE_STATES = [
        EmploymentStatus.OUT_OF_SCOPE,
        EmploymentStatus.FUTURE_HIRE,
        EmploymentStatus.SERVICE_ACCOUNT,
    ];
    private readonly OFFBOARDING_WITHIN_PERIOD = 24;
    private readonly UNUSED_COMPLIANCE_CHECK_TYPES = [
        ComplianceCheckType.LOCATION_SERVICES,
        ComplianceCheckType.AGENT_INSTALLED,
    ];

    constructor(
        private readonly tenancyContext: TenancyContext,
        private readonly companiesCoreService: CompaniesCoreService,
        private readonly complianceChecksCoreService: ComplianceChecksCoreService,
        private readonly complianceCheckExclusionsCoreService: ComplianceCheckExclusionsCoreService,
        private readonly devicesCoreService: DevicesCoreService,
        private readonly edrCoreService: EdrCoreService,
        private readonly personnelTemplateCoreService: PersonnelTemplateCoreService,
        private readonly htmlToPdfConverter: HtmlToPdfConverter,
        private readonly httpService: HttpService,
        private readonly downloader: Downloader,
        private readonly ticketsCoreService: TicketsCoreService,
        private readonly entryCoreService: EntryCoreService,
        private readonly userIdentitiesCoreService: UserIdentitiesCoreService,
    ) {
        super();
    }

    areConnectedToHRIS(userIds: number[]): Promise<{ userId: number; isConnected: boolean }[]> {
        return this.areConnectedUsersToProviderType(userIds, ProviderTypeEnum.HRIS);
    }

    async areConnectedUsersToProviderType(
        userIds: number[],
        providerType: ProviderTypeEnum,
    ): Promise<{ userId: number; isConnected: boolean }[]> {
        const userIdentitiesForMultipleUsers =
            await this.userIdentitiesCoreService.getUserIdentitiesByClientTypesAndUserIds(
                userIds,
                getProviderTypeClientTypeMap().get(providerType),
            );

        return userIdentitiesForMultipleUsers.map(userData => {
            return {
                userId: userData.userId,
                isConnected: !isEmptyArray(userData.userIdentities),
            };
        });
    }

    async createAdminEntryByEmail(dto: BaseUserDataType, account: Account): Promise<Entry> {
        const { email } = dto;

        let entry = await this.entryCoreService.getEntryByEmailNoFail(email);

        if (!isNil(entry)) {
            throw new ConflictException('User already exists');
        }

        entry = await this.entryCoreService.createEntryByEmail(email, account);

        return entry;
    }

    async findAllPersonnelByIds(ids: number[]): Promise<Personnel[]> {
        return this.personnelRepository.findAllByIds(ids);
    }

    async getPersonnelDetailsZip(
        originalPersonnel: Personnel,
        account: Account,
        requestorUser: User,
        personnelStatus?: AuditorPersonnelStatus,
    ): Promise<FileBufferType[]> {
        const { user } = originalPersonnel;

        // Use shared logic to get document types and documents to download
        const { documentsToDownload } = await this.getPersonnelDocumentsData(
            originalPersonnel,
            personnelStatus,
        );

        let offboardingTicketsFiles: FileBufferTicketType[] = [];
        if (isNil(personnelStatus)) {
            offboardingTicketsFiles = await this.getTicketsFiles(
                account,
                [originalPersonnel.id],
                requestorUser,
            );
        }

        const originalPersonnelUserId = originalPersonnel?.user?.id;
        if (isNil(originalPersonnelUserId)) {
            throw new Error(
                `Original personnel user Id not found for personnel ${originalPersonnel.id}`,
            );
        }
        const doneTickets = await this.personnelTicketRepository.getTicketsDoneForUserByType(
            originalPersonnelUserId,
            PersonnelTicketType.OFFBOARDING,
        );

        let personnelData: FileBufferType[] = [];
        try {
            personnelData = await this.personnelDetailPdfFile(
                originalPersonnel,
                documentsToDownload,
                account,
                doneTickets,
                personnelStatus,
            );
        } catch (error) {
            this.error(error, account, { user });
        }

        return isNil(personnelStatus)
            ? [...personnelData, ...offboardingTicketsFiles]
            : personnelData;
    }

    /**
     * Get personnel evidence list with signed URLs for existing documents and filenames for generated documents
     */
    async getPersonnelEvidenceList(
        originalPersonnel: Personnel,
        account: Account,
        requestorUser: User,
        personnelStatus: AuditorPersonnelStatus,
        includeFiles = false,
    ): Promise<
        Array<{
            filename: string;
            filepath?: string;
            signedUrl?: string;
            fileData?: string;
            fileType?: string;
        }>
    > {
        const { user } = originalPersonnel;

        // Use shared logic to get document types and documents to download
        const { documentsToDownload } = await this.getPersonnelDocumentsData(
            originalPersonnel,
            personnelStatus,
        );

        const evidenceList: Array<{
            filename: string;
            filepath?: string;
            signedUrl?: string;
            fileData?: string;
            fileType?: string;
        }> = [];

        const fileSuffix = PERSONNEL_STATUS_FILE_SUFFIX_MAP.get(personnelStatus);

        // Process documents concurrently to avoid await in loop
        const documentPromises = documentsToDownload
            .filter(doc => doc.file)
            .map(async doc => {
                const documentTypePrefix = DocumentTypePrefix.get(doc.type);
                const filename = documentTypePrefix + getFilenameFromDirectory(doc.file);

                try {
                    const { signedUrl } = await this.downloader.getDownloadUrl(doc.file);
                    return {
                        filename,
                        filepath: doc.file,
                        signedUrl,
                    };
                } catch (error) {
                    return {
                        filename,
                        filepath: doc.file,
                    };
                }
            });

        const documentResults = await Promise.allSettled(documentPromises);
        documentResults.forEach(result => {
            if (result.status === 'fulfilled') {
                evidenceList.push(result.value);
            }
        });

        if (includeFiles) {
            try {
                const originalPersonnelUserId = originalPersonnel?.user?.id;
                if (!isNil(originalPersonnelUserId)) {
                    const doneTickets =
                        await this.personnelTicketRepository.getTicketsDoneForUserByType(
                            originalPersonnelUserId,
                            PersonnelTicketType.OFFBOARDING,
                        );

                    // Generate personnel detail PDF with actual content using the same logic as getPersonnelDetailsZip
                    const personnelData = await this.personnelDetailPdfFile(
                        originalPersonnel,
                        documentsToDownload,
                        account,
                        doneTickets,
                        personnelStatus,
                    );

                    // Clear existing evidence list and add generated files with file data
                    evidenceList.length = 0; // Clear existing documents since we're generating everything
                    // Add generated files with file data
                    personnelData.forEach(fileBuffer => {
                        const filename =
                            fileBuffer.filename.replace('.pdf', '') + `${fileSuffix}.pdf`;
                        evidenceList.push({
                            filename,
                            fileData: fileBuffer.stream?.toString('base64'),
                            fileType: fileBuffer.fileType || 'application/pdf',
                        });
                    });

                    // Add offboarding tickets with actual files if applicable (same as getPersonnelDetailsZip)
                    if (isNil(personnelStatus)) {
                        const offboardingTicketsFiles = await this.getTicketsFiles(
                            account,
                            [originalPersonnel.id],
                            requestorUser,
                        );

                        offboardingTicketsFiles.forEach(ticketFile => {
                            evidenceList.push({
                                filename: ticketFile.filename,
                                fileData: ticketFile.stream?.toString('base64'),
                                fileType: 'application/pdf',
                            });
                        });
                    }
                }
            } catch (error) {
                this.error(error, account, { user });
                const personnelName = `${originalPersonnel.user?.firstName}_${originalPersonnel.user?.lastName}`;
                this.logger.warn(
                    PolloAdapter.acct(
                        `Failed to generate personnel detail PDF for ${personnelName}: ${error.message}`,
                        account,
                    ),
                );
            }
        } else {
            try {
                evidenceList.length = 0;

                const personnelDetailFileName = `${originalPersonnel.user?.firstName}_${originalPersonnel.user?.lastName}${fileSuffix}.pdf`;
                evidenceList.push({
                    filename: personnelDetailFileName,
                });

                documentsToDownload.forEach(document => {
                    const documentTypePrefix = DocumentTypePrefix.get(document.type);
                    const filename = documentTypePrefix + getFilenameFromDirectory(document.file);
                    evidenceList.push({
                        filename,
                        filepath: document.file,
                    });
                });

                if (isNil(personnelStatus)) {
                    try {
                        const ticketsFiles = await this.getTicketsFiles(
                            account,
                            [originalPersonnel.id],
                            requestorUser,
                        );

                        const ticketFileNames = ticketsFiles.map(ticketFile => ticketFile.filename);
                        evidenceList.push(...ticketFileNames.map(name => ({ filename: name })));
                    } catch (error) {
                        this.error(error, account, { user });
                    }
                }
            } catch (error) {
                this.error(error, account, { user });
                const personnelDetailFileName = `${originalPersonnel.user?.firstName}_${originalPersonnel.user?.lastName}${fileSuffix}.pdf`;
                evidenceList.push({
                    filename: personnelDetailFileName,
                });
            }
        }

        return evidenceList;
    }

    /**
     * Shared logic to get document types and documents to download for personnel
     */
    private async getPersonnelDocumentsData(
        originalPersonnel: Personnel,
        personnelStatus?: AuditorPersonnelStatus,
    ): Promise<{
        documentsToDownload: any[];
        usedDocTypes: UserDocumentType[];
    }> {
        const hiredDocTypes = [UserDocumentType.SEC_TRAINING];
        const formerDocTypes = [UserDocumentType.OFFBOARDING_EVIDENCE];
        const docTypes = [
            UserDocumentType.SEC_TRAINING,
            UserDocumentType.PASSWORD_MANAGER_EVIDENCE,
            UserDocumentType.AUTO_UPDATES_EVIDENCE,
            UserDocumentType.HARD_DRIVE_ENCRYPTION_EVIDENCE,
            UserDocumentType.ANTIVIRUS_EVIDENCE,
            UserDocumentType.LOCK_SCREEN_EVIDENCE,
            UserDocumentType.MFA_EVIDENCE,
        ];

        // Check for additional training types
        const isHipaaFrameworkEnabled = await this.companiesCoreService.getIsTrainingEnabled(
            TrainingType.HIPAA_TRAINING,
        );
        if (isHipaaFrameworkEnabled) {
            docTypes.push(UserDocumentType.HIPAA_TRAINING_EVIDENCE);
        }

        const isNistAiFrameworkEnabled = await this.companiesCoreService.getIsTrainingEnabled(
            TrainingType.NIST_AI_TRAINING,
        );
        if (isNistAiFrameworkEnabled) {
            docTypes.push(UserDocumentType.NIST_AI_TRAINING_EVIDENCE);
        }

        const isStrictFormer = allStrictFormerStatuses.includes(originalPersonnel.employmentStatus);
        if (isNil(personnelStatus) && isStrictFormer) {
            docTypes.push(UserDocumentType.OFFBOARDING_EVIDENCE);
        }

        // Determine which document types to use
        let usedDocTypes: UserDocumentType[] = [];
        if (personnelStatus === AuditorPersonnelStatus.HIRED) {
            usedDocTypes = hiredDocTypes;
        } else if (personnelStatus === AuditorPersonnelStatus.FORMER) {
            usedDocTypes = formerDocTypes;
        } else {
            usedDocTypes = docTypes;
        }

        // Get existing documents
        const documentsToDownload = await this.getDocumentsToDownload(
            originalPersonnel,
            usedDocTypes,
        );

        // Set security training compliance checks
        const personnelSecTrainingComplianceChecks =
            await this.personnelRepository.getTrainingComplianceChecksPersonnel(
                TrainingCampaignType.SECURITY_TRAINING,
                originalPersonnel.id,
            );
        originalPersonnel.securityTrainingComplianceChecks =
            personnelSecTrainingComplianceChecks?.securityTrainingComplianceChecks ?? [];

        return { documentsToDownload, usedDocTypes };
    }

    async createPolicyAcceptanceReport(
        dto: PersonnelRequestDto,
        account: Account,
        user: User,
        personnelIds?: number[],
    ): Promise<CsvDataSetType> {
        let personnelList: Personnel[] = [];
        let totalItems = 0;
        let page = 1;

        do {
            const personnelPage =
                // eslint-disable-next-line no-await-in-loop
                await this.personnelRepository.getPolicyAcceptanceReportPersonnelData(
                    dto,
                    page,
                    100,
                    personnelIds,
                );

            page++;
            totalItems = personnelPage?.total ?? 0;
            personnelList = personnelList.concat(personnelPage.data);
        } while (personnelList.length !== totalItems);

        const policies =
            await this.policyRepository.getActiveFrameworkPoliciesWithCurrentPublishedVersion();
        const sortedPolicies = policies.sort((policyA, policyB) =>
            policyA.name.localeCompare(policyB.name),
        );
        const todaysDate = new Date().toISOString().split('T')[0];

        const filename = `Personnel-Policy-Acknowledgment-${todaysDate}`;

        // Don't emit the event if report is for an audit
        if (!personnelIds) {
            this.sendUserDocumentDownloadedEvent(account, user, filename);
        }

        return {
            data: personnelList,
            filename,
            additionalData: { policies: sortedPolicies },
        };
    }

    sendUserDocumentDownloadedEvent(account: Account, user: User, filename: string): void {
        if (shouldEmitEvent(account, user)) {
            this._eventBus.publish(
                new UserDocumentDownloadedEvent(account, user, filename, {
                    name: filename,
                } as UserDocument),
            );
        }
    }

    async getTicketsFiles(
        account: Account,
        personnelIds: number[],
        requestorFullname: User,
    ): Promise<FileBufferTicketType[]> {
        const personnelTickets =
            await this.personnelTicketRepository.getPersonnelTicketsByPersonnelIdsAndType(
                personnelIds,
                PersonnelTicketType.OFFBOARDING,
            );

        const mappedPersonnelTickets = groupBy(personnelTickets, 'ticket.externalTicketId');

        const buildedTickets = await this.buildTicketsWithAttachments(
            account,
            map(personnelTickets, 'ticket'),
        );

        const settledFiles = await (Promise as any).allSettled(
            flatMap(buildedTickets, externalBuildedTicket => {
                const { ticketingIssueType, ticket } = externalBuildedTicket;
                const { externalTicketId } = ticket;
                const { attachments = [] } = ticketingIssueType;
                const personnel = mappedPersonnelTickets[externalTicketId];
                return flatMap(personnel, async ({ personnel: p }) => {
                    const formattedAttachments = attachments.map(
                        ({ stream: attachmentStream, fileName: attachmentFilename }) => ({
                            stream: attachmentStream,
                            fileName: `${externalTicketId}/${sanitizeFileName(attachmentFilename)}`,
                            ticket,
                            personnel: p,
                        }),
                    );

                    const { filename: fileName, stream } = await this.buildTicketPdf(
                        account,
                        requestorFullname,
                        externalBuildedTicket,
                    );
                    return [
                        ...formattedAttachments,
                        {
                            stream,
                            filename: `${externalTicketId}/${sanitizeFileName(fileName)}`,

                            ticket,
                            personnel: p,
                        },
                    ];
                });
            }),
        );

        return chain(settledFiles)
            .filter(({ status }) => status === 'fulfilled')
            .map(({ value }) => value)
            .flatten()
            .value();
    }

    async buildTicketPdf(
        account: Account,
        requestorUser: User,
        ticketType: TicketType,
    ): Promise<FileBufferType> {
        try {
            const externalTicketId = ticketType.ticket.externalTicketId;

            const template = this.personnelTemplateCoreService.getOffboardingTicketTemplate();

            const html = template(
                await this.getTicketTemplateData(ticketType, account, requestorUser),
            );

            if (isNil(html)) {
                throw new Error(`Html template not found for ticketType ${ticketType}`);
            }

            const { data: stream } = await this.htmlToPdfConverter.convertToPdfBuffer(
                html,
                account,
            );

            const fileName = sanitizeFileName(`${externalTicketId}.pdf`);

            return {
                stream,
                filename: fileName,
            };
        } catch (error) {
            this.error(error, account, { error });
            throw error;
        }
    }

    async getTicketTemplateData(
        ticketType: TicketType,
        account: Account,
        requestorUser: User,
    ): Promise<TicketDataTemplate> {
        const clientType = get(ticketType, 'ticket.connection.clientType', null);

        const ticketService = await this.ticketsCoreService.getTicketService(
            account,
            ticketType.ticket?.connection?.id,
        );

        if (isNil(ticketService)) {
            throw new Error(
                `Ticket service not found for connection ${ticketType.ticket?.connection?.id}`,
            );
        }

        if (
            ![
                ClientType.JIRA,
                ClientType.MERGEDEV_SERVICENOW,
                ClientType.MERGEDEV_JIRA_DATA_CENTER,
                ClientType.ASANA,
                ClientType.CLICKUP,
                ClientType.GITLAB_ISSUES,
                ClientType.*********************,
                ClientType.LINEAR,
                ClientType.MERGEDEV_AHA,
                ClientType.MERGEDEV_BASECAMP,
                ClientType.MERGEDEV_BITBUCKET,
                ClientType.MERGEDEV_TEAMWORK,
                ClientType.MERGEDEV_HIVE,
                ClientType.MERGEDEV_WRIKE,
                ClientType.MERGEDEV_FRESHDESK,
                ClientType.MERGEDEV_ZENDESK,
                ClientType.MERGEDEV_FRESHSERVICE,
                ClientType.MERGEDEV_HEIGHT,
                ClientType.MERGEDEV_FRONT,
                ClientType.AZURE_BOARDS,
            ].includes(clientType)
        ) {
            throw new Error('Client not supported');
        }

        return ticketService.getTicketData(ticketType, account, requestorUser).getTemplateData();
    }

    async getConnection(connectionId: number): Promise<ConnectionEntity | null> {
        let connection: ConnectionEntity | null = null;

        try {
            connection = await this.connectionRepository.getTicketingConnectionById(connectionId);
        } catch (error) {
            if (!(error instanceof EntityNotFoundError)) {
                throw new BadRequestException(
                    `Connection, designated by 'connectionId', must have provider type: ${ProviderType.TICKETING}`,
                );
            }
        }

        if (isNil(connection)) {
            throw new Error(`Connection not found for connectionId ${connectionId}`);
        }

        return connection;
    }

    async buildTicketsWithAttachments(account: Account, tickets: Ticket[]): Promise<TicketType[]> {
        if (isEmpty(tickets)) {
            return [];
        }

        const mappedTickets = groupBy(tickets, 'connection.id');
        const mappedConnections = chain(tickets).map('connection').keyBy('id').value();

        const externalTickets = await (Promise as any).allSettled(
            map(mappedTickets, (item, key) => {
                const connection = mappedConnections[key];
                const issueIds = map(item, 'externalTicketId');
                const ticketsIds = map(item, 'id');
                return this.getTicketsWithAttachments(
                    account,
                    issueIds,
                    connection.id,
                    connection,
                ).catch(e => {
                    tickets = filter(tickets, ({ id }) => !ticketsIds.includes(id));
                    throw new TicketConnectionError(e, connection);
                });
            }),
        );

        const mappedExternalTickets = this.handleExternalTickets(account, externalTickets);

        return !isEmpty(mappedExternalTickets)
            ? map(tickets, ticket => ({
                  ticket,
                  ticketingIssueType: mappedExternalTickets[ticket.externalTicketId] ?? null,
              }))
            : [];
    }

    async getTicketsWithAttachments(
        account: Account,
        issueIds: string[],
        connectionId: number,
        conn?: ConnectionEntity,
    ): Promise<TicketingIssueType[]> {
        try {
            const ticketService = await this.ticketsCoreService.getTicketService(
                account,
                connectionId,
                conn,
            );
            if (!ticketService) {
                throw new Error(`Ticket Service not found for connectionId ${connectionId}`);
            }
            return await ticketService.buildIssuesWithAttachments(issueIds);
        } catch (error) {
            this.error(error, account, { issueIds });
            throw new ServiceUnavailableException(error);
        }
    }

    handleExternalTickets(
        account: Account,
        externalTickets: PromiseSettled<TicketingIssueType>[],
    ): object {
        const { mappedExternalTickets, falsyConnections } = reduce(
            externalTickets,
            (acc, item) => {
                const { status, reason, value } = item;
                const invalidConnections: PromiseSettled<TicketingIssueType>[] = [];
                const ticketsForValidConnection: TicketingIssueType[] = [];
                if (isEqual(status, 'rejected')) {
                    const httpStatus = get(reason, 'e.status');
                    const connection = get(reason, 'connection');
                    const e = get(reason, 'e');
                    if (HttpStatus.SERVICE_UNAVAILABLE === httpStatus) {
                        invalidConnections.push(item);
                    }
                    this.logger.log(
                        PolloAdapter.acct('SDLC Service Unavailable', account).setIdentifier({
                            error: new ErrorCodeException(
                                'SDLC Service Unavailable',
                                HttpStatus.SERVICE_UNAVAILABLE,
                                ErrorCode.SDLC_SERVICE_UNAVAILABLE,
                            ),
                            connection: omit(connection, ['metadata']),
                            e,
                        }),
                    );
                } else {
                    ticketsForValidConnection.push(...flatten(value));
                }

                return {
                    ...acc,
                    mappedExternalTickets: {
                        ...acc.mappedExternalTickets,
                        ...keyBy(ticketsForValidConnection, 'ticketId'),
                    },
                    falsyConnections: [...acc.falsyConnections, ...invalidConnections],
                };
            },
            { mappedExternalTickets: {}, falsyConnections: [] },
        );

        if (falsyConnections.length === externalTickets.length) {
            throw new ErrorCodeException(
                'SDLC Service Unavailable',
                HttpStatus.SERVICE_UNAVAILABLE,
                ErrorCode.SDLC_SERVICE_UNAVAILABLE,
            );
        }
        return mappedExternalTickets;
    }

    async personnelDetailPdfFile(
        originalPersonnel: Personnel,
        documentsToDownload: any[],
        account: Account,
        doneTickets: Ticket[],
        personnelStatus?: AuditorPersonnelStatus,
    ): Promise<FileBufferType[]> {
        const customFieldsValues: CustomFieldValueType[] = [];
        const hasCustomFieldsEnabled = await isCustomFieldsEnabled(account);
        if (hasCustomFieldsEnabled) {
            const customFieldSubmission =
                await this.customFieldLocationRepository.getCustomFieldLocationsWithPersonnelSubmissions(
                    originalPersonnel.id,
                );

            if (!isNil(customFieldSubmission)) {
                const sectionData = customFieldSubmission.filter(
                    placement => placement.section === CustomFieldsSection.PERSONNEL_DETAILS,
                );

                sectionData?.forEach(section => {
                    const value = getFormatedValue({
                        type: section.customField.fieldType,
                        value: section.submission?.submission?.value ?? null,
                        code: section.customField.currency?.code,
                        options: section.customField.options,
                    });
                    const customFieldValue = {
                        name: section.customField.name,
                        submissionValue: section.submission?.submission?.value ? value : '-',
                    };
                    if (customFieldValue.submissionValue) {
                        customFieldsValues.push(customFieldValue);
                    }
                });
            }
        }
        let personnelData: PromiseSettledResult<FileBufferType>[] = [];

        if (isNil(personnelStatus)) {
            personnelData = await Promise.allSettled([
                this.getPersonnelDetailPdfFile(
                    account,
                    originalPersonnel,
                    documentsToDownload,
                    doneTickets,
                    customFieldsValues,
                ),
                ...documentsToDownload.map(document => this.getPersonnelDocument(document)),
            ]);
        }

        if (
            personnelStatus === AuditorPersonnelStatus.CURRENT ||
            personnelStatus === AuditorPersonnelStatus.HIRED
        ) {
            personnelData = await Promise.allSettled([
                this.getPersonnelDetailPdfFile(
                    account,
                    originalPersonnel,
                    documentsToDownload,
                    doneTickets,
                    customFieldsValues,
                    personnelStatus,
                ),
                ...documentsToDownload.map(document => this.getPersonnelDocument(document)),
            ]);
        }

        if (personnelStatus === AuditorPersonnelStatus.FORMER) {
            personnelData = await Promise.allSettled([
                this.getPersonnelDetailPdfFile(
                    account,
                    originalPersonnel,
                    documentsToDownload,
                    doneTickets,
                    customFieldsValues,
                    personnelStatus,
                ),
                ...documentsToDownload.map(document => this.getPersonnelDocument(document)),
            ]);
        }

        return (
            personnelData.filter(
                ({ status }) => status === 'fulfilled',
            ) as PromiseFulfilledResult<FileBufferType>[]
        ).map(({ value }) => value);
    }

    async getPersonnelDetailPdfFile(
        account: Account,
        originalPersonnel: Personnel,
        documentsToDownload: UserDocument[],
        doneTickets: Ticket[],
        customFieldsValues: CustomFieldValueType[],
        personnelStatus?: AuditorPersonnelStatus,
    ): Promise<FileBufferType> {
        const template = this.personnelTemplateCoreService.getPersonnelDetailsTemplateDelegate();

        const isEmploymentStatusOutOfScope = this.OUT_OF_SCOPE_STATES.includes(
            originalPersonnel.employmentStatus,
        );

        const isHuman = !isEmploymentStatusOutOfScope;

        const businessRationale = originalPersonnel.notHumanReason ?? '';

        const employeeStatus = isOutOfScope(originalPersonnel.employmentStatus)
            ? 'Out of Scope'
            : getEmployeeStatusInformation(originalPersonnel);

        const isEDRConnectionActive = await this.eDRConnectionRepository.isConnectionActive();

        const personnelDevices = await this.getPersonnelDevices(
            originalPersonnel,
            isEDRConnectionActive,
        );

        const currentExclusions = await this.getCurrentExclusionsForPersonnel([originalPersonnel]);

        const offboardingTicketsNames = doneTickets
            .map(ticket => ticket.externalTicketId)
            .join(', ');

        const isFormerPersonnel = allStrictFormerStatuses.includes(
            originalPersonnel.employmentStatus,
        );

        const isHipaaEnabled = await this.companiesCoreService.getIsTrainingEnabled(
            TrainingType.HIPAA_TRAINING,
        );

        const isNistEnabled = await this.companiesCoreService.getIsTrainingEnabled(
            TrainingType.NIST_AI_TRAINING,
        );

        const filterComplianceChecksFlowFunc = flow(
            complianceChecks => this.filterUnusedTypes(complianceChecks),
            complianceChecks => this.filterOffboarding(complianceChecks, isFormerPersonnel),
            complianceChecks => this.filterHipaaTraining(complianceChecks, isHipaaEnabled),
            complianceChecks => this.filterNistAiTraining(complianceChecks, isNistEnabled),
            complianceChecks =>
                this.filterIdentityMFA(complianceChecks, isEmploymentStatusOutOfScope),
            complianceChecks =>
                this.filterByAuditorPersonnelStatus(complianceChecks, personnelStatus),
        );

        const personnelComplianceChecks = filterComplianceChecksFlowFunc(
            originalPersonnel.complianceChecks,
        );
        if (isNil(originalPersonnel.user)) {
            throw new Error(`User not found for personnel ${originalPersonnel.id}`);
        }
        const { backgroundChecks } = originalPersonnel.user;

        const complianceCheckForPDF: PersonnelDetailsPDF[] =
            await this.personnelTemplateCoreService.mapPersonnelComplianceChecks(
                originalPersonnel,
                personnelComplianceChecks,
                documentsToDownload,
                currentExclusions,
                personnelDevices,
                backgroundChecks,
                doneTickets,
            );

        const isOffboardingWithinPeriod = isWithinHours(
            normalizeDate(originalPersonnel?.separationDate ?? undefined),
            this.OFFBOARDING_WITHIN_PERIOD,
        );

        const {
            fullComplianceCheck,
            acceptedPoliciesComplianceCheck,
            offboardingComplianceCheck,
            securityTrainingComplianceChecks,
        } = this.groupComplianceChecksBySection(complianceCheckForPDF);

        const showDashWhenFormer =
            allStrictFormerStatuses.includes(originalPersonnel.employmentStatus) &&
            isOffboardingWithinPeriod;

        const context = this.personnelTemplateCoreService.buildPersonnelDetailsTemplateContext(
            employeeStatus,
            originalPersonnel,
            fullComplianceCheck,
            acceptedPoliciesComplianceCheck,
            offboardingComplianceCheck,
            securityTrainingComplianceChecks,
            complianceCheckForPDF,
            businessRationale,
            isHuman,
            offboardingTicketsNames,
            showDashWhenFormer,
            isEDRConnectionActive,
            customFieldsValues,
        );

        this.personnelTemplateCoreService.addPersonnelDetailsTemplateHelpers();
        const html = template(context);
        if (isNil(html)) {
            throw new Error(`Html not found`);
        }

        const { data } = await this.htmlToPdfConverter.convertToPdfBuffer(html, account);
        const originalPersonnelUser = originalPersonnel?.user;
        if (isNil(originalPersonnelUser)) {
            throw new Error(`User not found for personnel ${originalPersonnel.id}`);
        }
        return {
            stream: data,
            filename: PersonnelDetailsPDFFormatterHelper.getFilename(originalPersonnelUser),
        };
    }

    async getPersonnelDocument(document: UserDocument): Promise<FileBufferType> {
        const documentTypePrefix = DocumentTypePrefix.get(document.type);

        const documents = await this.downloader.getDownloadUrl(document.file);
        if (isNil(documents.signedUrl)) {
            throw new Error(
                `Document signed Url not found for document ${document.id} file ${document.file}`,
            );
        }

        // document prefix is to avoid file overwriting
        // when there are two files with the same name
        const filename = documentTypePrefix + getFilenameFromDirectory(document.file);

        const file = await this.httpService
            .get<Buffer>(documents.signedUrl, { responseType: 'arraybuffer' })
            .toPromise();
        if (isNil(file)) {
            throw new Error(`Response not found for signed Url on document ${document.id}`);
        }
        return {
            stream: file.data,
            filename,
        };
    }

    async getDocumentsToDownload(
        personnel: Personnel,
        docTypes: UserDocumentType[],
    ): Promise<(UserDocument | DeviceDocument)[]> {
        const response: (UserDocument | DeviceDocument)[] = [];
        const filteredOutDeviceDocTypes = docTypes.filter(
            d => !DeviceDocument.getDocumentTypes().includes(d),
        );

        if (!isEmpty(filteredOutDeviceDocTypes)) {
            const userId = personnel?.user?.id;
            if (isNil(userId)) {
                throw new Error('User Id not found');
            }
            const filteredOutresponse = await this.complianceChecksCoreService.getAllDocuments(
                userId,
                filteredOutDeviceDocTypes,
            );

            response.push(...filteredOutresponse);
        }

        const filteredInDeviceDocTypes = docTypes.filter(d =>
            DeviceDocument.getDocumentTypes().includes(d),
        );

        if (!isEmpty(filteredInDeviceDocTypes)) {
            const filteredInresponse = await this.devicesCoreService.getAllDevicesDocuments(
                personnel.id,
                filteredInDeviceDocTypes,
            );

            response.push(...filteredInresponse);
        }

        return response;
    }

    filterUnusedTypes(complianceChecks: ComplianceCheck[]): ComplianceCheck[] {
        return complianceChecks.filter(
            ({ type }) => !this.UNUSED_COMPLIANCE_CHECK_TYPES.includes(type),
        );
    }

    filterOffboarding(
        complianceChecks: ComplianceCheck[],
        isFormerPersonnel: boolean,
    ): ComplianceCheck[] {
        if (isFormerPersonnel) {
            return complianceChecks;
        }
        return complianceChecks.filter(({ type }) => type !== ComplianceCheckType.OFFBOARDING);
    }

    filterHipaaTraining(
        complianceChecks: ComplianceCheck[],
        isHipaaEnabled: boolean,
    ): ComplianceCheck[] {
        if (isHipaaEnabled) {
            return complianceChecks;
        }
        return complianceChecks.filter(({ type }) => type !== ComplianceCheckType.HIPAA_TRAINING);
    }

    filterNistAiTraining(
        complianceChecks: ComplianceCheck[],
        isNistEnabled: boolean,
    ): ComplianceCheck[] {
        if (isNistEnabled) {
            return complianceChecks;
        }
        return complianceChecks.filter(({ type }) => type !== ComplianceCheckType.NIST_AI_TRAINING);
    }

    filterIdentityMFA(
        complianceChecks: ComplianceCheck[],
        isEmploymentStatusOutOfScope: boolean,
    ): ComplianceCheck[] {
        if (!isEmploymentStatusOutOfScope) {
            return complianceChecks;
        }
        return complianceChecks.filter(({ type }) => type !== ComplianceCheckType.IDENTITY_MFA);
    }

    filterByAuditorPersonnelStatus(
        complianceChecks: ComplianceCheck[],
        personnelStatus?: AuditorPersonnelStatus,
    ): ComplianceCheck[] {
        if (personnelStatus === AuditorPersonnelStatus.CURRENT) {
            return complianceChecks.filter(
                compliance =>
                    ![ComplianceCheckType.BG_CHECK, ComplianceCheckType.OFFBOARDING].includes(
                        compliance.type,
                    ),
            );
        }

        if (personnelStatus === AuditorPersonnelStatus.HIRED) {
            return complianceChecks.filter(compliance =>
                [
                    ComplianceCheckType.BG_CHECK,
                    ComplianceCheckType.SECURITY_TRAINING,
                    ComplianceCheckType.ACCEPTED_POLICIES,
                ].includes(compliance.type),
            );
        }

        if (personnelStatus === AuditorPersonnelStatus.FORMER) {
            return complianceChecks.filter(compliance =>
                [ComplianceCheckType.OFFBOARDING].includes(compliance.type),
            );
        }

        return complianceChecks;
    }

    groupComplianceChecksBySection(complianceCheckForPDF: PersonnelDetailsPDF[]): {
        fullComplianceCheck: PersonnelDetailsPDF[];
        acceptedPoliciesComplianceCheck: PersonnelDetailsPDF[];
        offboardingComplianceCheck: PersonnelDetailsPDF[];
        securityTrainingComplianceChecks: PersonnelDetailsPDF[];
    } {
        const fullComplianceCheck = remove(
            complianceCheckForPDF,
            complianceCheck => complianceCheck.type === ComplianceCheckType.FULL_COMPLIANCE,
        );

        const acceptedPoliciesComplianceCheck = remove(
            complianceCheckForPDF,
            complianceCheck => complianceCheck.type === ComplianceCheckType.ACCEPTED_POLICIES,
        );

        const offboardingComplianceCheck = remove(
            complianceCheckForPDF,
            complianceCheck => complianceCheck.type === ComplianceCheckType.OFFBOARDING,
        );

        const securityTrainingComplianceChecks = remove(complianceCheckForPDF, complianceCheck =>
            [
                ComplianceCheckType.SECURITY_TRAINING,
                ComplianceCheckType.HIPAA_TRAINING,
                ComplianceCheckType.NIST_AI_TRAINING,
            ].includes(complianceCheck.type),
        );

        return {
            fullComplianceCheck,
            acceptedPoliciesComplianceCheck,
            offboardingComplianceCheck,
            securityTrainingComplianceChecks,
        };
    }

    async getCurrentExclusionsForPersonnel(
        allPersonnel: Personnel[],
    ): Promise<ComplianceCheckExclusion[]> {
        const company = await this.companyRepository.findOneBy({});
        if (isNil(company)) {
            throw new Error(`Company not found`);
        }
        const options = getExclusionFiltersForPersonnel(allPersonnel, String(company.id));
        return this.complianceCheckExclusionRepository.findByOptions(options);
    }

    async getPersonnelDevices(
        originalPersonnel: Personnel,
        isEDRConnectionActive: boolean,
    ): Promise<Device[]> {
        let devices = await this.devicesCoreService.getDevicesByPersonnel(originalPersonnel);
        if (isEDRConnectionActive) {
            devices = await this.edrCoreService.addDeviceAgents(devices);
        }
        return devices;
    }

    async getComplianceChecksWithExclusionsForPersonnel(
        personnel: Personnel,
    ): Promise<ComplianceCheckWithExclusion[]> {
        let { complianceChecks } = personnel;

        if (isNil(complianceChecks)) {
            complianceChecks =
                await this.complianceCheckRepository.getComplianceChecksByPersonnel(personnel);
        }

        const complianceCheckExclusions = await this.getCurrentExclusionsForPersonnel([personnel]);

        return this.complianceChecksCoreService.getComplianceChecksWithExclusions(
            complianceCheckExclusions,
            complianceChecks,
        );
    }

    get companyRepository(): CompanyRepository {
        return this.tenancyContext.getCustomRepository(CompanyRepository);
    }

    get personnelRepository(): PersonnelRepository {
        return this.tenancyContext.getCustomRepository(PersonnelRepository);
    }

    get complianceCheckRepository(): ComplianceCheckRepository {
        return this.tenancyContext.getCustomRepository(ComplianceCheckRepository);
    }

    get personnelTicketRepository(): PersonnelTicketRepository {
        return this.tenancyContext.getCustomRepository(PersonnelTicketRepository);
    }

    get customFieldLocationRepository(): CustomFieldsLocationsRepository {
        return this.tenancyContext.getCustomRepository(CustomFieldsLocationsRepository);
    }

    get connectionRepository(): ConnectionsRepository {
        return this.tenancyContext.getCustomRepository(ConnectionsRepository);
    }

    get eDRConnectionRepository(): EDRConnectionRepository {
        return this.tenancyContext.getCustomRepository(EDRConnectionRepository);
    }

    get policyRepository(): PolicyRepository {
        return this.tenancyContext.getCustomRepository(PolicyRepository);
    }

    get complianceCheckExclusionRepository(): ComplianceCheckExclusionRepository {
        return this.tenancyContext.getCustomRepository(ComplianceCheckExclusionRepository);
    }
}

/* eslint-disable max-len */
import {
    AsyncActionType,
    ErrorCode,
    SocReportType,
    SortDir,
    SortType,
    VendorDataAccessedOrProcessed as VendorDataAccessedOrProcessedEnum,
} from '@drata/enums';
import { CustomFieldType } from '@drata/expression-evaluator';
import {
    BadRequestException,
    Injectable,
    InternalServerErrorException,
    NotFoundException,
} from '@nestjs/common';
import { AccessApplication } from 'app/access-review/entities/access-application.entity';
import { AccessApplicationRepository } from 'app/access-review/repositories/access-application.repository';
import { ConnectionEntity } from 'app/companies/connections/entities/connection.entity';
import { Product } from 'app/companies/products/entities/product.entity';
import { CustomFieldSubmissionRequestDto } from 'app/custom-fields/dtos/custom-field-submission-request.dto';
import { CustomFieldSubmissionsRequestDto } from 'app/custom-fields/dtos/custom-field-submissions-request.dto';
import { CustomFieldLocation } from 'app/custom-fields/entities/custom-field-location.entity';
import { CustomFieldSubmission } from 'app/custom-fields/entities/custom-field-submission.entity';
import { CustomField } from 'app/custom-fields/entities/custom-field.entity';
import { isCustomFieldsEnabled } from 'app/custom-fields/helpers/custom-fields.helper';
import { CustomFieldsLocationsRepository } from 'app/custom-fields/repositories/custom-fields-locations.repository';
import { CustomFieldsRepository } from 'app/custom-fields/repositories/custom-fields.repository';
import { CustomFieldsCoreService } from 'app/custom-fields/services/custom-fields-core.service';
import { CustomFieldsSubmissionCoreService } from 'app/custom-fields/services/custom-fields-submissions-core.service';
import { QuestionnairesCoreService } from 'app/questionnaires/questionnaires-core.service';
import { QuestionnaireVendorSecurityTypeformRepository } from 'app/questionnaires/repositories/questionnaire-vendor-security-typeform.repository';
import { LastQuestionnaireResponse } from 'app/questionnaires/types/last-questionnaire-response.type';
import { RiskVersion } from 'app/risk-management/entities/risk-version.entity';
import { userHasRiskManagerRestrictedView } from 'app/risk-management/helpers/risk-management.helper';
import { RiskRepository } from 'app/risk-management/repositories/risk.repository';
import { SchedulesQuestionnairesCoreService } from 'app/schedules/services/schedules-questionnaires-core.service';
import { ScheduleVendorQuestionnairesCriteriaType } from 'app/schedules/types/schedule-vendor-questionnaires-criteria-type';
import { DEFAULT_TC_WORKSPACE_ID } from 'app/trust-center/trust-center.constants';
import { UserRole } from 'app/users/entities/user-role.entity';
import { User } from 'app/users/entities/user.entity';
import { UsersCoreService } from 'app/users/services/users-core.service';
import { VendorDtoAdapter } from 'app/users/vendors/adapters/vendor-dto.adapter';
import { VendorBulkUpdateCsvValidation } from 'app/users/vendors/classes/vendor-bulk-update-csv-validation.class';
import { VendorCsvConstants } from 'app/users/vendors/classes/vendor-csv-constants.class';
import { VendorCsvFileRecord } from 'app/users/vendors/classes/vendor-csv-file-record.class';
import { VendorReportReview } from 'app/users/vendors/classes/vendor-report-review.class';
import { VendorReviewFactory } from 'app/users/vendors/classes/vendor-review-factory.class';
import { SocReportLabel } from 'app/users/vendors/configs/soc-report-label.config';
import { VendorDiscoveredThroughConnectionConfig } from 'app/users/vendors/configs/vendor-discovered-through-connection.config';
import { VendorsBulkMaxLengthMap } from 'app/users/vendors/configs/vendors-bulk-max-length-map.config';
import { CustomFieldSort } from 'app/users/vendors/dtos/custom-field-sort-request.dto';
import { VendorFeatureDismissalRequestDto } from 'app/users/vendors/dtos/vendor-feature-dismissal-request.dto';
import { VendorModifyRequestDto } from 'app/users/vendors/dtos/vendor-modify-request.dto';
import { VendorRequestDto } from 'app/users/vendors/dtos/vendor-request.dto';
import { VendorReviewLocationRequestDto } from 'app/users/vendors/dtos/vendor-review-location-request.dto';
import { VendorReviewRequestDto } from 'app/users/vendors/dtos/vendor-review-request.dto';
import { VendorReviewServiceRequestDto } from 'app/users/vendors/dtos/vendor-review-service-request.dto';
import { VendorsBulkRequestDto } from 'app/users/vendors/dtos/vendors-bulk-request.dto';
import { VendorsRequestDto } from 'app/users/vendors/dtos/vendors-request.dto';
import { VendorsStatsRequestDto } from 'app/users/vendors/dtos/vendors-stats-request.dto';
import { VendorStatusRequestDto } from 'app/users/vendors/dtos/vendors-update-status-request.dto';
import { VendorsV2RequestDto } from 'app/users/vendors/dtos/vendors-v2-request.dto';
import { AccountEventControl } from 'app/users/vendors/entities/account-event-control.entity';
import { VendorDiscovered } from 'app/users/vendors/entities/vendor-discovered.entity';
import { VendorDocument } from 'app/users/vendors/entities/vendor-document.entity';
import { VendorFeatureDismissal } from 'app/users/vendors/entities/vendor-feature-dismissal.entity';
import { VendorReviewFinding } from 'app/users/vendors/entities/vendor-review-finding.entity';
import { VendorReviewLocation } from 'app/users/vendors/entities/vendor-review-location.entity';
import { VendorReviewService } from 'app/users/vendors/entities/vendor-review-service.entity';
import { VendorReviewTrustServiceCategoryMap } from 'app/users/vendors/entities/vendor-review-trust-service-category-map.entity';
import { VendorReviewUserControl } from 'app/users/vendors/entities/vendor-review-user-control.entity';
import { VendorReview } from 'app/users/vendors/entities/vendor-review.entity';
import { VendorSetting } from 'app/users/vendors/entities/vendor-setting.entity';
import { VendorWithCustomFields } from 'app/users/vendors/entities/vendor-with-custom-fields';
import { Vendor } from 'app/users/vendors/entities/vendor.entity';
import { VendorBulkUpdateExclusionReason } from 'app/users/vendors/enums/vendor-bulk-update-exclusion-reason.enum';
import { VendorCriticality } from 'app/users/vendors/enums/vendor-criticality.enum';
import { VendorDocumentTypeEnum } from 'app/users/vendors/enums/vendor-document-type.enum';
import { VendorFeatureDismissalType } from 'app/users/vendors/enums/vendor-feature-dismissal-type.enum';
import { VendorFileHeaderEnum } from 'app/users/vendors/enums/vendor-file-header.enum';
import { VendorImpactLevelForStats } from 'app/users/vendors/enums/vendor-impact-level-for-stats.enum';
import { VendorImpactLevel } from 'app/users/vendors/enums/vendor-impact-level.enum';
import { VendorPasswordPolicy } from 'app/users/vendors/enums/vendor-password-policy.enum';
import { VendorReviewServiceCategory } from 'app/users/vendors/enums/vendor-review-service-category.enum';
import { VendorSecurityReviewDocumentsType } from 'app/users/vendors/enums/vendor-security-review-documents-type.enum';
import { VendorStatsScope } from 'app/users/vendors/enums/vendor-stats-scope.enum';
import {
    csvToJson,
    extractCustomFields,
    jsonToCsv,
    modifyKeys,
} from 'app/users/vendors/helpers/csv.helper';
import { getAmountOrDefault } from 'app/users/vendors/helpers/get-amount-or-default.helper';
import { getListOfStringValuesFromEnum } from 'app/users/vendors/helpers/get-list-of-strings-values-from-enum.helper';
import {
    validateBooleanValue,
    validateCost,
    validatePasswordMinLength,
    validateVendorCategory,
    validateVendorImpactLevel,
    validateVendorPasswordPolicy,
    validateVendorRisk,
    validateVendorStatus,
    validateVendorType,
} from 'app/users/vendors/helpers/vendors-csv-validation.helper';
import {
    getVendorDiscoveredNameFromType,
    isTprmProActive,
} from 'app/users/vendors/helpers/vendors.helper';
import { VendorListOptions } from 'app/users/vendors/interfaces/vendor-list-options';
import { VendorCreatedEvent } from 'app/users/vendors/observables/events/vendor-created.event';
import { VendorDeletedEvent } from 'app/users/vendors/observables/events/vendor-deleted.event';
import { VendorSharedAccountUpdatedEvent } from 'app/users/vendors/observables/events/vendor-shared-account-updated.event';
import { VendorStatusUpdatedEvent } from 'app/users/vendors/observables/events/vendor-status-updated.event';
import { VendorUpdatedEvent } from 'app/users/vendors/observables/events/vendor-updated.event';
import { VendorDataAccessedOrProcessedRepository } from 'app/users/vendors/repositories/vendor-data-accessed-or-processed.repository';
import { VendorDiscoveredRepository } from 'app/users/vendors/repositories/vendor-discovered.repository';
import { VendorDocumentRepository } from 'app/users/vendors/repositories/vendor-document.repository';
import { VendorReviewRepository } from 'app/users/vendors/repositories/vendor-review.repository';
import { VendorSecurityReviewDocumentRepository } from 'app/users/vendors/repositories/vendor-security-review-document.repository';
import { VendorSecurityReviewRepository } from 'app/users/vendors/repositories/vendor-security-review.repository';
import { VendorRepository } from 'app/users/vendors/repositories/vendor.repository';
import { VendorsDocumentsService } from 'app/users/vendors/services/vendors-documents.service';
import { VendorBulkCreatedResponse } from 'app/users/vendors/types/vendor-bulk-created-response.type';
import { VendorBulkUpdateDescription } from 'app/users/vendors/types/vendor-bulk-update-description.type';
import { VendorBulkUpdatedResponse } from 'app/users/vendors/types/vendor-bulk-updated-response.type';
import { VendorModifyRequestType } from 'app/users/vendors/types/vendor-modify-request.type';
import { VendorMonthlyDigest } from 'app/users/vendors/types/vendor-monthly-digest.type';
import { VendorResponse } from 'app/users/vendors/types/vendor-response.type';
import { VendorReviewCategoryType } from 'app/users/vendors/types/vendor-review-category.type';
import { VendorReviewFindingType } from 'app/users/vendors/types/vendor-review-finding.type';
import { VendorReviewUserControlType } from 'app/users/vendors/types/vendor-review-user-control.type';
import { VendorStatsFilters } from 'app/users/vendors/types/vendor-stats-filters.type';
import { VendorToDiscover } from 'app/users/vendors/types/vendor-to-discover.type';
import { VendorsBulkUpdateExclusionValidation } from 'app/users/vendors/types/vendors-bulk-update-exclusion-validation.type';
import { VendorsCsvExportFlags } from 'app/users/vendors/types/vendors-csv-export-flags.type';
import { VendorStatsItem } from 'app/users/vendors/types/vendors-stats-item.type';
import { VendorsStats } from 'app/users/vendors/types/vendors-stats.type';
import { VendorsCustomFieldsService } from 'app/users/vendors/vendors-custom-fields.service';
import { VendorsConstants } from 'app/users/vendors/vendors.constants';
import { VendorRisksData } from 'app/vendors/repositories/vendors-risks.data';
import { VendorData, VendorFilterOptions } from 'app/vendors/repositories/vendors.data';
import { vendorReportDownloadWorkflowV1 } from 'app/worker/workflows';
import { Account } from 'auth/entities/account.entity';
import { AccountEventService } from 'auth/services/account-event.service';
import { AccountsCoreService } from 'auth/services/accounts-core.service';
import { SharedAccountService } from 'auth/services/shared-accounts.service';
import { SharedAccountEventType } from 'auth/types/shared-account-event.type';
import axios from 'axios';
import { isEmail, isURL } from 'class-validator';
import { EmailConfig } from 'commons/configs/email.config';
import { csvMaxFileSize } from 'commons/configs/upload.config';
import { ExtractedRequestProps } from 'commons/decorators/get-request-notifier-props.decorator';
import { TenancyTransaction } from 'commons/decorators/transaction';
import { CustomFieldFiltersRequestDto } from 'commons/dtos/custom-field-filters-request.dto';
import { CustomRangeFieldFiltersRequestDto } from 'commons/dtos/custom-range-field-filters-request.dto';
import { AccountEntitlementType } from 'commons/enums/account-entitlement-type.enum';
import { ClientType } from 'commons/enums/auth/client-type.enum';
import { CustomFieldsEntityType } from 'commons/enums/custom-fields-entity-type.enum';
import { CustomFieldsSortableType } from 'commons/enums/custom-fields-sortable.type.enum';
import { MimeType } from 'commons/enums/mime-type.enum';
import { NextReviewDeadlineStatus } from 'commons/enums/next-review-deadline-status.enum';
import { RenewalScheduleType } from 'commons/enums/renewal-schedule.enum';
import { RenewalDateStatus } from 'commons/enums/renewal-status.enum';
import { SecurityReviewStatus } from 'commons/enums/security-review-status-enum';
import { UploadType } from 'commons/enums/upload-type.enum';
import { Role } from 'commons/enums/users/role.enum';
import { TrustCenterProvider } from 'commons/enums/vendor/trust-center-provider.enum';
import { VendorCategory, VendorCategoryLabels } from 'commons/enums/vendor/vendor-category.enum';
import { VendorDiscoveredSource } from 'commons/enums/vendor/vendor-discovered-source.enum';
import {
    VendorRisk,
    VendorRiskColors,
    VendorRiskLabels,
} from 'commons/enums/vendor/vendor-risk.enum';
import { VendorSecurityReviewStatus } from 'commons/enums/vendor/vendor-security-review-status.enum';
import { VendorSecurityReviewType } from 'commons/enums/vendor/vendor-security-review-type.enum';
import { VendorStatus } from 'commons/enums/vendor/vendor-status.enum';
import { VendorType } from 'commons/enums/vendor/vendor-type.enum';
import { ConflictException } from 'commons/exceptions/conflict.exception';
import { asyncForEach } from 'commons/helpers/array.helper';
import { fileNameDate } from 'commons/helpers/date.helper';
import { promiseAllSettledInBatches } from 'commons/helpers/promise.helper';
import { isStringABoolean } from 'commons/helpers/string.helper';
import { getTemporalClient } from 'commons/helpers/temporal/client';
import { sanitizeFileName } from 'commons/helpers/upload.helper';
import { ensureSecureUrl, getDomain } from 'commons/helpers/url.helper';
import { fullName, hasRole } from 'commons/helpers/user.helper';
import { AppService } from 'commons/services/app.service';
import { EmailService } from 'commons/services/email.service';
import { WorkspacesBaseService } from 'commons/services/workspaces-base.service';
import { CsvDataSetType } from 'commons/types/csv-data-set.type';
import { PaginationType } from 'commons/types/pagination.type';
import { renewalScheduleMonthValues } from 'commons/types/renewal-schedule-month-values.enum';
import {
    SearchFilterOptions,
    SearchFilterSortOrder,
} from 'commons/types/search-filter-options.type';
import { UploadedFileType } from 'commons/types/uploaded-file.type';
import config from 'config';
import { Downloader } from 'dependencies/downloader/downloader';
import { DownloaderPayloadType } from 'dependencies/downloader/types/downloader-payload.interface';
import { HtmlToPdfConverter } from 'dependencies/html-to-pdf-converter/html-to-pdf-converter';
import { Uploader } from 'dependencies/uploader/uploader';
import { FeatureFlagCategory } from 'feature-flags/enums/feature-flag-category.enum';
import { FeatureFlag } from 'feature-flags/feature-flag.enum';
import { FeatureFlagService } from 'feature-flags/feature-flag.service';
import { FileValidationResults } from 'file-validation/classes/file-validation-results.class';
import { FileValidationService } from 'file-validation/services/file-validation.service';
import { FileValidationBuilder } from 'file-validation/validations/base/file-validation.builder';
import fs from 'fs';
import hbs from 'hbs';
import {
    chunk,
    cloneDeep,
    compact,
    each,
    first,
    get,
    isEmpty,
    isNil,
    isNumber,
    map,
    parseInt,
    toUpper,
    trim,
} from 'lodash';
import moment from 'moment';
import { Span } from 'nestjs-ddtrace';
import path from 'path';
import { PolloMessage } from 'pollo-logger/classes/pollo.message';
import { In, IsNull, Not, Repository } from 'typeorm';

@Injectable()
export class VendorsService extends AppService {
    /**
     * Setters for saving vendor review details
     */
    private readonly VENDOR_REVIEW_SETTERS = [
        this.setTrustServiceCategories,
        this.setFindings,
        this.setUserControls,
        this.setServices,
        this.setLocations,
    ] as const;

    /**
     * @param connection connection
     */
    constructor(
        private readonly usersCoreService: UsersCoreService,
        private readonly customFieldsSubmissionCoreService: CustomFieldsSubmissionCoreService,
        private readonly vendorsCustomFieldsService: VendorsCustomFieldsService,
        private readonly uploader: Uploader,
        private readonly downloader: Downloader,
        private readonly htmlToPdfConverter: HtmlToPdfConverter,
        private readonly accountsCoreService: AccountsCoreService,
        private readonly sharedAccountService: SharedAccountService,
        private readonly accountEventService: AccountEventService,
        private readonly featureFlagService: FeatureFlagService,
        private readonly fileValidationService: FileValidationService,
        private readonly schedulesQuestionnairesCoreService: SchedulesQuestionnairesCoreService,
        private readonly emailService: EmailService,
        private readonly emailConfig: EmailConfig,
        private readonly questionnairesCoreService: QuestionnairesCoreService,
        private readonly vendorV2Repository: VendorData,
        private readonly vendorRisksRepository: VendorRisksData,
        private readonly workspaceBaseService: WorkspacesBaseService,
        private readonly vendorsDocumentsService: VendorsDocumentsService,
        private readonly customFieldsCoreService: CustomFieldsCoreService,
    ) {
        super();
    }

    /**
     * @deprecated Use VendorsCoreService.getVendor
     *
     * @param number id
     */
    getVendor(id: number, withSecurityReviews = true): Promise<Vendor> {
        return this.vendorRepository.findOneByVendorIdOrFail(id, withSecurityReviews);
    }

    /**
     *
     * @param number id
     */
    async getVendorWithQuestionnaire(id: number): Promise<Vendor> {
        const vendor = await this.vendorRepository.findOneByVendorIdOrFail(id, true);
        const lastQuestionnaire =
            await this.questionnairesCoreService.listLastQuestionnaireByVendorIds([id]);

        if (lastQuestionnaire.length > 0) {
            vendor.lastQuestionnaire = lastQuestionnaire[0];
        }

        return vendor;
    }

    async getVendorDetails(account: Account, id: number): Promise<VendorResponse> {
        // find the vendor by id
        const vendor: VendorResponse = await this.vendorRepository.findOneByVendorIdOrFail(id);

        const { riskCount, risksWithAnticipatedCompletionDateCount } =
            await this.riskRepository.getVendorRiskCount(vendor.id);
        vendor.riskCount = riskCount;
        vendor.risksWithAnticipatedCompletionDateCount = risksWithAnticipatedCompletionDateCount;

        if (!isNil(vendor.sharedAccountId) && !isNil(vendor.url)) {
            try {
                const domain =
                    !isEmpty(vendor.url) && !isNil(vendor.url) ? getDomain(vendor.url) : '';
                const sharedAccount = await this.sharedAccountService.getSharedAccountById(
                    vendor.sharedAccountId,
                    domain,
                );
                if (!isNil(sharedAccount)) {
                    vendor.account = sharedAccount.account;
                }
            } catch (error) {
                this.error(error, account, error);
            }
        }

        // find the most recent security review that is in progress
        const latestActiveSecurityReview = await this.vendorSecurityReviewRepository.findOne({
            where: {
                vendor: { id: vendor.id },
                status: In([
                    VendorSecurityReviewStatus.IN_PROGRESS,
                    VendorSecurityReviewStatus.NOT_YET_STARTED,
                ]),
                type: VendorSecurityReviewType.SECURITY,
            },
            relations: {
                securityReviewDocuments: true,
            },
            order: {
                createdAt: 'DESC',
            },
        });

        vendor.latestActiveSecurityReview = latestActiveSecurityReview;
        return vendor;
    }

    async listVendorsData(
        account: Account,
        user: User,
        dto: VendorsV2RequestDto,
    ): Promise<PaginationType<VendorResponse>> {
        const hasRiskManagerRestrictedView = await this.getHasRiskManagerRestrictedView(
            account,
            user,
        );

        const workspaceId = await this.workspaceBaseService.getPrimaryProductId();

        // vendorsIds will be undefined if hasRiskManagerRestrictedView is false
        const vendorIds = await this.vendorRisksRepository.getRisksVendors(
            account.id,
            workspaceId,
            hasRiskManagerRestrictedView,
            user.id,
        );

        const options = this.buildSearchOptions(dto);

        this.getSearchFilters(dto, options, vendorIds);
        this.buildReviewDeadlineAtFilter(options, dto.nextReviewDeadlineStatus);
        this.getSearchQuery(options, dto);

        const { data: vendorsSummary, ...response } =
            await this.vendorV2Repository.filterVendorDocuments(account.id, workspaceId, options);

        if (isEmpty(vendorsSummary)) {
            return {
                data: [],
                total: 0,
                page: dto.page ?? config.get('pagination.page'),
                limit: dto.limit ?? config.get('pagination.limit'),
            };
        }

        const isListVendorPerformanceFlagEnabled =
            await this.isListVendorPerformanceEnabled(account);

        const vendors = (await this.vendorRepository.getVendorsByIds(
            vendorsSummary.map(vendor => Number(vendor.vendorId)),
            account,
            isListVendorPerformanceFlagEnabled,
        )) as VendorResponse[];

        const customFields = await this.customFieldsCoreService.getCustomFieldsForFacets(
            CustomFieldsEntityType.VENDOR,
        );

        const customFieldsSubmissions = vendorsSummary.reduce((acc, vendor) => {
            acc[vendor.vendorId] = customFields.reduce(
                (arr: CustomFieldSubmission[], customField) => {
                    const value = vendor.customFields?.[customField.name]?.value;
                    if (isNil(value)) {
                        return arr;
                    }

                    arr.push({
                        submission: {
                            value,
                            fieldType: customField.fieldType,
                        },
                        customFieldLocation: {
                            customField,
                        },
                    } as CustomFieldSubmission);

                    return arr;
                },
                [],
            );
            return acc;
        }, {});

        each(vendors, vendor => {
            vendor.customFieldSubmission = customFieldsSubmissions[vendor.id];
        });

        return { ...response, data: vendors };
    }

    private buildSearchOptions(dto: VendorsV2RequestDto): SearchFilterOptions {
        const page = dto.page ?? config.get('pagination.page');
        const limit = dto.limit ?? config.get('pagination.limit');
        const sortOptions =
            dto.sort && dto.sortDir
                ? [{ field: SortType[dto.sort], order: dto.sortDir }]
                : VendorData.defaultSortClause;

        const searchOptions: SearchFilterOptions = this.buildPaginationAndSorting(
            sortOptions,
            page,
            limit,
            dto.customFieldSort,
        );

        return searchOptions;
    }

    private getSearchFilters(
        dto: VendorsV2RequestDto,
        searchOptions: SearchFilterOptions,
        vendorIds: number[] | undefined,
    ) {
        const {
            risk,
            securityReviewStatus,
            category,
            impactLevel,
            status,
            type,
            isSubProcessor,
            securityOwner,
            customFields,
            customRangeFields,
        } = dto;

        const filters: Record<string, any> = {};
        const rangeFilters: Record<string, any> = {};

        if (!isNil(vendorIds)) {
            filters['vendorId'] = vendorIds;
        }

        if (!isNil(risk)) {
            filters['risk.keyword'] = VendorRisk[risk];
        }
        if (!isNil(securityReviewStatus)) {
            if (securityReviewStatus === SecurityReviewStatus.NO_PAST_REVIEW) {
                filters['securityReviewStatus.keyword'] = '';
            } else {
                filters['securityReviewStatus.keyword'] =
                    SecurityReviewStatus[securityReviewStatus];
            }
        }
        if (!isNil(category)) {
            if (category === VendorCategory.NONE) {
                filters['category.keyword'] = '';
            } else {
                filters['category.keyword'] = VendorCategory[category];
            }
        }
        if (!isNil(impactLevel)) {
            if (impactLevel === VendorImpactLevel.UNSCORED) {
                filters['impactLevel.keyword'] = '';
            } else {
                filters['impactLevel.keyword'] = VendorImpactLevel[impactLevel];
            }
        }
        if (!isNil(status)) {
            if (status === VendorStatus.NONE) {
                filters['status.keyword'] = '';
            } else {
                filters['status.keyword'] = VendorStatus[status];
            }
        }
        if (!isNil(type)) {
            if (type === VendorType.NONE) {
                filters['type.keyword'] = '';
            } else {
                filters['type.keyword'] = VendorType[type];
            }
        }
        if (!isNil(isSubProcessor)) {
            filters['isSubProcessor'] = isSubProcessor ? true : false;
        }
        if (!isNil(securityOwner)) {
            filters['securityOwner.keyword'] = securityOwner;
        }
        if (!isEmpty(customFields) || !isEmpty(customRangeFields)) {
            const mustClauses = this.buildCustomFieldsFilterQuery(customFields, customRangeFields);

            if (mustClauses) {
                searchOptions.mustClause = { must: mustClauses };
            }
        }

        searchOptions.rangeFilters = rangeFilters;
        searchOptions.filters = filters;
    }

    buildCustomFieldsFilterQuery(
        customFields: CustomFieldFiltersRequestDto[] = [],
        customRangeFields: CustomRangeFieldFiltersRequestDto[] = [],
    ): any {
        const mustClauses: any[] = [];

        customFields.forEach(customField => {
            const fieldPath = this.getCustomFieldPath(customField.name, customField.type);

            if (Array.isArray(customField.value)) {
                mustClauses.push({
                    terms: {
                        [fieldPath]: customField.value,
                    },
                });
            } else {
                mustClauses.push({
                    term: {
                        [fieldPath]: customField.value,
                    },
                });
            }
        });

        customRangeFields.forEach(customRangeField => {
            const fieldPath = this.getCustomFieldPath(customRangeField.name, customRangeField.type);

            const rangeQuery: any = {
                range: {
                    [fieldPath]: {},
                },
            };

            if (!isNil(customRangeField.range.gte)) {
                rangeQuery.range[fieldPath].gte = customRangeField.range.gte;
            }

            if (!isNil(customRangeField.range.lte)) {
                rangeQuery.range[fieldPath].lte = customRangeField.range.lte;
            }

            mustClauses.push(rangeQuery);
        });

        return mustClauses;
    }

    private buildReviewDeadlineAtFilter(
        searchOptions: SearchFilterOptions,
        nextReviewDeadlineStatus: NextReviewDeadlineStatus,
    ) {
        switch (nextReviewDeadlineStatus) {
            case NextReviewDeadlineStatus.NO_RENEWAL:
                searchOptions.mustClause = {
                    must_not: { exists: { field: 'reviewDeadlineAt' } },
                };
                return;
            case NextReviewDeadlineStatus.OVERDUE:
                searchOptions.rangeFilters = { reviewDeadlineAt: { lt: 'now' } };
                return;
            case NextReviewDeadlineStatus.DUE_SOON:
                searchOptions.rangeFilters = { reviewDeadlineAt: { gte: 'now', lte: 'now+7d' } };
                return;
        }
    }

    private getSearchQuery(searchOptions: SearchFilterOptions, dto: VendorsV2RequestDto) {
        const { q } = dto;

        if (searchOptions && !isEmpty(q)) {
            const isQNan = isNaN(Number(q));
            const isQDate = moment(q, 'YYYY-MM-DD', true).isValid();

            if (!isQNan) {
                searchOptions.shouldClause = {
                    vendorId: q,
                    riskCount: q,
                };
                searchOptions.queryStringFilters = {
                    query: q,
                    fields: ['customFields.*.value'],
                    lenient: true,
                };
            } else if (isQDate) {
                searchOptions.shouldClause = {
                    renewalDate: q,
                    requestedAt: q,
                    reviewDeadlineAt: q,
                    updatedAt: q,
                };
            } else {
                const searchFields = [
                    'name',
                    'securityOwner',
                    'securityReviewStatus',
                    'risk',
                    'impactLevel',
                    'type',
                    'status',
                    'scheduledQuestionnaireStatus',
                    'category',
                ];

                searchOptions.queryStringFilters = {
                    query: q,
                    fields: ['customFields.*.value'],
                    lenient: true,
                    boost: 3,
                };
                searchOptions.searchFields = searchFields;
                searchOptions.searchQuery = q;
            }
        }
    }

    private getVendorSortField(field: string): string {
        switch (toUpper(field)) {
            case SortType[SortType.NAME]:
                return 'name.keyword';
            case SortType[SortType.UPDATED_AT]:
                return 'updatedAt';
            case SortType[SortType.REVIEW_STATUS]:
                return 'securityReviewStatus.keyword';
            case SortType[SortType.REVIEW_DEADLINE]:
                return 'reviewDeadlineAt';
            case SortType[SortType.RISK]:
                return 'risk.keyword';
            case SortType[SortType.STATUS]:
                return 'status.keyword';
            case SortType[SortType.IMPACT_LEVEL]:
                return 'impactLevel.keyword';
            case SortType[SortType.SUBPROCESSOR]:
                return 'isSubProcessor';
            case SortType[SortType.TYPE]:
                return 'type.keyword';
            case SortType[SortType.CATEGORY]:
                return 'category.keyword';
            case SortType[SortType.RENEWAL_DATE]:
                return 'renewalDate';
            case SortType[SortType.RISK_ASSOCIATED]:
                return 'riskCount';
            default:
                return field;
        }
    }

    private getRiskSortField(field: string): string {
        switch (toUpper(field)) {
            case SortType[SortType.IDENTIFIED_DATE]:
                return 'identifiedAt';
            case SortType[SortType.RISK_TITLE]:
                return 'name.keyword';
            case SortType[SortType.RISK_RESIDUAL_IMPACT]:
                return 'residualImpact';
            case SortType[SortType.RISK_RESIDUAL_LIKELIHOOD]:
                return 'residualLikelihood';
            case SortType[SortType.RISK_RESIDUAL_SCORE]:
                return 'residualScore';
            case SortType[SortType.RISK_SCORE]:
                return 'score';
            case SortType[SortType.RISK_TREATMENT]:
                return 'treatmentPlan.keyword';
            case SortType[SortType.RISK_OWNERS]:
                return 'ownerEmails.keyword';
            case SortType[SortType.ANTICIPATED_COMPLETION_DATE]:
                return 'anticipatedCompletionDate';
            case SortType[SortType.RISK_IMPACT]:
                return 'impact';
            case SortType[SortType.NAME]:
                return 'vendorName.keyword';
            case SortType[SortType.UPDATED_AT]:
                return 'updatedAt';
            case SortType[SortType.ID]:
                return 'riskId';
            case SortType[SortType.RISK_STATUS]:
                return 'status.keyword';
            case SortType[SortType.RISK_LIKELIHOOD]:
                return 'likelihood';
            default:
                return field;
        }
    }

    private buildEnumScriptSort(
        field: string,
        enumToMap: Record<string, number>,
        order: SortDir,
        defaultValue: string = '',
    ): any {
        const enumMapping = Object.fromEntries(
            Object.entries(enumToMap)
                .filter(([key]) => isNaN(Number(key)))
                .map(([key, value]) => [key, Number(value)]),
        );

        const mapString = Object.entries(enumMapping)
            .map(([key, value]) => `'${key}': ${value}`)
            .join(', ');

        return {
            _script: {
                type: 'number',
                script: {
                    source: `
                    def valueMap = [${mapString}];
                    def fieldValue = doc['${field}'].size() == 0 ? '${defaultValue}' : doc['${field}'].value;
                    return valueMap.containsKey(fieldValue) ? valueMap[fieldValue] : 0;
                `,
                    lang: 'painless',
                },
                order: SortDir[order],
            },
        };
    }

    async resolveListVendor(account: Account, dto: VendorsRequestDto, user: User) {
        const isFeatureFlagEnabled = await this.isListVendorPerformanceEnabled(account);

        return isFeatureFlagEnabled
            ? this.listVendorsV2(account, dto, user, isFeatureFlagEnabled)
            : this.listVendors(account, dto, user);
    }

    /**
     * @param VendorsRequestDto dto
     */
    async listVendors(
        account: Account,
        dto: VendorsRequestDto,
        user: User,
        vendorListOptions?: VendorListOptions,
    ): Promise<PaginationType<VendorResponse>> {
        const isReleaseRMExtendedToVendors = await this.featureFlagService.evaluate(
            {
                name: FeatureFlag.RELEASE_RBAC_RISK_MANAGER_EXTENDED_TO_VENDORS,
                category: FeatureFlagCategory.NONE,
                defaultValue: false,
            },
            user,
            account,
        );
        const hasRiskManagerRestrictedView =
            isReleaseRMExtendedToVendors && userHasRiskManagerRestrictedView(user);

        let vendorIds: number[] = [];
        if (hasRiskManagerRestrictedView) {
            vendorIds = await this.riskRepository.getVendorsWithRiskByUser(user.id);
        }

        if (hasRiskManagerRestrictedView && !vendorIds.length) {
            return {
                limit: 0,
                data: [],
            };
        }

        const vendorSettings = await this.vendorSettingRepository.findOneBy({});
        let defaultReviewPeriod = config.get('vendors.defaultReviewPeriod');
        if (!isNil(vendorSettings)) {
            defaultReviewPeriod = vendorSettings.defaultReviewPeriod;
        }

        const vendors: PaginationType<VendorResponse> = await this.vendorRepository.getVendors(
            dto,
            account,
            vendorIds,
            defaultReviewPeriod,
            {
                isRiskManagementActiveRestrictedView: hasRiskManagerRestrictedView,
            },
            vendorListOptions,
        );

        if (!isEmpty(vendors.data)) {
            const sharedAccountIds: string[] = compact(
                vendors.data.map(vendor => vendor.sharedAccountId),
            );
            let vendorSharedAccountEvents: SharedAccountEventType[] = [];
            if (!isEmpty(sharedAccountIds)) {
                vendorSharedAccountEvents =
                    await this.accountEventService.getVendorsSharedAccountEvents(sharedAccountIds);
            }
            const vendorsIds = vendors.data.map(vendor => vendor.id);
            let lastQuestionnaires: LastQuestionnaireResponse[] = [];
            if (dto.withLastQuestionnaires) {
                lastQuestionnaires =
                    await this.questionnairesCoreService.listLastQuestionnaireByVendorIds(
                        vendorsIds,
                    );
            }
            await Promise.allSettled(
                vendors.data.map(async (vendor: Vendor) => {
                    const vendorResponse: VendorResponse = vendor;
                    if (!isEmpty(lastQuestionnaires)) {
                        const lastVendorQuestionnaire = lastQuestionnaires.find(
                            questionnaire => vendor.id === questionnaire.vendorId,
                        );
                        if (!isNil(lastVendorQuestionnaire)) {
                            vendorResponse.lastQuestionnaire = lastVendorQuestionnaire;
                        }
                    }

                    vendorResponse.events = 0;

                    if (!isNil(vendor.sharedAccountId) && !isEmpty(vendorSharedAccountEvents)) {
                        const vendorNotifications: SharedAccountEventType[] = [];

                        const sharedAccountEvents = vendorSharedAccountEvents.filter(
                            sharedAccountEvent =>
                                sharedAccountEvent?.sharedAccount?.id === vendor.sharedAccountId,
                        );
                        if (isEmpty(sharedAccountEvents)) {
                            return;
                        }

                        const accountEventIds = sharedAccountEvents.map(
                            accountEvent => accountEvent.id,
                        );

                        const accountEventControls = await this.accountEventControlRepository.find({
                            where: {
                                accountEventId: In(accountEventIds),
                                user: { id: user.id },
                            },
                        });

                        if (isEmpty(accountEventControls)) {
                            vendorNotifications.push(...sharedAccountEvents);
                        } else {
                            sharedAccountEvents.forEach(accountEvent => {
                                const readAccountEventControls = accountEventControls.filter(
                                    accountEventControl =>
                                        accountEventControl.accountEventId === accountEvent.id,
                                );
                                if (!readAccountEventControls.length) {
                                    vendorNotifications.push(accountEvent);
                                }
                            });
                        }

                        vendorResponse.events = vendorNotifications.length;
                    }

                    if (
                        !isNil(vendor.scheduleConfiguration) &&
                        !isEmpty(vendor.scheduleConfiguration)
                    ) {
                        const { scheduleConfiguration } = vendor;
                        const { criteria } = scheduleConfiguration;
                        if (!isEmpty(criteria)) {
                            const criteriaType: ScheduleVendorQuestionnairesCriteriaType =
                                JSON.parse(criteria);
                            if (!isEmpty(criteriaType.questionnaireIds)) {
                                const questionnaireVendors =
                                    await this.questionnairesVendorsRepository.getVendorQuestionnaires(
                                        criteriaType.questionnaireIds,
                                    );
                                criteriaType.questionnaireVendors = questionnaireVendors;
                            }
                            vendorResponse.scheduleConfiguration = {
                                ...scheduleConfiguration,
                                criteria: criteriaType,
                            };
                        }
                    }
                    return vendorResponse;
                }),
            );
        }

        return vendors;
    }

    async listVendorsV2(
        account: Account,
        dto: VendorsRequestDto,
        user: User,
        isListVendorPerformanceFlagEnabled = false,
    ): Promise<PaginationType<VendorResponse>> {
        const [isReleaseRMExtendedToVendors, vendorSettings] = await Promise.all([
            this.featureFlagService.evaluate(
                {
                    name: FeatureFlag.RELEASE_RBAC_RISK_MANAGER_EXTENDED_TO_VENDORS,
                    category: FeatureFlagCategory.NONE,
                    defaultValue: false,
                },
                user,
                account,
            ),
            this.vendorSettingRepository.findOneBy({}),
        ]);
        const hasRiskManagerRestrictedView =
            isReleaseRMExtendedToVendors && userHasRiskManagerRestrictedView(user);
        const vendorIds = hasRiskManagerRestrictedView
            ? await this.riskRepository.getVendorsWithRiskByUser(user.id)
            : [];

        if (hasRiskManagerRestrictedView && !vendorIds.length) {
            return { limit: 0, data: [] };
        }

        const defaultReviewPeriod =
            vendorSettings?.defaultReviewPeriod || config.get('vendors.defaultReviewPeriod');
        const vendors = (await this.vendorRepository.getVendors(
            dto,
            account,
            vendorIds,
            defaultReviewPeriod,
            {
                isRiskManagementActiveRestrictedView: hasRiskManagerRestrictedView,
                isListVendorPerformanceFlagEnabled,
            },
        )) as PaginationType<VendorResponse>;

        return vendors;
    }

    /**
     *
     */
    getAllVendors(): Promise<Vendor[]> {
        // get ALL of the vendors - order by name
        return this.vendorRepository.find({
            where: [
                {
                    status: Not(VendorStatus.PROSPECTIVE),
                },
                { status: IsNull() },
            ],
            order: {
                name: 'ASC',
            },
        });
    }

    private async getAllVendorExportFlags(
        account: Account,
        user: User,
    ): Promise<VendorsCsvExportFlags> {
        let vendorIds: number[] = [];

        const isReleaseRMExtendedToVendors = await this.featureFlagService.evaluate(
            {
                name: FeatureFlag.RELEASE_RBAC_RISK_MANAGER_EXTENDED_TO_VENDORS,
                category: FeatureFlagCategory.NONE,
                defaultValue: false,
            },
            user,
            account,
        );

        const hasRiskManagerRestrictedView =
            isReleaseRMExtendedToVendors && userHasRiskManagerRestrictedView(user);

        if (hasRiskManagerRestrictedView) {
            vendorIds = await this.riskRepository.getVendorsWithRiskByUser(user.id);
        }

        const accountWithEntitlements =
            await this.accountsCoreService.getAccountWithEntitlementsOrFail(account.id);

        const isTprmPro = isTprmProActive(accountWithEntitlements);

        return {
            vendorIds,
            hasRiskManagerRestrictedView,
            isTprmPro,
        };
    }

    /**
     *
     * @param account
     * @param user
     * @param filePrefix
     * @param responseDto
     * @param requestDto
     */
    private async downloadAllVendorsByWorkflow(
        account: Account,
        user: User,
        filePrefix: string,
        responseDtoName: string,
        requestDto: VendorsRequestDto,
        exportFlags: VendorsCsvExportFlags,
        requestMetadata: ExtractedRequestProps,
        actionType: AsyncActionType,
        sendSnackNotification: boolean,
    ): Promise<DownloaderPayloadType> {
        this.logger.log(
            PolloMessage.msg('Downloading vendors using Temporal workflow').setIdentifier({
                accountId: account.id,
            }),
        );
        const temporalClient = await getTemporalClient(account.domain);
        // @ts-expect-error disabling ts until this is replaced with executeWorkflow
        return temporalClient.startWorkflow(vendorReportDownloadWorkflowV1, {
            taskQueue: config.get('temporal.taskQueues.temporal-default'),
            args: [
                {
                    account,
                    user,
                    requestDto,
                    responseDtoName,
                    filePrefix,
                    exportFlags,
                    requestMetadata,
                    actionType,
                    sendSnackNotification,
                },
            ],
            memo: { accountId: account.id, domain: account.domain },
        });
    }

    async downloadAllVendorsByDto(
        account: Account,
        user: User,
        filePrefix: string,
        responseDtoName: string,
        requestDto: VendorsRequestDto,
        requestMetadata: ExtractedRequestProps,
        actionType: AsyncActionType,
        sendSnackNotification: boolean = false,
    ): Promise<DownloaderPayloadType> {
        const exportFlags = await this.getAllVendorExportFlags(account, user);

        return this.downloadAllVendorsByWorkflow(
            account,
            user,
            filePrefix,
            responseDtoName,
            requestDto,
            exportFlags,
            requestMetadata,
            actionType,
            sendSnackNotification,
        );
    }

    getVendorDataAccessedOrProcessed(
        dataAccessedOrProcessedList: VendorDataAccessedOrProcessedEnum[] | null | undefined,
    ) {
        if (isEmpty(dataAccessedOrProcessedList) || isNil(dataAccessedOrProcessedList)) {
            return null;
        }
        return this.vendorDataAccessedOrProcessedRepository.findManyDataAccessedByType(
            dataAccessedOrProcessedList,
        );
    }

    private async uploadVendorLogo(dto: VendorModifyRequestDto, vendor: Vendor, account: Account) {
        if (!isNil(dto.url) && isNil(dto.logo)) {
            try {
                const logo = await this.uploader.uploadVendorLogo(
                    ensureSecureUrl(dto.url),
                    account.id,
                );
                vendor.logo = logo.key;
            } catch (error) {
                this.logger.warn(
                    PolloMessage.msg('VendorUploadLogo error on upload logo')
                        .setIdentifier(dto)
                        .setError(error),
                );
            }
        }
    }

    /**
     *
     * @param account
     * @param user
     * @param dto
     */
    async createVendor(
        account: Account,
        user: User,
        dto: VendorRequestDto,
        skipHandlers?: boolean,
    ): Promise<Vendor> {
        // create a new vendor
        const vendor = new Vendor();

        vendor.integrations =
            (await this.validateAndFetchVendorIntegrations(account, dto.integrations)) ?? [];
        vendor.dataAccessedOrProcessed =
            (await this.getVendorDataAccessedOrProcessed(dto.dataAccessedOrProcessedList)) ?? [];
        if (!isNil(dto.userId)) {
            const userWithRoles = await this.usersCoreService.getUserWithRoles(dto.userId, [
                Role.ADMIN,
                Role.TECHGOV,
                Role.WORKSPACE_ADMINISTRATOR,
            ]);

            vendor.user = userWithRoles;
        }

        // get the user from the service - ONLY allow ADMIN|TECHGOV|WORKSPACE_ADMINISTRATOR|EMPLOYEE
        if (!isNil(dto.contact?.id)) {
            const contactWithRoles = await this.usersCoreService.getUserWithRoles(dto.contact.id, [
                Role.ADMIN,
                Role.TECHGOV,
                Role.WORKSPACE_ADMINISTRATOR,
                Role.EMPLOYEE,
            ]);
            vendor.contact = contactWithRoles;
        }

        if (isNil(dto.risk)) {
            dto.risk = VendorRisk.NONE;
        }

        // set the data from the entire DTO here ...
        const updatedVendor = VendorDtoAdapter.merge(vendor, dto);

        // upload the logo from the URL and return the path to it
        await this.uploadVendorLogo(dto, updatedVendor, account);

        if (!isNil(dto.logo)) {
            updatedVendor.logo = dto.logo;
        }

        if (updatedVendor.status === VendorStatus.ARCHIVED) {
            const now = moment().toDate();
            updatedVendor.archivedAt = now;
        }

        // push it to the db and return the vendor
        const savedVendor = await this.vendorRepository.save(updatedVendor);
        // emit the VendorCreatedEvent event here

        if (skipHandlers) {
            return savedVendor;
        }

        const vendorCreatedEvent = !isNil(dto.contact?.id)
            ? new VendorCreatedEvent(
                  account,
                  user,
                  dto,
                  savedVendor,
                  savedVendor.contact ?? undefined,
                  DEFAULT_TC_WORKSPACE_ID,
              )
            : new VendorCreatedEvent(
                  account,
                  user,
                  dto,
                  savedVendor,
                  undefined,
                  DEFAULT_TC_WORKSPACE_ID,
              );
        this._eventBus.publish(vendorCreatedEvent);

        this._eventBus.publish(
            new VendorSharedAccountUpdatedEvent(
                account,
                savedVendor.id,
                savedVendor.url ?? null,
                null,
            ),
        );

        return savedVendor;
    }

    /**
     *
     * @param account
     * @param user
     * @param id
     * @param dto
     */
    async updateVendor(
        account: Account,
        user: User,
        id: number,
        vendorModifyRequestType: VendorModifyRequestType,
        skipHandlers?: boolean,
    ): Promise<Vendor> {
        account = await this.accountsCoreService.getAccountWithEntitlementsOrFail(account.id);

        const isAccessReviewEnabled = !isNil(
            account.entitlements.find(f => f.type === AccountEntitlementType.ACCESS_REVIEW),
        );

        const integrations = await this.validateAndFetchVendorIntegrations(
            account,
            vendorModifyRequestType.integrations,
            id,
        );

        const newDataAccessedOrProcessed = await this.getVendorDataAccessedOrProcessed(
            vendorModifyRequestType.dataAccessedOrProcessedList ?? [],
        );

        const vendor = await this.getVendor(id);
        const originalVendor = cloneDeep(vendor);

        const prevVendorStatus = !isNil(vendor.status) ? vendor.status : null;
        const isNewSubprocessor =
            vendorModifyRequestType.isSubProcessor &&
            vendor.isSubProcessor !== vendorModifyRequestType.isSubProcessor;

        let contactWithRoles: User;
        const isNewUrl = vendor.url !== vendorModifyRequestType.url;
        const previousUrl = !isEmpty(vendor.url) && !isNil(vendor.url) ? vendor.url : null;
        const previousRenewalDate = vendor.renewalDate;
        // set the data from the entire DTO here ...
        const updatedVendor = VendorDtoAdapter.merge(
            vendor,
            vendorModifyRequestType as VendorModifyRequestDto,
        );
        const shouldUpdateVendorRenewalDate =
            vendorModifyRequestType.shouldRestartRenewalDate ?? false;

        updatedVendor.integrations = integrations ?? [];

        if (!isNil(previousRenewalDate) && isNil(vendorModifyRequestType.renewalDate)) {
            await this.schedulesQuestionnairesCoreService.deleteSchedulesVendorQuestionnaires(
                id,
                account,
            );

            vendor.scheduleConfiguration = null;
        }

        if (
            shouldUpdateVendorRenewalDate &&
            !isNil(vendor.renewalDate) &&
            !isNil(vendor.renewalScheduleType)
        ) {
            updatedVendor.renewalDate = await this.updateVendorRenewalDate(
                vendor.renewalDate,
                vendor.renewalScheduleType,
            );
        }

        if (!isNil(vendorModifyRequestType?.contact?.id)) {
            contactWithRoles = await this.usersCoreService.getUserWithRoles(
                vendorModifyRequestType.contact.id,
                [Role.ADMIN, Role.TECHGOV, Role.WORKSPACE_ADMINISTRATOR, Role.EMPLOYEE],
            );
            updatedVendor.contact = contactWithRoles;
        } else {
            updatedVendor.contact = null;
        }

        if (!isNil(vendorModifyRequestType.userId)) {
            // get the user from the service - ONLY allow ADMIN|TECHGOV|WORKSPACE_ADMINISTRATOR
            const userWithRoles = await this.usersCoreService.getUserWithRoles(
                vendorModifyRequestType.userId,
                [Role.ADMIN, Role.TECHGOV, Role.WORKSPACE_ADMINISTRATOR],
            );

            // add reviewer role to vendor owner
            if (
                !isNil(userWithRoles) &&
                !hasRole(userWithRoles, [Role.REVIEWER, Role.WORKSPACE_ADMINISTRATOR]) &&
                isAccessReviewEnabled
            ) {
                const userRole = new UserRole();
                userRole.role = Role.REVIEWER;
                userRole.user = userWithRoles;
                await this.usersCoreService.saveUserRole(userRole);
            }

            // set the user
            updatedVendor.user = userWithRoles;
        } else {
            updatedVendor.user = null;
        }

        if (isNil(vendor.risk)) {
            vendor.risk = VendorRisk.NONE;
        }

        if (isNewUrl) {
            // upload the logo from the URL and return the path to it
            await this.uploadVendorLogo(
                vendorModifyRequestType as VendorModifyRequestDto,
                updatedVendor,
                account,
            );
        }

        updatedVendor.status = vendorModifyRequestType.status;

        updatedVendor.dataAccessedOrProcessed = newDataAccessedOrProcessed ?? [];

        if (vendorModifyRequestType.status !== VendorStatus.ARCHIVED) {
            updatedVendor.archivedAt = null;
        }
        // Check if vendor is already archived, necessary for public api where a request can be made to an already archived vendor
        else if (isNil(vendor.archivedAt)) {
            const now = moment().toDate();
            updatedVendor.archivedAt = now;
        }

        // Removing securityReviews to avoid ER_NO_REFERENCED_ROW_2 error
        updatedVendor.securityReviews = undefined;

        // push it to the db
        const savedVendor = await this.vendorRepository.save<Vendor>(updatedVendor);

        savedVendor.riskCount = await this.riskRepository.countBy({
            currentVersion: {
                vendors: { id: vendor.id },
            },
        });

        if (skipHandlers) {
            return savedVendor;
        }

        let vendorUpdateEvent: VendorUpdatedEvent;

        // By default first trust center will be sharedAccount for vendor.ToDo: define which trust center will be shared
        if (!isNil(vendorModifyRequestType.contact?.id)) {
            vendorUpdateEvent = new VendorUpdatedEvent(
                account,
                user,
                vendorModifyRequestType as VendorModifyRequestDto,
                updatedVendor as unknown as Vendor,
                originalVendor,
                vendor.contact ?? undefined,
                isNewSubprocessor,
                DEFAULT_TC_WORKSPACE_ID,
            );
        } else {
            vendorUpdateEvent = new VendorUpdatedEvent(
                account,
                user,
                vendorModifyRequestType as VendorModifyRequestDto,
                updatedVendor as unknown as Vendor,
                originalVendor,
                undefined,
                isNewSubprocessor,
                DEFAULT_TC_WORKSPACE_ID,
            );
        }
        this._eventBus.publish(vendorUpdateEvent);

        this._eventBus.publish(
            new VendorSharedAccountUpdatedEvent(
                account,
                savedVendor.id,
                savedVendor.url ?? null,
                previousUrl,
            ),
        );

        this._eventBus.publish(
            new VendorStatusUpdatedEvent(
                account,
                user,
                vendor,
                prevVendorStatus,
                vendorModifyRequestType.status ?? null,
            ),
        );

        return savedVendor;
    }

    /**
     * @deprecated Use VendorsCoreService.updateTrustCenterUrl
     *
     * @param vendorId
     * @param url
     * @param provider
     */
    async updateTrustCenterUrl(
        vendorId: number,
        url: string | null | undefined,
        provider: TrustCenterProvider | null,
    ): Promise<void> {
        const vendor = await this.getVendor(vendorId);

        vendor.trustCenterUrl = url;
        vendor.trustCenterProvider = provider;

        await this.vendorRepository.save<Vendor>(vendor, { reload: false });
    }

    /**
     *
     * @param account
     * @param user
     * @param id
     * @param dto
     */
    @TenancyTransaction()
    async updateVendorStatus(
        id: number,
        dto: VendorStatusRequestDto,
        account: Account,
        user: User,
    ): Promise<Vendor> {
        const { vendorStatus: newVendorStatus } = dto;

        const withSecurityReviews = false;
        const originalVendor = await this.getVendor(id, withSecurityReviews);
        const prevVendorStatus = !isNil(originalVendor.status) ? originalVendor.status : null;

        const updatedVendor = cloneDeep(originalVendor);
        updatedVendor.status = newVendorStatus;

        // Check if vendor is already archived, necessary for public api where a request can be made to an already archived vendor
        if (newVendorStatus === VendorStatus.ARCHIVED) {
            if (isNil(originalVendor.archivedAt)) {
                const now = moment().toDate();
                updatedVendor.archivedAt = now;
                updatedVendor.archivedAs = originalVendor.status;
            }
            await this.removeArchivedVendorFromIntegrations(account, updatedVendor.id);
        } else {
            if (!isNil(originalVendor.archivedAt)) {
                updatedVendor.status = originalVendor.archivedAs;
            }
            updatedVendor.archivedAs = null;
            updatedVendor.archivedAt = null;
        }

        const savedVendor = await this.vendorRepository.save(updatedVendor);

        this._eventBus.publish(
            new VendorStatusUpdatedEvent(
                account,
                user,
                originalVendor,
                prevVendorStatus,
                newVendorStatus,
            ),
        );

        return savedVendor;
    }

    /**
     *
     * @param account
     * @param user
     * @param id
     */
    async deleteVendor(account: Account, user: User, id: number): Promise<void> {
        // get the vendor
        const vendor = await this.getVendor(id);
        // delete related risks
        await this.riskRepository.deleteCustomRiskByVendorId(vendor.id);
        // delete scheduled questionnaires
        await this.schedulesQuestionnairesCoreService.deleteSchedulesVendorQuestionnaires(
            id,
            account,
        );
        // soft delete the vendor
        await this.vendorRepository.softDelete(id);

        // emit the VendorDeletedEvent event here
        this._eventBus.publish(new VendorDeletedEvent(account, user, vendor));
    }

    /**
     * @deprecated Use VendorsCoreService.getVendors
     *
     * @returns
     */
    getVendors(): Promise<Vendor[]> {
        return this.vendorRepository.find({
            where: [
                {
                    status: Not(VendorStatus.PROSPECTIVE),
                },
                { status: IsNull() },
            ],
        });
    }

    getVendorsWithDocuments(): Promise<Vendor[]> {
        return this.vendorRepository.find({
            select: ['id', 'name'],
            where: [
                {
                    status: Not(VendorStatus.PROSPECTIVE),
                },
                { status: IsNull() },
            ],
            relations: ['documents'],
        });
    }

    /**
     * Get vendor review
     * @param id Vendor Review ID
     * @returns Vendor Review
     */
    async getVendorReview(id: number, withDeleted?: boolean): Promise<VendorReview> {
        const vendorReview = await this.vendorReviewRepository.getVendorReview({ id }, withDeleted);
        if (isNil(vendorReview)) {
            throw new NotFoundException('Vendor Review does not exist');
        }

        return vendorReview;
    }

    /**
     * Gets vendor Report View Info
     * @param vendorReviewId
     * @return VendorReportReview
     */
    async getVendorReportViewInfo(vendorReviewId: number): Promise<VendorReportReview> {
        const vendorReview = await this.getVendorReview(vendorReviewId, true);
        const vendorReportReview = new VendorReportReview();
        vendorReportReview.build(vendorReview);
        return vendorReportReview;
    }

    /**
     * Generates a PDF representing a vendors report from the submitted form based on
     * the data provided. This function is intended
     * to be used whenever vendors report needs to be generated
     * @param account
     * @param user
     * @param securityTrainingData
     * @returns Vendors report URL
     */
    async generateVendorReportReview(
        account: Account,
        user: User,
        vendorReviewId: number,
    ): Promise<DownloaderPayloadType> {
        try {
            // Generate source file
            const vendorReview = await this.getVendorReportViewInfo(vendorReviewId);

            const source = fs.readFileSync(
                path.join(__dirname, VendorsConstants.REPORT_FILE_PATH),
                'utf8',
            );

            const template = hbs.handlebars.compile(source);
            const bridgeLetter = await this.getLatestVendorDocument(
                vendorReview.vendorId,
                VendorDocumentTypeEnum.BRIDGE_LETTER,
            );
            vendorReview.bridgeLetter = !isNil(bridgeLetter) ? 'Attached' : 'Unattached';

            const html = template({
                companyName: account.companyName,
                userName: fullName(user),
                vendorReview,
                createdAt: new Date(),
                isNotSoc1: vendorReview.socReport !== SocReportLabel[SocReportType.SOC_1],
            });

            if (isNil(html)) {
                throw new InternalServerErrorException();
            }

            const { data, mimetype } = await this.htmlToPdfConverter.convertToPdfBuffer(
                html,
                account,
            );

            const vendorSecurityReviewDocument =
                await this.vendorSecurityReviewDocumentRepository.findOne({
                    where: {
                        documentId: vendorReviewId,
                        type: VendorSecurityReviewDocumentsType.REVIEW,
                        vendorSecurityReview: {
                            deletedAt: IsNull(),
                            vendor: {
                                id: vendorReview.vendorId,
                            },
                        },
                    },
                    relations: ['vendorSecurityReview'],
                });

            let fileName = sanitizeFileName(
                `${fileNameDate()}-${vendorReview.reviewer}-${
                    VendorDocumentTypeEnum.COMPLIANCE_REPORT_REVIEW
                }.pdf`,
            );
            if (
                !isNil(vendorSecurityReviewDocument) &&
                !isNil(vendorSecurityReviewDocument.vendorSecurityReview)
            ) {
                fileName = `${vendorSecurityReviewDocument.vendorSecurityReview.title}.pdf`;
            }

            const uploadedFile = await this.uploader.uploadPrivateFileFromBuffer(
                account.id,
                UploadType.EVIDENCE,
                data,
                fileName,
                mimetype,
            );

            const vendor = await this.getVendor(vendorReview.vendorId);

            const vendorDocument = await this.vendorsDocumentsService.saveVendorDocument(
                account,
                user,
                uploadedFile,
                vendor,
                VendorDocumentTypeEnum.COMPLIANCE_REPORT_REVIEW,
            );
            const downloaderPayload = await this.downloader.getDownloadUrl(uploadedFile.key);
            if (!isNil(downloaderPayload)) {
                downloaderPayload.vendorDocumentId = vendorDocument.id;
            }

            return downloaderPayload;
        } catch (error) {
            this.logger.error(PolloMessage.msg(error.message).setError(error));

            throw error;
        }
    }

    /**
     * Get Latests Vendor Document filtered by VendorId and VendorDocumentType
     * @param vendorId: number
     * @param type: VendorDocumentTypeEnum
     * @return VendorDocument
     */
    async getLatestVendorDocument(
        vendorId: number,
        type: VendorDocumentTypeEnum,
    ): Promise<VendorDocument> {
        return this.vendorDocumentRepository.findOne({
            where: {
                type: type,
                vendor: { id: vendorId },
            },
            relations: ['vendor'],
            order: {
                createdAt: 'DESC',
            },
        });
    }

    /**
     * Upsert for vendor report reviews
     */
    @TenancyTransaction()
    async saveVendorReportReview(
        account: Account,
        vendorId: number,
        dto: VendorReviewRequestDto,
    ): Promise<VendorReview> {
        const isReleaseOptimizedVendorsReviewEndpoint =
            await this.featureFlagService.evaluateAsTenant(
                {
                    category: FeatureFlagCategory.NONE,
                    name: FeatureFlag.RELEASE_OPTIMIZED_VENDORS_REVIEW_ENDPOINT,
                    defaultValue: false,
                },
                account,
            );

        const vendor = isReleaseOptimizedVendorsReviewEndpoint
            ? await this.vendorRepository.findOne({
                  select: ['id'],
                  where: { id: vendorId },
                  loadEagerRelations: false,
              })
            : await this.getVendor(vendorId);

        if (isNil(vendor)) {
            throw new NotFoundException(`No vendor was found for ID ${vendorId}`);
        }

        const isUpdating = Boolean(dto.id);

        this.logger.log(PolloMessage.msg(`${isUpdating ? 'Updating' : 'Creating'} report review`));

        let existingVendorReview: VendorReview | undefined;

        if (isNumber(dto.id)) {
            if (isReleaseOptimizedVendorsReviewEndpoint) {
                existingVendorReview =
                    (await this.vendorReviewRepository.findOne({
                        where: { id: dto.id },
                    })) || undefined;
            } else {
                existingVendorReview = await this.getVendorReview(dto.id);
            }

            if (isNil(existingVendorReview)) {
                throw new NotFoundException('Vendor Review does not exist');
            }
        }

        const newVendorReview = new VendorReviewFactory({ dto, vendor }).create(
            existingVendorReview,
        );

        const savedVendorReview = await this.vendorReviewRepository.save(newVendorReview);

        this.logger.log(
            PolloMessage.msg(
                `Report review ${isUpdating ? 'updated' : 'created'} [id: ${
                    savedVendorReview.id
                }] `,
            ),
        );

        await this.updateVendorReviewDetails(dto, savedVendorReview, account, {
            isReleaseOptimizedVendorsReviewEndpoint,
        });

        let currentVendorReview: VendorReview | null;

        if (isReleaseOptimizedVendorsReviewEndpoint) {
            currentVendorReview = await this.getVendorReviewWithDetails(savedVendorReview.id);
        } else {
            currentVendorReview = await this.vendorReviewRepository.getVendorReview(
                { id: savedVendorReview.id },
                false,
            );
        }

        if (isNil(currentVendorReview)) {
            throw new InternalServerErrorException('Failed to get vendor review');
        }

        return currentVendorReview;
    }

    async getVendorReviewWithDetails(vendorReviewId: number): Promise<VendorReview | null> {
        const vendorReview = await this.vendorReviewRepository.findOne({
            where: { id: vendorReviewId },
            relations: {
                vendor: true,
            },
        });

        if (isNil(vendorReview)) {
            return null;
        }

        vendorReview.services = await this.getVendorReviewServices(vendorReview.id);
        vendorReview.locations = await this.getVendorReviewLocations(vendorReview.id);
        vendorReview.findings = await this.getVendorReviewFindings(vendorReview.id);
        vendorReview.userControls = await this.getVendorReviewUserControls(vendorReview.id);
        vendorReview.trustServiceCategories = await this.getVendorReviewTrustServices(
            vendorReview.id,
        );

        return vendorReview;
    }

    private async getVendorReviewServices(reviewId: number): Promise<VendorReviewService[]> {
        return this.vendorServiceRepository
            .createQueryBuilder('service')
            .where('service.fk_vendor_review_id = :reviewId', { reviewId })
            .andWhere('service.deletedAt IS NULL')
            .getMany();
    }

    private async getVendorReviewLocations(reviewId: number): Promise<VendorReviewLocation[]> {
        return this.vendorLocationRepository
            .createQueryBuilder('location')
            .where('location.fk_vendor_review_id = :reviewId', { reviewId })
            .andWhere('location.deletedAt IS NULL')
            .getMany();
    }

    private async getVendorReviewFindings(reviewId: number): Promise<VendorReviewFinding[]> {
        return this.vendorFindingRepository
            .createQueryBuilder('finding')
            .where('finding.fk_vendor_review_id = :reviewId', { reviewId })
            .andWhere('finding.deletedAt IS NULL')
            .getMany();
    }

    private async getVendorReviewUserControls(
        reviewId: number,
    ): Promise<VendorReviewUserControl[]> {
        return this.vendorUserControlRepository
            .createQueryBuilder('userControl')
            .where('userControl.fk_vendor_review_id = :reviewId', { reviewId })
            .andWhere('userControl.deletedAt IS NULL')
            .getMany();
    }

    private async getVendorReviewTrustServices(
        reviewId: number,
    ): Promise<VendorReviewTrustServiceCategoryMap[]> {
        return this.vendorReviewTrustServiceCategoryRepository
            .createQueryBuilder('trustService')
            .where('trustService.fk_vendor_review_id = :reviewId', { reviewId })
            .andWhere('trustService.deletedAt IS NULL')
            .getMany();
    }

    private async updateVendorReviewDetails(
        dto: VendorReviewRequestDto,
        savedVendorReview: VendorReview,
        account: Account,
        opts: {
            isReleaseOptimizedVendorsReviewEndpoint?: boolean;
        } = {},
    ): Promise<void> {
        try {
            await Promise.all(
                this.VENDOR_REVIEW_SETTERS.map(setter =>
                    setter.call(this, dto, savedVendorReview, account, opts),
                ),
            );
        } catch (error) {
            this.logger.error(
                PolloMessage.msg(`Failed to set vendor review data: ${error.message}`)
                    .setError(error)
                    .setIdentifier({
                        dto,
                        savedVendorReview,
                    }),
            );
            throw new InternalServerErrorException('Failed to set vendor review data');
        }
    }

    private async setLocations(
        dto: VendorReviewRequestDto,
        vendorReview: VendorReview,
        account: Account,
        options?: {
            isReleaseOptimizedVendorsReviewEndpoint?: boolean;
        },
    ): Promise<void> {
        const { isReleaseOptimizedVendorsReviewEndpoint = false } = options || {};
        const locations = compact(dto.locations);

        if (isEmpty(locations)) {
            await this.vendorLocationRepository.delete({ review: { id: vendorReview.id } });
            return;
        }

        this.logger.log(
            PolloMessage.msg(`Adding locations to report review [id: ${vendorReview.id}]`),
        );

        const locationsToPreserve = compact(map(locations, 'id'));

        if (!isEmpty(locationsToPreserve)) {
            await this.vendorLocationRepository.delete({
                id: Not(In(locationsToPreserve)),
                review: {
                    id: vendorReview.id,
                },
            });
        }

        if (isReleaseOptimizedVendorsReviewEndpoint) {
            await this.syncLocationsOptimized(account, locations, vendorReview);
        } else {
            await this.syncLocations(account, locations, vendorReview);
        }
    }

    private async syncLocations(
        account: Account,
        locations: VendorReviewLocationRequestDto[],
        vendorReview: VendorReview,
    ): Promise<void> {
        const newLocations: VendorReviewLocation[] = [];

        for (const dtoItem of locations) {
            const location = !isNil(dtoItem.id)
                ? // eslint-disable-next-line no-await-in-loop
                  await this.vendorLocationRepository.findOne({
                      where: { id: dtoItem.id },
                  })
                : new VendorReviewLocation();

            if (!isNil(location)) {
                location.city = dtoItem.city;
                location.stateCountry = dtoItem.stateOrCountry;

                location.review = vendorReview;
                newLocations.push(location);
            }
        }

        if (!isEmpty(newLocations)) {
            await this.vendorLocationRepository.save(newLocations);
            this.logger.log(
                PolloMessage.msg(
                    `${newLocations.length} Location(s) added to report review [id: ${vendorReview.id}] `,
                ),
            );
        }
    }

    private async syncLocationsOptimized(
        account: Account,
        locations: VendorReviewLocationRequestDto[],
        vendorReview: VendorReview,
    ): Promise<void> {
        const locationsToPreserve = compact(map(locations, 'id'));

        const existingLocations = !isEmpty(locationsToPreserve)
            ? await this.vendorLocationRepository.find({
                  select: ['id'],
                  where: { id: In(locationsToPreserve) },
              })
            : [];

        const existingLocationsMap = new Map(
            existingLocations.map(location => [location.id, location]),
        );

        const locationsToUpsert = locations.map(dtoItem => {
            const existing = dtoItem.id ? existingLocationsMap.get(dtoItem.id) : null;

            const location = existing ?? new VendorReviewLocation();
            location.city = dtoItem.city;
            location.stateCountry = dtoItem.stateOrCountry;
            location.review = vendorReview;

            return location;
        });

        await this.vendorLocationRepository.upsert(
            locationsToUpsert.map(location => ({
                id: location.id,
                city: location.city,
                stateCountry: location.stateCountry,
                review: { id: location.review.id },
            })),
            {
                conflictPaths: ['id', 'review'],
                skipUpdateIfNoValuesChanged: true,
            },
        );

        this.logger.log(
            PolloMessage.msg(
                `${locationsToUpsert.length} Location(s) added/updated to report review [id: ${vendorReview.id}] `,
            ),
        );
    }

    private async setServices(
        dto: VendorReviewRequestDto,
        vendorReview: VendorReview,
        account: Account,
        options?: {
            isReleaseOptimizedVendorsReviewEndpoint?: boolean;
        },
    ): Promise<void> {
        const { isReleaseOptimizedVendorsReviewEndpoint = false } = options || {};
        const services = compact(dto.services);

        if (isEmpty(services)) {
            await this.vendorServiceRepository.delete({ review: { id: vendorReview.id } });
            return;
        }

        this.logger.log(
            PolloMessage.msg(`Adding services to report review [id: ${vendorReview.id}] `),
        );

        const servicesToPreserve = compact(map(services, 'id'));

        if (!isEmpty(servicesToPreserve)) {
            await this.vendorServiceRepository.delete({
                id: Not(In(servicesToPreserve)),
                review: {
                    id: vendorReview.id,
                },
            });
        }

        if (isReleaseOptimizedVendorsReviewEndpoint) {
            await this.syncServicesOptimized(account, services, vendorReview);
        } else {
            await this.syncServices(account, services, vendorReview);
        }
    }

    private async syncServices(
        account: Account,
        services: VendorReviewServiceRequestDto[],
        vendorReview: VendorReview,
    ): Promise<void> {
        const newServices: VendorReviewService[] = [];

        for (const dtoItem of services) {
            const service = !isNil(dtoItem.id)
                ? // eslint-disable-next-line no-await-in-loop
                  await this.vendorServiceRepository.findOne({
                      where: { id: dtoItem.id },
                  })
                : new VendorReviewService();

            if (!isNil(service)) {
                service.name = dtoItem.service;
                service.review = vendorReview;
                newServices.push(service);
            }
        }

        if (!isEmpty(newServices)) {
            await this.vendorServiceRepository.save(newServices);
            this.logger.log(
                PolloMessage.msg(
                    `${newServices.length} Service(s) added to report review [id: ${vendorReview.id}] `,
                ),
            );
        }
    }

    private async syncServicesOptimized(
        account: Account,
        services: VendorReviewServiceRequestDto[],
        vendorReview: VendorReview,
    ): Promise<void> {
        const servicesToPreserve = compact(map(services, 'id'));

        const existingServices = !isEmpty(servicesToPreserve)
            ? await this.vendorServiceRepository.find({
                  select: ['id'],
                  where: { id: In(servicesToPreserve) },
              })
            : [];

        const existingServicesMap = new Map(existingServices.map(service => [service.id, service]));

        const servicesToUpsert = services.map(dtoItem => {
            const existing = dtoItem.id ? existingServicesMap.get(dtoItem.id) : null;

            const service = existing ?? new VendorReviewService();
            service.name = dtoItem.service;
            service.review = vendorReview;

            return service;
        });

        await this.vendorServiceRepository.upsert(
            servicesToUpsert.map(service => ({
                id: service.id,
                name: service.name,
                review: { id: service.review.id },
            })),
            {
                conflictPaths: ['id', 'review'],
                skipUpdateIfNoValuesChanged: true,
            },
        );

        this.logger.log(
            PolloMessage.msg(
                `${servicesToUpsert.length} Service(s) added/updated to report review [id: ${vendorReview.id}] `,
            ),
        );
    }

    private async setUserControls(
        dto: VendorReviewRequestDto,
        vendorReview: VendorReview,
        account: Account,
        options?: {
            isReleaseOptimizedVendorsReviewEndpoint?: boolean;
        },
    ): Promise<void> {
        const { isReleaseOptimizedVendorsReviewEndpoint = false } = options || {};
        const userControls = compact(dto.userControls);

        if (isEmpty(userControls)) {
            await this.vendorUserControlRepository.delete({
                review: { id: vendorReview.id },
            });
            return;
        }

        this.logger.log(
            PolloMessage.msg(`Adding controls to report review [id: ${vendorReview.id}] `),
        );

        const usersToPreserve = compact(map(userControls, 'id'));

        if (!isEmpty(usersToPreserve)) {
            await this.vendorUserControlRepository.delete({
                id: Not(In(usersToPreserve)),
                review: {
                    id: vendorReview.id,
                },
            });
        }

        if (isReleaseOptimizedVendorsReviewEndpoint) {
            await this.syncUserControlsOptimized(account, userControls, vendorReview);
        } else {
            await this.syncUserControls(account, userControls, vendorReview);
        }
    }

    private async syncUserControls(
        account: Account,
        userControls: VendorReviewUserControlType[],
        vendorReview: VendorReview,
    ): Promise<void> {
        const newUserControls: VendorReviewUserControl[] = [];

        for (const dtoItem of userControls) {
            const userControl = !isNil(dtoItem.id)
                ? // eslint-disable-next-line no-await-in-loop
                  await this.vendorUserControlRepository.findOne({
                      where: { id: dtoItem.id },
                  })
                : new VendorReviewUserControl();

            if (!isNil(userControl)) {
                userControl.name = dtoItem.userControl;
                userControl.inPlace = dtoItem.inPlace;
                userControl.review = vendorReview;
                newUserControls.push(userControl);
            }
        }

        if (!isEmpty(newUserControls)) {
            await this.vendorUserControlRepository.save(newUserControls);
            this.logger.log(
                PolloMessage.msg(
                    `${newUserControls.length} Control(s) added to report review [id: ${vendorReview.id}] `,
                ),
            );
        }
    }

    private async syncUserControlsOptimized(
        account: Account,
        userControls: VendorReviewUserControlType[],
        vendorReview: VendorReview,
    ): Promise<void> {
        const controlsToPreserve = compact(map(userControls, 'id'));

        const existingControls = !isEmpty(controlsToPreserve)
            ? await this.vendorUserControlRepository.find({
                  select: ['id'],
                  where: { id: In(controlsToPreserve) },
              })
            : [];

        const existingControlsMap = new Map(existingControls.map(control => [control.id, control]));

        const controlsToUpsert = userControls.map(dtoItem => {
            const existing = dtoItem.id ? existingControlsMap.get(dtoItem.id) : null;

            const control = existing ?? new VendorReviewUserControl();
            control.name = dtoItem.userControl;
            control.inPlace = dtoItem.inPlace;
            control.review = vendorReview;

            return control;
        });

        await this.vendorUserControlRepository.upsert(
            controlsToUpsert.map(control => ({
                id: control.id,
                name: control.name,
                inPlace: control.inPlace,
                review: { id: control.review.id },
            })),
            {
                conflictPaths: ['id', 'review'],
                skipUpdateIfNoValuesChanged: true,
            },
        );

        this.logger.log(
            PolloMessage.msg(
                `${controlsToUpsert.length} Control(s) added/updated to report review [id: ${vendorReview.id}] `,
            ),
        );
    }

    private async setFindings(
        dto: VendorReviewRequestDto,
        vendorReview: VendorReview,
        account: Account,
        options?: {
            isReleaseOptimizedVendorsReviewEndpoint?: boolean;
        },
    ): Promise<void> {
        const { isReleaseOptimizedVendorsReviewEndpoint = false } = options || {};
        const findings = compact(dto.findings);

        if (isEmpty(findings)) {
            await this.vendorFindingRepository.delete({ review: { id: vendorReview.id } });
            return;
        }

        this.logger.log(
            PolloMessage.msg(`Adding findings to report review [id: ${vendorReview.id}] `),
        );

        const findingsToPreserve = compact(map(findings, 'id'));

        if (!isEmpty(findingsToPreserve)) {
            await this.vendorFindingRepository.delete({
                id: Not(In(findingsToPreserve)),
                review: {
                    id: vendorReview.id,
                },
            });
        }

        if (isReleaseOptimizedVendorsReviewEndpoint) {
            await this.syncFindingsOptimized(account, findings, vendorReview);
        } else {
            await this.syncFindings(account, findings, vendorReview);
        }
    }

    private async syncFindings(
        account: Account,
        findings: VendorReviewFindingType[],
        vendorReview: VendorReview,
    ): Promise<void> {
        const newFindings: VendorReviewFinding[] = [];

        for (const dtoItem of findings) {
            const finding = !isNil(dtoItem.id)
                ? // eslint-disable-next-line no-await-in-loop
                  await this.vendorFindingRepository.findOne({
                      where: { id: dtoItem.id },
                  })
                : new VendorReviewFinding();

            if (!isNil(finding)) {
                finding.description = dtoItem.finding;
                finding.review = vendorReview;
                newFindings.push(finding);
            }
        }

        if (!isEmpty(newFindings)) {
            await this.vendorFindingRepository.save(newFindings);
            this.logger.log(
                PolloMessage.msg(
                    `${newFindings.length} Finding(s) added to report review [id: ${vendorReview.id}] `,
                ),
            );
        }
    }

    private async syncFindingsOptimized(
        account: Account,
        findings: VendorReviewFindingType[],
        vendorReview: VendorReview,
    ): Promise<void> {
        const findingsToPreserve = compact(map(findings, 'id'));

        const existingFindings = !isEmpty(findingsToPreserve)
            ? await this.vendorFindingRepository.find({
                  select: ['id'],
                  where: { id: In(findingsToPreserve) },
              })
            : [];

        const existingFindingsMap = new Map(existingFindings.map(finding => [finding.id, finding]));

        const findingsToUpsert = findings.map(dtoItem => {
            const existing = dtoItem.id ? existingFindingsMap.get(dtoItem.id) : null;

            const finding = existing ?? new VendorReviewFinding();
            finding.description = dtoItem.finding;
            finding.review = vendorReview;

            return finding;
        });

        await this.vendorFindingRepository.upsert(
            findingsToUpsert.map(finding => ({
                id: finding.id,
                description: finding.description,
                review: { id: finding.review.id },
            })),
            {
                conflictPaths: ['id', 'review'],
                skipUpdateIfNoValuesChanged: true,
            },
        );

        this.logger.log(
            PolloMessage.msg(
                `${findingsToUpsert.length} Finding(s) added/updated to report review [id: ${vendorReview.id}] `,
            ),
        );
    }

    private async setTrustServiceCategories(
        dto: VendorReviewRequestDto,
        vendorReview: VendorReview,
        account: Account,
        options?: {
            isReleaseOptimizedVendorsReviewEndpoint?: boolean;
        },
    ): Promise<void> {
        const { isReleaseOptimizedVendorsReviewEndpoint = false } = options || {};
        const trustServiceCategories = compact(dto.trustServiceCategories);

        if (isEmpty(trustServiceCategories)) {
            await this.vendorReviewTrustServiceCategoryRepository.delete({
                review: { id: vendorReview.id },
            });
            return;
        }

        this.logger.log(
            PolloMessage.msg(`Adding categories to report review [id: ${vendorReview.id}] `),
        );

        const serviceCategoriesToPreserve = compact(map(trustServiceCategories, 'id'));

        if (!isEmpty(serviceCategoriesToPreserve)) {
            await this.vendorReviewTrustServiceCategoryRepository.delete({
                id: Not(In(serviceCategoriesToPreserve)),
                review: {
                    id: vendorReview.id,
                },
            });
        }

        if (isReleaseOptimizedVendorsReviewEndpoint) {
            await this.syncTrustServiceCategoriesOptimized(
                account,
                trustServiceCategories,
                vendorReview,
            );
        } else {
            await this.syncTrustServiceCategories(account, trustServiceCategories, vendorReview);
        }
    }

    private async syncTrustServiceCategories(
        account: Account,
        trustServiceCategories: VendorReviewCategoryType[],
        vendorReview: VendorReview,
    ): Promise<void> {
        const newTrustServiceCategories: VendorReviewTrustServiceCategoryMap[] = [];

        for (const dtoItem of trustServiceCategories) {
            const category = !isNil(dtoItem.id)
                ? // eslint-disable-next-line no-await-in-loop
                  await this.vendorReviewTrustServiceCategoryRepository.findOne({
                      where: { id: dtoItem.id },
                  })
                : new VendorReviewTrustServiceCategoryMap();

            if (!isNil(category)) {
                category.category = VendorReviewServiceCategory[dtoItem.category];
                category.review = vendorReview;
                newTrustServiceCategories.push(category);
            }
        }

        if (!isEmpty(newTrustServiceCategories)) {
            await this.vendorReviewTrustServiceCategoryRepository.save(newTrustServiceCategories);
            this.logger.log(
                PolloMessage.msg(
                    `${newTrustServiceCategories.length} Category(s) added to report review [id: ${vendorReview.id}] `,
                ),
            );
        }
    }

    private async syncTrustServiceCategoriesOptimized(
        account: Account,
        trustServiceCategories: VendorReviewCategoryType[],
        vendorReview: VendorReview,
    ): Promise<void> {
        const serviceCategoriesToPreserve = compact(map(trustServiceCategories, 'id'));

        const existingCategories = !isEmpty(serviceCategoriesToPreserve)
            ? await this.vendorReviewTrustServiceCategoryRepository.find({
                  select: ['id'],
                  where: { id: In(serviceCategoriesToPreserve) },
              })
            : [];

        const existingCategoriesMap = new Map(
            existingCategories.map(category => [category.id, category]),
        );

        const categoriesToUpsert = trustServiceCategories.map(dtoItem => {
            const existing = dtoItem.id ? existingCategoriesMap.get(dtoItem.id) : null;

            const category = existing ?? new VendorReviewTrustServiceCategoryMap();
            category.category = VendorReviewServiceCategory[dtoItem.category];
            category.review = vendorReview;

            return category;
        });

        await this.vendorReviewTrustServiceCategoryRepository.upsert(
            categoriesToUpsert.map(category => ({
                id: category.id,
                category: category.category,
                review: { id: category.review.id },
            })),
            {
                conflictPaths: ['id', 'review'],
                skipUpdateIfNoValuesChanged: true,
            },
        );

        this.logger.log(
            PolloMessage.msg(
                `${categoriesToUpsert.length} Category(s) added/updated to report review [id: ${vendorReview.id}] `,
            ),
        );
    }

    async getActiveVendorsReminderStats(
        filters: VendorStatsFilters,
    ): Promise<VendorStatsItem<RenewalDateStatus>[]> {
        const [dueSoon, overdue] = await Promise.all([
            this.vendorRepository.getVendorsRemindersDueSoonCount(filters),
            this.vendorRepository.getVendorsRemindersDueCount(filters),
        ]);
        return [
            { key: RenewalDateStatus.RENEWAL_DUE_SOON, amount: dueSoon },
            { key: RenewalDateStatus.RENEWAL_DUE, amount: overdue },
        ];
    }

    async getActiveVendorsStatsByHasPii(
        filters: VendorStatsFilters,
    ): Promise<VendorStatsItem<boolean>[]> {
        const query = await this.vendorRepository.getVendorsCountByHasPiiValue(filters);
        const hasPiiStat = query.find(({ hasPii }) => Boolean(hasPii));
        const doesNotHavePiiStat = query.find(({ hasPii }) => !Boolean(hasPii));

        return [
            { key: true, amount: getAmountOrDefault(hasPiiStat?.total) },
            {
                key: false,
                amount: getAmountOrDefault(doesNotHavePiiStat?.total),
            },
        ];
    }

    async getActiveVendorsStatsByPasswordPolicy(
        filters: VendorStatsFilters,
    ): Promise<VendorStatsItem<VendorPasswordPolicy>[]> {
        const query = await this.vendorRepository.getVendorsCountByPasswordPolicy(filters);

        const passwordPolicyEnumStringValues: string[] =
            getListOfStringValuesFromEnum(VendorPasswordPolicy);

        return passwordPolicyEnumStringValues.map(passwordPolicy => {
            const statItem = query.find(
                ({ indexedCase }) => indexedCase === VendorPasswordPolicy[passwordPolicy],
            );

            return {
                key: VendorPasswordPolicy[passwordPolicy],
                amount: getAmountOrDefault(statItem?.total),
            };
        });
    }

    async getActiveVendorsStatsByBusinessUnits(
        filters: VendorStatsFilters,
    ): Promise<VendorStatsItem<VendorCategory>[]> {
        const query = await this.vendorRepository.getVendorsCountByBusinessUnit(filters);

        const businessUnitsEnumStringValues: string[] =
            getListOfStringValuesFromEnum(VendorCategory);

        return businessUnitsEnumStringValues.map(businessUnitType => {
            const statItemForBusinessUnit = query.find(
                ({ indexedCase }) => indexedCase === VendorCategory[businessUnitType],
            );

            return {
                key: VendorCategory[businessUnitType],
                amount: getAmountOrDefault(statItemForBusinessUnit?.total),
            };
        });
    }

    async getActiveVendorsStatsByType(
        filters: VendorStatsFilters,
    ): Promise<VendorStatsItem<VendorType>[]> {
        const query = await this.vendorRepository.getVendorsCountByType(filters);

        const typesEnumStringValues: string[] = getListOfStringValuesFromEnum(VendorType);

        return typesEnumStringValues.map(type => {
            const statItemForType = query.find(
                ({ indexedCase }) => indexedCase === VendorType[type],
            );

            return {
                key: VendorType[type],
                amount: getAmountOrDefault(statItemForType?.total),
            };
        });
    }

    async getVendorStatusStats(
        filters: VendorStatsFilters,
    ): Promise<VendorStatsItem<VendorStatus>[]> {
        const query = await this.vendorRepository.getVendorsStatusCount(filters);

        const statusEnumStringValues: string[] = getListOfStringValuesFromEnum(VendorStatus);
        const { excludeArchived } = filters;

        const filteredStatusValues = statusEnumStringValues.filter(
            statusType => !excludeArchived || statusType !== VendorStatus[VendorStatus.ARCHIVED],
        );

        return filteredStatusValues.map(statusType => {
            const statItemForStatus = query.find(({ status }) => {
                if (isNil(status)) {
                    return statusType === VendorStatus[VendorStatus.NONE];
                }

                return status === VendorStatus[statusType];
            });

            return {
                key: VendorStatus[statusType],
                amount: getAmountOrDefault(statItemForStatus?.total),
            };
        });
    }

    async getVendorIsCriticalStats(
        filters: VendorStatsFilters,
    ): Promise<VendorStatsItem<VendorCriticality>[]> {
        const query = await this.vendorRepository.getVendorsIsCriticalCount(filters);

        const isCriticalStats: VendorStatsItem<VendorCriticality>[] = Object.values(
            VendorCriticality,
        ).map(criticalType => {
            const statItemForIsCritical = query.find(({ isCritical }) => {
                if (isNil(isCritical)) {
                    return criticalType === VendorCriticality.NONE;
                }

                const formattedCriticalType = isCritical
                    ? VendorCriticality.YES
                    : VendorCriticality.NO;
                return criticalType === formattedCriticalType;
            });

            return {
                key: criticalType,
                amount: getAmountOrDefault(statItemForIsCritical?.total),
            };
        });

        return isCriticalStats;
    }

    async getVendorImpactLevelStats(
        filters: VendorStatsFilters,
    ): Promise<VendorStatsItem<VendorImpactLevelForStats>[]> {
        const query = await this.vendorRepository.getVendorsByImpactLevelCount(filters);

        const impactLevelStringValues: string[] =
            getListOfStringValuesFromEnum(VendorImpactLevelForStats);

        return impactLevelStringValues.map(impactLevel => {
            const statItem = query.find(({ indexedCase }) => {
                return indexedCase === VendorImpactLevelForStats[impactLevel];
            });

            return {
                key: VendorImpactLevelForStats[impactLevel],
                amount: getAmountOrDefault(statItem?.total),
            };
        });
    }

    async getIsSubProcessorStats(filters: VendorStatsFilters): Promise<VendorStatsItem<boolean>[]> {
        const query = await this.vendorRepository.getVendorsIsSubProcessorCount(filters);

        const isSubProcessorStat = query.find(({ isSubProcessor }) => Boolean(isSubProcessor));
        const isNotSubProcessorStat = query.find(({ isSubProcessor }) => !Boolean(isSubProcessor));

        return [
            {
                key: true,
                amount: getAmountOrDefault(isSubProcessorStat?.total),
            },
            {
                key: false,
                amount: getAmountOrDefault(isNotSubProcessorStat?.total),
            },
        ];
    }

    async getVendorRiskStats(filters: VendorStatsFilters): Promise<VendorStatsItem<VendorRisk>[]> {
        const query = await this.vendorRepository.getVendorsRiskCount(filters);
        const riskEnumStringValues: string[] = getListOfStringValuesFromEnum(VendorRisk);

        return riskEnumStringValues
            .map(riskType => {
                const statItemForRisk = query.find(({ indexedCase }) => {
                    return indexedCase === VendorRisk[riskType];
                });

                return {
                    key: VendorRisk[riskType],
                    amount: getAmountOrDefault(statItemForRisk?.total),
                };
            })
            .sort((a, b) => b.key - a.key);
    }

    private async getVendorStatsFromPromiseArray(promisesScopes): Promise<VendorsStats> {
        const {
            INDEX_OF_BUSINESS_UNIT_STAT,
            INDEX_OF_CRITICALITY_STAT,
            INDEX_OF_HAS_PII_STAT,
            INDEX_OF_IMPACT_LEVEL_STAT,
            INDEX_OF_PASSWORD_POLICY_STAT,
            INDEX_OF_REMINDER_STAT,
            INDEX_OF_RISK_STAT,
            INDEX_OF_STATUS_STAT,
            INDEX_OF_SUBPROCESSOR_STAT,
            INDEX_OF_TYPE_STAT,
            ALL_SCOPES_STATS,
        } = VendorsConstants;

        const promises = new Array(ALL_SCOPES_STATS.length).fill(null);

        promisesScopes.forEach((scope: VendorStatsScope) => {
            switch (scope) {
                case VendorStatsScope.businessUnits:
                    promises[INDEX_OF_BUSINESS_UNIT_STAT] =
                        this.getActiveVendorsStatsByBusinessUnits({
                            excludeArchived: true,
                        });
                    return;
                case VendorStatsScope.isCritical:
                    promises[INDEX_OF_CRITICALITY_STAT] = this.getVendorIsCriticalStats({
                        excludeArchived: true,
                    });
                    return;

                case VendorStatsScope.hasPii:
                    promises[INDEX_OF_HAS_PII_STAT] = this.getActiveVendorsStatsByHasPii({
                        excludeArchived: true,
                    });
                    return;

                case VendorStatsScope.impactLevel:
                    promises[INDEX_OF_IMPACT_LEVEL_STAT] = this.getVendorImpactLevelStats({
                        excludeArchived: true,
                    });
                    return;

                case VendorStatsScope.passwordPolicy:
                    promises[INDEX_OF_PASSWORD_POLICY_STAT] =
                        this.getActiveVendorsStatsByPasswordPolicy({
                            excludeArchived: true,
                        });
                    return;

                case VendorStatsScope.reminder:
                    promises[INDEX_OF_REMINDER_STAT] = this.getActiveVendorsReminderStats({
                        excludeArchived: true,
                    });
                    return;

                case VendorStatsScope.risk:
                    promises[INDEX_OF_RISK_STAT] = this.getVendorRiskStats({
                        excludeArchived: true,
                    });
                    return;

                case VendorStatsScope.status:
                    promises[INDEX_OF_STATUS_STAT] = this.getVendorStatusStats({
                        excludeArchived: true,
                    });
                    return;

                case VendorStatsScope.isSubprocessor:
                    promises[INDEX_OF_SUBPROCESSOR_STAT] = this.getIsSubProcessorStats({
                        excludeArchived: true,
                    });
                    return;

                case VendorStatsScope.type:
                    promises[INDEX_OF_TYPE_STAT] = this.getActiveVendorsStatsByType({
                        excludeArchived: true,
                    });
                    return;

                default:
                    return;
            }
        });

        const results = await Promise.all(promises);

        return {
            businessUnits: results[INDEX_OF_BUSINESS_UNIT_STAT],
            hasPii: results[INDEX_OF_HAS_PII_STAT],
            impactLevel: results[INDEX_OF_IMPACT_LEVEL_STAT],
            isCritical: results[INDEX_OF_CRITICALITY_STAT],
            isSubProcessor: results[INDEX_OF_SUBPROCESSOR_STAT],
            passwordPolicy: results[INDEX_OF_PASSWORD_POLICY_STAT],
            reminder: results[INDEX_OF_REMINDER_STAT],
            risk: results[INDEX_OF_RISK_STAT],
            status: results[INDEX_OF_STATUS_STAT],
            type: results[INDEX_OF_TYPE_STAT],
        };
    }

    async getVendorStatsByScopes(
        account: Account,
        user: User,
        dto: VendorsStatsRequestDto,
    ): Promise<VendorsStats> {
        const { includeScopes, excludeScopes } = dto;

        let listOfScopes: VendorStatsScope[] = VendorsConstants.ALL_SCOPES_STATS;

        if (!isEmpty(excludeScopes)) {
            listOfScopes = listOfScopes.filter(scope => !excludeScopes.includes(scope));
        }
        if (!isEmpty(includeScopes)) {
            listOfScopes = includeScopes;
        }

        return this.getVendorStatsFromPromiseArray(
            listOfScopes.filter(scope => scope !== 'isCritical'),
        );
    }

    async getHasRiskManagerRestrictedView(account: Account, user: User): Promise<boolean> {
        const isReleaseRMExtendedToVendors = await this.featureFlagService.evaluate(
            {
                name: FeatureFlag.RELEASE_RBAC_RISK_MANAGER_EXTENDED_TO_VENDORS,
                category: FeatureFlagCategory.NONE,
                defaultValue: false,
            },
            user,
            account,
        );
        return isReleaseRMExtendedToVendors && userHasRiskManagerRestrictedView(user);
    }

    private getCustomFieldPath(field: string, type: CustomFieldsSortableType): string {
        if (type === CustomFieldsSortableType.OPTIONS) {
            return `customFields.${trim(field)}.value.keyword`;
        }

        return `customFields.${trim(field)}.value`;
    }

    buildPaginationAndSorting(
        sortOptions: { field: string; order: SortDir }[],
        page: number,
        limit: number,
        customFieldSort?: CustomFieldSort | null,
        isRiskSort: boolean = false,
    ): SearchFilterOptions {
        if (!isNil(customFieldSort)) {
            return {
                page: page ?? config.get('pagination.page'),
                size: limit ?? config.get('pagination.limit'),
                sort: [
                    {
                        field: this.getCustomFieldPath(customFieldSort.field, customFieldSort.type),
                        order:
                            SortDir[customFieldSort.order] === SortDir[SortDir.ASC]
                                ? 'asc'
                                : 'desc',
                        missing:
                            SortDir[customFieldSort.order] === SortDir[SortDir.ASC]
                                ? '_first'
                                : '_last',
                    },
                ],
            };
        }

        const FIELDS_TO_SORT_BY_ENUM_VALUE = [SortType.RISK, SortType.IMPACT_LEVEL];
        const FIELDS_TO_SORT_MAP = {
            [SortType.RISK]: VendorRisk,
            [SortType.IMPACT_LEVEL]: VendorImpactLevel,
        };

        const fieldName = sortOptions[0].field;
        const sortDir = sortOptions[0].order;
        if (!isEmpty(sortOptions) && FIELDS_TO_SORT_BY_ENUM_VALUE.includes(SortType[fieldName])) {
            return {
                page: page ?? config.get('pagination.page'),
                size: limit ?? config.get('pagination.limit'),
                scriptSort: this.buildEnumScriptSort(
                    isRiskSort
                        ? this.getRiskSortField(fieldName)
                        : this.getVendorSortField(fieldName),
                    FIELDS_TO_SORT_MAP[SortType[fieldName]] as unknown as Record<string, number>,
                    sortDir ?? SortDir.ASC,
                ),
            };
        }

        const sort = sortOptions.map(({ field, order }) => ({
            field: isRiskSort ? this.getRiskSortField(field) : this.getVendorSortField(field),
            order: order === SortDir.DESC ? 'desc' : 'asc',
            missing: order === SortDir.DESC ? '_last' : '_first',
        }));

        return {
            page: page ?? config.get('pagination.page'),
            size: limit ?? config.get('pagination.limit'),
            sort: sort as {
                field: string;
                order: SearchFilterSortOrder;
                missing?: '_last' | '_first';
            }[],
        };
    }

    /**
     * Provide template url
     * @return string url
     */
    async getVendorsCsvTemplate(account: Account): Promise<CsvDataSetType> {
        let csvResponse: string;
        const { data } = await axios.get(VendorCsvConstants.VENDORS_CSV_TEMPLATE_URL);
        const hasCustomFieldsEnabled = await isCustomFieldsEnabled(account);

        if (hasCustomFieldsEnabled) {
            const vendorsCustomFields = await this.vendorsCustomFieldsService.getCustomFieldsList();
            const csvToJsonParsed = csvToJson(data);
            const vendorTemplate = { ...csvToJsonParsed[0] };
            vendorsCustomFields.forEach(({ customField }) => {
                vendorTemplate[
                    `Custom-Field-${customField.name} ${
                        customField.isRequired ? '(Required)' : '(Optional)'
                    } `
                ] = customField.fieldType === CustomFieldType.TEXT ? 'custom field' : '123';
            });
            csvResponse = jsonToCsv([vendorTemplate]);
        } else {
            csvResponse = data;
        }
        return {
            data: [csvResponse],
            filename: 'vendor-bulk-upload-template-v3.csv',
        };
    }

    private validateDuplicateVendorsBulkUpdate(
        vendors: Vendor[],
    ): VendorsBulkUpdateExclusionValidation {
        const response = {
            nonDuplicateVendors: [] as Vendor[],
            vendorsExcluded: [] as VendorBulkUpdateDescription[],
        };

        if (isEmpty(vendors)) {
            return response;
        }

        const duplicatesCheckMap = new Map();
        for (const vendorToBeVerified of vendors) {
            if (!isEmpty(vendorToBeVerified.name)) {
                const vendorKey = vendorToBeVerified.name.toLowerCase().trim();
                if (isNil(duplicatesCheckMap.get(vendorKey))) {
                    duplicatesCheckMap.set(vendorKey, false);
                } else {
                    duplicatesCheckMap.set(vendorKey, true);
                }
            }
        }
        vendors.forEach(vendor => {
            if (!isEmpty(vendor.name)) {
                const vendorMapKey = vendor.name.toLowerCase().trim();
                // Exclude if this vendor is duplicate
                if (duplicatesCheckMap.get(vendorMapKey) === true) {
                    const vendorExcluded = {
                        ...vendor,
                        exclusionReasons: [
                            VendorBulkUpdateExclusionReason[
                                VendorBulkUpdateExclusionReason.DATA_ROW_DUPLICATE
                            ],
                        ],
                    } as VendorBulkUpdateDescription;
                    response.vendorsExcluded.push(vendorExcluded);
                } else {
                    response.nonDuplicateVendors.push(vendor);
                }
            } else {
                response.nonDuplicateVendors.push(vendor);
            }
        });

        return response;
    }

    @Span()
    private async prefetchVendorsByName(
        account: Account,
        vendors: Vendor[],
    ): Promise<Map<string, Vendor[]>> {
        // Batch preload existing vendors by name to avoid per-row queries
        const names = [
            ...new Set(vendors.filter(v => isNil(v.id) && v.name).map(v => v.name.trim())),
        ];
        const vendorsByNameMap = new Map<string, Vendor[]>();
        if (isEmpty(names)) return vendorsByNameMap;

        const chunkSize = config.get('vendors.csvProcessingBatchSize', 50); // names per query
        const nameChunks = chunk(names, chunkSize);

        const parallel = 3; // consider making this configurable
        const { fulfilled, rejected } = await promiseAllSettledInBatches<string[], Vendor[]>(
            nameChunks,
            parallel,
            async ch => this.vendorRepository.getVendorsForBulkValidationByNames(ch),
        );

        if (rejected.count > 0) {
            this.logger.warn(
                PolloMessage.msg(
                    `prefetchVendorsByName: ${rejected.count} chunk(s) failed: ${rejected.reasons.join(', ')}`,
                ).setIdentifier({
                    failedChunks: rejected.count,
                    reasons: rejected.reasons,
                }),
            );
        }

        for (const batch of fulfilled) {
            for (const vend of batch) {
                const key = vend.name?.trim?.() ?? vend.name;
                const curr = vendorsByNameMap.get(key) ?? [];
                curr.push(vend);
                vendorsByNameMap.set(key, curr);
            }
        }

        return vendorsByNameMap;
    }

    @Span()
    private async prefetchVendorsById(vendors: Vendor[]): Promise<Map<number, Vendor>> {
        // Prefetch existing vendors by id in batches to avoid per-item DB calls
        const ids = [...new Set(vendors.filter(v => !isNil(v.id)).map(v => v.id))];

        const vendorsByIdMap = new Map<number, Vendor>();
        if (!isEmpty(ids)) {
            const batchSize = config.get('vendors.csvProcessingBatchSize', 50);
            const { fulfilled } = await promiseAllSettledInBatches<
                number,
                { id: number; vendor: Vendor | null }
            >(ids, batchSize, async (id: number) => {
                try {
                    const vendor = await this.vendorRepository.getVendorForBulkValidationById(id);
                    return { id, vendor };
                } catch (err) {
                    // Treat not found as null; we'll handle as "to add" below
                    return { id, vendor: null };
                }
            });

            fulfilled.forEach(result => {
                if (result?.vendor) {
                    vendorsByIdMap.set(result.id, result.vendor);
                }
            });
        }

        return vendorsByIdMap;
    }

    private async validateBulkUpdateVendors(
        account: Account,
        vendors: Vendor[],
    ): Promise<VendorsBulkUpdateExclusionValidation> {
        const response = {
            vendorsToAdd: [] as Vendor[],
            vendorsToUpdate: [] as VendorBulkUpdateDescription[],
            vendorsExcluded: [] as VendorBulkUpdateDescription[],
        };

        if (isEmpty(vendors)) {
            return response;
        }

        // Batch preload existing vendors by name to avoid per-row queries
        const vendorsByNameMap = await this.prefetchVendorsByName(account, vendors);

        // Batch preload existing vendors by id to avoid per-row queries
        const vendorsByIdMap = await this.prefetchVendorsById(vendors);

        // Now iterate synchronously over input vendors using pre-fetched data
        for (const vendor of vendors) {
            let vendorsFound: Vendor[] = [];
            if (isNil(vendor.id)) {
                vendorsFound = vendorsByNameMap.get(vendor.name.trim()) ?? [];
            } else {
                const foundById = vendorsByIdMap.get(vendor.id);
                vendorsFound = !isNil(foundById) ? [foundById] : [];
            }

            if (isEmpty(vendorsFound)) {
                const newVendor: Vendor = {
                    ...vendor,
                    risk: vendor.risk ?? VendorRisk.NONE,
                    isSubProcessor: vendor.isSubProcessor ?? false,
                    isSubProcessorActive: vendor.isSubProcessor ?? false,
                    hasPii: vendor.hasPii ?? false,
                    passwordMfaEnabled: vendor.passwordMfaEnabled ?? false,
                    passwordRequiresMinLength: vendor.passwordRequiresMinLength ?? false,
                    passwordRequiresNumber: vendor.passwordRequiresNumber ?? false,
                    passwordRequiresSymbol: vendor.passwordRequiresSymbol ?? false,
                } as Vendor;
                response.vendorsToAdd.push(newVendor);
                continue;
            }

            vendorsFound.forEach(vendorFound => {
                let changesAmount = 0;
                const vendorUpdates = structuredClone(vendorFound);

                for (const [key, value] of Object.entries(vendor)) {
                    if (key === 'user') {
                        if (!isNil(value) && value['id'] !== vendorUpdates.user?.id) {
                            changesAmount += 1;
                            vendorUpdates.user = value as User;
                        }
                    } else if (key === 'contact') {
                        if (!isNil(value) && value['id'] !== vendorUpdates.contact?.id) {
                            changesAmount += 1;
                            vendorUpdates.contact = value as User;
                        }
                    } else if (key === 'integrations') {
                        const integrations = value as Vendor[];
                        const isSameIntegrations = integrations.every(data =>
                            vendorUpdates.integrations?.find(
                                integration => data.id === integration.id,
                            ),
                        );
                        if (!isSameIntegrations) {
                            changesAmount += 1;
                            vendorUpdates.integrations = integrations;
                        }
                    } else if (key === 'customFieldSubmission') {
                        const submissions = value as CustomFieldSubmission[];
                        const filterSubmission: CustomFieldSubmission[] = [];
                        for (const s of submissions) {
                            if (!isNil(s.submission?.value)) {
                                filterSubmission.push(s);
                            }
                        }
                        const isSameSubmissions = filterSubmission.every(data =>
                            vendorUpdates.customFieldSubmission?.find(
                                submission =>
                                    data?.customFieldLocation?.id ===
                                        submission?.customFieldLocation?.id &&
                                    data?.submission?.value === submission?.submission?.value,
                            ),
                        );
                        if (!isSameSubmissions) {
                            changesAmount += 1;
                            vendorUpdates.customFieldSubmission = submissions;
                        }
                    } else if (key === 'status') {
                        if (vendorUpdates.status !== vendor.status) {
                            changesAmount += 1;
                            vendorUpdates.status = vendor.status;
                            if (vendor.status !== VendorStatus.ARCHIVED) {
                                vendorUpdates.archivedAt = null;
                                vendorUpdates.archivedAs = null;
                            }
                        }
                    } else if (key === 'cost') {
                        const vendorUpdatesCost = getAmountOrDefault(vendorUpdates.cost ?? 0, 0);
                        const vendorCost = getAmountOrDefault(vendor.cost ?? 0, 0);
                        if (vendorUpdatesCost !== vendorCost) {
                            changesAmount += 1;
                            vendorUpdates.cost = vendor.cost;
                        }
                    } else if (!isNil(value) && vendorUpdates[key] !== value) {
                        changesAmount += 1;
                        (vendorUpdates as any)[key] = value;
                    }
                }

                if (changesAmount === 0) {
                    const vendorExcluded = {
                        ...vendor,
                        exclusionReasons: [
                            VendorBulkUpdateExclusionReason[
                                VendorBulkUpdateExclusionReason.NO_CHANGES
                            ],
                        ],
                    } as VendorBulkUpdateDescription;
                    response.vendorsExcluded.push(vendorExcluded);
                    return;
                }

                const vendorToUpdate = {
                    ...vendorUpdates,
                    changesAmount,
                } as VendorBulkUpdateDescription;
                response.vendorsToUpdate.push(vendorToUpdate);
            });
        }

        return response;
    }

    private async getVendorFromBulkData(vendorData: VendorCsvFileRecord) {
        const vendor = new Vendor();
        let vendorRelationship: User | null = null;
        let securityOwner: User | null = null;
        let integrations: Vendor[] = [];

        if (isEmail(vendorData[VendorFileHeaderEnum.VENDOR_RELATIONSHIP])) {
            const user = await this.usersCoreService.getUserByEmailNoFail(
                vendorData[VendorFileHeaderEnum.VENDOR_RELATIONSHIP],
            );

            const roles = [Role.ADMIN, Role.TECHGOV, Role.WORKSPACE_ADMINISTRATOR, Role.EMPLOYEE];

            vendorRelationship =
                !isNil(user) && hasRole(user, roles) && isNil(user.personnel?.separationDate)
                    ? user
                    : null;
        }

        if (isEmail(vendorData[VendorFileHeaderEnum.SECURITY_OWNER])) {
            const user = await this.usersCoreService.getUserByEmailNoFail(
                vendorData[VendorFileHeaderEnum.SECURITY_OWNER],
            );

            const roles = [Role.ADMIN, Role.TECHGOV, Role.WORKSPACE_ADMINISTRATOR];

            securityOwner =
                !isNil(user) && hasRole(user, roles) && isNil(user.personnel?.separationDate)
                    ? user
                    : null;
        }

        if (!isEmpty(vendorData[VendorFileHeaderEnum.INTEGRATIONS])) {
            const vendorsIntegration = vendorData[VendorFileHeaderEnum.INTEGRATIONS]
                .split(';')
                .map(integration => integration.trim());
            integrations = await this.vendorRepository.findBy({
                name: In(vendorsIntegration),
            });
        }

        vendor.name = vendorData[VendorFileHeaderEnum.NAME];
        vendor.url = isURL(vendorData[VendorFileHeaderEnum.URL])
            ? vendorData[VendorFileHeaderEnum.URL]
            : null;
        vendor.privacyUrl = isURL(vendorData[VendorFileHeaderEnum.PRIVACY_URL])
            ? vendorData[VendorFileHeaderEnum.PRIVACY_URL]
            : null;
        vendor.termsUrl = isURL(vendorData[VendorFileHeaderEnum.TERMS_URL])
            ? vendorData[VendorFileHeaderEnum.TERMS_URL]
            : null;
        vendor.trustCenterUrl = isURL(vendorData[VendorFileHeaderEnum.TRUST_CENTER_URL])
            ? vendorData[VendorFileHeaderEnum.TRUST_CENTER_URL]
            : null;
        vendor.risk = validateVendorRisk(vendorData[VendorFileHeaderEnum.RISK]);
        vendor.impactLevel = validateVendorImpactLevel(
            vendorData[VendorFileHeaderEnum.IMPACT_LEVEL],
        );
        vendor.servicesProvided = vendorData[VendorFileHeaderEnum.SERVICES_PROVIDED] || null;
        vendor.type = validateVendorType(vendorData[VendorFileHeaderEnum.TYPE]);
        vendor.status = validateVendorStatus(vendorData[VendorFileHeaderEnum.STATUS]);
        vendor.cost = validateCost(vendorData[VendorFileHeaderEnum.ANNUAL_CONTRACT_VALUE]);
        vendor.notes = !isEmpty(vendorData[VendorFileHeaderEnum.ADDITIONAL_NOTES])
            ? vendorData[VendorFileHeaderEnum.ADDITIONAL_NOTES]
            : null;
        if (isStringABoolean(vendorData[VendorFileHeaderEnum.IS_SUB_PROCESSOR])) {
            vendor.isSubProcessor = validateBooleanValue(
                vendorData[VendorFileHeaderEnum.IS_SUB_PROCESSOR],
            );
            vendor.isSubProcessorActive = vendor.isSubProcessor;
        }
        vendor.location =
            vendor.isSubProcessor &&
            !isEmpty(vendorData[VendorFileHeaderEnum.SUB_PROCESSOR_DATA_LOCATION])
                ? vendorData[VendorFileHeaderEnum.SUB_PROCESSOR_DATA_LOCATION]
                : null;
        vendor.integrations = integrations;
        vendor.category = validateVendorCategory(vendorData[VendorFileHeaderEnum.CATEGORY]);
        if (isStringABoolean(vendorData[VendorFileHeaderEnum.HAS_PII])) {
            vendor.hasPii = validateBooleanValue(vendorData[VendorFileHeaderEnum.HAS_PII]);
        }
        vendor.dataStored = !isEmpty(vendorData[VendorFileHeaderEnum.STORED_DATA])
            ? vendorData[VendorFileHeaderEnum.STORED_DATA]
            : null;
        vendor.contact = vendorRelationship;
        vendor.user = securityOwner;
        vendor.contactAtVendor = !isEmpty(vendorData[VendorFileHeaderEnum.CONTACT_AT_VENDOR])
            ? vendorData[VendorFileHeaderEnum.CONTACT_AT_VENDOR]
            : null;
        vendor.contactsEmail = isEmail(vendorData[VendorFileHeaderEnum.CONTACTS_EMAIL])
            ? vendorData[VendorFileHeaderEnum.CONTACTS_EMAIL]
            : null;
        vendor.passwordPolicy = validateVendorPasswordPolicy(
            vendorData[VendorFileHeaderEnum.PASSWORD_POLICY],
        );
        if (isStringABoolean(vendorData[VendorFileHeaderEnum.PASSWORD_REQUIRES_NUMBER])) {
            vendor.passwordRequiresMinLength = validateBooleanValue(
                vendorData[VendorFileHeaderEnum.PASSWORD_REQUIRES_MIN_LENGTH],
            );
        }
        if (!isNil(vendor.passwordRequiresMinLength)) {
            if (
                vendor.passwordRequiresMinLength &&
                validatePasswordMinLength(vendorData[VendorFileHeaderEnum.PASSWORD_MIN_LENGTH])
            ) {
                vendor.passwordMinLength = Number(
                    vendorData[VendorFileHeaderEnum.PASSWORD_MIN_LENGTH],
                );
            } else {
                vendor.passwordRequiresMinLength = false;
            }
        }
        if (isStringABoolean(vendorData[VendorFileHeaderEnum.PASSWORD_REQUIRES_NUMBER])) {
            vendor.passwordRequiresNumber = validateBooleanValue(
                vendorData[VendorFileHeaderEnum.PASSWORD_REQUIRES_NUMBER],
            );
        }
        if (isStringABoolean(vendorData[VendorFileHeaderEnum.PASSWORD_REQUIRES_SYMBOL])) {
            vendor.passwordRequiresSymbol = validateBooleanValue(
                vendorData[VendorFileHeaderEnum.PASSWORD_REQUIRES_SYMBOL],
            );
        }
        if (isStringABoolean(vendorData[VendorFileHeaderEnum.PASSWORD_MFA_ENABLED])) {
            vendor.passwordMfaEnabled = validateBooleanValue(
                vendorData[VendorFileHeaderEnum.PASSWORD_MFA_ENABLED],
            );
        }

        if (!isNil(vendorData.customFields)) {
            vendor.customFieldSubmission = vendorData.customFields.map(
                ({ customField, value, location }) => {
                    const customFieldSubmission = new CustomFieldSubmission();
                    const customFieldLocation = new CustomFieldLocation();
                    customFieldLocation.customField = customField;
                    customFieldLocation.id = Number(location.id);
                    customFieldSubmission.customFieldLocation = customFieldLocation;
                    customFieldSubmission.submission = {
                        fieldType: customField.fieldType,
                        value: value,
                    };
                    return customFieldSubmission;
                },
            );
        } else {
            vendor.customFieldSubmission = [];
        }

        return vendor;
    }

    getCustomFieldsFromCsvFile(validData: VendorCsvFileRecord) {
        const customFields = extractCustomFields(validData);
        return modifyKeys(customFields);
    }

    /**
     * Formats and validates the given value based on the custom field type.
     *
     * @param value - The value to be formatted and validated.
     * @param customField - An optional custom field configuration.
     * @returns The formatted value or option id if valid, otherwise null.
     * @throws BadRequestException if the value is not valid for the given custom field.
     */
    async formatAndValidateValue(value: any, customField?: CustomField) {
        let optionSelected;
        if (isNil(customField)) {
            return null;
        }

        if (isNil(value) && customField.isRequired) {
            throw new BadRequestException('Option is not valid for custom field.');
        }

        const regexPositiveInteger = /^\d+$/;

        if (
            customField.fieldType === CustomFieldType.OPTIONS ||
            customField.fieldType === CustomFieldType.OPTIONS_NUMERIC
        ) {
            const fetchedCustomField = await this.customFieldsRepository.getCustomFieldById(
                customField.id,
            );

            if (!isNil(fetchedCustomField)) {
                optionSelected = fetchedCustomField.options.find(option => {
                    if (isNil(value)) {
                        return value;
                    }
                    return (
                        option.value?.toLocaleLowerCase() === value.toLowerCase() ||
                        option.valueNumber === Number(value)
                    );
                });
            }

            if (!optionSelected && !isNil(value)) {
                throw new BadRequestException('Option is not valid for custom field.');
            }

            if (optionSelected) {
                return optionSelected.id;
            }
        }

        if (customField.fieldType === CustomFieldType.CURRENCY) {
            if (isNil(value)) {
                return null;
            }
            const parsedValue = parseFloat(value?.replace(',', '.'));
            if (!isNil(value) && isNaN(parsedValue)) {
                throw new Error('Value is not a number');
            }
            return parsedValue;
        }

        if (customField.fieldType === CustomFieldType.NUMBER) {
            if ((customField.isRequired && !isNil(value)) || !isNil(value)) {
                const cleanValue = regexPositiveInteger.test(value);
                if (cleanValue) {
                    return parseInt(value);
                } else {
                    return value;
                }
            } else {
                return null;
            }
        }

        if (customField.fieldType === CustomFieldType.TEXT) {
            if ((customField.isRequired && !isNil(value)) || !isNil(value)) {
                return value;
            } else {
                return null;
            }
        }

        if (customField.fieldType === CustomFieldType.LONG_TEXT) {
            const withinBounds = !isNil(value) && value.length <= 30000;
            return withinBounds ? value : null;
        }

        return null;
    }

    /**
     * Process and custom fields from CSV
     * @param {FileValidationResults} fileValidationResults
     */
    async processCustomFieldsFromCsv(
        fileValidationResults: FileValidationResults<VendorCsvFileRecord>,
    ): Promise<void> {
        const invalidDataIndices: number[] = [];

        const processValidData = async (validData: VendorCsvFileRecord, index: number) => {
            const customFieldsAndValue: any[] = [];
            try {
                const customFieldResponse = this.getCustomFieldsFromCsvFile(validData);
                const customFieldsKeys = customFieldResponse.map(obj => Object.keys(obj)[0]);

                if (!isNil(customFieldResponse)) {
                    await asyncForEach(customFieldsKeys, async customFieldKey => {
                        const customField =
                            await this.customFieldsRepository.findCustomFieldByName(customFieldKey);
                        const location = customField
                            ? await this.customFieldsLocationsRepository.getCustomFieldLocationByEntity(
                                  CustomFieldsEntityType.VENDOR,
                                  customField.id,
                              )
                            : null;

                        const valueObj = customFieldResponse.find(field => field[customFieldKey]);
                        const newValue = valueObj ? valueObj[customFieldKey] : null;

                        const validateValue = await this.formatAndValidateValue(
                            newValue,
                            customField ?? undefined,
                        );

                        customFieldsAndValue.push({
                            value: validateValue,
                            customField,
                            location,
                        });
                    });
                }

                if (!isNil(customFieldsAndValue)) {
                    await asyncForEach(
                        customFieldsAndValue,
                        async ({ value, customField, location }) => {
                            const dto = new CustomFieldSubmissionRequestDto();
                            dto.customFieldId = customField.id;
                            dto.value = value;
                            dto.customFieldLocationId = location.id;
                            await this.customFieldsSubmissionCoreService.validateAndPrepareSubmissionRequest(
                                dto,
                            );

                            if (validData.customFields) {
                                validData.customFields.push({
                                    customField,
                                    value,
                                    location,
                                });
                            } else {
                                validData.customFields = [{ customField, value, location }];
                            }
                        },
                    );
                }
            } catch (error) {
                invalidDataIndices.push(index);
            }
        };

        // Process custom fields in batches to prevent timeout
        const batchSize = config.get('vendors.csvProcessingBatchSize', 100);
        const validDataWithIndices = fileValidationResults.validData.map((data, index) => ({
            data,
            index,
        }));

        const { rejected } = await promiseAllSettledInBatches(
            validDataWithIndices,
            batchSize,
            async ({ data, index }) => processValidData(data, index),
            10,
        );

        if (rejected.count > 0) {
            this.logger.warn(
                PolloMessage.msg(
                    `Failed to process ${rejected.count} custom field records: ${rejected.reasons.join(', ')}`,
                ),
            );
        }

        invalidDataIndices.sort((a, b) => b - a);

        invalidDataIndices.forEach(index => {
            const [invalidItem] = fileValidationResults.validData.splice(index, 1);
            fileValidationResults.invalidData.push(invalidItem);
        });
    }

    /**
     * Validate file containing vendors to add and update
     * @param account Account
     * @param user User
     * @param file File metadata
     * @returns Validation data, such as validation status, requests, and issues (if any)
     */
    @Span()
    async validateVendorsBulkUpdateCsvFile(
        account: Account,
        file: UploadedFileType,
    ): Promise<VendorBulkUpdateCsvValidation> {
        try {
            if (isNil(file?.buffer)) {
                throw new BadRequestException('File buffer cannot be null');
            }

            const vendorsCsvValidationResults = new VendorBulkUpdateCsvValidation();

            const uploadedFile = await this.uploader.uploadPrivateFile(
                file,
                UploadType.VENDOR_BULK_DOCUMENT,
                account.id,
            );

            vendorsCsvValidationResults.setFile(uploadedFile);

            const { vendorsToAdd, vendorsExcluded, vendorsToUpdate, issues } =
                await this.validateVendorsCsvFile(account, file);

            vendorsCsvValidationResults.setVendorsToAdd(vendorsToAdd || []);
            vendorsCsvValidationResults.setVendorsExcluded(vendorsExcluded);
            vendorsCsvValidationResults.setVendorsToUpdate(vendorsToUpdate || []);

            if (!isNil(issues) && !isEmpty(issues)) {
                vendorsCsvValidationResults.setIssues(issues);
            }

            return vendorsCsvValidationResults;
        } catch (error) {
            this.logger.error(
                PolloMessage.msg('Error validating vendor bulk update CSV file').setError(error),
            );
            throw error;
        }
    }

    /**
     * Validate file containing customer requests
     *
     * @param account Account
     * @param user User
     * @param file File metadata
     * @returns {VendorsBulkUpdateExclusionValidation}
     */
    @Span()
    async validateVendorsCsvFile(
        account: Account,
        file: UploadedFileType,
    ): Promise<VendorsBulkUpdateExclusionValidation> {
        try {
            this.logger.log(
                PolloMessage.msg('Starting vendor CSV file validation').setIdentifier({
                    vendorFile: file.originalname,
                    fileSize: file.size,
                    mimeType: file.mimetype,
                }),
            );

            if (isNil(file?.buffer)) {
                throw new BadRequestException('File buffer cannot be null');
            }

            const hasCustomFieldsEnabled = await isCustomFieldsEnabled(account);

            const requiredHeaders = Object.values(VendorFileHeaderEnum);

            const fileValidationBuilder = new FileValidationBuilder()
                .setInvalidFileExtensionValidation(file.mimetype, [MimeType.CSV_FILE])
                .setInvalidFileSizeValidation(file.size, csvMaxFileSize)
                .setMissingColumnsValidation(requiredHeaders)
                .setNoRecordsValidation()
                .setEmptyRowValidation()
                .setMaxRowsExceededValidation(config.get('validation.maxRowsCsv'))
                .setInvalidDataValidation(VendorCsvConstants.INVALID_CHARS_REGEX)
                .setMissingRequiredValuesValidation(VendorCsvConstants.REQUIRED_HEADERS)
                .setMaxLengthExceededValidation(VendorsBulkMaxLengthMap);

            const fileValidationResults =
                await this.fileValidationService.validateCsvFile<VendorCsvFileRecord>(
                    account.domain,
                    file,
                    fileValidationBuilder,
                    VendorCsvFileRecord,
                );

            this.logger.log(
                PolloMessage.msg('[validateCsvFile]: CSV file validation completed').setIdentifier({
                    vendorFile: file.originalname,
                    validRecords: fileValidationResults.validData?.length || 0,
                    invalidRecords: fileValidationResults.invalidData?.length || 0,
                    totalIssues: fileValidationResults.issues?.length || 0,
                    issues: fileValidationResults.issues,
                    hasCustomFieldsEnabled,
                }),
            );

            const vendors: Vendor[] = [];

            if (hasCustomFieldsEnabled && !isEmpty(fileValidationResults.validData)) {
                await this.processCustomFieldsFromCsv(fileValidationResults);

                this.logger.log(
                    PolloMessage.msg(
                        '[processCustomFieldsFromCsv]: Custom fields processing completed',
                    ).setIdentifier({
                        vendorFile: file.originalname,
                        recordsProcessed: fileValidationResults.validData?.length || 0,
                    }),
                );
            }

            if (!isEmpty(fileValidationResults.validData)) {
                // Process in batches to prevent timeout
                const batchSize = config.get('vendors.csvProcessingBatchSize', 50);

                this.logger.log(
                    PolloMessage.msg(
                        '[getVendorFromBulkData]: Processing vendors in batches',
                    ).setIdentifier({
                        vendorFile: file.originalname,
                        totalRecords: fileValidationResults.validData.length,
                        batchSize,
                        totalBatches: Math.ceil(fileValidationResults.validData.length / batchSize),
                    }),
                );

                const { fulfilled: batchVendors, rejected } = await promiseAllSettledInBatches(
                    fileValidationResults.validData,
                    batchSize,
                    async data => this.getVendorFromBulkData(data),
                    10,
                );

                vendors.push(...batchVendors);

                if (rejected.count > 0) {
                    this.logger.warn(
                        PolloMessage.msg(
                            `Failed to create ${rejected.count} vendors from CSV data`,
                        ).setIdentifier({
                            vendorFile: file.originalname,
                            rejectedReasons: rejected,
                        }),
                    );
                }

                this.logger.log(
                    PolloMessage.msg(
                        '[getVendorFromBulkData]: All vendor objects created from CSV data',
                    ).setIdentifier({
                        vendorFile: file.originalname,
                        vendorsCreated: vendors.length,
                        failedVendors: rejected.count,
                    }),
                );
            }

            const { vendorsExcluded: duplicateVendors, nonDuplicateVendors } =
                this.validateDuplicateVendorsBulkUpdate(vendors);

            this.logger.log(
                PolloMessage.msg(
                    '[validateDuplicateVendorsBulkUpdate]: Duplicate vendor validation completed',
                ).setIdentifier({
                    vendorFile: file.originalname,
                    duplicateVendors: duplicateVendors.length,
                    nonDuplicateVendors: nonDuplicateVendors?.length || 0,
                }),
            );

            const result = await this.validateBulkUpdateVendors(account, nonDuplicateVendors || []);

            const finalResult = {
                ...result,
                vendorsExcluded: [...result.vendorsExcluded, ...duplicateVendors],
                issues: fileValidationResults.issues,
            };

            this.logger.log(
                PolloMessage.msg(
                    '[validateBulkUpdateVendors]: Vendor CSV file validation completed successfully',
                ).setIdentifier({
                    vendorFile: file.originalname,
                    vendorsToAdd: finalResult.vendorsToAdd?.length || 0,
                    vendorsToUpdate: finalResult.vendorsToUpdate?.length || 0,
                    vendorsExcluded: finalResult.vendorsExcluded?.length || 0,
                    totalIssues: finalResult.issues?.length || 0,
                }),
            );

            return finalResult;
        } catch (error) {
            this.logger.error(PolloMessage.msg('Error validating vendor CSV file').setError(error));
            throw error;
        }
    }

    private async handleVendorsBulkCreatedEvent(
        account: Account,
        user: User,
        vendorsCreated: VendorBulkCreatedResponse[],
    ): Promise<void> {
        await promiseAllSettledInBatches<VendorBulkCreatedResponse, boolean>(
            vendorsCreated,
            10,
            ({ requestDto, vendorCreated }) => {
                const vendorCreatedEvent = !isNil(requestDto.contact?.id)
                    ? new VendorCreatedEvent(
                          account,
                          user,
                          requestDto,
                          vendorCreated,
                          vendorCreated.contact ?? undefined,
                          DEFAULT_TC_WORKSPACE_ID,
                      )
                    : new VendorCreatedEvent(
                          account,
                          user,
                          requestDto,
                          vendorCreated,
                          undefined,
                          DEFAULT_TC_WORKSPACE_ID,
                      );
                this._eventBus.publish(vendorCreatedEvent);

                this._eventBus.publish(
                    new VendorSharedAccountUpdatedEvent(
                        account,
                        vendorCreated.id,
                        vendorCreated.url ?? null,
                        null,
                    ),
                );
                return Promise.resolve(true);
            },
        );
    }

    private async handleVendorsBulkUpdatedEvent(
        account: Account,
        user: User,
        vendorsUpdated: VendorBulkUpdatedResponse[],
    ): Promise<void> {
        await promiseAllSettledInBatches<VendorBulkUpdatedResponse, boolean>(
            vendorsUpdated,
            10,
            ({ requestDto, originalVendor, vendorUpdated }) => {
                const isNewSubProcessor =
                    requestDto.isSubProcessor &&
                    originalVendor.isSubProcessor !== requestDto.isSubProcessor;
                const vendorUpdateEvent = !isNil(requestDto.contact?.id)
                    ? new VendorUpdatedEvent(
                          account,
                          user,
                          requestDto,
                          vendorUpdated,
                          originalVendor,
                          originalVendor.contact ?? undefined,
                          isNewSubProcessor,
                          DEFAULT_TC_WORKSPACE_ID,
                      )
                    : new VendorUpdatedEvent(
                          account,
                          user,
                          requestDto,
                          vendorUpdated,
                          originalVendor,
                          undefined,
                          isNewSubProcessor,
                          DEFAULT_TC_WORKSPACE_ID,
                      );
                this._eventBus.publish(vendorUpdateEvent);

                this._eventBus.publish(
                    new VendorSharedAccountUpdatedEvent(
                        account,
                        vendorUpdated.id,
                        vendorUpdated.url ?? null,
                        originalVendor.url ?? null,
                    ),
                );

                this._eventBus.publish(
                    new VendorStatusUpdatedEvent(
                        account,
                        user,
                        originalVendor,
                        originalVendor.status ?? null,
                        requestDto.status ?? null,
                    ),
                );

                return Promise.resolve(true);
            },
        );
    }

    private async handleVendorsBulkCreationStep(
        account: Account,
        user: User,
        vendorsToAdd: VendorRequestDto[],
    ): Promise<VendorBulkCreatedResponse[]> {
        const { fulfilled, rejected } = await promiseAllSettledInBatches<
            VendorRequestDto,
            VendorBulkCreatedResponse
        >(vendorsToAdd, 10, async dto => {
            const vendor = await this.createVendor(account, user, dto, true);
            return {
                requestDto: dto,
                vendorCreated: vendor,
            };
        });

        if (rejected.count > 0) {
            this.logger.error(
                PolloMessage.msg(
                    `There was an error during the creation step of ${rejected.count} vendors bulk update`,
                ),
                rejected.reasons.join(),
            );
        }

        return fulfilled;
    }

    private async handleVendorsBulkUpdateStep(
        account: Account,
        user: User,
        vendorsToUpdate: VendorBulkUpdateDescription[],
    ): Promise<VendorBulkUpdatedResponse[]> {
        const { fulfilled, rejected } = await promiseAllSettledInBatches<
            VendorBulkUpdateDescription,
            VendorBulkUpdatedResponse
        >(vendorsToUpdate, 10, async vendorToUpdate => {
            if (!isNil(vendorToUpdate.id)) {
                const originalVendor = await this.vendorRepository.findOneOrFail({
                    where: { id: vendorToUpdate.id },
                });

                const requestDto = VendorDtoAdapter.toModifyRequestDto(
                    vendorToUpdate,
                ) as VendorModifyRequestType;
                const vendorUpdated = await this.updateVendor(
                    account,
                    user,
                    vendorToUpdate.id,
                    requestDto,
                    true,
                );
                return {
                    vendorToUpdate,
                    requestDto,
                    originalVendor,
                    vendorUpdated,
                };
            } else {
                return Promise.reject('Vendor id should not be empty');
            }
        });

        if (rejected.count > 0) {
            this.logger.error(
                PolloMessage.msg(
                    `There was an error during the update step of ${rejected.count} vendors bulk update`,
                ),
                rejected.reasons.join(),
            );
        }
        return fulfilled;
    }

    /**
     * Processes a list of vendors to submit their custom field submissions.
     *
     * @param {Account} account
     * @param {User} user
     * @param {Array} vendors - The list of vendors to process.
     * @param {Function} getId - A function that takes a vendor object and returns the vendor ID.
     * @returns {Promise<void>} A promise that resolves when all vendors have been processed.
     * */
    async processVendors(
        account: Account,
        user: User,
        vendors: VendorBulkUpdateDescription[] | undefined,
        getId,
    ) {
        try {
            if (!vendors || vendors.length === 0) {
                return;
            }

            const batchSize = config.get('vendors.csvProcessingBatchSize', 50);
            const { rejected } = await promiseAllSettledInBatches<
                VendorBulkUpdateDescription,
                void
            >(
                vendors,
                batchSize,
                async (vendor: VendorBulkUpdateDescription) => {
                    if (isEmpty(vendor.customFieldSubmission)) {
                        return;
                    }

                    // removing invalid data
                    const validCustomFieldSubmissions = (vendor.customFieldSubmission =
                        vendor.customFieldSubmission.filter(
                            customField =>
                                !isNil(customField.customFieldLocation?.customField?.id) &&
                                !isNil(customField.customFieldLocation?.id),
                        ));

                    const customFieldSubmissionsRequestDto = new CustomFieldSubmissionsRequestDto();

                    customFieldSubmissionsRequestDto.customFieldSubmissions = await Promise.all(
                        validCustomFieldSubmissions.map(async customField => {
                            const customFieldSubmissionRequestDto =
                                new CustomFieldSubmissionRequestDto();

                            customFieldSubmissionRequestDto.customFieldId =
                                customField.customFieldLocation?.customField?.id;

                            customFieldSubmissionRequestDto.customFieldLocationId =
                                customField.customFieldLocation?.id;

                            customFieldSubmissionRequestDto.value =
                                customField.submission?.value ?? null;

                            return customFieldSubmissionRequestDto;
                        }),
                    );

                    const vendorId = getId(vendor);

                    if (!isNil(vendorId)) {
                        await this.vendorsCustomFieldsService.submitCustomFieldSubmission(
                            vendorId,
                            customFieldSubmissionsRequestDto,
                            user,
                            account,
                        );
                    }
                },
                10,
            );

            if (rejected.count > 0) {
                this.logger.warn(
                    PolloMessage.msg(
                        `processVendors: ${rejected.count} vendor(s) failed to process: ${rejected.reasons.join(', ')}`,
                    ),
                );
            }
        } catch (error) {
            this.error(error, account);
            throw error;
        }
    }

    /**
     * Create vendors bulk
     * @param account Account
     * @param user User
     * @param dto VendorsBulkRequestDto
     * @returns Created vendors VendorResponse[]
     */
    @Span()
    async createVendorsBulk(
        account: Account,
        user: User,
        dto: VendorsBulkRequestDto,
    ): Promise<VendorResponse[]> {
        const fileStreamBuffer = await this.downloader.getPrivateFileBuffer(dto.file);
        const uploadedFileType = {
            fieldname: 'Vendors-Directory-For-Bulk-Update',
            originalname: 'Vendors-Directory-For-Bulk-Update',
            encoding: '',
            mimetype: MimeType.CSV_FILE,
            buffer: fileStreamBuffer,
            size: Buffer.byteLength(fileStreamBuffer),
        };

        const { vendorsToAdd = [], vendorsToUpdate = [] } = await this.validateVendorsCsvFile(
            account,
            uploadedFileType,
        );

        if (isEmpty(vendorsToAdd) && isEmpty(vendorsToUpdate)) {
            throw new BadRequestException('There are not vendors to create or update');
        }

        this.logger.log(
            PolloMessage.msg(
                `[createVendorsBulk]: ${vendorsToAdd.length} vendors will be created and ${vendorsToUpdate.length} updated`,
            ).setIdentifier({
                vendorsToAdd,
                vendorsToUpdate,
            }),
        );

        const { vendorsCreatedResponse, vendorsUpdatedResponse } =
            await this.startVendorBulkCreation(account, user, vendorsToAdd, vendorsToUpdate);

        this.logger.log(
            PolloMessage.msg(
                `[createVendorsBulk]: Publishing events for ${vendorsCreatedResponse.length} created vendors and ${vendorsUpdatedResponse.length} updated vendors`,
            ).setIdentifier({
                vendorsCreatedResponse,
                vendorsUpdatedResponse,
            }),
        );
        await this.handleVendorsBulkCreatedEvent(account, user, vendorsCreatedResponse);
        await this.handleVendorsBulkUpdatedEvent(account, user, vendorsUpdatedResponse);

        const vendorsCreated = vendorsCreatedResponse.map(response => response.vendorCreated);
        const vendorsUpdated = vendorsUpdatedResponse.map(response => response.vendorUpdated);

        this.logger.log(
            PolloMessage.msg(
                `[createVendorsBulk]: ${vendorsCreated.length} vendors created and ${vendorsUpdated.length} updated`,
            ).setIdentifier({
                vendorsToAdd,
                vendorsToUpdate,
            }),
        );

        return [...vendorsCreated, ...vendorsUpdated] as VendorResponse[];
    }

    @Span()
    @TenancyTransaction()
    async startVendorBulkCreation(
        account: Account,
        user: User,
        vendorsToAdd: Vendor[],
        vendorsToUpdate: Vendor[],
    ): Promise<{
        vendorsCreatedResponse: VendorBulkCreatedResponse[];
        vendorsUpdatedResponse: VendorBulkUpdatedResponse[];
    }> {
        const hasCustomFieldsEnabled = await isCustomFieldsEnabled(account);

        const vendorsCreatedResponse = await this.handleVendorsBulkCreationStep(
            account,
            user,
            vendorsToAdd.map(vendor => VendorDtoAdapter.toModifyCreateDto(vendor)),
        );

        const vendorsUpdatedResponse = await this.handleVendorsBulkUpdateStep(
            account,
            user,
            vendorsToUpdate,
        );

        if (hasCustomFieldsEnabled) {
            if (!isEmpty(vendorsToUpdate)) {
                await this.processVendors(account, user, vendorsToUpdate, vendor => vendor.id);
            }

            if (!isEmpty(vendorsToAdd)) {
                await this.processVendors(account, user, vendorsToAdd, vendor => {
                    const vendorsCreated = vendorsCreatedResponse.map(
                        response => response.vendorCreated,
                    );
                    const vendorFound = vendorsCreated.find(v => v.name === vendor.name);
                    return !isNil(vendorFound) && vendorFound.id;
                });
            }
        }

        return { vendorsCreatedResponse, vendorsUpdatedResponse };
    }

    /**
     * @deprecated Use VendorsCoreService.validateSharedAccountFromDomain
     **/
    async validateSharedAccountFromDomain(vendorDomain: string, primaryWorkspaceId: number) {
        if (isNil(vendorDomain)) {
            return null;
        }

        // Query the sharedAccount records including deleted ones
        const sharedAccount =
            await this.sharedAccountService.getFirstActiveSharedAccountByDomain(vendorDomain);
        if (isNil(sharedAccount)) {
            return null;
        }

        // Validate with Trust Center for workspaces enabled
        return this.sharedAccountService.retrieveOrCreateSharedAccount(
            sharedAccount,
            primaryWorkspaceId,
        );
    }

    /**
     * @deprecated Use VendorsCoreService.updateVendorSharedAccountId
     *
     * @param vendorId
     * @param sharedAccountId
     * @returns
     */
    async updateVendorSharedAccountId(vendorId: number, sharedAccountId: string | null) {
        if (!isNil(vendorId)) {
            return this.vendorRepository.save({
                id: vendorId,
                sharedAccountId,
            });
        }
    }

    async validateAndFetchVendorIntegrations(
        account: Account,
        integrations?: number[],
        vendorId?: number,
    ) {
        this.validateIntegrations(integrations, vendorId);
        if (isEmpty(integrations)) {
            return null;
        }
        return this.fetchVendorIntegrations(account, integrations);
    }

    private validateIntegrations(integrations?: number[], vendorId?: number) {
        if (isNil(vendorId)) {
            return;
        }
        if (!isNil(integrations) && integrations.includes(vendorId)) {
            throw new BadRequestException(
                `Integrations must not include the vendor itself: ${integrations}, ${vendorId}`,
            );
        }
    }

    private async fetchVendorIntegrations(
        account: Account,
        integrations?: number[],
    ): Promise<Vendor[]> {
        const integrationsList: Vendor[] = [];
        if (isNil(integrations)) {
            return integrationsList;
        }
        await asyncForEach(integrations, async vendorId => {
            const vendorIntegrated = await this.vendorRepository.findOneByOrFail({ id: vendorId });

            // Vendors that are Archived cannot be included
            if (
                vendorIntegrated.status === VendorStatus.ARCHIVED ||
                !isNil(vendorIntegrated.archivedAt)
            ) {
                this.logger.error(
                    PolloMessage.msg(
                        `Error: Archived vendor found, removing from integrations: [vendorId: ${vendorId}]`,
                    ),
                );
                return;
            }
            integrationsList.push(vendorIntegrated);
        });
        return integrationsList;
    }

    /**
     * Remove an archived vendor from the integrations lists of all vendors
     * @param idOfArchived Vendor id
     */
    private async removeArchivedVendorFromIntegrations(account: Account, idOfArchived: number) {
        const vendors = await this.vendorRepository.getAllVendors();
        if (isNil(vendors)) {
            this.logger.error(
                PolloMessage.msg(
                    `Unable to get vendors, could not remove vendor: [vendorId: ${idOfArchived}]`,
                ),
            );
            return;
        }

        try {
            let processedCount = 0;
            await asyncForEach(vendors, async (vendor: Vendor) => {
                const { integrations } = vendor;
                if (isEmpty(integrations)) {
                    this.logger.warn(
                        PolloMessage.msg(
                            `Unable to get vendors, could not remove vendor: [vendorId: ${idOfArchived}]`,
                        ),
                    );
                    processedCount++;
                    return;
                }

                const updatedIntegrations: Vendor[] = [];
                for (const integration of integrations) {
                    if (integration.id !== idOfArchived) {
                        updatedIntegrations.push(integration);
                    }
                }
                if (integrations.length === updatedIntegrations.length) {
                    processedCount++;
                    return;
                }

                // Partial updating since all undefined properties are skipped
                await this.vendorRepository.save({
                    id: vendor.id,
                    integrations: updatedIntegrations,
                });
                processedCount++;
            });
            this.logger.log(
                PolloMessage.msg(
                    `Successfully archived vendor from ${processedCount} integrations: [vendorId: ${idOfArchived}]`,
                ),
            );
        } catch (error) {
            const totalIntegrations = vendors.flatMap(vendor => vendor.integrations).length;
            const processedIntegrations = error?.processedCount;
            this.logger.error(
                PolloMessage.msg(
                    `Failed to archive vendor from integrations: [vendorId: ${idOfArchived}]`,
                )
                    .setError(error)
                    .setIdentifier({
                        failedVendorId: idOfArchived,
                        totalVendors: totalIntegrations,
                        processedVendors: processedIntegrations,
                        remainingVendors: totalIntegrations - processedIntegrations,
                    }),
            );
            throw error;
        }
    }

    /**
     *  @deprecated use VendorOrchestrationService.createVendorDiscoveredThroughConnection
     **/
    async createVendorDiscoveredThroughConnection(
        account: Account,
        connection: ConnectionEntity,
    ): Promise<VendorDiscovered | null> {
        if (isNil(connection)) {
            return null;
        }
        const { clientType: connectionType } = connection;
        if (isNil(connectionType)) {
            return null;
        }

        const vendorName =
            VendorDiscoveredThroughConnectionConfig[connectionType]?.name ??
            getVendorDiscoveredNameFromType(ClientType[connectionType]);
        const vendorsDiscoveredFound =
            await this.vendorDiscoveredRepository.getVendorsDiscoveredByName(vendorName, {
                includeDeleted: true,
            });
        if (!isEmpty(vendorsDiscoveredFound)) {
            return null;
        }

        const vendorDiscovered = new VendorDiscovered();
        vendorDiscovered.name = vendorName;
        const vendorsFound = await this.vendorRepository.getVendorsByName(vendorDiscovered.name);
        if (!isEmpty(vendorsFound)) {
            vendorDiscovered.vendor = first(vendorsFound);
        }
        vendorDiscovered.url = VendorDiscoveredThroughConnectionConfig[connectionType]?.url ?? null;
        vendorDiscovered.logo = await this.getWebsiteLogoFromUrl(account, vendorDiscovered.url);
        vendorDiscovered.connection = connection;
        vendorDiscovered.source = VendorDiscoveredSource.DRATA_CONNECTION;
        vendorDiscovered.isDrataUser =
            !isNil(vendorDiscovered.url) &&
            !isNil(
                await this.validateSharedAccountFromDomain(
                    getDomain(vendorDiscovered.url),
                    DEFAULT_TC_WORKSPACE_ID,
                ),
            );

        return this.vendorDiscoveredRepository.save(vendorDiscovered);
    }

    private async getWebsiteLogoFromUrl(
        account: Account,
        url?: string | null,
    ): Promise<string | null> {
        try {
            if (isNil(url)) {
                return null;
            }

            const data = await this.uploader.uploadVendorLogo(url, account.id);
            return get(data, 'key', null);
        } catch (error) {
            this.logger.warn(
                PolloMessage.msg('Something went wrong fetching the logo from the website url.')
                    .setError(error)
                    .setIdentifier({
                        vendorLogoUrl: url ?? '',
                    }),
            );

            return null;
        }
    }

    async getVendorsUpdateCsvTemplate(account: Account): Promise<CsvDataSetType> {
        let vendors: VendorWithCustomFields[];

        const hasCustomFieldsEnabled = await isCustomFieldsEnabled(account);

        if (hasCustomFieldsEnabled) {
            vendors = await this.vendorRepository.getAllVendorsWithSubmission();
            const vendorsCustomFields = await this.vendorsCustomFieldsService.getCustomFieldsList();
            vendors.forEach(vendor => {
                vendor.customFields = vendorsCustomFields;
            });
        } else {
            vendors = await this.vendorRepository.getAllVendors();
        }

        return {
            data: vendors,
            filename: 'vendor-directory-for-bulk-update',
        };
    }

    async updateVendorRisksStatus(
        vendor: Vendor,
        account: Account,
        vendorStatus: VendorStatus | null,
    ) {
        const risks = await this.riskRepository.find({
            where: {
                currentVersion: {
                    vendors: { id: vendor.id },
                },
            },
        });

        for (const risk of risks) {
            try {
                // eslint-disable-next-line no-await-in-loop
                const riskCurrentVersion = await this.riskVersionRepository.findOne({
                    where: {
                        id: risk.currentVersion.id,
                    },
                });

                if (!isNil(riskCurrentVersion)) {
                    const newRiskVersion = Object.assign(new RiskVersion(), {
                        ...riskCurrentVersion,
                        id: undefined,
                        applicable:
                            vendorStatus === VendorStatus.ARCHIVED ||
                            vendorStatus === VendorStatus.OFFBOARDED
                                ? false
                                : true,
                        risk: risk,
                        vendors: [vendor],
                    });

                    const savedNewRiskVersion =
                        // eslint-disable-next-line no-await-in-loop
                        await this.riskVersionRepository.save(newRiskVersion);

                    if (!isNil(savedNewRiskVersion)) {
                        risk.currentVersion = savedNewRiskVersion;

                        // eslint-disable-next-line no-await-in-loop
                        await this.riskRepository.save(risk);

                        this.logger.log(
                            PolloMessage.msg(
                                `Risk version ID: ${
                                    (newRiskVersion as { id: string }).id
                                } has been created`,
                            ),
                        );
                    }
                }
            } catch (error) {
                const message = `Error creating new risk version [id: ${risk.currentVersion.id}, riskId:${risk.id}]`;

                this.logger.error(PolloMessage.msg(message).setError(error));
            }
        }
    }

    async sendMonthlyDigestEmail(account: Account) {
        const vendorSettings = await this.vendorSettingRepository.findOneBy({});

        this.logger.log(
            PolloMessage.msg(
                `Getting the default review period for: ${account.companyName} has been created`,
            ),
        );

        let defaultReviewPeriod = config.get('vendors.defaultReviewPeriod');
        if (!isNil(vendorSettings)) {
            defaultReviewPeriod = vendorSettings.defaultReviewPeriod;
        }

        this.logger.log(PolloMessage.msg(`The default review period is : ${defaultReviewPeriod} `));

        const startDate = moment().startOf('month').format('MMM YY');
        const endDate = moment().add(2, 'month').endOf('month').format('MMM YY');

        this.logger.log(PolloMessage.msg(`The evaluated period is : ${startDate} to ${endDate}`));

        const vendorMonthlyDigestEmail = await this.emailConfig.vendorMonthlyDigestEmail(
            account.language,
            startDate,
            endDate,
        );

        this.logger.log(
            PolloMessage.msg(
                'Getting vendors with reviews with deadlines in the next 3 months for the evaluated period',
            ),
        );

        const vendors = await this.vendorRepository.getVendorsRemindersDueMonthlyDigest();

        this.logger.log(PolloMessage.msg(`Total vendor with dead lines: ${vendors.length}`));

        const nextReviews = await this.prepareNextReviews(vendors, defaultReviewPeriod);

        const ctaUrl = `${config.get(
            'url.webApp',
        )}/risk/vendors/directory/list?sort=RENEWAL_DATE&sortDir=ASC&page=1`;

        const templateVariables = {
            nextReviews,
            ctaUrl,
        };

        const adminUser = await this.usersCoreService.getAdminUser();

        const emailOptions = {
            ...vendorMonthlyDigestEmail,
            toEmail: adminUser.email,
            templateVariables,
        };

        try {
            await this.emailService.sendEmail(emailOptions, account);
            this.logger.log(
                PolloMessage.msg(
                    `Vendor monthly digest has been sent successfully to: ${adminUser.email}`,
                ),
            );
        } catch (error) {
            this.logger.warn(
                PolloMessage.msg('Error sending the vendor monthly digest').setError(error),
            );
        }
    }

    async prepareNextReviews(vendors, defaultReviewPeriod: number) {
        const maxReviewsForList = 10;
        const moreReviews = vendors.length - maxReviewsForList;

        const nextReviews = {
            firstMonth: {
                name: moment().toDate().toLocaleString('default', { month: 'long' }),
                total: 0,
                reviews: [] as VendorMonthlyDigest[],
                shouldShow: true,
            },
            secondMonth: {
                name: moment()
                    .add(1, 'month')
                    .toDate()
                    .toLocaleString('default', { month: 'long' }),
                total: 0,
                reviews: [] as VendorMonthlyDigest[],
                shouldShow: true,
            },
            thirdMonth: {
                name: moment()
                    .add(2, 'month')
                    .toDate()
                    .toLocaleString('default', { month: 'long' }),
                total: 0,
                reviews: [] as VendorMonthlyDigest[],
                shouldShow: true,
            },
            total: vendors.length,
            moreReviews: moreReviews > 0 ? moreReviews : null,
        };

        let totalReviewsPushed = 0;
        for await (const vendor of vendors) {
            let questionnaireVendors;
            if (!isNil(vendor.scheduleConfiguration)) {
                const { scheduleConfiguration } = vendor;
                const { criteria } = scheduleConfiguration;
                if (!isEmpty(criteria)) {
                    const criteriaType: ScheduleVendorQuestionnairesCriteriaType =
                        JSON.parse(criteria);
                    if (!isEmpty(criteriaType.questionnaireIds)) {
                        questionnaireVendors =
                            await this.questionnairesVendorsRepository.getVendorQuestionnaires(
                                criteriaType.questionnaireIds,
                            );
                        criteriaType.questionnaireVendors = questionnaireVendors;
                    }
                }
            }

            const reviewStartDate = moment(vendor.renewalDate)
                .subtract(defaultReviewPeriod, 'd')
                .format('MMM D YYYY');
            let scheduledQuestionnaire;
            if (!isNil(questionnaireVendors)) {
                const totalQuestionnaires = questionnaireVendors.length;

                if (totalQuestionnaires === 1) {
                    scheduledQuestionnaire = `${questionnaireVendors[0]?.title} scheduled to send on ${reviewStartDate}`;
                }

                if (totalQuestionnaires === 2) {
                    scheduledQuestionnaire = `${questionnaireVendors[0]?.title} and ${questionnaireVendors[1]?.title}  scheduled to send on ${reviewStartDate}`;
                }

                if (totalQuestionnaires > 2) {
                    const remainingQuestionnaires = totalQuestionnaires - 2;
                    scheduledQuestionnaire = `${questionnaireVendors[0]?.title}, ${questionnaireVendors[1]?.title} and  ${remainingQuestionnaires} other questionnaires  scheduled to send on ${reviewStartDate}`;
                }
            }

            const vendorReview = {
                vendorName: vendor.name,
                url: `${config.get('url.webApp')}/risk/vendors/profile/${vendor.id}/overview`,
                category: !isNil(vendor.category)
                    ? VendorCategoryLabels[VendorCategory[vendor.category]]
                    : '',
                riskLevel: !isNil(vendor.risk) ? VendorRiskLabels[VendorRisk[vendor.risk]] : null,
                riskColor: !isNil(vendor.risk) ? VendorRiskColors[VendorRisk[vendor.risk]] : null,
                reviewStartDate,
                reviewDeadLine: moment(vendor.renewalDate).format('MMM D YYYY'),
                scheduledQuestionnaire: !isNil(scheduledQuestionnaire)
                    ? scheduledQuestionnaire
                    : null,
            } as VendorMonthlyDigest;

            const renewalDate = moment(vendor.renewalDate).toDate();

            if (
                renewalDate.toLocaleString('default', { month: 'long' }) ===
                nextReviews.firstMonth.name
            ) {
                nextReviews.firstMonth.total += 1;
                if (totalReviewsPushed < maxReviewsForList) {
                    nextReviews.firstMonth.reviews.push(vendorReview);
                }
            }

            if (
                renewalDate.toLocaleString('default', { month: 'long' }) ===
                nextReviews.secondMonth.name
            ) {
                nextReviews.secondMonth.total += 1;
                if (totalReviewsPushed < maxReviewsForList) {
                    nextReviews.secondMonth.reviews.push(vendorReview);
                }
            }

            if (
                renewalDate.toLocaleString('default', { month: 'long' }) ===
                nextReviews.thirdMonth.name
            ) {
                nextReviews.thirdMonth.total += 1;
                if (totalReviewsPushed < maxReviewsForList) {
                    nextReviews.thirdMonth.reviews.push(vendorReview);
                }
            }
            totalReviewsPushed++;
        }

        if (nextReviews.firstMonth.reviews.length === maxReviewsForList) {
            nextReviews.secondMonth.shouldShow = false;
            nextReviews.thirdMonth.shouldShow = false;
        } else if (
            nextReviews.firstMonth.reviews.length + nextReviews.secondMonth.reviews.length ===
            maxReviewsForList
        ) {
            nextReviews.thirdMonth.shouldShow = false;
        }

        return nextReviews;
    }

    async getVendorFeatureDismissals(
        vendorId: number,
        requestDto: VendorFeatureDismissalRequestDto,
    ): Promise<VendorFeatureDismissal[]> {
        const { type } = requestDto;
        return this.vendorFeatureDismissalRepository.find({
            where: {
                vendor: {
                    id: vendorId,
                },
                type,
            },
        });
    }

    async createVendorFeatureDismissal(
        user: User,
        vendorId: number,
        requestDto: VendorFeatureDismissalRequestDto,
    ): Promise<VendorFeatureDismissal> {
        const { type } = requestDto;
        const vendor = await this.getVendor(vendorId);

        const dismissal = await this.vendorFeatureDismissalRepository.findOne({
            where: {
                type: VendorFeatureDismissalType[type],
                vendor: {
                    id: vendorId,
                },
            },
        });

        if (!isNil(dismissal)) {
            throw new ConflictException(
                `A feature dismissal of type ${VendorFeatureDismissalType[type]} for vendor id ${vendorId} already exists`,
                ErrorCode.VENDOR_FEATURE_DISMISSAL_DUPLICATED,
            );
        }

        const vendorFeatureDismissal = new VendorFeatureDismissal();

        vendorFeatureDismissal.type = VendorFeatureDismissalType[type];
        vendorFeatureDismissal.user = user;
        vendorFeatureDismissal.vendor = vendor;

        return this.vendorFeatureDismissalRepository.save(vendorFeatureDismissal);
    }

    /**
     * @deprecated Use VendorsCoreService.getSharedVendorsByDomain
     *
     * @param domain
     * @returns
     */
    async getSharedVendorsByDomain(domain: string): Promise<Vendor[]> {
        return this.vendorRepository.getSharedVendorsByDomain(domain);
    }

    /**
     *  @deprecated Use VendorsOrchestrationService.createVendorsDiscovered
     **/
    async createVendorsDiscovered(
        account: Account,
        vendorsToDiscover: VendorToDiscover[],
        clientType: ClientType,
    ) {
        this.logger.log(PolloMessage.msg('VendorProxyService createVendorsDiscovered init'));

        const currentVendorsDiscovered =
            await this.vendorDiscoveredRepository.listAllVendorsDiscovered(true);

        const currentVendorsDiscoveredIds: number[] = [];

        currentVendorsDiscovered.forEach(vendorDiscovered => {
            if (!isNil(vendorDiscovered.accessApplicationExternalId)) {
                currentVendorsDiscoveredIds.push(vendorDiscovered.accessApplicationExternalId);
            }
        });

        const currentExternalIds = vendorsToDiscover.map(
            vendorDiscovered => vendorDiscovered.externalId,
        );

        const accessApplications = await this.accessApplicationRepository.getVendorsToDiscover(
            currentVendorsDiscoveredIds,
            currentExternalIds,
            clientType,
        );

        const vendorsDiscoveredToSave = [];

        const processVendorDiscovered = async (application: AccessApplication) => {
            const vendorsDiscoveredFound =
                this.vendorDiscoveredRepository.getVendorsDiscoveredByName(application.name, {
                    includeDeleted: true,
                });

            if (!isEmpty(vendorsDiscoveredFound)) {
                return null;
            }

            const vendorDiscovered = new VendorDiscovered();
            vendorDiscovered.accessApplicationExternalId = application.id;
            vendorDiscovered.name = application.name;

            if (!isEmpty(vendorsToDiscover)) {
                const vendorApplication = vendorsToDiscover.find(
                    vendorToDiscover => vendorToDiscover.externalId === application.externalId,
                );

                vendorDiscovered.isDrataUser = vendorApplication.isDrataUser;
                vendorDiscovered.url = ensureSecureUrl(vendorApplication.domain);

                if (!isURL(vendorDiscovered.url)) {
                    vendorDiscovered.url = null;
                }

                vendorDiscovered.logo = application.logo;

                const knowVendors = await this.vendorRepository.getVendorsByName(application.name);
                const [vendor = null] = knowVendors ?? [];
                vendorDiscovered.vendor = vendor;

                /**
                 * TODO: In the future when a new SSO is added, we should adapt the source accordingly
                 */
                vendorDiscovered.source = VendorDiscoveredSource.OKTA_SSO;
            }

            vendorsDiscoveredToSave.push(vendorDiscovered);
        };

        if (isEmpty(accessApplications)) {
            return;
        }

        this.logger.log(
            PolloMessage.msg(
                `VendorProxyService createVendorsDiscovered processing ${accessApplications.length}`,
            ),
        );

        for (const apps of chunk(accessApplications, 10)) {
            this.logger.log(
                PolloMessage.msg(
                    `VendorProxyService createVendorsDiscovered processing chunk ${apps.length}`,
                ),
            );

            // eslint-disable-next-line no-await-in-loop
            await Promise.all([...apps.map(processVendorDiscovered)]);
        }

        try {
            await this.vendorDiscoveredRepository.save(vendorsDiscoveredToSave);
        } catch (error) {
            this.logger.log(
                PolloMessage.msg(
                    `VendorProxyService createVendorsDiscovered newVendorDiscovered error ${JSON.stringify(
                        error,
                    )}`,
                ).setError(error),
            );

            throw new Error(error);
        }
    }

    private async updateVendorRenewalDate(
        renewalDate: string,
        renewalScheduleType: RenewalScheduleType,
    ): Promise<string> {
        return moment(renewalDate)
            .utc()
            .add(renewalScheduleMonthValues[renewalScheduleType], 'M')
            .format('YYYY-MM-DD');
    }

    private async isListVendorPerformanceEnabled(account: Account) {
        const isFFEnabled = await this.featureFlagService.evaluateAsTenant(
            {
                name: FeatureFlag.RELEASE_LIST_VENDOR_PERFORMANCE,
                category: FeatureFlagCategory.NONE,
                defaultValue: false,
            },
            account,
        );
        if (!isFFEnabled) {
            this.logger.log(
                PolloMessage.msg(
                    'GET /vendors endpoint optimizations Flag is disabled',
                ).setIdentifier({
                    flag: isFFEnabled,
                }),
            );
        }
        return isFFEnabled;
    }

    async getPrimaryWorkspaceOrFail(): Promise<Product> {
        const product = await this.workspaceBaseService.getPrimaryProduct();

        if (isNil(product)) {
            throw new NotFoundException('Primary workspace not found');
        }

        return product;
    }

    async getVendorsFilters(account: Account, user: User): Promise<VendorFilterOptions> {
        const hasRiskManagerRestrictedView = await this.getHasRiskManagerRestrictedView(
            account,
            user,
        );

        const product = await this.getPrimaryWorkspaceOrFail();

        // vendorsIds will be undefined if hasRiskManagerRestrictedView is false
        const vendorIds = await this.vendorRisksRepository.getRisksVendors(
            account.id,
            product.id,
            hasRiskManagerRestrictedView,
            user.id,
        );

        const customFields = await this.customFieldsCoreService.getCustomFieldsForFacets(
            CustomFieldsEntityType.VENDOR,
        );

        return this.vendorV2Repository.getVendorFacets(
            account.id,
            product.id,
            customFields,
            vendorIds,
        );
    }

    private get accountEventControlRepository(): Repository<AccountEventControl> {
        return this.getTenantRepository(AccountEventControl);
    }

    private get riskRepository(): RiskRepository {
        return this.getCustomTenantRepository(RiskRepository);
    }

    private get riskVersionRepository(): Repository<RiskVersion> {
        return this.getTenantRepository(RiskVersion);
    }
    private get vendorRepository(): VendorRepository {
        return this.getCustomTenantRepository(VendorRepository);
    }
    private get vendorReviewRepository(): VendorReviewRepository {
        return this.getCustomTenantRepository(VendorReviewRepository);
    }

    private get vendorDocumentRepository(): VendorDocumentRepository {
        return this.getCustomTenantRepository(VendorDocumentRepository);
    }

    private get vendorReviewTrustServiceCategoryRepository(): Repository<VendorReviewTrustServiceCategoryMap> {
        return this.getTenantRepository(VendorReviewTrustServiceCategoryMap);
    }

    private get vendorFindingRepository(): Repository<VendorReviewFinding> {
        return this.getTenantRepository(VendorReviewFinding);
    }

    private get vendorFeatureDismissalRepository(): Repository<VendorFeatureDismissal> {
        return this.getTenantRepository(VendorFeatureDismissal);
    }

    private get vendorUserControlRepository(): Repository<VendorReviewUserControl> {
        return this.getTenantRepository(VendorReviewUserControl);
    }

    private get vendorServiceRepository(): Repository<VendorReviewService> {
        return this.getTenantRepository(VendorReviewService);
    }

    private get vendorLocationRepository(): Repository<VendorReviewLocation> {
        return this.getTenantRepository(VendorReviewLocation);
    }

    private get vendorDiscoveredRepository(): VendorDiscoveredRepository {
        return this.getCustomTenantRepository(VendorDiscoveredRepository);
    }

    private get vendorDataAccessedOrProcessedRepository(): VendorDataAccessedOrProcessedRepository {
        return this.getCustomTenantRepository(VendorDataAccessedOrProcessedRepository);
    }

    private get vendorSettingRepository(): Repository<VendorSetting> {
        return this.getTenantRepository(VendorSetting);
    }

    private get questionnairesVendorsRepository(): QuestionnaireVendorSecurityTypeformRepository {
        return this.getCustomTenantRepository(QuestionnaireVendorSecurityTypeformRepository);
    }

    private get vendorSecurityReviewRepository(): VendorSecurityReviewRepository {
        return this.getCustomTenantRepository(VendorSecurityReviewRepository);
    }

    private get vendorSecurityReviewDocumentRepository(): VendorSecurityReviewDocumentRepository {
        return this.getCustomTenantRepository(VendorSecurityReviewDocumentRepository);
    }

    private get customFieldsRepository(): CustomFieldsRepository {
        return this.getCustomTenantRepository(CustomFieldsRepository);
    }

    private get customFieldsLocationsRepository(): CustomFieldsLocationsRepository {
        return this.getCustomTenantRepository(CustomFieldsLocationsRepository);
    }

    private get accessApplicationRepository(): AccessApplicationRepository {
        return this.getCustomTenantRepository(AccessApplicationRepository);
    }
}

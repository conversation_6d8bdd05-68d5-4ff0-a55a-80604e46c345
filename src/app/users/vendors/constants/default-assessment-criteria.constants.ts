import { VendorImpactLevelLabels } from 'app/users/vendors/enums/vendor-impact-level-labels.enum';
import { DefaultAssessmentCriteria } from 'app/users/vendors/interfaces/default-assessment-criteria.interface';

export const DEFAULT_CRITERIA_SOURCE = 'Drata default';

export const DEFAULT_ASSESSMENT_CRITERIA: DefaultAssessmentCriteria[] = [
    {
        name: 'Security Contact Information',
        description: `{ "title": "The vendor provides valid contact information for their security team or security personnel." },
    { "title": "The vendor has designated security personnel or a security team available for security-related matters." },
    { "title": "The vendor provides a primary point of contact (name and/or email) for security communications." }`,
        testableCriteria:
            'To verify that the vendor has established proper security contact channels and dedicated security ' +
            'personnel for handling security-related communications and incidents.',
        questions: [
            {
                question:
                    'Does the vendor provide valid contact information for their security team or security personnel?',
                answer: 'Yes',
            },
            {
                question:
                    'Has the vendor designated specific security personnel or a security team available for security-related matters?',
                answer: 'Yes',
            },
            {
                question:
                    'Does the vendor provide a primary point of contact (name and/or email) for security communications?',
                answer: 'Yes',
            },
        ],
        impactLevels: [VendorImpactLevelLabels.CRITICAL],
    },
    {
        name: 'Infrastructure and Data Processing Transparency',
        description:
            `{ "title": "The vendor identifies and discloses the cloud service providers used to deliver their services." },
    { "title": "The vendor specifies the geographic locations and regions where customer data is stored and processed." },
    { "title": "The vendor identifies content delivery network (CDN) providers used in their service delivery." },
    { "title": "The vendor discloses whether they collect, access, process, or retain customer data that includes ` +
            `personal information or personal data." },
    { "title": "The vendor identifies DNS service providers used in their infrastructure." },
    { "title": "The vendor maintains and provides access to a current list of subprocessors involved in data processing activities." }`,
        testableCriteria:
            "To assess the vendor's transparency regarding their technology infrastructure, data storage " +
            'locations, third-party service providers, data processing activities, and subprocessor relationships ' +
            'that may impact data security and compliance obligations.',
        questions: [
            {
                question:
                    'Does the vendor identify and disclose the cloud service providers (CSPs) used to deliver their services?',
                answer: 'Yes',
            },
            {
                question:
                    'Does the vendor specify the geographic locations and regions where customer data is stored and processed?',
                answer: 'Yes',
            },
            {
                question:
                    'Does the vendor identify content delivery network (CDN) providers used in their service delivery?',
                answer: 'Yes',
            },
            {
                question:
                    'Does the vendor disclose whether they collect, access, process, or retain customer data that ' +
                    'includes personal information (PI) or personal data (PD)?',
                answer: 'Yes',
            },
            {
                question:
                    'Does the vendor identify Domain Name System (DNS) service providers used in their infrastructure?',
                answer: 'Yes',
            },
            {
                question:
                    'Does the vendor maintain and provide access to a current list of subprocessors involved in data processing activities?',
                answer: 'Yes',
            },
        ],
        impactLevels: [VendorImpactLevelLabels.CRITICAL],
    },
    {
        name: 'Compliance Documentation',
        description: `{ "title": "The vendor identifies specific compliance frameworks and regulations they adhere to." },
    { "title": "The vendor provides documented compliance reports or certifications as evidence of their compliance status." }`,
        testableCriteria:
            'To verify that the vendor maintains compliance with relevant regulations and standards and can provide ' +
            'documented evidence of their compliance status through formal reports or certifications.',
        questions: [
            {
                question:
                    'What specific compliance frameworks and regulations does the organization adhere to?',
                answer: 'Yes',
            },
            {
                question:
                    'Does the organization provide documented compliance reports or certifications (e.g., SOC 2, ISO 27001, HIPAA) ' +
                    'as evidence of compliance status?',
                answer: 'Yes',
            },
        ],
        impactLevels: [VendorImpactLevelLabels.CRITICAL],
    },
    {
        name: 'Breach History and Notification',
        description:
            `{ "title": "The vendor has not experienced any data breaches or security incidents in the last 36 months." },
    { "title": "The vendor has documented contractual obligations and procedures for notifying clients of data breaches ` +
            `within a specified timeframe." },
    { "title": "The vendor maintains record-keeping obligations for all security breaches and incidents." }`,
        testableCriteria:
            'To assess whether the vendor has maintained a clean security record over the past 36 months and has ' +
            'established proper contractual obligations and procedures for breach notification and record-keeping ' +
            'when incidents do occur.',
        questions: [
            {
                question:
                    'Has the vendor experienced any data breaches or security incidents in the last 36 months?',
                answer: 'Yes',
            },
            {
                question:
                    'Does the vendor have documented contractual obligations and procedures for notifying clients of data ' +
                    'breaches within a specified timeframe?',
                answer: 'Yes',
            },
            {
                question:
                    'Does the vendor maintain record-keeping obligations for all security breaches and incidents?',
                answer: 'Yes',
            },
        ],
        impactLevels: [VendorImpactLevelLabels.CRITICAL],
    },
    {
        name: 'Recent Penetration Testing',
        description: `{ "title": "The vendor completes a penetration test every 2 months." },
    { "title": "The vendor can provide documentation (such as an executive summary or report) from their most recent penetration test." }`,
        testableCriteria:
            'To verify that the vendor conducts regular penetration testing and can provide evidence of recent ' +
            'testing activities through both timing information and documentation.',
        questions: [
            {
                question: 'Has a penetration test been completed within the last 12 months?',
                answer: 'Yes',
            },
            {
                question:
                    'Can documentation (such as an executive summary or report) from the most recent penetration test be provided?',
                answer: 'Yes',
            },
        ],
        impactLevels: [VendorImpactLevelLabels.CRITICAL],
    },
    {
        name: 'Corporate Device Endpoint Protection',
        description:
            `{ "title": "The vendor has not experienced any data breaches or security incidents in the last 36 months." },
    { "title": "The vendor has documented contractual obligations and procedures for notifying clients of data breaches ` +
            `within a specified timeframe." },
    { "title": "The vendor maintains record-keeping obligations for all security breaches and incidents." }`,
        testableCriteria:
            'Verify that the vendor has implemented comprehensive endpoint protection through antivirus/EDR ' +
            'solutions deployed across all corporate devices, with clear identification of the specific security ' +
            'solution used.',
        questions: [
            {
                question:
                    'Does the vendor install antivirus or endpoint detection and response (EDR) software on all corporate devices?',
                answer: 'Yes',
            },
            {
                question:
                    'Can the vendor identify the specific antivirus/endpoint detection and response (EDR) solution ' +
                    'deployed on corporate devices?',
                answer: 'Yes',
            },
        ],
        impactLevels: [VendorImpactLevelLabels.CRITICAL],
    },
    {
        name: 'MDM Solution Implementation',
        description: `{ "title": "The vendor has deployed a specific MDM solution to manage corporate devices." }`,
        testableCriteria:
            'To verify that the vendor has implemented a Mobile Device Management (MDM) solution to manage and ' +
            'secure corporate devices within their organization.',
        questions: [
            {
                question:
                    'What specific Mobile Device Management (MDM) solution has been deployed to manage corporate devices?',
                answer: 'Yes',
            },
        ],
        impactLevels: [VendorImpactLevelLabels.CRITICAL],
    },
    {
        name: 'SSO/LDAP Authentication Enforcement',
        description: `{ "title": "The company enforces SSO or LDAP authentication for access to applications or systems." },
    { "title": "The company can identify the specific SSO or LDAP provider used for authentication." }`,
        testableCriteria:
            'To verify that the vendor enforces centralized authentication mechanisms (SSO or LDAP) for ' +
            'accessing applications and systems, and can identify the specific authentication provider used.',
        questions: [
            {
                question:
                    'Does the company enforce Single Sign-On (SSO) or Lightweight Directory Access Protocol (LDAP) ' +
                    'authentication for access to applications or systems?',
                answer: 'Yes',
            },
            {
                question:
                    'Can the company identify the specific Single Sign-On (SSO) or Lightweight Directory Access Protocol (LDAP) ' +
                    'provider used for authentication?',
                answer: 'Yes',
            },
        ],
        impactLevels: [VendorImpactLevelLabels.CRITICAL],
    },
    {
        name: 'Datacenter and Cloud Security Framework',
        description:
            `{ "title": "The vendor has documented security controls and practices for protecting data and systems in their ` +
            `datacenter/cloud environments across multiple security domains (logical, physical, environmental, software, ` +
            `privacy, and/or regulatory compliance)." },
    { "title": "The vendor's security approach includes specific measures for data protection throughout its ` +
            `lifecycle (entry, transfer, storage, and access)." }`,
        testableCriteria:
            'To assess whether the vendor has implemented a comprehensive security framework covering the ' +
            'fundamental security domains necessary to protect data and systems in datacenter and cloud ' +
            'environments, including logical, physical, environmental, software, privacy, and regulatory ' +
            'compliance controls.',
        questions: [
            {
                question:
                    'Does the vendor have documented security controls and practices for protecting data and systems in their ' +
                    /* 'datacenter/cloud environments across multiple security domains (logical, physical, environmental, software, ' + */
                    'privacy, and regulatory compliance)?',
                answer: 'Yes',
            },
            {
                question:
                    "Does the vendor's security approach include specific measures for data protection throughout its " +
                    'lifecycle (entry, transfer, storage, and access)?',
                answer: 'Yes',
            },
        ],
        impactLevels: [VendorImpactLevelLabels.CRITICAL],
    },
    {
        name: 'SDLC Security Implementation',
        description:
            `{ "title": "The vendor has documented a formal SDLC process that includes security considerations integrated ` +
            `throughout the development lifecycle." },
    { "title": "The vendor implements pre-release security controls including vulnerability testing and code review ` +
            `processes that prevent vulnerable code from reaching production." },
    { "title": "The vendor has established post-release vulnerability management processes that include identification, ` +
            `assessment, prioritization, and remediation of security vulnerabilities with defined timelines." }`,
        testableCriteria:
            'To verify that the vendor has implemented a structured software development lifecycle (SDLC) process ' +
            'that incorporates security controls throughout development, including pre-release security testing ' +
            'and vulnerability management, and post-release vulnerability identification and remediation processes.',
        questions: [
            {
                question:
                    'Does the organization have a documented formal software development lifecycle (SDLC) process that ' +
                    'integrates security considerations throughout all development phases?',
                answer: 'Yes',
            },
            {
                question:
                    'Does the organization implement pre-release security controls including vulnerability testing and code ' +
                    'review processes to prevent vulnerable code from reaching production environments?',
                answer: 'Yes',
            },
            {
                question:
                    'Has the organization established post-release vulnerability management processes that include ',
                /* 'identification, assessment, prioritization, and remediation of security vulnerabilities with defined timelines?' */ answer: 'Yes',
            },
        ],
        impactLevels: [VendorImpactLevelLabels.CRITICAL],
    },
    {
        name: 'WAF/RASP Application Protection',
        description:
            `{ "title": "The vendor has deployed either Web Application Firewall (WAF) or Runtime Application ` +
            `Self-Protection (RASP) technology inline with their application infrastructure." }`,
        testableCriteria:
            'To verify that the vendor has implemented Web Application Firewall (WAF) or Runtime Application ' +
            'Self-Protection (RASP) technology deployed inline with their application to provide real-time ' +
            'protection against web-based attacks and application-layer threats.',
        questions: [
            {
                question:
                    'Has the organization deployed either Web Application Firewall (WAF) or Runtime Application ' +
                    'Self-Protection (RASP) technology inline with the application infrastructure?',
                answer: 'Yes',
            },
        ],
        impactLevels: [VendorImpactLevelLabels.CRITICAL],
    },
    {
        name: 'SAST/DAST Code Testing',
        description: `{ "title": "The company performs Static Application Security Testing (SAST) on all code before release." },
    { "title": "The company performs Dynamic Application Security Testing (DAST) on all code before release." }`,
        testableCriteria:
            'To verify that the vendor implements both Static Application Security Testing (SAST) and Dynamic ' +
            'Application Security Testing (DAST) for all code releases to identify security vulnerabilities ' +
            'before deployment.',
        questions: [
            {
                question:
                    'Does the company perform Static Application Security Testing (SAST) on all code before release?',
                answer: 'Yes',
            },
            {
                question:
                    'Does the company perform Dynamic Application Security Testing (DAST) on all code before release?',
                answer: 'Yes',
            },
        ],
        impactLevels: [VendorImpactLevelLabels.CRITICAL],
    },
    {
        name: 'Public Security and Privacy Documentation',
        description:
            `{ "title": "The vendor provides a publicly accessible URL to a security-focused web page or document ` +
            `that demonstrates their security practices and commitments." },
    { "title": "The vendor provides a publicly accessible URL to their privacy policy document." }`,
        testableCriteria:
            'To verify that the vendor maintains publicly accessible documentation demonstrating their commitment ' +
            'to security and privacy through dedicated web pages or policy documents.',
        questions: [
            {
                question:
                    'Does the vendor provide a publicly accessible URL to a security-focused web page or document ' +
                    'that demonstrates their security practices and commitments?',
                answer: 'Yes',
            },
            {
                question:
                    'Does the vendor provide a publicly accessible URL to their privacy policy document?',
                answer: 'Yes',
            },
        ],
        impactLevels: [VendorImpactLevelLabels.CRITICAL],
    },
    {
        name: 'Information Security Policy Documentation',
        description: `{ "title": "A formal information security policy document exists and has been provided by the vendor." }`,
        testableCriteria:
            'To verify that the vendor has established and documented a formal information security policy that ' +
            'governs their security practices and demonstrates their commitment to information security management.',
        questions: [
            {
                question:
                    'Does a formal information security policy document exist and has it been made available for review?',
                answer: 'Yes',
            },
        ],
        impactLevels: [VendorImpactLevelLabels.CRITICAL],
    },
    {
        name: 'Password Policy Documentation',
        description: `{ "title": "A documented password policy exists and is available for review." }`,
        testableCriteria:
            'To verify that the vendor has established and documented a formal password policy that governs ' +
            'password requirements and security practices within their organization.',
        questions: [
            {
                question: 'Does a documented password policy exist and is it available for review?',
                answer: 'Yes',
            },
        ],
        impactLevels: [VendorImpactLevelLabels.CRITICAL],
    },
    {
        name: 'Compliance Policy Framework',
        description:
            `{ "title": "Formal policies and procedures exist that address compliance with legislative, regulatory, ` +
            `and contractual requirements." },
    { "title": "The policies and procedures specifically cover intellectual property rights for business processes ` +
            `and IT software products." },
    { "title": "The compliance policies are documented and accessible." }`,
        testableCriteria:
            'To verify that the vendor has established formal policies and procedures that ensure compliance ' +
            'with applicable legislative, regulatory, and contractual requirements, including intellectual ' +
            'property rights related to business processes and IT software products.',
        questions: [
            {
                question:
                    'Does the organization have formal policies and procedures that address compliance with legislative, ' +
                    'regulatory, and contractual requirements?',
                answer: 'Yes',
            },
            {
                question:
                    'Do the compliance policies and procedures specifically cover intellectual property rights for business ' +
                    'processes and IT software products?',
                answer: 'Yes',
            },
            {
                question:
                    'Are the compliance policies documented and accessible to relevant personnel?',
                answer: 'Yes',
            },
        ],
        impactLevels: [VendorImpactLevelLabels.CRITICAL],
    },
    {
        name: 'BCDR Plan Documentation',
        description: `{ "title": "A documented Business Continuity and Disaster Recovery (BCDR) plan exists and is available for review." }`,
        testableCriteria:
            'To verify that the vendor has established and documented a Business Continuity and Disaster ' +
            'Recovery (BCDR) plan that outlines procedures for maintaining operations and recovering from ' +
            'disruptive events.',
        questions: [
            {
                question:
                    'Does a documented Business Continuity and Disaster Recovery (BCDR) plan exist and is it available for review?',
                answer: 'Yes',
            },
        ],
        impactLevels: [VendorImpactLevelLabels.CRITICAL],
    },
    {
        name: 'Legal Compliance Reporting',
        description: `{ "title": "The vendor maintains internal management reporting processes that comply with applicable legal requirements." },
    { "title": "The vendor maintains external reporting processes to government agencies that comply with applicable legal requirements." }`,
        testableCriteria:
            'To verify that the vendor maintains appropriate internal management reporting and external reporting ' +
            'to government agencies in accordance with applicable laws and regulations.',
        questions: [
            {
                question:
                    'Does the vendor maintain internal management reporting processes that comply with applicable legal requirements?',
                answer: 'Yes',
            },
            {
                question:
                    'Does the vendor maintain external reporting processes to government agencies that comply with applicable legal requirements?',
                answer: 'Yes',
            },
        ],
        impactLevels: [VendorImpactLevelLabels.CRITICAL],
    },
    {
        name: 'Background Screening Compliance',
        description: `{ "title": "The company has a documented background screening policy that applies to all job candidates." },
    { "title": "The background screening process complies with relevant legal and ethical requirements." }`,
        testableCriteria:
            'To verify that the vendor has established and implements a comprehensive background screening ' +
            'process for all job candidates that meets appropriate security standards while maintaining ' +
            'compliance with applicable legal and ethical requirements.',
        questions: [
            {
                question:
                    'Does the company have a documented background screening policy that applies to all job candidates?',
                answer: 'Yes',
            },
            {
                question:
                    'Does the background screening process comply with relevant legal and ethical requirements?',
                answer: 'Yes',
            },
        ],
        impactLevels: [VendorImpactLevelLabels.CRITICAL],
    },
    {
        name: 'Workforce Security Awareness Training',
        description:
            `{ "title": "The company has a documented information security awareness, education, and training ` +
            `program that covers all workforce personnel." }`,
        testableCriteria:
            'To verify that the vendor has established and implements a comprehensive information security ' +
            'awareness, education, and training program that ensures all workforce personnel receive appropriate ' +
            'levels of security training.',
        questions: [
            {
                question:
                    'Does the company have a documented information security awareness, education, and training program ' +
                    'that covers all workforce personnel?',
                answer: 'Yes',
            },
        ],
        impactLevels: [VendorImpactLevelLabels.CRITICAL],
    },
    {
        name: 'Formal Disciplinary Process',
        description: `{ "title": "A formal disciplinary process exists and is documented." },
    { "title": "The disciplinary process has been communicated throughout the organization." }`,
        testableCriteria:
            'To verify that the vendor has established and communicated a formal disciplinary process ' +
            'throughout their organization to ensure consistent handling of employee misconduct and policy ' +
            'violations.',
        questions: [
            {
                question: 'Does a formal disciplinary process exist and is it documented?',
                answer: 'Yes',
            },
            {
                question:
                    'Has the disciplinary process been communicated throughout the organization?',
                answer: 'Yes',
            },
        ],
        impactLevels: [VendorImpactLevelLabels.CRITICAL],
    },
    {
        name: 'Consumer Information Secure Disposal',
        description:
            `{ "title": "Documented policies and procedures exist for the secure disposal of consumer information ` +
            `that prevent unauthorized access to or use of consumer report information or information derived from ` +
            `consumer reports." }`,
        testableCriteria:
            'To verify that the vendor has established and maintains appropriate policies and procedures for the ' +
            'secure disposal of consumer information to prevent unauthorized access to or use of consumer report ' +
            'information or information derived from consumer reports.',
        questions: [
            {
                question:
                    'Does the organization have documented policies and procedures for the secure disposal of consumer ' +
                    /* 'information that prevent unauthorized access to or use of consumer report information or information ' + */
                    'derived from consumer reports?',
                answer: 'Yes',
            },
        ],
        impactLevels: [VendorImpactLevelLabels.CRITICAL],
    },
    {
        name: 'AI Governance Framework',
        description:
            `{ "title": "The organization has documented policies and procedures for AI risk management that include ` +
            `defined organizational roles and responsibilities." },
    { "title": "The organization conducts periodic reviews of AI risk management processes and their outcomes." },
    { "title": "The organization has established processes to inform personnel of legal and regulatory requirements ` +
            `specific to AI systems in their industry and business context." },
    { "title": "Organizational policies, processes, and procedures explicitly incorporate characteristics of trustworthy AI." },
    { "title": "The organization has documented policies that define the creation and maintenance of AI system inventories." }`,
        testableCriteria:
            'To assess whether the organization has established a comprehensive AI governance framework that ' +
            'includes systematic risk management processes, regulatory compliance awareness, trustworthy AI ' +
            'principles integration, and AI system inventory management.',
        questions: [
            {
                question:
                    'Does the organization have documented policies and procedures for artificial intelligence (AI) risk ' +
                    'management that include defined organizational roles and responsibilities?',
                answer: 'Yes',
            },
            {
                question:
                    'Does the organization conduct periodic reviews of artificial intelligence (AI) risk management ' +
                    'processes and their outcomes?',
                answer: 'Yes',
            },
            {
                question:
                    'Has the organization established processes to inform personnel of legal and regulatory requirements ' +
                    'specific to artificial intelligence (AI) systems in their industry and business context?',
                answer: 'Yes',
            },
            {
                question:
                    'Do organizational policies, processes, and procedures explicitly incorporate characteristics of ' +
                    'trustworthy artificial intelligence (AI)?',
                answer: 'Yes',
            },
            {
                question:
                    'Does the organization have documented policies that define the creation and maintenance of ' +
                    'artificial intelligence (AI) system inventories?',
                answer: 'Yes',
            },
        ],
        impactLevels: [VendorImpactLevelLabels.CRITICAL],
    },
];

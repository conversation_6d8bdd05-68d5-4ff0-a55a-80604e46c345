import { VendorDataAccessedOrProcessed } from '@drata/enums';
import { ApiProperty } from '@nestjs/swagger';
import { UserCardCompactResponsePublicDto } from 'app/grc/dtos/public/v1/user-card-compact-response.public.dto';
import { LastQuestionnaireResponse } from 'app/questionnaires/types/last-questionnaire-response.type';
import { UserResponsePublicDto } from 'app/users/dtos/public/v1/user-response.public.dto';
import { VendorDocumentResponsePublicDto } from 'app/users/vendors/dtos/public/v1/vendor-document-response.public.dto';
import { VendorIntegrationResponsePublicDto } from 'app/users/vendors/dtos/public/v1/vendor-integration-response.public.dto';
import { VendorReviewResponsePublicDto } from 'app/users/vendors/dtos/public/v1/vendor-review-response.public.dto';
import { VendorSecurityReviewResponsePublicDto } from 'app/users/vendors/dtos/public/v1/vendor-security-review-response.public.dto';
import { VendorEnvironmentAccess } from 'app/users/vendors/enums/vendor-environment-access.enum';
import { VendorImpactLevel } from 'app/users/vendors/enums/vendor-impact-level.enum';
import { VendorOperationalImpact } from 'app/users/vendors/enums/vendor-operational-impact.enum';
import { VendorPasswordPolicy } from 'app/users/vendors/enums/vendor-password-policy.enum';
import { VendorResponse } from 'app/users/vendors/types/vendor-response.type';
import { VendorsConstants } from 'app/users/vendors/vendors.constants';
import { ResponsePublicDto } from 'commons/dtos/public/v1/response.public.dto';
import { RenewalScheduleType } from 'commons/enums/renewal-schedule.enum';
import { RenewalDateStatus } from 'commons/enums/renewal-status.enum';
import { TrustCenterProvider } from 'commons/enums/vendor/trust-center-provider.enum';
import { VendorCategory } from 'commons/enums/vendor/vendor-category.enum';
import { VendorRisk } from 'commons/enums/vendor/vendor-risk.enum';
import { VendorStatus } from 'commons/enums/vendor/vendor-status.enum';
import { VendorType } from 'commons/enums/vendor/vendor-type.enum';
import { getRenewalDateStatus } from 'commons/helpers/renewal.helper';
import { getCdnUrl } from 'commons/helpers/user.helper';
import config from 'config';
import { get, isEmpty, isNil } from 'lodash';
import { Relation } from 'typeorm';

export class VendorResponsePublicDto extends ResponsePublicDto {
    @ApiProperty({
        type: 'number',
        example: 1,
        description: 'Vendor ID',
    })
    id: number;

    @ApiProperty({
        type: 'string',
        example: 'Acme',
        description: 'The name of a vendor',
    })
    name: string;

    @ApiProperty({
        type: 'string',
        example: VendorCategory[VendorCategory.ENGINEERING],
        description: 'The business domain of the vendor',
        nullable: true,
    })
    category: string | null;

    @ApiProperty({
        type: 'string',
        example: VendorRisk[VendorRisk.MODERATE],
        description: 'The vendor risk type',
    })
    risk: string;

    @ApiProperty({
        type: 'string',
        example: VendorType[VendorType.CONTRACTOR],
        description: 'The vendor sub-type (Vendor, Contractor, Supplier, etc)',
        nullable: true,
    })
    type: string | null;

    @ApiProperty({
        type: 'boolean',
        example: false,
        description: 'Does the vendor is considered critical or not',
        nullable: true,
    })
    critical: boolean | null;

    @ApiProperty({
        type: 'string',
        example: 'USA',
        description: 'Vendor location',
        nullable: true,
    })
    location: string | null;

    @ApiProperty({
        type: 'string',
        example: config.get('swagger.examples.url'),
        description: 'Vendor URL',
        nullable: true,
    })
    url: string | null;

    @ApiProperty({
        type: 'string',
        example: config.get('swagger.examples.url'),
        description: 'Vendor Privacy Policy URL',
        nullable: true,
    })
    privacyUrl: string | null;

    @ApiProperty({
        type: 'string',
        example: config.get('swagger.examples.url'),
        description: 'Vendor Terms of Use URL',
        nullable: true,
    })
    termsUrl: string | null;

    @ApiProperty({
        type: 'string',
        example: 'https://trust.drata.com',
        description: 'Vendor Trust Center URL',
        nullable: true,
    })
    trustCenterUrl: string | null;

    @ApiProperty({
        example: TrustCenterProvider[TrustCenterProvider.DRATA],
        description: 'The providing service for this Trust Center',
        enum: TrustCenterProvider,
        nullable: true,
    })
    trustCenterProvider: TrustCenterProvider | null;

    @ApiProperty({
        type: 'string',
        example: 'Perform security scans once a month',
        description: 'Describe vendor services',
        nullable: true,
    })
    servicesProvided: string | null;

    @ApiProperty({
        type: 'string',
        example: 'Resulting reports of security scans',
        description: 'What type of data the vendor stores',
        nullable: true,
    })
    dataStored: string | null;

    @ApiProperty({
        type: 'boolean',
        example: true,
        description: 'Does this vendor store any type of PII',
    })
    hasPii: boolean;

    @ApiProperty({
        type: 'string',
        example: VendorPasswordPolicy[VendorPasswordPolicy.USERNAME_PASSWORD],
        description: 'The vendor password policy',
        nullable: true,
    })
    passwordPolicy: string | null;

    @ApiProperty({
        type: 'boolean',
        example: true,
        description: 'Is there a minimum length for user passwords',
    })
    passwordRequiresMinLength: boolean;

    @ApiProperty({
        type: 'number',
        example: 8,
        description: 'Minimum character length for a password',
        nullable: true,
    })
    passwordMinLength: number | null;

    @ApiProperty({
        type: 'boolean',
        example: true,
        description: 'Does a password require numbers',
    })
    passwordRequiresNumber: boolean;

    @ApiProperty({
        type: 'boolean',
        example: true,
        description: 'Does a password require non-alpha-numeric characters',
    })
    passwordRequiresSymbol: boolean;

    @ApiProperty({
        type: 'boolean',
        example: true,
        description: 'Is multi-factor authentication enabled for this vendor',
    })
    passwordMfaEnabled: boolean;

    @ApiProperty({
        type: 'string',
        example: 'John Doe',
        description: 'The name of the corresponding account manager for this vendor',
        nullable: true,
    })
    contactAtVendor: string | null;

    @ApiProperty({
        type: 'string',
        example: '<EMAIL>',
        description: 'The email of the corresponding account manager for this vendor',
        nullable: true,
    })
    contactsEmail: string | null;

    @ApiProperty({
        type: 'string',
        example: 'Meeting once a month to adjust contract',
        description: 'Additional notes for vendor',
        nullable: true,
    })
    notes: string | null;

    @ApiProperty({
        type: 'string',
        example: 'https://cdn-prod.imgpilot.com/logo.png',
        description: 'Vendor logo URL',
        nullable: true,
    })
    logoUrl: string | null;

    @ApiProperty({
        format: 'date-time',
        type: 'string',
        example: config.get('swagger.examples.dateTime'),
        description: 'Vendor created date timestamp',
    })
    createdAt: Date;

    @ApiProperty({
        format: 'date-time',
        type: 'string',
        example: config.get('swagger.examples.dateTime'),
        description: 'Vendor updated date timestamp',
    })
    updatedAt: Date;

    @ApiProperty({
        type: UserResponsePublicDto,
        description: 'The user that is responsible for the compliance of this vendor',
        nullable: true,
    })
    user: Relation<UserResponsePublicDto> | null;

    @ApiProperty({
        description: 'A list of vendor documents',
        isArray: true,
        type: VendorDocumentResponsePublicDto,
    })
    documents: VendorDocumentResponsePublicDto[];

    @ApiProperty({
        type: 'object',
        properties: {
            vendorId: { type: 'number' },
            sendAt: { type: 'string' },
            sentEmail: { type: 'string' },
            file: { type: 'string' },
            respondedAt: { type: 'string' },
            responseId: { type: 'number' },
            isManualUpload: { type: 'boolean' },
            completedBy: { type: 'string' },
        },
        description: 'The last questionnaire associated with the vendor',
        nullable: true,
    })
    lastQuestionnaire: LastQuestionnaireResponse | null;

    @ApiProperty({
        type: 'boolean',
        example: false,
        description: 'Is this vendor a sub-processor?',
    })
    isSubProcessor: boolean;

    @ApiProperty({
        type: 'boolean',
        example: false,
        description: 'Is this vendor an active Sub-processor?',
    })
    isSubProcessorActive: boolean;

    @ApiProperty({
        format: 'date-time',
        type: 'string',
        example: config.get('swagger.examples.dateTime'),
        description: 'Timestamp when the status of the vendor changed',
        nullable: true,
    })
    archivedAt: Date | null;

    @ApiProperty({
        type: 'string',
        example: VendorStatus[VendorStatus.ACTIVE],
        description: 'The vendor status',
        nullable: true,
    })
    status: string | null;

    @ApiProperty({
        type: 'string',
        example: config.get('swagger.examples.date'),
        description: 'Vendor renewal date',
        nullable: true,
    })
    renewalDate: string | null;

    @ApiProperty({
        type: 'string',
        example: RenewalScheduleType[RenewalScheduleType.ONE_YEAR],
        description: 'Vendor renewal schedule type',
        nullable: true,
    })
    renewalScheduleType: string | null;

    @ApiProperty({
        type: 'string',
        example: RenewalDateStatus[RenewalDateStatus.COMPLETED],
        description: 'Vendor renewal status based on the proximity to the renewal due date',
        nullable: true,
    })
    renewalDateStatus: string | null;

    @ApiProperty({
        format: 'date-time',
        type: 'string',
        example: config.get('swagger.examples.dateTime'),
        description: 'Timestamp when the vendor details were confirmed',
        nullable: true,
    })
    confirmedAt: Date | null;

    @ApiProperty({
        description: 'Vendor Reviews',
        isArray: true,
        type: VendorReviewResponsePublicDto,
    })
    reviews: VendorReviewResponsePublicDto[];

    @ApiProperty({
        type: 'string',
        example: config.get('swagger.examples.uuid'),
        description: 'Shared account id',
        nullable: true,
    })
    sharedAccountId: string | null;

    @ApiProperty({
        type: 'boolean',
        description: 'Is the vendor an user of drata',
        example: false,
        nullable: true,
    })
    isDrataUser: boolean;

    @ApiProperty({
        type: 'number',
        description: 'The number of event notifications pending for the vendor',
        example: 4,
        nullable: true,
    })
    events: number | null;

    @ApiProperty({
        type: VendorIntegrationResponsePublicDto,
        description: 'Vendor integrations list',
        isArray: true,
        nullable: true,
    })
    integrations: VendorIntegrationResponsePublicDto[] | null;

    @ApiProperty({
        type: 'string',
        description: 'Annual Contract Value for the vendor in Cents unit',
        example: '1088',
        nullable: true,
    })
    cost: string | null;

    @ApiProperty({
        type: 'string',
        description: 'Vendor level of operational impact',
        example: VendorOperationalImpact[VendorOperationalImpact.CRITICAL],
        nullable: true,
    })
    operationalImpact: string | null;

    @ApiProperty({
        type: 'string',
        description: 'Vendor environment access privileges',
        example: VendorEnvironmentAccess[VendorEnvironmentAccess.READ_ONLY],
        nullable: true,
    })
    environmentAccess: string | null;

    @ApiProperty({
        type: 'string',
        description: 'Vendor overall impact level',
        example: VendorImpactLevel[VendorImpactLevel.INSIGNIFICANT],
        nullable: true,
    })
    impactLevel: string | null;

    @ApiProperty({
        type: 'string',
        description: 'List of data accessed or processed',
        nullable: true,
        isArray: true,
    })
    dataAccessedOrProcessedList: string[] | null;

    @ApiProperty({
        required: false,
        type: VendorSecurityReviewResponsePublicDto,
        description: 'Latest Security Reviews',
        isArray: true,
        nullable: true,
    })
    latestSecurityReviews?: VendorSecurityReviewResponsePublicDto[] | null;

    @ApiProperty({
        nullable: true,
        type: 'number',
        description: 'The number of associated risks',
    })
    riskCount: number | null;

    @ApiProperty({
        nullable: true,
        type: 'number',
        description:
            'The number of risks with anticipated completion date within the next 30 days',
    })
    risksWithAnticipatedCompletionDateCount: number | null;

    @ApiProperty({
        type: UserCardCompactResponsePublicDto,
        description: 'The vendor relationship contact',
        example: {
            id: 1,
            email: '<EMAIL>',
            firstName: 'Adam',
            lastName: 'Attack',
            createdAt: '2025-01-08T21:18:10.846Z',
            updatedAt: '2025-01-10T23:46:09.000Z',
        },
        nullable: true,
    })
    vendorRelationshipContact: Relation<UserCardCompactResponsePublicDto> | null;

    // build the response
    build(vendor: VendorResponse): VendorResponsePublicDto {
        this.id = vendor.id;
        this.name = vendor.name;
        this.critical = vendor.critical;
        this.location = vendor.location;
        this.url = vendor.url;
        this.privacyUrl = vendor.privacyUrl;
        this.termsUrl = vendor.termsUrl;
        this.trustCenterUrl = vendor.trustCenterUrl;
        this.trustCenterProvider = vendor.trustCenterProvider;
        this.servicesProvided = vendor.servicesProvided;
        this.dataStored = vendor.dataStored;
        this.hasPii = vendor.hasPii;
        this.passwordRequiresMinLength = vendor.passwordRequiresMinLength;
        this.passwordMinLength = vendor.passwordMinLength;
        this.passwordRequiresNumber = vendor.passwordRequiresNumber;
        this.passwordRequiresSymbol = vendor.passwordRequiresSymbol;
        this.passwordMfaEnabled = vendor.passwordMfaEnabled;
        this.contactAtVendor = vendor.contactAtVendor;
        this.contactsEmail = vendor.contactsEmail;
        this.notes = vendor.notes;
        this.createdAt = vendor.createdAt;
        this.updatedAt = vendor.updatedAt;
        this.isSubProcessor = vendor.isSubProcessor;
        this.isSubProcessorActive = vendor.isSubProcessorActive;
        this.category = VendorCategory[vendor.category] ?? null;
        this.risk = VendorRisk[vendor.risk] ?? null;
        this.passwordPolicy = VendorPasswordPolicy[vendor.passwordPolicy] ?? null;
        this.user = !isNil(vendor.user) ? new UserResponsePublicDto().build(vendor.user) : null;
        if (vendor.contact) {
            this.vendorRelationshipContact = new UserCardCompactResponsePublicDto().build(
                vendor.contact,
            );
        }
        this.logoUrl = getCdnUrl(vendor.logo);
        this.documents = this.list(vendor.documents, VendorDocumentResponsePublicDto);
        this.archivedAt = vendor.archivedAt;
        this.lastQuestionnaire = get(vendor, 'lastQuestionnaire', null);
        this.renewalDate = vendor.renewalDate ?? null;
        this.renewalScheduleType = !isNil(vendor.renewalScheduleType)
            ? RenewalScheduleType[vendor.renewalScheduleType]
            : null;
        // ensuring that legacy archivedAt works
        this.status = !isNil(vendor.archivedAt)
            ? VendorStatus[VendorStatus.ARCHIVED]
            : VendorStatus[vendor.status] ?? null;
        const renewalStatus = getRenewalDateStatus(
            vendor.renewalDate,
            VendorsConstants.DAYS_TO_RENEWAL_DUE,
        );
        this.renewalDateStatus = RenewalDateStatus[renewalStatus] ?? null;
        this.confirmedAt = vendor.confirmedAt ?? null;
        this.reviews = !isEmpty(vendor.reviews)
            ? vendor.reviews.map(review => new VendorReviewResponsePublicDto().build(review))
            : [];
        this.type = VendorType[vendor.type] ?? null;
        this.sharedAccountId = vendor.sharedAccountId ?? null;
        this.isDrataUser = !isNil(vendor.sharedAccountId);
        this.events = vendor?.events ?? null;
        this.integrations = null;
        if (!isNil(vendor.integrations) && !isEmpty(vendor?.integrations)) {
            this.integrations = vendor.integrations.map(vendorIntegrated =>
                new VendorIntegrationResponsePublicDto().build(vendorIntegrated),
            );
        }
        this.cost = vendor.cost;
        this.operationalImpact = VendorOperationalImpact[vendor.operationalImpact] ?? null;
        this.environmentAccess = VendorEnvironmentAccess[vendor.environmentAccess] ?? null;
        this.impactLevel = VendorImpactLevel[vendor.impactLevel] ?? null;
        this.dataAccessedOrProcessedList = !isEmpty(vendor.dataAccessedOrProcessed)
            ? vendor.dataAccessedOrProcessed.map(
                  dataItem => VendorDataAccessedOrProcessed[dataItem.type],
              )
            : null;

        if (vendor.securityReviews) {
            this.latestSecurityReviews = vendor.securityReviews.map(securityReview =>
                new VendorSecurityReviewResponsePublicDto().build(securityReview),
            );
        }

        this.riskCount = vendor.riskCount || 0;
        this.risksWithAnticipatedCompletionDateCount =
            vendor.risksWithAnticipatedCompletionDateCount || null;

        return this.send();
    }
}

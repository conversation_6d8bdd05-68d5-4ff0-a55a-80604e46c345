import { VendorDataAccessedOrProcessed } from '@drata/enums';
import { ApiProperty } from '@nestjs/swagger';
import { CustomFieldSubmission } from 'app/custom-fields/entities/custom-field-submission.entity';
import { LastQuestionnaireResponse } from 'app/questionnaires/types/last-questionnaire-response.type';
import { ScheduleVendorQuestionnairesType } from 'app/schedules/types/schedule-vendor-questionnaires-type';
import { SharedTrustCenterAccountResponseDto } from 'app/trust-center/dtos/shared-trust-center-account-response.dto';
import { UserResponseDto } from 'app/users/dtos/user-response.dto';
import { VendorDocumentResponseDto } from 'app/users/vendors/dtos/vendor-document-response.dto';
import { VendorIntegrationResponseDto } from 'app/users/vendors/dtos/vendor-integration-response.dto';
import { VendorReviewResponseDto } from 'app/users/vendors/dtos/vendor-review-response.dto';
import { VendorSecurityReviewResponseDto } from 'app/users/vendors/dtos/vendor-security-review-response.dto';
import { Vendor } from 'app/users/vendors/entities/vendor.entity';
import { VendorEnvironmentAccess } from 'app/users/vendors/enums/vendor-environment-access.enum';
import { VendorImpactLevel } from 'app/users/vendors/enums/vendor-impact-level.enum';
import { VendorOperationalImpact } from 'app/users/vendors/enums/vendor-operational-impact.enum';
import { VendorPasswordPolicy } from 'app/users/vendors/enums/vendor-password-policy.enum';
import { VendorResponse } from 'app/users/vendors/types/vendor-response.type';
import { VendorsConstants } from 'app/users/vendors/vendors.constants';
import { ResponseDto } from 'commons/dtos/response.dto';
import { RenewalScheduleType } from 'commons/enums/renewal-schedule.enum';
import { RenewalDateStatus } from 'commons/enums/renewal-status.enum';
import { TrustCenterProvider } from 'commons/enums/vendor/trust-center-provider.enum';
import { VendorCategory } from 'commons/enums/vendor/vendor-category.enum';
import { VendorRisk } from 'commons/enums/vendor/vendor-risk.enum';
import { VendorStatus } from 'commons/enums/vendor/vendor-status.enum';
import { VendorType } from 'commons/enums/vendor/vendor-type.enum';
import { getRenewalDateStatus } from 'commons/helpers/renewal.helper';
import { getCdnUrl } from 'commons/helpers/user.helper';
import config from 'config';
import { get, isEmpty, isNil } from 'lodash';
import { Relation } from 'typeorm';

export class VendorResponseDto extends ResponseDto {
    @ApiProperty({
        type: 'number',
        example: 1,
        description: 'Vendor ID',
    })
    id: number;

    @ApiProperty({
        type: 'string',
        example: 'Acme',
        description: 'The name of a vendor',
    })
    name: string;

    @ApiProperty({
        type: 'string',
        example: VendorCategory[VendorCategory.ENGINEERING],
        enum: VendorCategory,
        description: 'The business domain of the vendor',
        nullable: true,
    })
    category: string | null;

    @ApiProperty({
        type: 'string',
        example: VendorRisk[VendorRisk.MODERATE],
        enum: VendorRisk,
        description: 'The vendor risk type',
    })
    risk: string;

    @ApiProperty({
        type: 'string',
        example: VendorStatus[VendorStatus.ACTIVE],
        enum: VendorStatus,
        description: 'The vendor status',
        nullable: true,
    })
    status: string | null;

    @ApiProperty({
        type: 'string',
        example: VendorStatus[VendorStatus.ACTIVE],
        enum: VendorStatus,
        description: 'The vendor status when was archived',
        nullable: true,
    })
    archivedAs: string | null;

    @ApiProperty({
        type: 'string',
        example: VendorType[VendorType.CONTRACTOR],
        enum: VendorType,
        description: 'The vendor sub-type (Vendor, Contractor, Supplier, etc)',
        nullable: true,
    })
    type: string | null;

    @ApiProperty({
        type: 'boolean',
        example: false,
        description: 'Does the vendor is considered critical or not',
        nullable: true,
    })
    critical: boolean | null;

    @ApiProperty({
        type: 'string',
        example: 'USA',
        description: 'Vendor location',
        nullable: true,
    })
    location: string | null;

    @ApiProperty({
        type: 'string',
        example: config.get('swagger.examples.url'),
        description: 'Vendor URL',
        nullable: true,
    })
    url: string | null;

    @ApiProperty({
        type: 'string',
        example: config.get('swagger.examples.url'),
        description: 'Vendor Privacy Policy URL',
        nullable: true,
    })
    privacyUrl: string | null;

    @ApiProperty({
        type: 'string',
        example: config.get('swagger.examples.url'),
        description: 'Vendor Terms of Use URL',
        nullable: true,
    })
    termsUrl: string | null;

    @ApiProperty({
        type: 'string',
        example: 'https://trust.drata.com',
        description: 'Vendor Trust Center URL',
        nullable: true,
    })
    trustCenterUrl: string | null;

    @ApiProperty({
        example: TrustCenterProvider[TrustCenterProvider.DRATA],
        description: 'The providing service for this Trust Center',
        enum: TrustCenterProvider,
        nullable: true,
    })
    trustCenterProvider: TrustCenterProvider | null;

    @ApiProperty({
        type: 'string',
        example: 'Perform security scans once a month',
        description: 'Describe vendor services',
        nullable: true,
    })
    servicesProvided: string | null;

    @ApiProperty({
        type: 'string',
        example: 'Resulting reports of security scans',
        description: 'What type of data the vendor stores',
        nullable: true,
    })
    dataStored: string | null;

    @ApiProperty({
        type: 'boolean',
        example: true,
        description: 'Does this vendor store any type of PII',
    })
    hasPii: boolean;

    @ApiProperty({
        type: 'string',
        example: VendorPasswordPolicy[VendorPasswordPolicy.USERNAME_PASSWORD],
        enum: VendorPasswordPolicy,
        description: 'The vendor password policy',
        nullable: true,
    })
    passwordPolicy: string | null;

    @ApiProperty({
        type: 'boolean',
        example: true,
        description: 'Is there a minimum length for user passwords',
    })
    passwordRequiresMinLength: boolean;

    @ApiProperty({
        type: 'number',
        example: 8,
        description: 'Minimum character length for a password',
        nullable: true,
    })
    passwordMinLength: number | null;

    @ApiProperty({
        type: 'boolean',
        example: true,
        description: 'Does a password require numbers',
    })
    passwordRequiresNumber: boolean;

    @ApiProperty({
        type: 'boolean',
        example: true,
        description: 'Does a password require non-alpha-numeric characters',
    })
    passwordRequiresSymbol: boolean;

    @ApiProperty({
        type: 'boolean',
        example: true,
        description: 'Is multi-factor authentication enabled for this vendor',
    })
    passwordMfaEnabled: boolean;

    @ApiProperty({
        type: 'string',
        example: 'John Doe',
        description: 'The name of the corresponding account manager for this vendor',
        nullable: true,
    })
    contactAtVendor: string | null;

    @ApiProperty({
        type: 'string',
        example: '<EMAIL>',
        description: 'The email of the corresponding account manager for this vendor',
        nullable: true,
    })
    contactsEmail: string | null;

    @ApiProperty({
        type: 'string',
        example: 'Meeting once a month to adjust contract',
        description: 'Additional notes for vendor',
        nullable: true,
    })
    notes: string | null;

    @ApiProperty({
        type: 'string',
        example: 'https://cdn-prod.imgpilot.com/logo.png',
        description: 'Vendor logo URL',
        nullable: true,
    })
    logoUrl: string | null;

    @ApiProperty({
        type: 'string',
        format: 'date-time',
        example: config.get('swagger.examples.dateTime'),
        description: 'Vendor created date timestamp',
    })
    createdAt: Date;

    @ApiProperty({
        type: 'string',
        format: 'date-time',
        example: config.get('swagger.examples.dateTime'),
        description: 'Vendor updated date timestamp',
    })
    updatedAt: Date;

    @ApiProperty({
        type: UserResponseDto,
        description: 'The user that is responsible for the compliance of this vendor',
        nullable: true,
    })
    user: Relation<UserResponseDto> | null;

    @ApiProperty({
        type: UserResponseDto,
        description: 'The user that is contact relationship for this vendor',
        nullable: true,
    })
    contact: Relation<UserResponseDto> | null;

    @ApiProperty({
        description: 'A list of vendor documents',
        isArray: true,
        type: VendorDocumentResponseDto,
    })
    documents: VendorDocumentResponseDto[];

    @ApiProperty({
        type: 'object',
        properties: {
            vendorId: { type: 'number' },
            sendAt: { type: 'string' },
            sentEmail: { type: 'string' },
            file: { type: 'string' },
            respondedAt: { type: 'string' },
            responseId: { type: 'number' },
            isManualUpload: { type: 'boolean' },
            completedBy: { type: 'string' },
        },
        description: 'The last questionnaire associated with the vendor',
        nullable: true,
    })
    lastQuestionnaire: LastQuestionnaireResponse | null;

    @ApiProperty({
        type: 'boolean',
        example: false,
        description: 'Is this vendor a Sub-processor?',
    })
    isSubProcessor: boolean;

    @ApiProperty({
        type: 'boolean',
        example: false,
        description: 'Is this vendor an active Sub-processor?',
    })
    isSubProcessorActive: boolean;

    @ApiProperty({
        type: 'string',
        format: 'date-time',
        example: config.get('swagger.examples.dateTime'),
        description: 'Timestamp when the status of the vendor changed',
        nullable: true,
    })
    archivedAt: Date | null;

    @ApiProperty({
        type: 'string',
        example: config.get('swagger.examples.date'),
        description: 'Vendor renewal date',
        nullable: true,
    })
    renewalDate: string | null;

    @ApiProperty({
        type: 'string',
        example: RenewalScheduleType[RenewalScheduleType.ONE_YEAR],
        enum: RenewalScheduleType,
        description: 'Vendor renewal schedule type',
        nullable: true,
    })
    renewalScheduleType: string | null;

    @ApiProperty({
        type: 'string',
        example: RenewalDateStatus[RenewalDateStatus.COMPLETED],
        enum: RenewalDateStatus,
        description: 'Vendor renewal status based on how close it is to the renewal due date',
        nullable: true,
    })
    renewalDateStatus: string | null;

    @ApiProperty({
        type: 'string',
        format: 'date-time',
        example: config.get('swagger.examples.dateTime'),
        description: 'Timestamp when the vendor details were confirmed',
        nullable: true,
    })
    confirmedAt: Date | null;

    @ApiProperty({
        description: 'Vendor Reviews',
        isArray: true,
        type: VendorReviewResponseDto,
    })
    reviews: VendorReviewResponseDto[];

    @ApiProperty({
        type: 'string',
        example: config.get('swagger.examples.uuid'),
        description: 'Shared account id',
        nullable: true,
    })
    sharedAccountId: string | null;

    @ApiProperty({
        type: SharedTrustCenterAccountResponseDto,
        description: 'The shared account related',
        example: 'SharedTrustCenterAccountResponseDto',
        nullable: true,
    })
    sharedAccount: SharedTrustCenterAccountResponseDto | null;

    @ApiProperty({
        type: 'boolean',
        description: 'Is the vendor an user of drata',
        example: false,
    })
    isDrataUser: boolean;

    @ApiProperty({
        type: 'number',
        description: 'The number of event notifications pending for the vendor',
        example: 4,
        nullable: true,
    })
    events: number | null;

    @ApiProperty({
        type: VendorIntegrationResponseDto,
        description: 'Vendor integrations list',
        isArray: true,
        nullable: true,
    })
    integrations: VendorIntegrationResponseDto[] | null;

    @ApiProperty({
        type: 'string',
        description: 'Annual Contract Value for the vendor in Cents unit',
        example: '1088',
        nullable: true,
    })
    cost: string | null;

    @ApiProperty({
        type: 'string',
        description: 'Vendor level of operational impact',
        example: VendorOperationalImpact[VendorOperationalImpact.CRITICAL],
        enum: VendorOperationalImpact,
        nullable: true,
    })
    operationalImpact: string | null;

    @ApiProperty({
        type: 'string',
        description: 'Vendor environment access privileges',
        example: VendorEnvironmentAccess[VendorEnvironmentAccess.READ_ONLY],
        enum: VendorEnvironmentAccess,
        nullable: true,
    })
    environmentAccess: string | null;

    @ApiProperty({
        type: 'string',
        description: 'Vendor overall impact level',
        example: VendorImpactLevel[VendorImpactLevel.INSIGNIFICANT],
        enum: VendorImpactLevel,
        nullable: true,
    })
    impactLevel: string | null;

    @ApiProperty({
        type: 'string',
        description: 'List of data accessed or processed',
        nullable: true,
        isArray: true,
        enum: VendorDataAccessedOrProcessed,
    })
    dataAccessedOrProcessedList: string[] | null;

    @ApiProperty({
        required: false,
        isArray: true,
        description: 'Latest Security Reviews',
        type: VendorSecurityReviewResponseDto,
        nullable: true,
    })
    latestSecurityReviews?: VendorSecurityReviewResponseDto[] | null;

    @ApiProperty({
        type: VendorSecurityReviewResponseDto,
        description: 'Latest Security Review in progress',
        example: 'VendorSecurityReviewResponseDto',
        nullable: true,
    })
    latestActiveSecurityReview: VendorSecurityReviewResponseDto | null;

    @ApiProperty({
        type: 'number',
        nullable: true,
        description: 'The number of associated risks',
    })
    riskCount: number | null;

    @ApiProperty({
        type: 'number',
        nullable: true,
        description:
            'The number of risks with anticipated completion date within the next 30 days',
    })
    risksWithAnticipatedCompletionDateCount: number | null;

    @ApiProperty({
        type: CustomFieldSubmission,
        description: 'List of custom fields value',
        nullable: true,
        isArray: true,
    })
    customFieldSubmission: CustomFieldSubmission[] | null;

    @ApiProperty({
        type: 'object',
        properties: {
            id: { type: 'number' },
            type: { type: 'string' },
            criteria: { type: 'object', additionalProperties: true },
            schedulingWindow: { type: 'number', nullable: true, required: false },
            actions: { type: 'string' },
            scheduleFeature: { type: 'object', additionalProperties: true },
            status: { type: 'string' },
            scheduleLog: { type: 'array', items: { type: 'object' } },
            createdAt: { type: 'string', format: 'date-time' },
            updatedAt: { type: 'string', format: 'date-time' },
            deletedAt: { type: 'string', format: 'date-time' },
        },
        nullable: true,
        description: 'The schedule configuration for the vendor',
    })
    scheduleConfiguration: ScheduleVendorQuestionnairesType | null;

    // build the response
    build(vendor: VendorResponse): VendorResponseDto {
        this.id = vendor.id;
        this.name = vendor.name;
        this.critical = vendor.critical || false;
        this.location = vendor.location || null;
        this.url = vendor.url || null;
        this.privacyUrl = vendor.privacyUrl || null;
        this.termsUrl = vendor.termsUrl || null;
        this.trustCenterUrl = vendor.trustCenterUrl || null;
        this.trustCenterProvider = vendor.trustCenterProvider || null;
        this.servicesProvided = vendor.servicesProvided || null;
        this.dataStored = vendor.dataStored || null;
        this.hasPii = vendor.hasPii;
        this.passwordRequiresMinLength = vendor.passwordRequiresMinLength;
        this.passwordMinLength = vendor.passwordMinLength || null;
        this.passwordRequiresNumber = vendor.passwordRequiresNumber;
        this.passwordRequiresSymbol = vendor.passwordRequiresSymbol;
        this.passwordMfaEnabled = vendor.passwordMfaEnabled;
        this.contactAtVendor = vendor.contactAtVendor || null;
        this.contactsEmail = vendor.contactsEmail || null;
        this.notes = vendor.notes || null;
        this.customFieldSubmission = vendor.customFieldSubmission;
        this.createdAt = vendor.createdAt;
        this.updatedAt = vendor.updatedAt;
        this.isSubProcessor = vendor.isSubProcessor;
        this.isSubProcessorActive = vendor.isSubProcessorActive;
        this.category = vendor.category ? VendorCategory[vendor.category] : null;
        this.risk = VendorRisk[vendor.risk] ?? null;
        this.passwordPolicy = VendorPasswordPolicy[vendor.passwordPolicy] ?? null;
        this.user = !isNil(vendor.user) ? new UserResponseDto().build(vendor.user) : null;
        this.contact = !isNil(vendor.contact) ? new UserResponseDto().build(vendor.contact) : null;
        this.logoUrl = getCdnUrl(vendor.logo);
        this.documents = this.list(vendor.documents, VendorDocumentResponseDto);
        this.archivedAt = vendor.archivedAt;
        this.lastQuestionnaire = get(vendor, 'lastQuestionnaire', null);
        this.renewalDate = vendor.renewalDate ?? null;
        this.renewalScheduleType = !isNil(vendor.renewalScheduleType)
            ? RenewalScheduleType[vendor.renewalScheduleType]
            : null;
        const renewalStatus = getRenewalDateStatus(
            vendor.renewalDate,
            VendorsConstants.DAYS_TO_RENEWAL_DUE,
        );
        this.renewalDateStatus = RenewalDateStatus[renewalStatus] ?? null;
        this.confirmedAt = vendor.confirmedAt ?? null;
        this.reviews = !isEmpty(vendor.reviews)
            ? vendor.reviews.map(review => new VendorReviewResponseDto().build(review))
            : [];
        // ensuring that legacy archivedAt works
        this.status = !isNil(vendor.archivedAt)
            ? VendorStatus[VendorStatus.ARCHIVED]
            : (VendorStatus[vendor.status] ?? null);
        this.archivedAs = !isNil(vendor.archivedAs) ? VendorStatus[vendor.archivedAs] : null;
        this.type = VendorType[vendor.type] ?? null;
        this.sharedAccountId = vendor.sharedAccountId ?? null;
        this.sharedAccount = vendor.account
            ? new SharedTrustCenterAccountResponseDto().build(vendor.account)
            : null;
        this.isDrataUser = !isNil(vendor.sharedAccountId);
        this.events = vendor?.events ?? null;
        this.integrations = !isEmpty(vendor.integrations)
            ? vendor.integrations.map(vendorIntegrated =>
                  new VendorIntegrationResponseDto().build(vendorIntegrated),
              )
            : null;
        this.cost = vendor.cost;
        this.operationalImpact = VendorOperationalImpact[vendor.operationalImpact] ?? null;
        this.environmentAccess = VendorEnvironmentAccess[vendor.environmentAccess] ?? null;
        this.impactLevel = VendorImpactLevel[vendor.impactLevel] ?? null;
        this.dataAccessedOrProcessedList = !isEmpty(vendor.dataAccessedOrProcessed)
            ? vendor.dataAccessedOrProcessed.map(
                  dataItem => VendorDataAccessedOrProcessed[dataItem.type],
              )
            : null;

        this.latestSecurityReviews = !isNil(vendor.securityReviews)
            ? vendor.securityReviews.map(securityReview =>
                  new VendorSecurityReviewResponseDto().build(securityReview),
              )
            : [];
        this.latestActiveSecurityReview = !isNil(vendor.latestActiveSecurityReview)
            ? new VendorSecurityReviewResponseDto().build(vendor.latestActiveSecurityReview)
            : null;
        this.riskCount = vendor.riskCount || 0;
        this.risksWithAnticipatedCompletionDateCount =
            vendor.risksWithAnticipatedCompletionDateCount || null;
        this.scheduleConfiguration = vendor.scheduleConfiguration || null;
        return this.send();
    }

    buildVendorRisk(vendor: VendorResponse | Vendor): VendorResponseDto {
        this.id = vendor.id;
        this.name = vendor.name;

        return this.send() as VendorResponseDto;
    }
}

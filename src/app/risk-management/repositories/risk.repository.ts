import {
    RiskClassification,
    RiskGroup,
    RiskScoreType,
    RiskStatusType,
    RiskTreatmentPlan,
    RiskVersionType,
} from '@drata/enums';
import { Product } from 'app/companies/products/entities/product.entity';
import { ControlRisksRequestDto } from 'app/grc/dtos/control-risks-request.dto';
import { CustomTask } from 'app/grc/entities/custom-task.entity';
import { RiskCustomTasksRequestDto } from 'app/risk-management/dtos/risk-custom-tasks-request.dto';
import { RiskDashboardRequestDto } from 'app/risk-management/dtos/risk-dashboard-request.dto';
import { RiskStatisticsRequestDto } from 'app/risk-management/dtos/risk-statistics-request.dto';
import { RisksRequestDto } from 'app/risk-management/dtos/risks-request.dto';
import { RisksWithExclusionsRequestDto } from 'app/risk-management/dtos/risks-with-exclusions-request.dto';
import { Risk } from 'app/risk-management/entities/risk.entity';
import { RiskExpand } from 'app/risk-management/enums/risk-expand.enum';
import { RiskFilter } from 'app/risk-management/enums/risk-filter.enum';
import { RiskSourceType } from 'app/risk-management/enums/risk-source-type.enum';
import {
    mapAndFilterRisksToDocuments,
    prepDocumentsForBulk,
} from 'app/risk-management/helpers/risk-search-engine.helper';
import { RiskIdAndVersion } from 'app/risk-management/types/risk-id-and-version.type';
import { RiskDocumentBulk } from 'app/risk-management/types/risk-search-engine.type';
import { RisksRequestType } from 'app/risk-management/types/risks-request.type';
import { RisksStatistics } from 'app/risk-management/types/risks-statistics-type';

import { SortDir, SortType } from '@drata/enums';
import { Document } from 'app/documents/entities/document.entity';
import { TicketsBaseRequestDto } from 'app/tickets/dtos/tickets-base-request.dto';
import { Ticket } from 'app/tickets/entities/ticket.entity';
import { User } from 'app/users/entities/user.entity';
import { Account } from 'auth/entities/account.entity';
import { GlobalNoteReferenceType } from 'commons/enums/global-note-reference-type.enum';
import { Action } from 'commons/enums/users/action.enum';
import { Subject } from 'commons/enums/users/subject.enum';
import {
    applyCursorFilters,
    getCursorPageAutoIncrementId,
    setupCursorQueryAutoIncrementId,
} from 'commons/helpers/cursor-repository.helper';
import { fqtn, like } from 'commons/helpers/database.helper';
import { getSkip } from 'commons/helpers/pagination.helper';
import { getSortDir } from 'commons/helpers/sort.helper';
import { hasUserPermission } from 'commons/helpers/user.helper';
import { BaseRepository } from 'commons/repositories/base.repository';
import {
    FindManyQueryOptions,
    JoinTypes,
    OrderOptions,
    RelationsOptions,
    WhereOptions,
} from 'commons/repositories/query.builder';
import { CursorPage } from 'commons/types/cursor-page.type';
import { CursorPaginationRequestType } from 'commons/types/cursor-pagination-request.type';
import { PaginationType } from 'commons/types/pagination.type';
import { CustomRepository } from 'database/typeorm/typeorm.extensions.decorator';
import {
    chain,
    defaultsDeep,
    flatten,
    get,
    isEmpty,
    isNil,
    isUndefined,
    map,
    maxBy,
    merge,
    omit,
    pick,
    property,
    spread,
} from 'lodash';
import moment from 'moment';
import { Span } from 'nestjs-ddtrace';
import { Brackets, SelectQueryBuilder } from 'typeorm';
export const RISK_RELATIONS: RelationsOptions = {
    currentVersion: [
        JoinTypes.innerJoinAndSelect,
        {
            categories: true,
            owners: true,
            reviewers: true,
            controls: {
                controlIsReady: [JoinTypes.leftJoinAndMapOne, true],
            },
            documents: true,
            vendors: {
                user: [JoinTypes.leftJoinAndSelect, true],
            },
            customFieldSubmission: {
                riskVersion: true,
                customFieldLocation: [
                    JoinTypes.leftJoinAndSelect,
                    {
                        customField: true,
                    },
                ],
            },
        },
    ],
    notes: [
        JoinTypes.leftJoinAndMapMany,
        {
            owner: true,
        },
    ],
    tickets: true,
    customTasks: true,
    customTaskConfigurations: true,
};

export const RISK_DASHBOARD_RELATIONS: RelationsOptions = {
    currentVersion: [
        JoinTypes.innerJoinAndSelect,
        {
            categories: true,
            owners: true,
            vendors: {
                user: [JoinTypes.leftJoinAndSelect, true],
            },
        },
    ],
};

const RISK_LISTS_SELECTS = [
    {
        '...': true,
        currentVersion: true,
    },
    {
        id: true,
        currentVersion: {
            id: true,
            categories: true,
        },
    },
    {
        id: true,
        currentVersion: {
            id: true,
            owners: true,
        },
    },
    {
        id: true,
        currentVersion: {
            id: true,
            reviewers: true,
        },
    },
    {
        id: true,
        currentVersion: {
            id: true,
            controls: {
                '...': true,
                controlIsReady: true,
            },
        },
    },
    {
        id: true,
        currentVersion: {
            id: true,
            vendors: {
                '...': true,
                user: true,
            },
        },
    },
    {
        id: true,
        currentVersion: {
            id: true,
            customFieldSubmission: {
                id: true,
                submission: true,
                customFieldLocation: {
                    id: true,
                    customField: {
                        id: true,
                    },
                },
            },
        },
    },
];
const RISK_DETAILS_SELECTS = [
    ...RISK_LISTS_SELECTS,
    {
        id: true,
        notes: {
            '...': true,
            owner: true,
        },
    },
    {
        id: true,
        currentVersion: {
            id: true,
            documents: true,
        },
    },
];

@CustomRepository(Risk)
export class RiskRepository extends BaseRepository<Risk> {
    saveRisk(risk: Risk): Promise<Risk> {
        return this.save(risk);
    }

    async getTicketsByRisk(
        risk: Risk,
        requestDto: TicketsBaseRequestDto,
    ): Promise<[Ticket[], number]> {
        const { limit, page, isCompleted: isDone } = requestDto;
        const query = this.buildQuery({
            mainAlias: 'Risk',
            select: {
                tickets: { id: true },
                id: true,
            },
            relations: {
                tickets: {
                    connection: [JoinTypes.innerJoinAndSelect, true],
                },
            },
            where: {
                id: risk.id,
                tickets: {
                    isDone,
                },
            },
            order: [
                {
                    property: 'tickets.createdAt',
                    order: 'DESC',
                },
            ],
        });

        query.andWhere(
            'json_extract(connection.metadata, "$.writeAccessEnabled") = :writeAccessEnabled',
            { writeAccessEnabled: true },
        );

        const risks = (await query.getOne()) || { tickets: [] };
        const tickets = risks?.tickets || [];

        let data = [];
        if (!isEmpty(tickets)) {
            const paginatedTickets = tickets.slice((page - 1) * limit, (page - 1) * limit + limit);
            if (!isEmpty(paginatedTickets)) {
                const response = await this.findOneWithQuery({
                    relations: {
                        tickets: {
                            user: true,
                            connection: true,
                        },
                    },
                    where: {
                        tickets: {
                            id: {
                                in$: map(paginatedTickets, 'id'),
                            },
                        },
                    },
                    order: [
                        {
                            property: 'tickets.createdAt',
                            order: 'DESC',
                        },
                    ],
                });
                data = response?.tickets || [];
            }
        }
        return [data, tickets.length];
    }

    /**
     *
     * @param {number} userId
     * @returns {Promise<number[]>}
     */
    async getVendorsWithRiskByUser(userId: number): Promise<number[]> {
        const vendorsIds = [];
        const query = await this.createQueryBuilder('risk')
            .leftJoinAndSelect('risk.currentVersion', 'risk_version')
            .leftJoinAndSelect('risk_version.owners', 'risk_owners')
            .leftJoinAndSelect('risk_version.vendors', 'risk_vendors')
            .leftJoinAndSelect('risk.customTasks', 'tasks')
            .leftJoinAndSelect('tasks.assignedTo', 'assignees')
            .orWhere('risk_owners.id = :userId', { userId })
            .orWhere('tasks.fk_assigned_to_user_id = :userId', { userId })
            .groupBy('risk_vendors.id')
            .getMany();

        query.forEach(risk => {
            vendorsIds.push(...risk.currentVersion.vendors.map(vendor => vendor.id));
        });

        return vendorsIds;
    }

    /**
     *
     * @param {RisksRequestDto} dto
     * @returns {Promise<PaginationType<Risk>>}
     */
    async listRisks(
        dto: RisksRequestDto,
        opts: {
            isReleaseScalingRiskFoundation?: boolean;
            isReleaseRiskStatus?: boolean;
            isReleaseCosmosTableForRisk?: boolean;
        },
    ): Promise<PaginationType<Risk>> {
        const { page, limit, sort, status, sortDir, sortByField, ...filters } = dto;
        const {
            isReleaseScalingRiskFoundation = false,
            isReleaseRiskStatus = false,
            isReleaseCosmosTableForRisk = false,
        } = opts;

        const { risks, total } = await this.findRisks(
            {
                mainAlias: 'risk',
                where: this.resolveFilters(
                    {
                        ...filters,
                        status: status && [status],
                        sort,
                    },
                    {
                        isReleaseScalingRiskFoundation,
                        isReleaseRiskStatus,
                        isReleaseCosmosTableForRisk,
                    },
                ),
                order: this.resolveSort({
                    sort,
                    sortDir,
                    sortByField,
                    isReleaseRiskStatus,
                    isReleaseCosmosTableForRisk,
                }),
                skip: getSkip(page, limit),
                take: limit,
            },
            true,
        );
        return {
            data: risks,
            page,
            limit,
            total,
        };
    }

    /**
     *
     * @param {RisksRequestDto} dto
     * @returns {Promise<Risk[]>}
     */
    async getRisksForDownload(
        dto: RisksRequestDto,
        isReleaseScalingRiskFoundation = false,
        isReleaseRiskStatus = false,
        isReleaseCosmosTableForRisk = false,
    ): Promise<Risk[]> {
        const { sort, sortDir, status, ...filters } = dto;
        const { risks } = await this.findRisks({
            mainAlias: 'risk',
            relations: RISK_RELATIONS,
            where: this.resolveFilters(
                {
                    ...filters,
                    status: isNil(status) ? undefined : [status],
                    sort,
                },
                {
                    isReleaseScalingRiskFoundation,
                    isReleaseRiskStatus,
                    isReleaseCosmosTableForRisk,
                },
            ),
            order: this.resolveSort({ sort, sortDir }),
        });
        return risks;
    }

    /**
     * @return {Promise<Map<number, number>> }
     */
    async getRisksCountByUsers(): Promise<Map<number, number>> {
        const risks = await this.findWithQuery({
            relations: {
                currentVersion: [JoinTypes.innerJoinAndSelect, { owners: true }],
            },
            where: {
                currentVersion: {
                    applicable: true,
                },
            },
            withRelationsOptimization: true,
        });

        //store user id and count
        const riskMap = new Map<number, number>();
        risks.forEach(risk => {
            risk.currentVersion.owners.forEach(o =>
                riskMap.set(o.id, (riskMap.get(o.id) ?? 0) + 1),
            );
        });

        return riskMap;
    }

    /**
     *
     * @param {number} userId
     * @returns {Promise<number[]>}
     */

    async getRiskTaskIdsByUserId(userId: number): Promise<number[]> {
        const query = await this.createQueryBuilder('risk')
            .innerJoinAndSelect('risk.currentVersion', 'risk_version')
            .innerJoinAndSelect('risk_version.owners', 'risk_owners')
            .where('risk_owners.id = :userId', { userId })
            .getMany();
        return query.map(r => r.id);
    }

    async findRisk(options: WhereOptions): Promise<Risk> {
        // we need to make 2 queries because the where filter only brings the sub-entities (one-to-many relations) that match it and we need the full entity
        const id = await this.findOneWithQuery({
            relations: RISK_RELATIONS,
            where: options,
            select: { id: true },
            withRelationsOptimization: true,
        }).then(property<Risk, number>('id'));

        const defaultOptions = {
            relations: RISK_RELATIONS,
            where: { id },
            withRelationsOptimization: true,
        };

        return isNil(id)
            ? undefined
            : Promise.all(
                  // we need to make each join in a separated query since multiple one-to-many joins queried together multiply the resulting sql rows exponentially
                  map(RISK_DETAILS_SELECTS, select =>
                      this.findOneWithQuery({
                          ...defaultOptions,
                          select,
                      }),
                  ),
              ).then(spread(defaultsDeep));
    }
    @Span('RiskRepository.getRisksForCustomFormulas')
    async getRisksForCustomFormulas(id?: number): Promise<RiskIdAndVersion[]> {
        const query = this.createQueryBuilder();
        query.select('Risk.id');
        query.leftJoinAndSelect('Risk.currentVersion', 'currentVersion');
        query.leftJoinAndSelect('currentVersion.customFieldSubmission', 'customFieldSubmission');
        query.leftJoinAndSelect('customFieldSubmission.customFieldLocation', 'customFieldLocation');
        query.leftJoinAndSelect('customFieldLocation.customField', 'customField');
        query.leftJoinAndSelect('customField.options', 'options');
        if (!isNil(id)) {
            query.where({ id });
        }
        return query.getMany();
    }

    async findRisks(
        options: Pick<
            FindManyQueryOptions,
            'skip' | 'take' | 'where' | 'mainAlias' | 'order' | 'withDeleted' | 'relations'
        >,
        listView?: boolean,
    ): Promise<{ risks: Risk[]; total?: number }> {
        const baseWhere = { where: { currentVersion: { not$: null } } };

        const baseOptions = merge(options, baseWhere);
        // We need to get the ids because the where filter only brings the sub-entities (one-to-many relations) that match it and we need the full entities
        const query = this.buildQuery({
            relations: RISK_RELATIONS,
            ...omit(baseOptions, 'skip', 'take'), // if we pass those to typeorm it will make 2 queries, we can do it in one!
            offset: options.skip,
            limit: options.take,
            distinct: true,
            select: { id: true }, // selecting only the distinct ids will ensure there's no difference between skip and offset nor take and limit
            withRelationsOptimization: true,
        });

        const { risksIds, total } = await query
            .getManyAndCount()
            .then(([risks, t]) => ({ risksIds: map(risks, 'id'), total: t }));

        const defaultOptions = {
            relations: RISK_RELATIONS,
            // since we already have the ids paginated and ordered we don't need any of these options
            ...omit(options, 'skip', 'take', 'order'),
            where: { id: { in$: risksIds } },
            withRelationsOptimization: true,
        };

        const partialRisks = isEmpty(risksIds)
            ? []
            : await Promise.all(
                  // we need to make each join in a separated query since multiple one-to-many joins queried together multiply the resulting sql rows exponentially
                  map(listView ? RISK_LISTS_SELECTS : RISK_DETAILS_SELECTS, select =>
                      this.findWithQuery({
                          ...defaultOptions,
                          select,
                      }),
                  ),
              );

        const filteredRisks = chain(partialRisks)
            .flatten()
            .groupBy('id')
            .mapValues(risks => {
                const risk = defaultsDeep({}, ...risks);
                if (Array.isArray(risk.currentVersion?.customFieldSubmission)) {
                    risk.currentVersion.customFieldSubmission = chain(
                        risk.currentVersion.customFieldSubmission,
                    )
                        .groupBy(submission => submission.customFieldLocation?.id)
                        .map(submissions => maxBy(submissions, 'id'))
                        .compact()
                        .value();
                }

                return risk;
            })
            .value();
        return {
            // the risks ids are already sorted since these come from the 1st query
            risks: map(risksIds, id => filteredRisks[id]),
            total,
        };
    }

    async getStatistics(
        dto?: RiskStatisticsRequestDto,
        isReleaseRiskStatus = false,
    ): Promise<RisksStatistics> {
        const classificationsFilter = !isEmpty(dto?.classifications)
            ? { classification: { in$: dto.classifications } }
            : null;

        const riskStatusWhereClause = isReleaseRiskStatus
            ? { status: RiskStatusType.ACTIVE }
            : { applicable: true };

        const ownersIdsFilter = !isEmpty(dto?.ownersIds)
            ? {
                  owners: {
                      id: dto.ownersIds,
                  },
              }
            : null;

        const scoredAssessedRisksQuery = this.buildQuery({
            withRelationsOptimization: true,
            relations: RISK_RELATIONS,
            select: { id: true },
            where: {
                currentVersion: {
                    ...riskStatusWhereClause,
                    not$: { score: null },
                    ...(dto?.isVendor === true
                        ? {
                              vendors: {
                                  id: { not$: null },
                              },
                          }
                        : null),
                    ...classificationsFilter,
                    ...ownersIdsFilter,
                },
            },
        });

        const assessedRisksQuery = this.buildQuery({
            withRelationsOptimization: true,
            relations: RISK_RELATIONS,
            select: { id: true },
            where: {
                currentVersion: {
                    ...riskStatusWhereClause,
                    ...(dto?.isVendor === true
                        ? {
                              vendors: {
                                  id: { not$: null },
                              },
                          }
                        : null),
                    ...classificationsFilter,
                    ...ownersIdsFilter,
                },
            },
        });

        const needsAttentionRisksQuery = this.buildQuery({
            withRelationsOptimization: true,
            relations: RISK_RELATIONS,
            select: { id: true },
            where: {
                currentVersion: {
                    ...riskStatusWhereClause,
                    controls: {
                        archivedAt: null,
                        controlIsReady: { isReady: false },
                    },
                    ...(dto?.isVendor === true
                        ? {
                              vendors: {
                                  id: { not$: null },
                              },
                          }
                        : null),
                    ...classificationsFilter,
                    ...ownersIdsFilter,
                },
            },
        });

        const totalVendorRiskQuery = this.buildQuery({
            withRelationsOptimization: true,
            relations: RISK_RELATIONS,
            select: { id: true },
            where: {
                currentVersion: {
                    vendors: {
                        id: { not$: null },
                    },
                    ...classificationsFilter,
                    ...ownersIdsFilter,
                },
            },
        });

        const totalRiskQuery = this.buildQuery({
            withRelationsOptimization: true,
            relations: RISK_RELATIONS,
            select: { id: true },
            where: {
                currentVersion: {
                    ...classificationsFilter,
                    ...ownersIdsFilter,
                },
            },
        });

        const obtainRiskCount = dto?.isVendor === true ? totalVendorRiskQuery : totalRiskQuery;

        const [totalScoredAssessedRisks, totalAssessedRisks, totalNeedsAttentionRisks, totalRisks] =
            await Promise.all([
                scoredAssessedRisksQuery.getCount(),
                assessedRisksQuery.getCount(),
                needsAttentionRisksQuery.getCount(),
                obtainRiskCount.getCount(),
            ]);

        return {
            totalAssessedRisks,
            totalNeedsAttentionRisks,
            totalScoredAssessedRisks,
            totalRisks,
        };
    }

    async getDashboardRiskData(
        dto: RiskDashboardRequestDto,
        onlyVendors = false,
        isReleaseRiskClosedStatus = false,
        isReleaseCosmosTableForRisk = false,
    ): Promise<Risk[]> {
        return this.findWithQuery({
            relations: RISK_DASHBOARD_RELATIONS,
            withRelationsOptimization: true,
            where: this.resolveFilters(
                {
                    ...dto,
                    ...(!isReleaseRiskClosedStatus && { applicable: true }),
                    onlyVendors,
                },
                { isReleaseRiskStatus: isReleaseRiskClosedStatus, isReleaseCosmosTableForRisk },
            ),
            select: {
                id: true,
                currentVersion: {
                    id: true,
                    categories: { id: true, name: true },
                    impact: true,
                    likelihood: true,
                    score: true,
                    treatmentPlan: true,
                    residualImpact: true,
                    residualLikelihood: true,
                    residualScore: true,
                },
            },
        });
    }

    async getDashboardRiskDataCount(
        dto: RiskDashboardRequestDto,
        onlyVendors = false,
        isReleaseRiskClosedStatus = false,
    ): Promise<number> {
        return this.countWithQuery({
            relations: RISK_DASHBOARD_RELATIONS,
            withRelationsOptimization: true,
            where: this.resolveFilters(
                {
                    ...dto,
                    ...(!isReleaseRiskClosedStatus && { applicable: true }),
                    onlyVendors,
                },
                {
                    isReleaseRiskStatus: isReleaseRiskClosedStatus,
                },
            ),
        });
    }

    getRiskCount(): Promise<number> {
        return this.count();
    }

    async getVendorRiskCount(
        vendorId: number,
    ): Promise<{ riskCount: number; risksWithAnticipatedCompletionDateCount: number }> {
        const thirtyDaysFromNow = moment().utc().add(30, 'days').toDate();
        const query = this.createQueryBuilder('risk')
            .leftJoin('risk.currentVersion', 'rv')
            .leftJoin('rv.vendors', 'vendor')
            .where('risk.deleted_at IS NULL')
            .andWhere('vendor.id = :vendorId', { vendorId })
            .andWhere('rv.applicable = :applicable', { applicable: true })
            .select('COUNT(*)', 'riskCount')
            .addSelect(
                `SUM(
                        CASE
                            WHEN (
                                rv.completion_date IS NULL AND
                                rv.anticipated_completion_date IS NOT NULL AND
                                rv.anticipated_completion_date < :thirtyDaysFromNow
                            ) THEN 1 ELSE 0
                        END
                    )`,
                'risksWithAnticipatedCompletionDateCount',
            )
            .setParameter('thirtyDaysFromNow', thirtyDaysFromNow);

        const result = await query.getRawOne();

        return {
            riskCount: Number(result.riskCount) || 0,
            risksWithAnticipatedCompletionDateCount:
                Number(result.risksWithAnticipatedCompletionDateCount) || 0,
        };
    }

    getApplicableAndNotScoredRisksCount(): Promise<number> {
        return this.buildQuery({
            withRelationsOptimization: true,
            relations: RISK_RELATIONS,
            select: { id: true },
            where: {
                currentVersion: {
                    score: null,
                    applicable: true,
                },
            },
        }).getCount();
    }

    getApplicableAndNotTreatedRisksCount(): Promise<number> {
        return this.buildQuery({
            withRelationsOptimization: true,
            relations: RISK_RELATIONS,
            select: { id: true },
            where: {
                currentVersion: {
                    treatmentPlan: RiskTreatmentPlan.UNTREATED,
                    applicable: true,
                },
            },
        }).getCount();
    }

    getRiskCreatedCount(source: RiskSourceType): Promise<number> {
        return this.createQueryBuilder('Risk')
            .andWhere('Risk.source = :source', { source })
            .withDeleted()
            .getCount();
    }

    async getMappedTasksByRiskIdAndUserId(riskId: number): Promise<Risk[]> {
        const taskQuery = this.createQueryBuilder('risk')
            .innerJoinAndSelect('risk.customTasks', 'tasks')
            .leftJoinAndSelect('tasks.assignedTo', 'assignees')
            .leftJoinAndSelect('tasks.schedulerDefinition', 'schedulerDefinition')
            .orderBy('tasks.dueDate, tasks.id')
            .where('risk.id = :riskId', { riskId })
            .andWhere('tasks.deletedAt IS NULL');
        return taskQuery.getMany();
    }

    async getMappedRecurringTasksByRiskIdAndUserId(riskId: number): Promise<Risk[]> {
        const taskQuery = this.createQueryBuilder('risk')
            .innerJoinAndSelect('risk.customTaskConfigurations', 'tasks')
            .leftJoinAndSelect('tasks.assignedTo', 'assignees')
            .where('risk.id = :riskId', { riskId })
            .andWhere('tasks.deletedAt IS NULL');
        return taskQuery.getMany();
    }

    async getMappedTasks(
        id: number,
        dto: RiskCustomTasksRequestDto,
        user?: User,
    ): Promise<PaginationType<CustomTask>> {
        const { includeTaskIds, completed, page, limit } = dto;

        const tasksCountAlias = 'tasksCount';
        const tasksAlias = 'tasks';

        const skip = getSkip(page, limit);

        let countCondition;

        const taskQuery = this.createQueryBuilder('risk')
            .offset(skip)
            .limit(limit)
            .innerJoinAndSelect('risk.customTasks', tasksAlias)
            .leftJoinAndSelect('tasks.assignedTo', 'assignees')
            .where('risk.id = :id', { id })
            .andWhere('tasks.deletedAt IS NULL')
            .orderBy('tasks.dueDate, tasks.id');

        // Only apply filtering if user has risk manager restricted view
        if (user) {
            const hasRiskManagerRestrictedView =
                !hasUserPermission(user, Action.READ, Subject.ViewAllRisks) &&
                hasUserPermission(user, Action.READ, Subject.Risk);

            if (hasRiskManagerRestrictedView) {
                taskQuery
                    .leftJoin('risk.currentVersion', 'risk_version')
                    .leftJoin('risk_version.owners', 'risk_owners')
                    .andWhere(
                        new Brackets(qb => {
                            qb.where('risk_owners.id = :userId', { userId: user.id }).orWhere(
                                'tasks.assignedTo = :userId',
                                { userId: user.id },
                            );
                        }),
                    );
            }
        }

        if (!isNil(completed) && isEmpty(includeTaskIds)) {
            const baseCompletedCondition = `completed_at IS ${completed ? 'NOT NULL' : 'NULL'}`;
            taskQuery.andWhere(`${tasksAlias}.${baseCompletedCondition}`);
            countCondition = qb => qb.where(`${tasksCountAlias}.${baseCompletedCondition}`);
        }

        if (!isEmpty(includeTaskIds)) {
            const baseIncludeCondition = 'id IN (:...includeTaskIds)';
            const includeParams = {
                includeTaskIds,
            };
            taskQuery.andWhere(`${tasksAlias}.${baseIncludeCondition}`, includeParams);
            countCondition = qb =>
                qb.where(`${tasksCountAlias}.${baseIncludeCondition}`, includeParams);
        }

        const riskWithTasks = await taskQuery
            .loadRelationCountAndMap(
                'risk.customTasksCount',
                'risk.customTasks',
                tasksCountAlias,
                countCondition,
            )
            .getOne();

        const count = get(riskWithTasks, 'customTasksCount', 0);

        return {
            data: riskWithTasks?.customTasks ?? [],
            page,
            limit,
            total: count,
        };
    }

    async getControlRisks(id: number, dto: ControlRisksRequestDto): Promise<PaginationType<Risk>> {
        const { page, sort, sortDir, limit } = dto;
        const paginatedRisksQuery = this.createQueryBuilder('risk');
        const {
            property: sortCondition,
            order,
            addSelect,
        } = this.resolveControlRisksSort(paginatedRisksQuery, sort, sortDir);

        paginatedRisksQuery
            .leftJoinAndSelect('risk.currentVersion', 'currentVersion')
            .leftJoinAndSelect('currentVersion.owners', 'riskOwners')
            .leftJoinAndSelect('currentVersion.controls', 'mappedControls')
            .where('mappedControls.id = :id', { id })
            .orderBy(sortCondition, order)
            .skip(getSkip(page, limit))
            .take(limit);
        const [risks, total] = await paginatedRisksQuery.getManyAndCount();

        let risksWithControls: Risk[] = [];
        if (risks.length > 0) {
            const risksWithControlQuery = this.createQueryBuilder('risk')
                .leftJoinAndSelect('risk.currentVersion', 'currentVersion')
                .leftJoinAndSelect('currentVersion.owners', 'riskOwners')
                .leftJoinAndSelect('currentVersion.controls', 'mappedControls')
                .where('risk.id IN (:...riskIds)', { riskIds: risks.map(risk => risk.id) })
                .orderBy(sortCondition, order);

            if (!isNil(addSelect)) {
                risksWithControlQuery.addSelect(addSelect[0], addSelect[1]);
            }
            risksWithControls = await risksWithControlQuery.getMany();
        }
        return {
            data: risksWithControls,
            page,
            limit,
            total,
        };
    }

    private resolveControlRisksSort(
        query: SelectQueryBuilder<Risk>,
        sort: SortType,
        sortDir: SortDir,
    ): { property: string; order: 'ASC' | 'DESC'; addSelect?: [string, string] } {
        const sortOrder = (sortDir ? SortDir[sortDir] : SortDir[SortDir.ASC]) as 'ASC' | 'DESC';

        let sortOptions: {
            property: string;
            order: 'ASC' | 'DESC';
            addSelect?: [string, string];
        };

        switch (sort) {
            case SortType.RISK_ID:
                sortOptions = {
                    property: 'risk.riskId',
                    order: sortOrder,
                };
                break;
            case SortType.RISK_TITLE:
                sortOptions = {
                    property: 'currentVersion.title',
                    order: sortOrder,
                };
                break;
            case SortType.RISK_TREATMENT:
                const addSelect: [string, string] = [
                    `
                            CASE currentVersion.treatment_plan
                                WHEN 0 THEN 'UNTREATED'
                                WHEN 1 THEN 'ACCEPT'
                                WHEN 2 THEN 'TRANSFER'
                                WHEN 3 THEN 'AVOID'
                                WHEN 4 THEN 'MITIGATE'
                                ELSE 1
                            END
                        `,
                    '_treatmentPlan',
                ];
                query.addSelect(addSelect[0], addSelect[1]);
                sortOptions = {
                    addSelect,
                    property: '_treatmentPlan',
                    order: sortOrder,
                };
                break;
            case SortType.RISK_SCORE:
                sortOptions = {
                    property: 'currentVersion.score',
                    order: sortOrder,
                };
                break;
            case SortType.RISK_RESIDUAL_SCORE:
                sortOptions = {
                    property: 'currentVersion.residualScore',
                    order: sortOrder,
                };
                break;
            case SortType.RISK_OWNERS:
                sortOptions = {
                    property: 'riskOwners.firstName',
                    order: sortOrder,
                };
                break;
            default:
                sortOptions = {
                    property: `risk.riskId`,
                    order: sortOrder,
                };
        }
        return sortOptions;
    }

    async getRisksWithExclusions(
        account: Account,
        dto: RisksWithExclusionsRequestDto,
    ): Promise<PaginationType<Risk>> {
        const { page, limit, excludeControlId, q } = dto;

        const query = this.createQueryBuilder('risk').leftJoinAndSelect(
            'risk.currentVersion',
            'RiskVersion',
        );

        if (!isNil(excludeControlId)) {
            query
                .leftJoin('RiskVersion.controls', 'Controls')
                .where(
                    `RiskVersion.id NOT IN (SELECT crvm.fk_risk_version_id FROM ${fqtn(
                        account.databaseName,
                        'controls_risk_versions_map',
                    )} crvm WHERE crvm.fk_control_id = :excludeControlId)`,
                    {
                        excludeControlId,
                    },
                );
        }

        if (!isEmpty(q)) {
            query.andWhere(`(risk.riskId ${like()} :both OR RiskVersion.title ${like()} :both)`, {
                both: `%${q}%`,
            });
        }

        query.orderBy('RiskVersion.title', 'ASC').skip(getSkip(page, limit)).take(limit);

        const [risks, total] = await query.getManyAndCount();

        return {
            data: risks,
            page,
            limit,
            total,
        };
    }

    async listRisksWithCursor(risksRequestType: RisksRequestType): Promise<CursorPage<Risk>> {
        const query = this.createQueryBuilder('risk');
        const { sort, sortDir, size, expand, ...filters } = risksRequestType;

        query.innerJoinAndSelect('risk.currentVersion', 'currentVersion');

        this.expandSubCollections(query, expand);

        setupCursorQueryAutoIncrementId(query, sort, sortDir);
        applyCursorFilters(risksRequestType.cursor, query, sort, sortDir);

        this.applyFiltersToQuery(query, filters);

        return getCursorPageAutoIncrementId(query, sort, size);
    }

    async findRiskByIdWithExpandOrFail(
        id: number,
        expand: RiskExpand[] | undefined,
    ): Promise<Risk> {
        const query = this.createQueryBuilder('risk')
            .innerJoinAndSelect('risk.currentVersion', 'currentVersion')
            .where('risk.id = :id', { id });

        this.expandSubCollections(query, expand);

        return query.getOneOrFail();
    }

    private expandSubCollections(
        query: SelectQueryBuilder<Risk>,
        expands: RiskExpand[] | undefined,
    ): void {
        if (!expands || expands.length === 0) {
            return;
        }

        const expandMapping = {
            [RiskExpand.categories]: () =>
                query.leftJoinAndSelect('currentVersion.categories', 'categories'),

            [RiskExpand.controls]: () =>
                query
                    .leftJoinAndSelect('currentVersion.controls', 'controls')
                    .leftJoinAndMapOne(
                        'controls.controlIsReady',
                        'ControlIsReadyView',
                        'controlIsReady',
                        'controlIsReady.controlId = controls.id',
                    ),

            [RiskExpand.documents]: () =>
                query.leftJoinAndSelect('currentVersion.documents', 'documents'),

            [RiskExpand.owners]: () => query.leftJoinAndSelect('currentVersion.owners', 'owners'),

            [RiskExpand.reviewers]: () =>
                query.leftJoinAndSelect('currentVersion.reviewers', 'reviewers'),

            [RiskExpand.notes]: () =>
                query.leftJoinAndMapMany(
                    'risk.notes',
                    'GlobalNote',
                    'notes',
                    'notes.entity_reference_id = risk.id AND notes.note_reference_type = :noteReferenceType',
                    { noteReferenceType: GlobalNoteReferenceType.RISK },
                ),

            [RiskExpand.tickets]: () => query.leftJoinAndSelect('risk.tickets', 'tickets'),

            [RiskExpand.tasks]: () => query.leftJoinAndSelect('risk.customTasks', 'customTasks'),
        };

        [...new Set(expands)].forEach(expand => expandMapping[expand]?.());
    }

    private applyFiltersToQuery(
        query: SelectQueryBuilder<Risk>,
        filters: Omit<RisksRequestType, 'cursor' | 'size' | 'sort' | 'sortDir' | 'expand'>,
    ): void {
        const filterMapping = [
            {
                key: 'currentVersion.applicable',
                operator: '=',
                value: filters.applicable,
                param: 'applicable',
            },
            {
                key: 'currentVersion.status',
                operator: '=',
                value: filters.status,
                param: 'status',
            },
            {
                key: 'currentVersion.score',
                operator: '>=',
                value: filters.minScore,
                param: 'minScore',
            },
            {
                key: 'currentVersion.score',
                operator: '<=',
                value: filters.maxScore,
                param: 'maxScore',
            },
            {
                key: 'vendors.id',
                operator: '=',
                value: filters.vendorId,
                param: 'vendorId',
                requiresJoin: () => query.leftJoinAndSelect('currentVersion.vendors', 'vendors'),
            },
            {
                key: 'currentVersion.treatmentPlan',
                operator: '=',
                value: filters.treatmentPlan,
                param: 'treatmentPlan',
            },
            {
                key: 'currentVersion.title',
                operator: 'LIKE',
                value: filters.currentVersionTitle,
                param: 'currentVersionTitle',
                customWhere: 'currentVersion.title LIKE :currentVersionTitle',
                customParams: { currentVersionTitle: `%${filters.currentVersionTitle}%` },
            },
        ];

        filterMapping.forEach(filter => {
            if (filter.value !== undefined) {
                // Handle custom where clause (for search)
                if (filter.customWhere && filter.customParams) {
                    query.andWhere(filter.customWhere, filter.customParams);
                    return;
                }

                if (filter.requiresJoin) {
                    filter.requiresJoin();
                }

                query.andWhere(`${filter.key} ${filter.operator} :${filter.param}`, {
                    [filter.param]: filter.value,
                });
            }
        });
    }

    private resolveFilters(
        filters: {
            ids?: number[];
            applicable?: boolean;
            status?: RiskStatusType[];
            isScored?: boolean;
            scoreType?: RiskScoreType;
            treatmentPlan?: RiskTreatmentPlan;
            treatmentPlans?: RiskTreatmentPlan[];
            categoriesIds?: number[];
            ownersIds?: number[];
            q?: string;
            sort?: SortType;
            hasTicketsDone?: boolean;
            impact?: number;
            likelihood?: number;
            minScore?: number;
            maxScore?: number;
            isOwned?: boolean;
            riskFilter?: RiskFilter;
            vendorId?: number | null;
            onlyVendors?: boolean;
            classifications?: RiskClassification[];
            isRestrictedView?: boolean;
            riskGroup?: RiskGroup;
        },
        opts: {
            isReleaseScalingRiskFoundation?: boolean;
            isReleaseRiskStatus?: boolean;
            isReleaseCosmosTableForRisk?: boolean;
        },
    ): WhereOptions {
        const {
            ids,
            applicable,
            status,
            treatmentPlan,
            treatmentPlans,
            categoriesIds,
            ownersIds,
            q,
            sort,
            hasTicketsDone,
            impact,
            likelihood,
            minScore,
            maxScore,
            scoreType,
            isOwned,
            riskFilter,
            vendorId,
            onlyVendors,
            classifications,
            isRestrictedView,
            riskGroup,
        } = filters;
        const { isReleaseRiskStatus = false, isReleaseCosmosTableForRisk = false } = opts;
        // default behavior it meant to bring risks treatments
        // only if they have been scored
        const isResidualScore = scoreType === RiskScoreType.RESIDUAL;
        const isSortedByScore = sort === SortType.RISK_SCORE;
        const useScoredFilter = !isNil(filters.isScored) || isSortedByScore;
        const isScored = isSortedByScore || (useScoredFilter && filters.isScored);
        const score = isScored ? { score: { not$: null } } : { score: null };

        const impactLikelihoodQuery = chain([
            { impact, likelihood, residualScore: null },
            {
                residualImpact: impact,
                residualLikelihood: likelihood,
            },
        ])
            .map(r =>
                pick(r, [
                    !isNil(impact) && 'impact',
                    !isNil(likelihood) && 'likelihood',
                    !isNil(impact) &&
                        !isNil(likelihood) &&
                        isResidualScore === true &&
                        'residualScore',
                    !isNil(impact) && isResidualScore === true && 'residualImpact',
                    !isNil(likelihood) && isResidualScore === true && 'residualLikelihood',
                ]),
            )
            .filter(r => !isEmpty(r))
            .value();

        const scoreQuery = chain([
            {
                score: {
                    not$: null,
                    lte$: maxScore,
                    gte$: minScore,
                },
                residualScore: null,
            },
            {
                residualScore: {
                    not$: null,
                    lte$: maxScore,
                    gte$: minScore,
                },
            },
        ])
            .map(r =>
                pick(r, [
                    isScored === true && 'score.not$',
                    !isNil(minScore) && 'score.gte$',
                    !isNil(maxScore) && 'score.lte$',
                    !isNil(minScore) &&
                        !isNil(maxScore) &&
                        isResidualScore === true &&
                        'residualScore',
                    isResidualScore === true && 'residualScore.not$',
                    !isNil(minScore) && isResidualScore === true && 'residualScore.gte$',
                    !isNil(maxScore) && isResidualScore === true && 'residualScore.lte$',
                ]),
            )
            .filter(r => !isEmpty(r))
            .value();

        const andDeepQuery = [impactLikelihoodQuery, scoreQuery].filter(r => !isEmpty(r));

        const whereOptions = {
            id: { in$: ids },
            riskGroup,
            nested$: [
                {
                    customTasks: {
                        assignedTo: ownersIds,
                    },
                },
                {
                    customTaskConfigurations: {
                        assignedTo: ownersIds,
                    },
                },
                {
                    currentVersion: {
                        owners: isEmpty(ownersIds)
                            ? { id: isOwned ? { not$: null } : null }
                            : { id: { in$: ownersIds } },
                    },
                },
            ],
            currentVersion: {
                applicable,
                status: { in$: status },
                ...score,
                treatmentPlan: !isUndefined(treatmentPlan)
                    ? treatmentPlan
                    : !isEmpty(treatmentPlans)
                      ? { in$: treatmentPlans }
                      : {},
                categories: { id: { in$: categoriesIds } },
                owners: isEmpty(ownersIds)
                    ? { id: isOwned ? { not$: null } : null }
                    : { id: { in$: ownersIds } },
                controls: {
                    archivedAt: null,
                    controlIsReady: { isReady: false },
                },
                type:
                    riskFilter === RiskFilter.INTERNAL_ONLY
                        ? RiskVersionType.INTERNAL
                        : RiskVersionType.EXTERNAL,
                vendors: !isNil(vendorId) ? { id: vendorId } : { id: { not$: null } },
                classification: { in$: classifications },
                andDeep$: andDeepQuery,
            },
            source:
                riskFilter === RiskFilter.INTERNAL_ONLY && isReleaseCosmosTableForRisk
                    ? [RiskSourceType.CUSTOM, RiskSourceType.DRATA]
                    : RiskSourceType.CUSTOM,
            and$: [
                { riskId: { like$: `%${q}%` } },
                { currentVersion: { title: { like$: `%${q}%` } } },
                { currentVersion: { description: { like$: `%${q}%` } } },
                { currentVersion: { vendors: { name: { like$: `%${q}%` } } } },
            ],
            tickets: { isDone: hasTicketsDone },
        };

        return pick(whereOptions, [
            !isEmpty(ids) && 'id',
            !isEmpty(andDeepQuery) && 'currentVersion.andDeep$',
            !isNil(applicable) && isReleaseRiskStatus === false && 'currentVersion.applicable',
            !isEmpty(status) && isReleaseRiskStatus === true && 'currentVersion.status',
            useScoredFilter && 'currentVersion.score',
            !isUndefined(treatmentPlan) && 'currentVersion.treatmentPlan',
            !isEmpty(treatmentPlans) && 'currentVersion.treatmentPlan',
            !isEmpty(categoriesIds) && 'currentVersion.categories',
            (isNil(isRestrictedView) || isRestrictedView === false) &&
                (!isEmpty(ownersIds) || !isNil(isOwned)) &&
                'currentVersion.owners',
            isRestrictedView === true && 'nested$',
            riskFilter === RiskFilter.NEEDS_ATTENTION && 'currentVersion.controls',
            !isNil(hasTicketsDone) && 'tickets',
            !isNil(riskFilter) && riskFilter !== RiskFilter.NEEDS_ATTENTION && 'source',
            (riskFilter === RiskFilter.INTERNAL_ONLY || riskFilter === RiskFilter.EXTERNAL_ONLY) &&
                'currentVersion.type',
            !isNil(vendorId) && 'currentVersion.vendors',
            onlyVendors === true && 'currentVersion.vendors.id.not$',
            !isEmpty(classifications) && 'currentVersion.classification',
            !isNil(riskGroup) && 'riskGroup',
            !isEmpty(q) && 'and$',
        ]);
    }

    private resolveSort(options: {
        sort: SortType;
        sortDir: SortDir;
        sortByField?: { field: string; values: string[] | number[] };
        isReleaseRiskStatus?: boolean;
        isReleaseCosmosTableForRisk?: boolean;
    }): OrderOptions {
        const {
            sort,
            sortDir,
            sortByField,
            isReleaseRiskStatus = false,
            isReleaseCosmosTableForRisk = false,
        } = options;
        let sortOptions: OrderOptions = [];

        const order = getSortDir(sortDir);

        switch (sort) {
            case SortType.NAME:
                sortOptions = [
                    {
                        property: 'currentVersion.title',
                        order,
                    },
                ];
                break;
            case SortType.RISK_STATUS:
                sortOptions = [
                    isReleaseRiskStatus
                        ? {
                              property: 'currentVersion.status',
                              order,
                          }
                        : {
                              property: `
                            CASE currentVersion.applicable
                                WHEN 0 THEN 'ARCHIVED'
                                WHEN 1 THEN 'ACTIVE'
                                ELSE 1
                            END
                        `,
                              order,
                          },
                ];
                break;
            case SortType.RISK_TREATMENT:
                sortOptions = [
                    {
                        property: `
                            CASE currentVersion.treatment_plan
                                WHEN 0 THEN 'UNTREATED'
                                WHEN 1 THEN 'ACCEPT'
                                WHEN 2 THEN 'TRANSFER'
                                WHEN 3 THEN 'AVOID'
                                WHEN 4 THEN 'MITIGATE'
                                ELSE 1
                            END
                        `,
                        order,
                    },
                ];
                break;
            case SortType.RISK_IMPACT:
                sortOptions = [
                    {
                        property: `COALESCE(currentVersion.impact, ${order === 'ASC' ? 9999 : 0})`,
                        order,
                    },
                    {
                        property: `SUBSTRING_INDEX(risk.riskId, '-', 1)`,
                        order,
                    },
                    {
                        property: `CONVERT(SUBSTRING_INDEX(risk.riskId, '-', -1), UNSIGNED integer)`,
                        order,
                    },
                ];
                break;
            case SortType.RISK_LIKELIHOOD:
                sortOptions = [
                    {
                        property: `COALESCE(currentVersion.likelihood, ${
                            order === 'ASC' ? 9999 : 0
                        })`,
                        order,
                    },
                    {
                        property: `SUBSTRING_INDEX(risk.riskId, '-', 1)`,
                        order,
                    },
                    {
                        property: `CONVERT(SUBSTRING_INDEX(risk.riskId, '-', -1), UNSIGNED integer)`,
                        order,
                    },
                ];
                break;
            case SortType.RISK_SCORE:
                sortOptions = [
                    isReleaseCosmosTableForRisk
                        ? {
                              property: `COALESCE(currentVersion.score, ${
                                  order === 'ASC' ? 9999 : 0
                              })`,
                              order,
                          }
                        : {
                              // will place null scores at the end when sort ascending
                              property: `COALESCE(COALESCE(currentVersion.residualScore, currentVersion.score), ${
                                  order === 'ASC' ? 9999 : 0
                              })`,
                              order,
                          },
                    {
                        property: `SUBSTRING_INDEX(risk.riskId, '-', 1)`,
                        order,
                    },
                    {
                        property: `CONVERT(SUBSTRING_INDEX(risk.riskId, '-', -1), UNSIGNED integer)`,
                        order,
                    },
                ];
                break;
            case SortType.RISK_RESIDUAL_IMPACT:
                sortOptions = [
                    {
                        /**
                         * If the risk treatment plan is either transfer or mitigate then use the current residual impact value,
                         * otherwise handle it as Zero or as a high unreachable value (9999) because it could have a valid value
                         * (like 5), but since the treatment plan is not a valid one then we should apply this specific sort logic.
                         */
                        property: `
                            CASE currentVersion.treatment_plan
                                WHEN 2 THEN COALESCE(currentVersion.residual_impact, ${
                                    order === 'ASC' ? 9999 : 0
                                })
                                WHEN 4 THEN COALESCE(currentVersion.residual_impact, ${
                                    order === 'ASC' ? 9999 : 0
                                })
                                ELSE ${order === 'ASC' ? 9999 : 0}
                            END
                        `,
                        order,
                    },
                    {
                        property: `SUBSTRING_INDEX(risk.riskId, '-', 1)`,
                        order,
                    },
                    {
                        property: `CONVERT(SUBSTRING_INDEX(risk.riskId, '-', -1), UNSIGNED integer)`,
                        order,
                    },
                ];
                break;
            case SortType.RISK_RESIDUAL_LIKELIHOOD:
                sortOptions = [
                    {
                        /**
                         * If the risk treatment plan is either transfer or mitigate then use the current residual likelihood value,
                         * otherwise handle it as Zero or as a high unreachable value (9999) because it could have a valid value
                         * (like 5), but since the treatment plan is not a valid one then we should apply this specific sort logic.
                         */
                        property: `
                            CASE currentVersion.treatment_plan
                                WHEN 2 THEN COALESCE(currentVersion.residual_likelihood, ${
                                    order === 'ASC' ? 9999 : 0
                                })
                                WHEN 4 THEN COALESCE(currentVersion.residual_likelihood, ${
                                    order === 'ASC' ? 9999 : 0
                                })
                                ELSE ${order === 'ASC' ? 9999 : 0}
                            END
                        `,
                        order,
                    },
                    {
                        property: `SUBSTRING_INDEX(risk.riskId, '-', 1)`,
                        order,
                    },
                    {
                        property: `CONVERT(SUBSTRING_INDEX(risk.riskId, '-', -1), UNSIGNED integer)`,
                        order,
                    },
                ];
                break;
            case SortType.RISK_RESIDUAL_SCORE:
                sortOptions = [
                    {
                        /**
                         * If the risk treatment plan is either transfer or mitigate then use the current residual score value,
                         * otherwise handle it as Zero or as a high unreachable value (9999) because it could have a valid value
                         * (like 10), but since the treatment plan is not a valid one then we should apply this specific sort logic.
                         */
                        property: `
                            CASE currentVersion.treatment_plan
                                WHEN 2 THEN COALESCE(currentVersion.residual_score, ${
                                    order === 'ASC' ? 9999 : 0
                                })
                                WHEN 4 THEN COALESCE(currentVersion.residual_score, ${
                                    order === 'ASC' ? 9999 : 0
                                })
                                ELSE ${order === 'ASC' ? 9999 : 0}
                            END
                        `,
                        order,
                    },
                    {
                        property: `SUBSTRING_INDEX(risk.riskId, '-', 1)`,
                        order,
                    },
                    {
                        property: `CONVERT(SUBSTRING_INDEX(risk.riskId, '-', -1), UNSIGNED integer)`,
                        order,
                    },
                ];
                break;
            case SortType.IDENTIFIED_DATE:
                sortOptions = [
                    {
                        property: 'currentVersion.identifiedAt',
                        order,
                    },
                    {
                        property: `SUBSTRING_INDEX(risk.riskId, '-', 1)`,
                        order,
                    },
                    {
                        property: `CONVERT(SUBSTRING_INDEX(risk.riskId, '-', -1), UNSIGNED integer)`,
                        order,
                    },
                ];
                break;
            case SortType.ANTICIPATED_COMPLETION_DATE:
                sortOptions = [
                    {
                        property: 'currentVersion.anticipatedCompletionDate',
                        order,
                    },
                    {
                        property: `SUBSTRING_INDEX(risk.riskId, '-', 1)`,
                        order,
                    },
                    {
                        property: `CONVERT(SUBSTRING_INDEX(risk.riskId, '-', -1), UNSIGNED integer)`,
                        order,
                    },
                ];
                break;
            case SortType.RISK_COMPLETION_DATE:
                sortOptions = [
                    {
                        property: 'currentVersion.completionDate',
                        order,
                    },
                    {
                        property: `SUBSTRING_INDEX(risk.riskId, '-', 1)`,
                        order,
                    },
                    {
                        property: `CONVERT(SUBSTRING_INDEX(risk.riskId, '-', -1), UNSIGNED integer)`,
                        order,
                    },
                ];
                break;
            case SortType.RISK_FIELD:
                sortOptions = [
                    {
                        property: `COALESCE(NULLIF(FIELD(${
                            sortByField?.field
                        }, ${sortByField?.values.join(',')}), 0), 9999)`,
                        order: getSortDir(SortDir.ASC), // `sortByField.values` already contains desired order.
                    },
                    {
                        property: `SUBSTRING_INDEX(risk.riskId, '-', 1)`,
                        order,
                    },
                    {
                        property: `CONVERT(SUBSTRING_INDEX(risk.riskId, '-', -1), UNSIGNED integer)`,
                        order,
                    },
                ];
                break;
            case SortType.RISK_TYPE:
                sortOptions = [
                    {
                        property: 'currentVersion.type',
                        order,
                    },
                    {
                        property: `SUBSTRING_INDEX(risk.riskId, '-', 1)`,
                        order,
                    },
                    {
                        property: `CONVERT(SUBSTRING_INDEX(risk.riskId, '-', -1), UNSIGNED integer)`,
                        order,
                    },
                ];
                break;
            default:
                sortOptions = [
                    {
                        property: `SUBSTRING_INDEX(risk.riskId, '-', 1)`,
                        order,
                    },
                    {
                        property: `CONVERT(SUBSTRING_INDEX(risk.riskId, '-', -1), UNSIGNED integer)`,
                        order,
                    },
                ];
        }

        return sortOptions;
    }

    async deleteCustomRiskByVendorId(vendorId: number): Promise<void> {
        const risks = await this.buildQuery({
            withRelationsOptimization: true,
            relations: RISK_RELATIONS,
            select: { id: true },
            where: {
                currentVersion: {
                    vendors: {
                        id: vendorId,
                    },
                },
            },
        }).getMany();
        const riskToDelete = risks.map(risk => risk.id);
        if (!isEmpty(riskToDelete)) {
            await this.softDelete(riskToDelete);
        }
    }

    async markDeletedRisks(ids: number[]): Promise<void> {
        await this.createQueryBuilder()
            .update(Risk)
            .set({
                riskId: () => `CONCAT(risk_id, '_DELETED_', id)`,
            })
            .where('id IN(:...ids)', { ids })
            .andWhere('source = :source', {
                source: RiskSourceType.DRATA,
            })
            .andWhere('deleted_at IS NOT NULL')
            .andWhere(`risk_id NOT LIKE '%_DELETED_%'`)
            .execute();
    }

    /**
     * This method has no input validation and is intended to be treated as a high performant query.
     */
    async updateRisksCurrentVersion(account: Account, risks: Risk[]): Promise<void> {
        await this.query(
            `
            UPDATE ${fqtn(account.databaseName, 'risk')}
            SET fk_current_version_id = CASE
                ${risks
                    .map(risk => `WHEN id = ${risk.id} THEN ${risk.currentVersion.id}`)
                    .join('\n')}
            END
            WHERE id IN (${risks.map(() => '?').join(', ')});
        `,
            risks.map(risk => risk.id),
        );
    }

    /**
     * This method has no input validation and is intended to be treated as a high performant query.
     */
    async assignRisksCategories(account: Account, risks: Risk[]): Promise<void> {
        const categoryValues = flatten(
            risks.map(risk =>
                risk.currentVersion.categories.map(category => [
                    risk.currentVersion.id,
                    category.id,
                ]),
            ),
        );

        if (isEmpty(categoryValues)) {
            return;
        }

        await this.query(
            `
            INSERT INTO ${fqtn(
                account.databaseName,
                'risk_version_category_map',
            )} (fk_risk_version_id, fk_category_id)
            VALUES ${categoryValues.map(() => '(?, ?)').join(', ')};
        `,
            flatten(categoryValues),
        );
    }

    /**
     * This method has no input validation and is intended to be treated as a high performant query.
     */
    async assignRisksControls(account: Account, risks: Risk[]): Promise<void> {
        const controlValues = flatten(
            risks.map(risk =>
                risk.currentVersion.controls.map(control => [risk.currentVersion.id, control.id]),
            ),
        );

        if (isEmpty(controlValues)) {
            return;
        }

        await this.query(
            `
            INSERT INTO ${fqtn(
                account.databaseName,
                'controls_risk_versions_map',
            )} (fk_risk_version_id, fk_control_id)
            VALUES ${controlValues.map(() => '(?, ?)').join(', ')};
        `,
            flatten(controlValues),
        );
    }

    async assignRisksOwners(risks: Risk[]): Promise<void> {
        const values = risks
            .filter(risk => risk.currentVersion?.owners)
            .map(risk =>
                risk.currentVersion.owners.map(owner => ({
                    fk_risk_version_id: risk.currentVersion.id,
                    fk_user_id: owner.id,
                })),
            )
            .flat();

        if (isEmpty(values)) {
            return;
        }

        await this.createQueryBuilder()
            .insert()
            .into('risk_versions_owners_map')
            .values(values)
            .execute();
    }

    async assignRisksReviewers(risks: Risk[]): Promise<void> {
        const values = risks
            .filter(risk => risk.currentVersion?.reviewers)
            .map(risk =>
                risk.currentVersion.reviewers.map(reviewer => ({
                    fk_risk_version_id: risk.currentVersion.id,
                    fk_user_id: reviewer.id,
                })),
            )
            .flat();

        if (isEmpty(values)) {
            return;
        }

        await this.createQueryBuilder()
            .insert()
            .into('risk_versions_reviewers_map')
            .values(values)
            .execute();
    }

    private getRisksWithCustomFieldsQuery(): SelectQueryBuilder<Risk> {
        return this.createQueryBuilder('risk')
            .innerJoinAndSelect('risk.currentVersion', 'risk_version')
            .leftJoinAndSelect('risk_version.controls', 'controls')
            .leftJoinAndSelect('risk_version.categories', 'categories')
            .leftJoinAndSelect('risk_version.owners', 'owners')
            .leftJoinAndSelect('risk_version.reviewers', 'reviewers')
            .leftJoinAndSelect('risk_version.vendors', 'vendors')
            .leftJoinAndSelect('risk_version.customFieldSubmission', 'custom_field_submission')
            .leftJoinAndSelect(
                'custom_field_submission.customFieldLocation',
                'custom_field_location',
            )
            .leftJoinAndSelect('custom_field_location.customField', 'custom_field')
            .leftJoinAndSelect('custom_field.options', 'custom_field_option')
            .orderBy('custom_field_submission.created_at', 'DESC')
            .select([
                'risk.id',
                'risk_version.id',
                'risk.riskId',
                'risk_version.title',
                'risk_version.description',
                'controls.code',
                'categories.id',
                'categories.name',
                'categories.source',
                'risk_version.treatmentPlan',
                'risk_version.impact',
                'risk_version.likelihood',
                'risk_version.score',
                'risk_version.residualImpact',
                'risk_version.residualLikelihood',
                'risk_version.residualScore',
                'risk_version.type',
                'risk_version.status',
                'risk_version.anticipatedCompletionDate',
                'risk_version.completionDate',
                'owners.email',
                'owners.id',
                'owners.firstName',
                'owners.lastName',
                'owners.avatar',
                'owners.jobTitle',
                'reviewers.email',
                'vendors.id',
                'vendors.name',
                'custom_field_submission.id',
                'custom_field_submission.submission',
                'custom_field_location.id',
                'custom_field.id',
                'custom_field.isHidden',
                'custom_field.name',
                'custom_field.fieldType',
                'custom_field_option.id',
                'custom_field_option.isHidden',
                'custom_field_option.value',
                'custom_field_option.valueNumber',
            ]);
    }

    getRisksByCustomFieldLocation(customFieldLocationId: number) {
        return this.createQueryBuilder('risk')
            .innerJoin('risk.currentVersion', 'risk_version')
            .innerJoin('risk_version.customFieldSubmission', 'custom_field_submission')
            .select(['risk.id', 'risk_version.id'])
            .where('custom_field_submission.fk_custom_field_location_id = :customFieldLocationId', {
                customFieldLocationId,
            })
            .getMany();
    }

    getRisksWithCustomFieldsByRiskVersionIds(ids: number[]): Promise<Risk[]> {
        if (isEmpty(ids)) {
            return Promise.resolve([]);
        }

        return this.getRisksWithCustomFieldsQuery()
            .where('risk_version.id IN(:...ids)', {
                ids,
            })
            .getMany();
    }

    async getDocumentsByCustomFieldLocation(
        account: Account,
        workspace: Product,
        customFieldLocationId: number,
    ): Promise<RiskDocumentBulk> {
        const affectedRisks = await this.getRisksByCustomFieldLocation(customFieldLocationId);
        const risks = await this.getRisksWithCustomFieldsByRiskVersionIds(
            affectedRisks.map(risk => risk.currentVersion.id),
        );

        return prepDocumentsForBulk(
            account,
            mapAndFilterRisksToDocuments(account, workspace, risks),
        );
    }

    async getDocuments(
        account: Account,
        workspace: Product,
        riskIds?: number[],
    ): Promise<RiskDocumentBulk> {
        const risks = await this.getRisksWithCustomFields(riskIds);

        return prepDocumentsForBulk(
            account,
            mapAndFilterRisksToDocuments(account, workspace, risks),
        );
    }

    async getRisksWithCustomFields(riskIds?: number[]): Promise<Risk[]> {
        const query = this.getRisksWithCustomFieldsQuery();

        if (!isEmpty(riskIds)) {
            query.where('risk.id IN(:...riskIds)', {
                riskIds,
            });
        }

        const risks = await query.getMany();

        if (riskIds && !isEmpty(riskIds)) {
            return this.reorderByIds(risks, riskIds);
        }

        return risks;
    }

    getRiskWithCurrentVersionId(riskId: number): Promise<Risk> {
        const query = this.buildQuery({
            where: { id: riskId },
            relations: { currentVersion: true },
            select: { id: true, currentVersion: { id: true } },
        });
        return query.getOneOrFail();
    }

    async assignRisksVendors(risks: Risk[]): Promise<void> {
        const values = risks
            .filter(risk => risk.currentVersion?.vendors)
            .map(risk =>
                risk.currentVersion.vendors.map(vendor => ({
                    fk_risk_version_id: risk.currentVersion.id,
                    fk_vendor_id: vendor.id,
                })),
            )
            .flat();

        if (isEmpty(values)) {
            return;
        }

        await this.createQueryBuilder()
            .insert()
            .into('risk_versions_vendors_map')
            .values(values)
            .execute();
    }

    async assignRisksDocuments(risks: Risk[]): Promise<void> {
        const values = risks
            .filter(risk => risk.currentVersion?.documents)
            .map(risk =>
                risk.currentVersion.documents.map(document => ({
                    fk_risk_version_id: risk.currentVersion.id,
                    fk_document_id: document.id,
                })),
            )
            .flat();

        if (isEmpty(values)) {
            return;
        }

        await this.createQueryBuilder()
            .insert()
            .into('risk_versions_documents_map')
            .values(values)
            .execute();
    }

    async getRiskDocumentsWithCursor(
        riskId: string,
        riskDocumentsRequestType: CursorPaginationRequestType,
    ): Promise<CursorPage<Document>> {
        const { sort, sortDir, size } = riskDocumentsRequestType;

        const query = this.manager
            .createQueryBuilder(Document, 'Document')
            .innerJoin('risk_versions_documents_map', 'rvdm', 'Document.id = rvdm.fk_document_id')
            .innerJoin('risk_version', 'rv', 'rv.id = rvdm.fk_risk_version_id')
            .innerJoin('risk', 'r', 'r.fk_current_version_id = rv.id')
            .where('r.riskId = :riskId', { riskId });

        setupCursorQueryAutoIncrementId(query, sort, sortDir);
        applyCursorFilters(riskDocumentsRequestType.cursor, query, sort, sortDir);

        return getCursorPageAutoIncrementId(query, sort, size);
    }

    private reorderByIds(risks: Risk[], ids: number[]): Risk[] {
        const riskMap = new Map(risks.map(risk => [risk.id, risk]));
        return ids
            .map(id => riskMap.get(id))
            .filter(Boolean)
            .filter(u => u !== undefined);
    }
}

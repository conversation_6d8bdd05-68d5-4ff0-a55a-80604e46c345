export enum AgentWorkflowVendor<PERSON>riteriaStepName {
    CREATE_CRITERIA_MISSING_QUESTIONNAIRES = 'CREATE_CRITERIA_MISSING_QUESTIONNAIRES',
    SEND_CRITERIA_QUESTIONNAIRE_TO_AI = 'SEND_CRITERIA_QUESTIONNAIRE_TO_AI',
    PROCESSING_QUESTIONNAIRES = 'PROCESSING_QUESTIONNAIRES',
    PROCESSING_QUESTIONS = 'PROCESSING_QUESTIONS',
    CREATING_CRITERIA = 'CREATING_CRITERIA',
    CRITERIA_CREATED = 'CRITERIA_CREATED',
    REVIEW_AND_MODIFY_CRITERIA = 'REVIEW_AND_MODIFY_CRITERIA',
    SAVE_GENERATED_CRITERIA = 'SAVE_GENERATED_CRITERIA',
    RESTORE_DRATA_CRITERIA = 'RESTORE_DRATA_CRITERIA',
    SEND_CREATE_CRITERIA_TO_AI = 'SEND_CREATE_CRITERIA_TO_AI',
    CRITERIA_QUESTIONS_CREATED = 'CRITERIA_QUESTIONS_CREATED',
}

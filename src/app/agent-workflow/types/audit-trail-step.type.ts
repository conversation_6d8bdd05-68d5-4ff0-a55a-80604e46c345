import { AgentWorkflowStepMessageRef } from 'app/agent-workflow/enums/agent-workflow-step-message-ref.enum';
import { AgentWorkflowStepStatus } from 'app/agent-workflow/enums/agent-workflow-step-status.enum';

/**
 * Type for audit trail step data
 */
export type AuditTrailStep = {
    id?: number;
    displayName: string;
    status: AgentWorkflowStepStatus;
    createdAt?: Date;
    ref?: AgentWorkflowStepMessageRef;
};

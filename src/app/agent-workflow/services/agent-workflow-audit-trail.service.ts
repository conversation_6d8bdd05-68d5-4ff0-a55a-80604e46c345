import { Injectable } from '@nestjs/common';
import { AgentWorkflowStep } from 'app/agent-workflow/entities';
import { AgentWorkflowStepStatus } from 'app/agent-workflow/enums';
import { AgentWorkflowAuditTrailFactory } from 'app/agent-workflow/factories/agent-workflow-audit-trail.factory';
import { AgentWorkflowRepository } from 'app/agent-workflow/repositories/agent-workflow.repository';
import { AuditTrailStepConfig } from 'app/agent-workflow/types/audit-trail-step-config.type';
import { AuditTrailStep } from 'app/agent-workflow/types/audit-trail-step.type';
import { Account } from 'auth/entities/account.entity';
import { AppService } from 'commons/services/app.service';

@Injectable()
export class AgentWorkflowAuditTrailService extends AppService {
    constructor(private readonly auditTrailFactory: AgentWorkflowAuditTrailFactory) {
        super();
    }

    /**
     * Get audit trail steps for a workflow
     * @param account - The account context
     * @param workflowId - The workflow ID
     * @returns Promise<AuditTrailStep[]> - Array of audit trail step data
     */
    async getWorkflowAuditTrail(account: Account, workflowId: number): Promise<AuditTrailStep[]> {
        this.log(`Getting audit trail for workflow ${workflowId}`, account);

        // Get the workflow with its steps
        const workflow = await this.agentWorkflowRepository.findOneOrFail({
            where: { id: workflowId },
            relations: ['steps'],
            order: {
                steps: { createdAt: 'ASC' },
            },
        });

        // Get the appropriate audit trail mapping for this workflow type
        const auditTrailMapping = this.auditTrailFactory.getAuditTrailMapping(
            workflow.agentType,
            workflow.agentWorkflowType,
        );

        // Use the new method to build audit trail steps
        const auditTrailStep = this.buildAuditTrailSteps(auditTrailMapping, workflow.steps);

        this.log(
            `Retrieved ${auditTrailStep.length} audit trail steps for workflow ${workflowId}`,
            account,
            {
                workflowId,
                stepCount: auditTrailStep.length,
                agentType: workflow.agentType,
                workflowType: workflow.agentWorkflowType,
            },
        );

        return auditTrailStep;
    }

    /**
     * Build audit trail steps from a mapping and a list of workflow steps
     * @param auditTrailMapping - The audit trail mapping configuration
     * @param steps - The list of agent workflow steps
     * @returns AuditTrailStep[] - Array of audit trail steps with step information
     */
    buildAuditTrailSteps(
        auditTrailMapping: Map<string, AuditTrailStepConfig>,
        steps: AgentWorkflowStep[],
    ): AuditTrailStep[] {
        // Filter steps to only include audit trail steps
        const auditTrailSteps = steps.filter(step =>
            this.isAuditTrailStep(step, auditTrailMapping),
        );

        return Array.from(auditTrailMapping, ([stepName, config]) => {
            const step = auditTrailSteps.find(s => s.stepName === stepName);

            return {
                id: step?.id,
                displayName: config.displayName,
                status: step?.status ?? AgentWorkflowStepStatus.PENDING,
                createdAt: step?.createdAt,
                ref: config.ref,
            };
        });
    }

    /**
     * Check if a step should be included in the audit trail
     * @param step - The workflow step
     * @param auditTrailMapping - The audit trail mapping for this workflow type
     * @returns boolean - Whether the step should be included
     */
    private isAuditTrailStep(
        step: AgentWorkflowStep,
        auditTrailMapping: Map<string, { displayName: string }>,
    ): boolean {
        return auditTrailMapping.has(step.stepName);
    }

    private get agentWorkflowRepository() {
        return this.getCustomTenantRepository(AgentWorkflowRepository);
    }
}

import { TestingModule } from '@nestjs/testing';
import { AgentWorkflow } from 'app/agent-workflow/entities';
import { AgentType } from 'app/agent-workflow/enums/agent-type.enum';
import { AgentWorkflowGeneralStepName } from 'app/agent-workflow/enums/agent-workflow-general-step-name.enum';
import { AgentWorkflowStepMessageRef } from 'app/agent-workflow/enums/agent-workflow-step-message-ref.enum';
import { AgentWorkflowStepStatus } from 'app/agent-workflow/enums/agent-workflow-step-status.enum';
import { AgentWorkflowVendorAssessmentStepName } from 'app/agent-workflow/enums/agent-workflow-vendor-assessment-step-name.enum';
import { AgentWorkflowVendorType } from 'app/agent-workflow/enums/agent-workflow-vendor-type.enum';
import { AgentWorkflowAuditTrailFactory } from 'app/agent-workflow/factories/agent-workflow-audit-trail.factory';
import { VendorRiskManagementAuditTrailMappingFactory } from 'app/agent-workflow/factories/vendor-risk-management-audit-trail-mapping.factory';
import { AgentWorkflowRepository } from 'app/agent-workflow/repositories/agent-workflow.repository';
import { AgentWorkflowAuditTrailService } from 'app/agent-workflow/services/agent-workflow-audit-trail.service';
import { Account } from 'auth/entities/account.entity';
import { createAppTestingModule } from 'commons/helpers/app-testing-module.helper';

describe('AgentWorkflowAuditTrailService', () => {
    let service: AgentWorkflowAuditTrailService;
    let mockRepository: jest.Mocked<AgentWorkflowRepository>;

    const mockAccount = { id: 1 } as unknown as Account;

    beforeEach(async () => {
        const module: TestingModule = await createAppTestingModule({
            providers: [
                AgentWorkflowAuditTrailService,
                AgentWorkflowAuditTrailFactory,
                VendorRiskManagementAuditTrailMappingFactory,
            ],
        }).compile();

        service = module.get<AgentWorkflowAuditTrailService>(AgentWorkflowAuditTrailService);

        // Mock the repository
        mockRepository = {
            findOneOrFail: jest.fn(),
        } as any;

        // Mock the getCustomTenantRepository method
        jest.spyOn(service as any, 'agentWorkflowRepository', 'get').mockReturnValue(
            mockRepository,
        );

        // Mock the log method to avoid console output during tests
        jest.spyOn(service as any, 'log').mockImplementation(() => {});
    });

    describe('getWorkflowAuditTrail', () => {
        it('should return audit trail steps for a workflow', async () => {
            // Arrange
            const workflowId = 1;
            const mockWorkflow = {
                id: workflowId,
                agentType: AgentType.VENDOR_RISK_MANAGEMENT,
                agentWorkflowType: AgentWorkflowVendorType.VENDOR_ASSESSMENT,
                steps: [
                    {
                        id: 1,
                        stepName: AgentWorkflowGeneralStepName.PROCESS_START,
                        status: AgentWorkflowStepStatus.COMPLETED,
                        createdAt: new Date('2024-01-01T10:00:00Z'),
                    },
                    {
                        id: 2,
                        stepName: AgentWorkflowVendorAssessmentStepName.SAFEBASE_ACCESS_GRANTED,
                        status: AgentWorkflowStepStatus.COMPLETED,
                        createdAt: new Date('2024-01-01T11:00:00Z'),
                    },
                    {
                        id: 3,
                        stepName:
                            AgentWorkflowVendorAssessmentStepName.SAFEBASE_SEND_DOCUMENTS_TO_AI,
                        status: AgentWorkflowStepStatus.IN_PROGRESS,
                        createdAt: new Date('2024-01-01T12:00:00Z'),
                    },
                    // This step should be filtered out as it's not in the audit trail mapping
                    {
                        id: 4,
                        stepName: AgentWorkflowVendorAssessmentStepName.SEND_DOCUMENTS_TO_AI,
                        status: AgentWorkflowStepStatus.COMPLETED,
                        createdAt: new Date('2024-01-01T13:00:00Z'),
                    },
                ],
            } as unknown as AgentWorkflow;

            const mockMapping = new Map([
                [
                    'PROCESS_START',
                    {
                        displayName: 'Started security review',
                        ref: AgentWorkflowStepMessageRef.SECURITY_REVIEW_ASSESSMENT,
                    },
                ],
                ['SAFEBASE_ACCESS_GRANTED', { displayName: 'Access Granted' }],
                [
                    'SAFEBASE_SEND_DOCUMENTS_TO_AI',
                    {
                        displayName: 'Documents collected',
                        ref: AgentWorkflowStepMessageRef.SECURITY_REVIEW_DOCUMENTS,
                    },
                ],
            ]);
            jest.spyOn(service['auditTrailFactory'], 'getAuditTrailMapping').mockReturnValue(
                mockMapping,
            );
            mockRepository.findOneOrFail.mockResolvedValue(mockWorkflow);

            // Act
            const result = await service.getWorkflowAuditTrail(mockAccount, workflowId);

            // Assert
            expect(mockRepository.findOneOrFail).toHaveBeenCalledWith({
                where: { id: workflowId },
                relations: ['steps'],
                order: {
                    steps: { createdAt: 'ASC' },
                },
            });
            expect(result).toHaveLength(3); // All steps from mapping should be returned

            // Check first step (PROCESS_START) - exists in workflow
            expect(result[0]).toEqual({
                id: 1,
                displayName: 'Started security review',
                status: AgentWorkflowStepStatus.COMPLETED,
                createdAt: new Date('2024-01-01T10:00:00Z'),
                ref: AgentWorkflowStepMessageRef.SECURITY_REVIEW_ASSESSMENT,
            });

            // Check second step (SAFEBASE_ACCESS_GRANTED) - exists in workflow
            expect(result[1]).toEqual({
                id: 2,
                displayName: 'Access Granted',
                status: AgentWorkflowStepStatus.COMPLETED,
                createdAt: new Date('2024-01-01T11:00:00Z'),
                ref: undefined,
            });

            // Check third step (SAFEBASE_SEND_DOCUMENTS_TO_AI) - exists in workflow
            expect(result[2]).toEqual({
                id: 3,
                displayName: 'Documents collected',
                status: AgentWorkflowStepStatus.IN_PROGRESS,
                createdAt: new Date('2024-01-01T12:00:00Z'),
                ref: AgentWorkflowStepMessageRef.SECURITY_REVIEW_DOCUMENTS,
            });
        });

        it('should return empty array when audit trail mapping is empty', async () => {
            // Arrange
            const workflowId = 1;
            const mockWorkflow = {
                id: workflowId,
                agentType: AgentType.VENDOR_RISK_MANAGEMENT,
                agentWorkflowType: AgentWorkflowVendorType.VENDOR_ASSESSMENT,
                steps: [
                    {
                        id: 1,
                        stepName: AgentWorkflowVendorAssessmentStepName.SEND_DOCUMENTS_TO_AI,
                        status: AgentWorkflowStepStatus.COMPLETED,
                        createdAt: new Date('2024-01-01T10:00:00Z'),
                    },
                ],
            } as unknown as AgentWorkflow;

            const mockMapping = new Map(); // Empty mapping - no audit trail steps
            jest.spyOn(service['auditTrailFactory'], 'getAuditTrailMapping').mockReturnValue(
                mockMapping,
            );
            mockRepository.findOneOrFail.mockResolvedValue(mockWorkflow);

            // Act
            const result = await service.getWorkflowAuditTrail(mockAccount, workflowId);

            // Assert
            expect(result).toHaveLength(0);
        });

        it('should map different step statuses correctly', async () => {
            // Arrange
            const workflowId = 1;
            const mockWorkflow = {
                id: workflowId,
                agentType: AgentType.VENDOR_RISK_MANAGEMENT,
                agentWorkflowType: AgentWorkflowVendorType.VENDOR_ASSESSMENT,
                steps: [
                    {
                        id: 1,
                        stepName: AgentWorkflowGeneralStepName.PROCESS_START,
                        status: AgentWorkflowStepStatus.PENDING,
                        createdAt: new Date('2024-01-01T10:00:00Z'),
                    },
                    {
                        id: 2,
                        stepName: AgentWorkflowVendorAssessmentStepName.SAFEBASE_ACCESS_GRANTED,
                        status: AgentWorkflowStepStatus.FAILED,
                        createdAt: new Date('2024-01-01T11:00:00Z'),
                    },
                ],
            } as unknown as AgentWorkflow;

            const mockMapping = new Map([
                ['PROCESS_START', { displayName: 'Started security review' }],
                ['SAFEBASE_ACCESS_GRANTED', { displayName: 'Access Granted' }],
            ]);
            jest.spyOn(service['auditTrailFactory'], 'getAuditTrailMapping').mockReturnValue(
                mockMapping,
            );
            mockRepository.findOneOrFail.mockResolvedValue(mockWorkflow);

            // Act
            const result = await service.getWorkflowAuditTrail(mockAccount, workflowId);

            // Assert
            expect(result).toHaveLength(2);
            expect(result[0].status).toBe(AgentWorkflowStepStatus.PENDING); // Status preserved
            expect(result[1].status).toBe(AgentWorkflowStepStatus.FAILED); // Status preserved
        });

        it('should include steps from mapping even if they do not exist in workflow yet', async () => {
            // Arrange
            const workflowId = 1;
            const mockWorkflow = {
                id: workflowId,
                agentType: AgentType.VENDOR_RISK_MANAGEMENT,
                agentWorkflowType: AgentWorkflowVendorType.VENDOR_ASSESSMENT,
                steps: [
                    {
                        id: 1,
                        stepName: AgentWorkflowGeneralStepName.PROCESS_START,
                        status: AgentWorkflowStepStatus.COMPLETED,
                        createdAt: new Date('2024-01-01T10:00:00Z'),
                    },
                    // SAFEBASE_ACCESS_GRANTED step does not exist yet
                    // SAFEBASE_SEND_DOCUMENTS_TO_AI step does not exist yet
                ],
            } as unknown as AgentWorkflow;

            const mockMapping = new Map([
                ['PROCESS_START', { displayName: 'Started security review' }],
                ['SAFEBASE_ACCESS_GRANTED', { displayName: 'Access Granted' }],
                ['SAFEBASE_SEND_DOCUMENTS_TO_AI', { displayName: 'Documents collected' }],
            ]);
            jest.spyOn(service['auditTrailFactory'], 'getAuditTrailMapping').mockReturnValue(
                mockMapping,
            );
            mockRepository.findOneOrFail.mockResolvedValue(mockWorkflow);

            // Act
            const result = await service.getWorkflowAuditTrail(mockAccount, workflowId);

            // Assert
            expect(result).toHaveLength(3); // All steps from mapping should be returned

            // First step exists in workflow
            expect(result[0]).toEqual({
                id: 1,
                displayName: 'Started security review',
                status: AgentWorkflowStepStatus.COMPLETED,
                createdAt: new Date('2024-01-01T10:00:00Z'),
                ref: undefined,
            });

            // Second step does not exist yet - should have undefined id/createdAt and PENDING status
            expect(result[1]).toEqual({
                id: undefined,
                displayName: 'Access Granted',
                status: AgentWorkflowStepStatus.PENDING,
                createdAt: undefined,
                ref: undefined,
            });

            // Third step does not exist yet - should have undefined id/createdAt and PENDING status
            expect(result[2]).toEqual({
                id: undefined,
                displayName: 'Documents collected',
                status: AgentWorkflowStepStatus.PENDING,
                createdAt: undefined,
                ref: undefined,
            });
        });

        it('should return steps in the order defined by the mapping', async () => {
            // Arrange
            const workflowId = 1;
            const mockWorkflow = {
                id: workflowId,
                agentType: AgentType.VENDOR_RISK_MANAGEMENT,
                agentWorkflowType: AgentWorkflowVendorType.VENDOR_ASSESSMENT,
                steps: [
                    {
                        id: 2,
                        stepName: AgentWorkflowVendorAssessmentStepName.SAFEBASE_ACCESS_GRANTED,
                        status: AgentWorkflowStepStatus.COMPLETED,
                        createdAt: new Date('2024-01-01T12:00:00Z'), // Created later
                    },
                    {
                        id: 1,
                        stepName: AgentWorkflowGeneralStepName.PROCESS_START,
                        status: AgentWorkflowStepStatus.COMPLETED,
                        createdAt: new Date('2024-01-01T10:00:00Z'), // Created earlier
                    },
                ],
            } as unknown as AgentWorkflow;

            // Mapping defines the order: PROCESS_START first, then SAFEBASE_ACCESS_GRANTED
            const mockMapping = new Map([
                ['PROCESS_START', { displayName: 'Started security review' }],
                ['SAFEBASE_ACCESS_GRANTED', { displayName: 'Access Granted' }],
            ]);
            jest.spyOn(service['auditTrailFactory'], 'getAuditTrailMapping').mockReturnValue(
                mockMapping,
            );
            mockRepository.findOneOrFail.mockResolvedValue(mockWorkflow);

            // Act
            const result = await service.getWorkflowAuditTrail(mockAccount, workflowId);

            // Assert
            expect(result).toHaveLength(2);
            // Order should follow mapping, not creation date
            expect(result[0].id).toBe(1); // PROCESS_START (first in mapping)
            expect(result[1].id).toBe(2); // SAFEBASE_ACCESS_GRANTED (second in mapping)
        });

        it('should log workflow details and step information', async () => {
            // Arrange
            const workflowId = 1;
            const mockWorkflow = {
                id: workflowId,
                agentType: AgentType.VENDOR_RISK_MANAGEMENT,
                agentWorkflowType: AgentWorkflowVendorType.VENDOR_ASSESSMENT,
                steps: [
                    {
                        id: 1,
                        stepName: AgentWorkflowGeneralStepName.PROCESS_START,
                        status: AgentWorkflowStepStatus.COMPLETED,
                        createdAt: new Date('2024-01-01T10:00:00Z'),
                    },
                ],
            } as unknown as AgentWorkflow;

            const mockMapping = new Map([
                ['PROCESS_START', { displayName: 'Started security review' }],
            ]);
            jest.spyOn(service['auditTrailFactory'], 'getAuditTrailMapping').mockReturnValue(
                mockMapping,
            );
            mockRepository.findOneOrFail.mockResolvedValue(mockWorkflow);

            const logSpy = jest.spyOn(service as any, 'log');

            // Act
            await service.getWorkflowAuditTrail(mockAccount, workflowId);

            // Assert
            expect(logSpy).toHaveBeenCalledWith(
                `Getting audit trail for workflow ${workflowId}`,
                mockAccount,
            );
            expect(logSpy).toHaveBeenCalledWith(
                `Retrieved 1 audit trail steps for workflow ${workflowId}`,
                mockAccount,
                {
                    workflowId,
                    stepCount: 1,
                    agentType: AgentType.VENDOR_RISK_MANAGEMENT,
                    workflowType: AgentWorkflowVendorType.VENDOR_ASSESSMENT,
                },
            );
        });

        it('should preserve step statuses as-is from workflow steps', async () => {
            // Arrange
            const workflowId = 1;
            const mockWorkflow = {
                id: workflowId,
                agentType: AgentType.VENDOR_RISK_MANAGEMENT,
                agentWorkflowType: AgentWorkflowVendorType.VENDOR_ASSESSMENT,
                steps: [
                    {
                        id: 1,
                        stepName: AgentWorkflowGeneralStepName.PROCESS_START,
                        status: AgentWorkflowStepStatus.PENDING,
                        createdAt: new Date('2024-01-01T10:00:00Z'),
                    },
                    {
                        id: 2,
                        stepName: AgentWorkflowVendorAssessmentStepName.SAFEBASE_ACCESS_GRANTED,
                        status: AgentWorkflowStepStatus.FAILED,
                        createdAt: new Date('2024-01-01T11:00:00Z'),
                    },
                    {
                        id: 3,
                        stepName:
                            AgentWorkflowVendorAssessmentStepName.SAFEBASE_SEND_DOCUMENTS_TO_AI,
                        status: AgentWorkflowStepStatus.COMPLETED,
                        createdAt: new Date('2024-01-01T12:00:00Z'),
                    },
                ],
            } as unknown as AgentWorkflow;

            const mockMapping = new Map([
                ['PROCESS_START', { displayName: 'Started security review' }],
                ['SAFEBASE_ACCESS_GRANTED', { displayName: 'Access Granted' }],
                ['SAFEBASE_SEND_DOCUMENTS_TO_AI', { displayName: 'Documents collected' }],
            ]);
            jest.spyOn(service['auditTrailFactory'], 'getAuditTrailMapping').mockReturnValue(
                mockMapping,
            );
            mockRepository.findOneOrFail.mockResolvedValue(mockWorkflow);

            // Act
            const result = await service.getWorkflowAuditTrail(mockAccount, workflowId);

            // Assert
            expect(result).toHaveLength(3);
            expect(result[0].status).toBe(AgentWorkflowStepStatus.PENDING); // Status preserved
            expect(result[1].status).toBe(AgentWorkflowStepStatus.FAILED); // Status preserved
            expect(result[2].status).toBe(AgentWorkflowStepStatus.COMPLETED); // Status preserved
        });
    });
});

import { Injectable } from '@nestjs/common';
import { AgentWorkflowGeneralStepName } from 'app/agent-workflow/enums/agent-workflow-general-step-name.enum';
import { AgentWorkflowVendorCriteriaStepName } from 'app/agent-workflow/enums/agent-workflow-vendor-criteria-step-name.enum';
import { CreateCriteriaMissingQuestionnairesExecutor } from 'app/agent-workflow/step-executor/executors/vendor-risk-management/vendors-criteria/create-criteria-missing-questionnaires.executor';
import { CreatingCriteriaExecutor } from 'app/agent-workflow/step-executor/executors/vendor-risk-management/vendors-criteria/creating-criteria.executor';
import { CriteriaCreatedExecutor } from 'app/agent-workflow/step-executor/executors/vendor-risk-management/vendors-criteria/criteria-created.executor';
import { CriteriaQuestionsCreatedExecutor } from 'app/agent-workflow/step-executor/executors/vendor-risk-management/vendors-criteria/criteria-questions-created.executor';
import { CriteriaCreationProcessStartExecutor } from 'app/agent-workflow/step-executor/executors/vendor-risk-management/vendors-criteria/process-start.executor';
import { ProcessingQuestionnairesExecutor } from 'app/agent-workflow/step-executor/executors/vendor-risk-management/vendors-criteria/processing-questionnaires.executor';
import { ProcessingQuestionsExecutor } from 'app/agent-workflow/step-executor/executors/vendor-risk-management/vendors-criteria/processing-questions.executor';
import { RestoreDrataCriteriaExecutor } from 'app/agent-workflow/step-executor/executors/vendor-risk-management/vendors-criteria/restore-drata-criteria.executor';
import { ReviewAndModifyCriteriaExecutor } from 'app/agent-workflow/step-executor/executors/vendor-risk-management/vendors-criteria/review-and-modify-criteria.executor';
import { SaveGeneratedCriteriaExecutor } from 'app/agent-workflow/step-executor/executors/vendor-risk-management/vendors-criteria/save-generated-criteria.executor';
import { SendCreateCriteriaToAiExecutor } from 'app/agent-workflow/step-executor/executors/vendor-risk-management/vendors-criteria/send-create-criteria-to-ai.executor';
import { SendCriteriaQuestionnaireToAiExecutor } from 'app/agent-workflow/step-executor/executors/vendor-risk-management/vendors-criteria/send-criteria-questionnaire-to-ai.executor';
import { FinalizeStepExecutor } from 'app/agent-workflow/step-executor/shared/finalize.executor';
import { IStepExecutor } from 'app/agent-workflow/step-executor/types/step-executor.interface';

/**
 * Mapper for vendors criteria step executors.
 * Handles the mapping between step names and their corresponding executor instances.
 */
@Injectable()
export class VendorsCriteriaStepExecutorMapper {
    constructor(
        private readonly processStartExecutor: CriteriaCreationProcessStartExecutor,
        private readonly createCriteriaMissingQuestionnairesExecutor: CreateCriteriaMissingQuestionnairesExecutor,
        private readonly sendCriteriaQuestionnaireToAiExecutor: SendCriteriaQuestionnaireToAiExecutor,
        private readonly processingQuestionnairesExecutor: ProcessingQuestionnairesExecutor,
        private readonly processingQuestionsExecutor: ProcessingQuestionsExecutor,
        private readonly creatingCriteriaExecutor: CreatingCriteriaExecutor,
        private readonly criteriaCreatedExecutor: CriteriaCreatedExecutor,
        private readonly reviewAndModifyCriteriaExecutor: ReviewAndModifyCriteriaExecutor,
        private readonly saveGeneratedCriteriaExecutor: SaveGeneratedCriteriaExecutor,
        private readonly restoreDrataCriteriaExecutor: RestoreDrataCriteriaExecutor,
        private readonly sendCreateCriteriaToAiExecutor: SendCreateCriteriaToAiExecutor,
        private readonly criteriaQuestionsCreatedExecutor: CriteriaQuestionsCreatedExecutor,
        private readonly finalizeStepExecutor: FinalizeStepExecutor,
    ) {}

    /**
     * Gets the appropriate executor for a vendors criteria step.
     * @param stepName - The name of the step to get an executor for
     * @returns The executor instance for the given step
     * @throws Error if no executor is found for the step
     */
    getExecutor(stepName: string): IStepExecutor {
        switch (stepName) {
            case AgentWorkflowGeneralStepName.PROCESS_START:
                return this.processStartExecutor;
            case AgentWorkflowVendorCriteriaStepName.CREATE_CRITERIA_MISSING_QUESTIONNAIRES:
                return this.createCriteriaMissingQuestionnairesExecutor;
            case AgentWorkflowVendorCriteriaStepName.SEND_CRITERIA_QUESTIONNAIRE_TO_AI:
                return this.sendCriteriaQuestionnaireToAiExecutor;
            case AgentWorkflowVendorCriteriaStepName.PROCESSING_QUESTIONNAIRES:
                return this.processingQuestionnairesExecutor;
            case AgentWorkflowVendorCriteriaStepName.PROCESSING_QUESTIONS:
                return this.processingQuestionsExecutor;
            case AgentWorkflowVendorCriteriaStepName.CREATING_CRITERIA:
                return this.creatingCriteriaExecutor;
            case AgentWorkflowVendorCriteriaStepName.CRITERIA_CREATED:
                return this.criteriaCreatedExecutor;
            case AgentWorkflowVendorCriteriaStepName.REVIEW_AND_MODIFY_CRITERIA:
                return this.reviewAndModifyCriteriaExecutor;
            case AgentWorkflowVendorCriteriaStepName.SAVE_GENERATED_CRITERIA:
                return this.saveGeneratedCriteriaExecutor;
            case AgentWorkflowVendorCriteriaStepName.RESTORE_DRATA_CRITERIA:
                return this.restoreDrataCriteriaExecutor;
            case AgentWorkflowVendorCriteriaStepName.SEND_CREATE_CRITERIA_TO_AI:
                return this.sendCreateCriteriaToAiExecutor;
            case AgentWorkflowVendorCriteriaStepName.CRITERIA_QUESTIONS_CREATED:
                return this.criteriaQuestionsCreatedExecutor;
            case AgentWorkflowGeneralStepName.FINALIZE:
                return this.finalizeStepExecutor;
            default:
                throw new Error(
                    `No step executor found for Vendors Criteria step: ${String(stepName)}`,
                );
        }
    }
}

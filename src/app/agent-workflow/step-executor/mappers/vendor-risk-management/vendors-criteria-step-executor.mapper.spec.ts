import { Test, TestingModule } from '@nestjs/testing';
import { AgentWorkflowGeneralStepName } from 'app/agent-workflow/enums/agent-workflow-general-step-name.enum';
import { AgentWorkflowVendorCriteriaStepName } from 'app/agent-workflow/enums/agent-workflow-vendor-criteria-step-name.enum';
import { CreateCriteriaMissingQuestionnairesExecutor } from 'app/agent-workflow/step-executor/executors/vendor-risk-management/vendors-criteria/create-criteria-missing-questionnaires.executor';
import { CreatingCriteriaExecutor } from 'app/agent-workflow/step-executor/executors/vendor-risk-management/vendors-criteria/creating-criteria.executor';
import { CriteriaCreatedExecutor } from 'app/agent-workflow/step-executor/executors/vendor-risk-management/vendors-criteria/criteria-created.executor';
import { CriteriaQuestionsCreatedExecutor } from 'app/agent-workflow/step-executor/executors/vendor-risk-management/vendors-criteria/criteria-questions-created.executor';
import { CriteriaCreationProcessStartExecutor } from 'app/agent-workflow/step-executor/executors/vendor-risk-management/vendors-criteria/process-start.executor';
import { ProcessingQuestionnairesExecutor } from 'app/agent-workflow/step-executor/executors/vendor-risk-management/vendors-criteria/processing-questionnaires.executor';
import { ProcessingQuestionsExecutor } from 'app/agent-workflow/step-executor/executors/vendor-risk-management/vendors-criteria/processing-questions.executor';
import { RestoreDrataCriteriaExecutor } from 'app/agent-workflow/step-executor/executors/vendor-risk-management/vendors-criteria/restore-drata-criteria.executor';
import { ReviewAndModifyCriteriaExecutor } from 'app/agent-workflow/step-executor/executors/vendor-risk-management/vendors-criteria/review-and-modify-criteria.executor';
import { SaveGeneratedCriteriaExecutor } from 'app/agent-workflow/step-executor/executors/vendor-risk-management/vendors-criteria/save-generated-criteria.executor';
import { SendCreateCriteriaToAiExecutor } from 'app/agent-workflow/step-executor/executors/vendor-risk-management/vendors-criteria/send-create-criteria-to-ai.executor';
import { SendCriteriaQuestionnaireToAiExecutor } from 'app/agent-workflow/step-executor/executors/vendor-risk-management/vendors-criteria/send-criteria-questionnaire-to-ai.executor';
import { VendorsCriteriaStepExecutorMapper } from 'app/agent-workflow/step-executor/mappers/vendor-risk-management/vendors-criteria-step-executor.mapper';
import { FinalizeStepExecutor } from 'app/agent-workflow/step-executor/shared/finalize.executor';
import { MockProxy, mockDeep } from 'jest-mock-extended';

describe('VendorsCriteriaStepExecutorMapper', () => {
    let mapper: VendorsCriteriaStepExecutorMapper;
    let mockProcessStartExecutor: MockProxy<CriteriaCreationProcessStartExecutor>;
    let mockCreateCriteriaMissingQuestionnairesExecutor: MockProxy<CreateCriteriaMissingQuestionnairesExecutor>;
    let mockSendCriteriaQuestionnaireToAiExecutor: MockProxy<SendCriteriaQuestionnaireToAiExecutor>;
    let mockProcessingQuestionnairesExecutor: MockProxy<ProcessingQuestionnairesExecutor>;
    let mockProcessingQuestionsExecutor: MockProxy<ProcessingQuestionsExecutor>;
    let mockCreatingCriteriaExecutor: MockProxy<CreatingCriteriaExecutor>;
    let mockCriteriaCreatedExecutor: MockProxy<CriteriaCreatedExecutor>;
    let mockReviewAndModifyCriteriaExecutor: MockProxy<ReviewAndModifyCriteriaExecutor>;
    let mockSaveGeneratedCriteriaExecutor: MockProxy<SaveGeneratedCriteriaExecutor>;
    let mockRestoreDrataCriteriaExecutor: MockProxy<RestoreDrataCriteriaExecutor>;
    let mockSendCreateCriteriaToAiExecutor: MockProxy<SendCreateCriteriaToAiExecutor>;
    let mockCriteriaQuestionsCreatedExecutor: MockProxy<CriteriaQuestionsCreatedExecutor>;
    let mockFinalizeStepExecutor: MockProxy<FinalizeStepExecutor>;

    beforeEach(async () => {
        mockProcessStartExecutor = mockDeep<CriteriaCreationProcessStartExecutor>();
        mockCreateCriteriaMissingQuestionnairesExecutor =
            mockDeep<CreateCriteriaMissingQuestionnairesExecutor>();
        mockSendCriteriaQuestionnaireToAiExecutor =
            mockDeep<SendCriteriaQuestionnaireToAiExecutor>();
        mockProcessingQuestionnairesExecutor = mockDeep<ProcessingQuestionnairesExecutor>();
        mockProcessingQuestionsExecutor = mockDeep<ProcessingQuestionsExecutor>();
        mockCreatingCriteriaExecutor = mockDeep<CreatingCriteriaExecutor>();
        mockCriteriaCreatedExecutor = mockDeep<CriteriaCreatedExecutor>();
        mockReviewAndModifyCriteriaExecutor = mockDeep<ReviewAndModifyCriteriaExecutor>();
        mockSaveGeneratedCriteriaExecutor = mockDeep<SaveGeneratedCriteriaExecutor>();
        mockRestoreDrataCriteriaExecutor = mockDeep<RestoreDrataCriteriaExecutor>();
        mockSendCreateCriteriaToAiExecutor = mockDeep<SendCreateCriteriaToAiExecutor>();
        mockCriteriaQuestionsCreatedExecutor = mockDeep<CriteriaQuestionsCreatedExecutor>();
        mockFinalizeStepExecutor = mockDeep<FinalizeStepExecutor>();

        const module: TestingModule = await Test.createTestingModule({
            providers: [
                VendorsCriteriaStepExecutorMapper,
                {
                    provide: CriteriaCreationProcessStartExecutor,
                    useValue: mockProcessStartExecutor,
                },
                {
                    provide: CreateCriteriaMissingQuestionnairesExecutor,
                    useValue: mockCreateCriteriaMissingQuestionnairesExecutor,
                },
                {
                    provide: SendCriteriaQuestionnaireToAiExecutor,
                    useValue: mockSendCriteriaQuestionnaireToAiExecutor,
                },
                {
                    provide: ProcessingQuestionnairesExecutor,
                    useValue: mockProcessingQuestionnairesExecutor,
                },
                {
                    provide: ProcessingQuestionsExecutor,
                    useValue: mockProcessingQuestionsExecutor,
                },
                {
                    provide: CreatingCriteriaExecutor,
                    useValue: mockCreatingCriteriaExecutor,
                },
                {
                    provide: CriteriaCreatedExecutor,
                    useValue: mockCriteriaCreatedExecutor,
                },
                {
                    provide: ReviewAndModifyCriteriaExecutor,
                    useValue: mockReviewAndModifyCriteriaExecutor,
                },
                {
                    provide: SaveGeneratedCriteriaExecutor,
                    useValue: mockSaveGeneratedCriteriaExecutor,
                },
                {
                    provide: RestoreDrataCriteriaExecutor,
                    useValue: mockRestoreDrataCriteriaExecutor,
                },
                {
                    provide: SendCreateCriteriaToAiExecutor,
                    useValue: mockSendCreateCriteriaToAiExecutor,
                },
                {
                    provide: CriteriaQuestionsCreatedExecutor,
                    useValue: mockCriteriaQuestionsCreatedExecutor,
                },
                { provide: FinalizeStepExecutor, useValue: mockFinalizeStepExecutor },
            ],
        }).compile();

        mapper = module.get(VendorsCriteriaStepExecutorMapper);
    });

    describe('getExecutor', () => {
        it('should return CriteriaCreationProcessStartExecutor for PROCESS_START step', () => {
            const result = mapper.getExecutor(AgentWorkflowGeneralStepName.PROCESS_START);
            expect(result).toBe(mockProcessStartExecutor);
        });

        it('should return SendCriteriaToAiExecutor for SEND_CRITERIA_QUESTIONNAIRE_TO_AI step', () => {
            const result = mapper.getExecutor(
                AgentWorkflowVendorCriteriaStepName.SEND_CRITERIA_QUESTIONNAIRE_TO_AI,
            );
            expect(result).toBe(mockSendCriteriaQuestionnaireToAiExecutor);
        });

        it('should return FinalizeStepExecutor for FINALIZE step', () => {
            const result = mapper.getExecutor(AgentWorkflowGeneralStepName.FINALIZE);
            expect(result).toBe(mockFinalizeStepExecutor);
        });

        it('should throw error for unknown step', () => {
            expect(() => mapper.getExecutor('UNKNOWN_STEP')).toThrow(
                'No step executor found for Vendors Criteria step: UNKNOWN_STEP',
            );
        });
    });
});

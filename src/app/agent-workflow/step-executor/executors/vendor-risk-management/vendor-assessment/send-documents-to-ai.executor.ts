/* eslint-disable max-len */
import { Injectable } from '@nestjs/common';
import { EventBus } from '@nestjs/cqrs';
import { AgentWorkflowStep } from 'app/agent-workflow/entities/agent-workflow-step.entity';
import { AgentWorkflow } from 'app/agent-workflow/entities/agent-workflow.entity';
import {
    AgentWorkflowStepName,
    AgentWorkflowStepStatus,
    AgentWorkflowVendorAssessmentStepName,
    AgentWorkflowVendorType,
} from 'app/agent-workflow/enums';
import { AgentWorkflowStepUpdatedEvent } from 'app/agent-workflow/observables/events/agent-workflow-step-updated.event';
import { VendorAssessmentStepExecutorBase } from 'app/agent-workflow/step-executor/base/vendor-assessment-step-executor-base';
import { AiExecutionGroupRequestDto } from 'app/ai/dtos/ai-execution-group-request.dto';
import { VendorSecurityReviewAssessmentStrategy } from 'app/ai/strategies/vendor-security-review-assessment.strategy';
import { VendorAssessmentCoreService } from 'app/users/vendors/services/vendor-assessment-core.service';
import { VendorsSecurityRiskCoreService } from 'app/users/vendors/services/vendors-security-risk-core.service';
import { Account } from 'auth/entities/account.entity';
import { ProcessFeature } from 'commons/enums/process-feature.enum';
import { ProcessType } from 'commons/enums/process-type.enum';
import { chunk } from 'lodash';
import { TenancyContext } from 'tenancy/contexts/tenancy.context';

@Injectable()
export class SendDocumentsToAiExecutor extends VendorAssessmentStepExecutorBase {
    constructor(
        tenant: TenancyContext,
        vendorsSecurityRiskCoreService: VendorsSecurityRiskCoreService,
        vendorAssessmentCoreService: VendorAssessmentCoreService,
        private readonly vendorSecurityReviewAssessmentStrategy: VendorSecurityReviewAssessmentStrategy,
        private readonly eventBus: EventBus,
    ) {
        super(tenant, vendorsSecurityRiskCoreService, vendorAssessmentCoreService);
    }
    async canExecute(agentWorkflow: AgentWorkflow): Promise<boolean> {
        // TODO: add validation based on workflow status/metadata
        if (agentWorkflow.agentWorkflowType !== AgentWorkflowVendorType.VENDOR_ASSESSMENT) {
            return false;
        }
        return true;
    }

    async execute(
        account: Account,
        workflow: AgentWorkflow,
        step: AgentWorkflowStep,
    ): Promise<void> {
        this.logAccountAndWorkflow(account, workflow, 'execute');
        await this.hideMessages(
            workflow,
            AgentWorkflowVendorAssessmentStepName.SECURITY_REVIEW_MISSING_DOCUMENTS,
        );
        await this.hideMessages(
            workflow,
            AgentWorkflowVendorAssessmentStepName.SECURITY_REVIEW_DOCUMENTS_COLLECTED,
        );

        try {
            // Retrieve the security review data using the base class method
            const securityReview = await this.getSecurityReview(workflow);

            // TODO: Get chunk size from config
            const chunkSize = 100;

            // TODO: Get criteria from database
            const criteriaWithQuestions = await this.getCriteriaWithQuestions();
            // Split criteria into chunks for processing
            const criteriaChunks = chunk(criteriaWithQuestions, chunkSize);

            this.logAccountAndWorkflow(account, workflow, 'execute');
            this.logger.log(
                `Processing ${criteriaWithQuestions.length} criteria in ${criteriaChunks.length} chunks of size ${chunkSize}`,
            );

            const documents = await this.getDocumentsForSecurityReview(securityReview.id);
            const documentIndexId = await this.getDocumentIndexId(securityReview.id);

            // Process each chunk sequentially
            for (let chunkIndex = 0; chunkIndex < criteriaChunks.length; chunkIndex++) {
                const criteriaChunk = criteriaChunks[chunkIndex];

                this.logger.log(
                    `Processing chunk ${chunkIndex + 1}/${criteriaChunks.length} with ${criteriaChunk.length} criteria`,
                );

                // Create the AI execution request with security review data for this chunk
                const request = new AiExecutionGroupRequestDto();
                request.processType = ProcessType.AGENT;
                request.processFeature = ProcessFeature.VENDOR_SECURITY_REVIEW_ASSESSMENT;
                request.featureId = workflow.workflowTypeId;
                request.data = JSON.stringify({
                    workflowId: workflow.id,
                    stepId: step.id,
                    productId: '123e4567-e89b-12d3-a456-************',
                    productName: 'Acme Payroll',
                    tenantId: account.id,
                    tenantName: account.companyName,
                    vendorAssessmentData: {
                        criteriaWithQuestions: criteriaChunk,
                        documentIndexId,
                        documents,
                        vendorId: securityReview.vendor.id,
                        vendorName: securityReview.vendor.name,
                        securityReviewId: securityReview.id,
                    },
                });

                // Execute the AI strategy for this chunk
                // eslint-disable-next-line no-await-in-loop
                await this.vendorSecurityReviewAssessmentStrategy.execute(account, request, null);
            }

            // Store result data with the required information
            const resultData = {
                numberOfCriteria: criteriaWithQuestions.length,
                numberOfChunks: criteriaChunks.length,
                chunkSize,
                numberOfDocuments: 2, // Based on the hardcoded documents above
                documentIndex: 'idx_123e4567-e89b-12d3-a456-************',
            };
            step.resultData = JSON.stringify({ request: resultData });
            step.progress = 0;
            await this.saveStep(step);

            // Emit step updated event
            const event = new AgentWorkflowStepUpdatedEvent(account, workflow, step);
            await this.eventBus.publish(event);
        } catch (error) {
            // Handle errors and mark step as failed if needed
            step.status = AgentWorkflowStepStatus.FAILED;
            await this.saveStep(step);
            const event = new AgentWorkflowStepUpdatedEvent(account, workflow, step);
            await this.eventBus.publish(event);
            throw error;
        }
    }

    async nextStep(
        account: Account,
        agentWorkflow: AgentWorkflow,
    ): Promise<AgentWorkflowStepName | null> {
        this.logAccountAndWorkflow(account, agentWorkflow, 'nextStep');

        // After we send the documents we go into a wait state step for the AI to evaluate the security posture
        return null;
    }

    private async getDocumentsForSecurityReview(securityReviewId: number): Promise<any[]> {
        // TODO: Get documents from database
        this.logger.log(`Getting documents for security review ${securityReviewId}`);
        return [
            { documentId: 'ffd5800f-ea27-4b2d-82bb-fa47409f52b0--DRATAVERSEDEMO--41' },
            { documentId: 'cab9c29c-cd92-4dbe-99ad-8f62b46e2d80--DRATAVERSEDEMO--40' },
            { documentId: 'afbee651-af6e-4758-9125-7a084b712223--DRATAVERSEDEMO--37' },
            { documentId: 'd7e7b445-71b2-44ae-a152-6293c49e799b--DRATAVERSEDEMO--39' },
            { documentId: '4bcc5695-ceea-4a1e-8832-************--DRATAVERSEDEMO--38' },
            { documentId: 'aba0f4a0-4967-41d1-b588-93e9ee4da168--DRATAVERSEDEMO--36' },
            { documentId: 'ef877c61-0cab-4d0e-854d-e36d08b49db5--DRATAVERSEDEMO--35' },
            { documentId: '938beaf5-4e39-46cb-8f4b-773e7d85c83f--DRATAVERSEDEMO--34' },
            { documentId: '3b54acf8-dddb-4220-b73f-5a52fd4ab328--DRATAVERSEDEMO--33' },
            { documentId: '2b74d3a9-8632-441d-a23c-a82213317905--DRATAVERSEDEMO--32' },
            { documentId: '34daf8a2-c040-4d4d-9a60-f038b8d79b35--DRATAVERSEDEMO--31' },
            { documentId: '601dcd35-1dd5-439e-842c-ddc70969ece5--DRATAVERSEDEMO--30' },
            { documentId: '8ed64406-ce47-42f9-a4da-a469749cb9f7--DRATAVERSEDEMO--29' },
            { documentId: 'e9df64a9-71e0-4f40-a776-4ba7e1173e16--DRATAVERSEDEMO--28' },
            { documentId: '3f880aad-658a-43e1-a27e-0f858b5d8d75--DRATAVERSEDEMO--27' },
            { documentId: '4c1057c3-8c8d-4516-b09d-91bc6fc2ec92--DRATAVERSEDEMO--25' },
            { documentId: 'f2ba71ec-0107-419b-a6bc-48cc4cd838e2--DRATAVERSEDEMO--26' },
            { documentId: '8841b879-df37-4a5b-b50a-800bec2f1adf--DRATAVERSEDEMO--24' },
            { documentId: 'ea5df636-2654-424a-a0c1-ab36202eaab9--DRATAVERSEDEMO--23' },
            { documentId: '04b218fa-a3b5-4ca9-b03b-3106aa21c574--DRATAVERSEDEMO--22' },
            { documentId: '771e074f-95f9-44e9-97b9-9157460fa849--DRATAVERSEDEMO--21' },
            { documentId: 'f881a4e4-5f1d-4bf4-bca4-6d0ed3e94f05--DRATAVERSEDEMO--20' },
            { documentId: '92d71bf8-e78a-4645-b65a-3c68898c81ff--DRATAVERSEDEMO--19' },
            { documentId: '6de1dacb-4063-4ef1-bbba-d527f2336a70--DRATAVERSEDEMO--18' },
            { documentId: 'f416debc-0462-45d4-a4c9-21e60d206ae9--DRATAVERSEDEMO--15' },
            { documentId: '90a2ad5f-4773-4879-8a9e-9241a793f6db--DRATAVERSEDEMO--17' },
            { documentId: '52ca068c-04c7-4936-8b9c-3d06a4ef6af8--DRATAVERSEDEMO--16' },
            { documentId: 'a1d78740-2eff-4a94-8658-6b6b682636af--DRATAVERSEDEMO--13' },
            { documentId: '7ea6b234-c69e-4f0b-9b08-76b6cba41755--DRATAVERSEDEMO--14' },
            { documentId: 'd722845c-8bed-43cb-b592-bac8c8c96b2b--DRATAVERSEDEMO--12' },
            { documentId: '3f5ba36b-35f7-44b9-93cf-d08be50660ef--DRATAVERSEDEMO--11' },
            { documentId: '06e91967-bd7f-4103-83d4-4a3f888e8e61--DRATAVERSEDEMO--10' },
            { documentId: '3145c4fa-8e41-4491-84b8-d48231460e45--DRATAVERSEDEMO--8' },
            { documentId: 'fd8284bc-88f5-4bf4-914e-f97cc25eb04d--DRATAVERSEDEMO--9' },
            { documentId: '42a30b63-0779-4bb8-bc02-2e665667ae31--DRATAVERSEDEMO--7' },
            { documentId: '15981113-7a01-42ac-9b92-bf05e2b51297--DRATAVERSEDEMO--5' },
            { documentId: '75392ced-db2a-45e5-bae6-c76d3ab00a4f--DRATAVERSEDEMO--4' },
            { documentId: 'fc6ae8f6-3ac7-4cb2-859d-798eb23bb163--DRATAVERSEDEMO--1' },
            { documentId: '82a9468c-ecf0-4182-b321-063892aeeb78--DRATAVERSEDEMO--6' },
            { documentId: 'b098b24a-2194-47ea-9f65-00c1ed52f1d8--DRATAVERSEDEMO--3' },
            { documentId: 'b78f16ac-5e3e-4bab-b9cf-4c06928d36a8--DRATAVERSEDEMO--2' },
            { documentId: '5113865c-b4ed-45d7-b70f-0ebd1ac1e50d--DRATAVERSEDEMO--0' },
        ];
    }

    private async getDocumentIndexId(securityReviewId: number): Promise<string> {
        // TODO: Get document index from database
        this.logger.log(`Getting documents for security review ${securityReviewId}`);
        return 'vrm-drataverse-demo';
    }

    private async getCriteriaWithQuestions(): Promise<any[]> {
        return [
            {
                criterionId: '9d6e40b83479475581796ccc51c935ad',
                name: 'Security Contact Information',
                description:
                    'To verify that the vendor has established proper security contact channels and dedicated security personnel for handling security-related communications and incidents.',
                questions: [
                    'Does the vendor provide valid contact information for their security team or security personnel?',
                    'Has the vendor designated specific security personnel or a security team available for security-related matters?',
                    'Does the vendor provide a primary point of contact (name and/or email) for security communications?',
                ],
            },
            {
                criterionId: 'ca72d8263796419e908d7fb16cace200',
                name: 'Infrastructure and Data Processing Transparency',
                description:
                    "To assess the vendor's transparency regarding their technology infrastructure, data storage locations, third-party service providers, data processing activities, and subprocessor relationships that may impact data security and compliance obligations.",
                questions: [
                    'Does the vendor identify and disclose the cloud service providers (CSPs) used to deliver their services?',
                    'Does the vendor specify the geographic locations and regions where customer data is stored and processed?',
                    'Does the vendor identify content delivery network (CDN) providers used in their service delivery?',
                    'Does the vendor disclose whether they collect, access, process, or retain customer data that includes personal information (PI) or personal data (PD)?',
                    'Does the vendor identify Domain Name System (DNS) service providers used in their infrastructure?',
                    'Does the vendor maintain and provide access to a current list of subprocessors involved in data processing activities?',
                ],
            },
            {
                criterionId: 'e8f54fbd2f26491b97f60933707d7770',
                name: 'Compliance Documentation',
                description:
                    'To verify that the vendor maintains compliance with relevant regulations and standards and can provide documented evidence of their compliance status through formal reports or certifications.',
                questions: [
                    'What specific compliance frameworks and regulations does the organization adhere to?',
                    'Does the organization provide documented compliance reports or certifications (e.g., SOC 2, ISO 27001, HIPAA) as evidence of compliance status?',
                ],
            },
            {
                criterionId: '7de76639f9544ee08fbe3e3d262f2251',
                name: 'Breach History and Notification',
                description:
                    'To assess whether the vendor has maintained a clean security record over the past 36 months and has established proper contractual obligations and procedures for breach notification and record-keeping when incidents do occur.',
                questions: [
                    'Has the vendor experienced any data breaches or security incidents in the last 36 months?',
                    'Does the vendor have documented contractual obligations and procedures for notifying clients of data breaches within a specified timeframe?',
                    'Does the vendor maintain record-keeping obligations for all security breaches and incidents?',
                ],
            },
            {
                criterionId: '56daef9b0eab4ff1b5dc1f8ffc6043e0',
                name: 'Recent Penetration Testing',
                description:
                    'To verify that the vendor conducts regular penetration testing and can provide evidence of recent testing activities through both timing information and documentation.',
                questions: [
                    'Has a penetration test been completed within the last 12 months?',
                    'Can documentation (such as an executive summary or report) from the most recent penetration test be provided?',
                ],
            },
            {
                criterionId: 'b2a507827136417c8f729e00b49d114c',
                name: 'Corporate Device Endpoint Protection',
                description:
                    'Verify that the vendor has implemented comprehensive endpoint protection through antivirus/EDR solutions deployed across all corporate devices, with clear identification of the specific security solution used.',
                questions: [
                    'Does the vendor install antivirus or endpoint detection and response (EDR) software on all corporate devices?',
                    'Can the vendor identify the specific antivirus/endpoint detection and response (EDR) solution deployed on corporate devices?',
                ],
            },
            {
                criterionId: 'e6fdd5e21e4c489cbdf9506423a456a1',
                name: 'MDM Solution Implementation',
                description:
                    'To verify that the vendor has implemented a Mobile Device Management (MDM) solution to manage and secure corporate devices within their organization.',
                questions: [
                    'What specific Mobile Device Management (MDM) solution has been deployed to manage corporate devices?',
                ],
            },
            {
                criterionId: 'df39b37abd6c49aa91261f9856a6bd9f',
                name: 'SSO/LDAP Authentication Enforcement',
                description:
                    'To verify that the vendor enforces centralized authentication mechanisms (SSO or LDAP) for accessing applications and systems, and can identify the specific authentication provider used.',
                questions: [
                    'Does the company enforce Single Sign-On (SSO) or Lightweight Directory Access Protocol (LDAP) authentication for access to applications or systems?',
                    'Can the company identify the specific Single Sign-On (SSO) or Lightweight Directory Access Protocol (LDAP) provider used for authentication?',
                ],
            },
            {
                criterionId: 'e8ef0ef1d9304959906bd2d7a575672c',
                name: 'Datacenter and Cloud Security Framework',
                description:
                    'To assess whether the vendor has implemented a comprehensive security framework covering the fundamental security domains necessary to protect data and systems in datacenter and cloud environments, including logical, physical, environmental, software, privacy, and regulatory compliance controls.',
                questions: [
                    'Does the vendor have documented security controls and practices for protecting data and systems in their datacenter/cloud environments across multiple security domains (logical, physical, environmental, software, privacy, and regulatory compliance)?',
                    "Does the vendor's security approach include specific measures for data protection throughout its lifecycle (entry, transfer, storage, and access)?",
                ],
            },
            {
                criterionId: '61b4a635c5604472a41da0348bef5b57',
                name: 'SDLC Security Implementation',
                description:
                    'To verify that the vendor has implemented a structured software development lifecycle (SDLC) process that incorporates security controls throughout development, including pre-release security testing and vulnerability management, and post-release vulnerability identification and remediation processes.',
                questions: [
                    'Does the organization have a documented formal software development lifecycle (SDLC) process that integrates security considerations throughout all development phases?',
                    'Does the organization implement pre-release security controls including vulnerability testing and code review processes to prevent vulnerable code from reaching production environments?',
                    'Has the organization established post-release vulnerability management processes that include identification, assessment, prioritization, and remediation of security vulnerabilities with defined timelines?',
                ],
            },
            {
                criterionId: '0f306ac1a79f49958c81a459d6528204',
                name: 'WAF/RASP Application Protection',
                description:
                    'To verify that the vendor has implemented Web Application Firewall (WAF) or Runtime Application Self-Protection (RASP) technology deployed inline with their application to provide real-time protection against web-based attacks and application-layer threats.',
                questions: [
                    'Has the organization deployed either Web Application Firewall (WAF) or Runtime Application Self-Protection (RASP) technology inline with the application infrastructure?',
                ],
            },
            {
                criterionId: '85d993e931a34c61842d5db92c0babc4',
                name: 'SAST/DAST Code Testing',
                description:
                    'To verify that the vendor implements both Static Application Security Testing (SAST) and Dynamic Application Security Testing (DAST) for all code releases to identify security vulnerabilities before deployment.',
                questions: [
                    'Does the company perform Static Application Security Testing (SAST) on all code before release?',
                    'Does the company perform Dynamic Application Security Testing (DAST) on all code before release?',
                ],
            },
            {
                criterionId: 'f7b4b74d6bdc4f348508eea68a7cbe24',
                name: 'Public Security and Privacy Documentation',
                description:
                    'To verify that the vendor maintains publicly accessible documentation demonstrating their commitment to security and privacy through dedicated web pages or policy documents.',
                questions: [
                    'Does the vendor provide a publicly accessible URL to a security-focused web page or document that demonstrates their security practices and commitments?',
                    'Does the vendor provide a publicly accessible URL to their privacy policy document?',
                ],
            },
            {
                criterionId: '933b2be3d33f493bbbef30bb6fe17717',
                name: 'Information Security Policy Documentation',
                description:
                    'To verify that the vendor has established and documented a formal information security policy that governs their security practices and demonstrates their commitment to information security management.',
                questions: [
                    'Does a formal information security policy document exist and has it been made available for review?',
                ],
            },
            {
                criterionId: '6fd0764a98fb42ad85ecc439e8b3bcbd',
                name: 'Password Policy Documentation',
                description:
                    'To verify that the vendor has established and documented a formal password policy that governs password requirements and security practices within their organization.',
                questions: [
                    'Does a documented password policy exist and is it available for review?',
                ],
            },
            {
                criterionId: '82467037010c4023a81b6c8e85666b9b',
                name: 'Compliance Policy Framework',
                description:
                    'To verify that the vendor has established formal policies and procedures that ensure compliance with applicable legislative, regulatory, and contractual requirements, including intellectual property rights related to business processes and IT software products.',
                questions: [
                    'Does the organization have formal policies and procedures that address compliance with legislative, regulatory, and contractual requirements?',
                    'Do the compliance policies and procedures specifically cover intellectual property rights for business processes and IT software products?',
                    'Are the compliance policies documented and accessible to relevant personnel?',
                ],
            },
            {
                criterionId: 'd7d09e476cac40e2b2342de36cf44268',
                name: 'BCDR Plan Documentation',
                description:
                    'To verify that the vendor has established and documented a Business Continuity and Disaster Recovery (BCDR) plan that outlines procedures for maintaining operations and recovering from disruptive events.',
                questions: [
                    'Does a documented Business Continuity and Disaster Recovery (BCDR) plan exist and is it available for review?',
                ],
            },
            {
                criterionId: '247fa93905944379be152bbb382f4558',
                name: 'Legal Compliance Reporting',
                description:
                    'To verify that the vendor maintains appropriate internal management reporting and external reporting to government agencies in accordance with applicable laws and regulations.',
                questions: [
                    'Does the vendor maintain internal management reporting processes that comply with applicable legal requirements?',
                    'Does the vendor maintain external reporting processes to government agencies that comply with applicable legal requirements?',
                ],
            },
            {
                criterionId: 'f953bf52f4f94fbe805d86068c2a0af0',
                name: 'Background Screening Compliance',
                description:
                    'To verify that the vendor has established and implements a comprehensive background screening process for all job candidates that meets appropriate security standards while maintaining compliance with applicable legal and ethical requirements.',
                questions: [
                    'Does the company have a documented background screening policy that applies to all job candidates?',
                    'Does the background screening process comply with relevant legal and ethical requirements?',
                ],
            },
            {
                criterionId: 'a08d80ee9b184728ad64d6648498abda',
                name: 'Workforce Security Awareness Training',
                description:
                    'To verify that the vendor has established and implements a comprehensive information security awareness, education, and training program that ensures all workforce personnel receive appropriate levels of security training.',
                questions: [
                    'Does the company have a documented information security awareness, education, and training program that covers all workforce personnel?',
                ],
            },
            {
                criterionId: '7734ee378541474ea69b0eb7948cdfcf',
                name: 'Formal Disciplinary Process',
                description:
                    'To verify that the vendor has established and communicated a formal disciplinary process throughout their organization to ensure consistent handling of employee misconduct and policy violations.',
                questions: [
                    'Does a formal disciplinary process exist and is it documented?',
                    'Has the disciplinary process been communicated throughout the organization?',
                ],
            },
            {
                criterionId: 'de610e72cacd448299cbf3917cdbf3c7',
                name: 'Consumer Information Secure Disposal',
                description:
                    'To verify that the vendor has established and maintains appropriate policies and procedures for the secure disposal of consumer information to prevent unauthorized access to or use of consumer report information or information derived from consumer reports.',
                questions: [
                    'Does the organization have documented policies and procedures for the secure disposal of consumer information that prevent unauthorized access to or use of consumer report information or information derived from consumer reports?',
                ],
            },
            {
                criterionId: '64a390d076454b1d8def881e015d93c2',
                name: 'AI Governance Framework',
                description:
                    'To assess whether the organization has established a comprehensive AI governance framework that includes systematic risk management processes, regulatory compliance awareness, trustworthy AI principles integration, and AI system inventory management.',
                questions: [
                    'Does the organization have documented policies and procedures for artificial intelligence (AI) risk management that include defined organizational roles and responsibilities?',
                    'Does the organization conduct periodic reviews of artificial intelligence (AI) risk management processes and their outcomes?',
                    'Has the organization established processes to inform personnel of legal and regulatory requirements specific to artificial intelligence (AI) systems in their industry and business context?',
                    'Do organizational policies, processes, and procedures explicitly incorporate characteristics of trustworthy artificial intelligence (AI)?',
                    'Does the organization have documented policies that define the creation and maintenance of artificial intelligence (AI) system inventories?',
                ],
            },
        ];
    }
}

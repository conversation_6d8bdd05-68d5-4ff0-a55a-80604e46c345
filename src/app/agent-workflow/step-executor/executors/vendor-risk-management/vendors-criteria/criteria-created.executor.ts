import { Injectable } from '@nestjs/common';
import { AgentWorkflowStep } from 'app/agent-workflow/entities/agent-workflow-step.entity';
import { AgentWorkflow } from 'app/agent-workflow/entities/agent-workflow.entity';
import {
    AgentWorkflowStepName,
    AgentWorkflowStepStatus,
    AgentWorkflowVendorCriteriaStepName,
    AgentWorkflowVendorType,
} from 'app/agent-workflow/enums';
import { VendorCriteriaStepExecutorBase } from 'app/agent-workflow/step-executor/base/vendor-criteria-step-executor-base';
import { VendorAssessmentCriteriaCoreService } from 'app/users/vendors/services/vendor-assessment-criteria-core.service';
import { Account } from 'auth/entities/account.entity';
import { TenancyContext } from 'tenancy/contexts/tenancy.context';

@Injectable()
export class CriteriaCreatedExecutor extends VendorCriteriaStepExecutorBase {
    constructor(
        tenant: TenancyContext,
        vendorAssessmentCriteriaCoreService: VendorAssessmentCriteriaCoreService,
    ) {
        super(tenant, vendorAssessmentCriteriaCoreService);
    }

    async canExecute(agentWorkflow: AgentWorkflow): Promise<boolean> {
        if (agentWorkflow.agentWorkflowType !== AgentWorkflowVendorType.VENDORS_CRITERIA) {
            return false;
        }
        return true;
    }

    async execute(
        account: Account,
        workflow: AgentWorkflow,
        step: AgentWorkflowStep,
    ): Promise<void> {
        this.logAccountAndWorkflow(account, workflow, 'execute');
        await this.hideMessages(
            workflow,
            AgentWorkflowVendorCriteriaStepName.SEND_CRITERIA_QUESTIONNAIRE_TO_AI,
        );
        step.status = AgentWorkflowStepStatus.COMPLETED;
        await this.saveStep(step);
    }

    async nextStep(
        account: Account,
        agentWorkflow: AgentWorkflow,
    ): Promise<AgentWorkflowStepName | null> {
        this.logAccountAndWorkflow(account, agentWorkflow, 'nextStep');
        return AgentWorkflowVendorCriteriaStepName.REVIEW_AND_MODIFY_CRITERIA;
    }
}

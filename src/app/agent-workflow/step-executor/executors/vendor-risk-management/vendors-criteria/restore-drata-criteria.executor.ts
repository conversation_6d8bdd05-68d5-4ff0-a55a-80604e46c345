import { Injectable } from '@nestjs/common';
import { AgentWorkflowStep } from 'app/agent-workflow/entities/agent-workflow-step.entity';
import { AgentWorkflow } from 'app/agent-workflow/entities/agent-workflow.entity';
import {
    AgentWorkflowGeneralStepName,
    AgentWorkflowStepName,
    AgentWorkflowStepStatus,
    AgentWorkflowVendorCriteriaStepName,
    AgentWorkflowVendorType,
} from 'app/agent-workflow/enums';
import { VendorCriteriaStepExecutorBase } from 'app/agent-workflow/step-executor/base/vendor-criteria-step-executor-base';
import { VendorAssessmentCriteriaCoreService } from 'app/users/vendors/services/vendor-assessment-criteria-core.service';
import { Account } from 'auth/entities/account.entity';
import { TenancyContext } from 'tenancy/contexts/tenancy.context';

@Injectable()
export class RestoreDrataCriteriaExecutor extends VendorCriteriaStepExecutorBase {
    constructor(
        tenant: TenancyContext,
        vendorAssessmentCriteriaCoreService: VendorAssessmentCriteriaCoreService,
    ) {
        super(tenant, vendorAssessmentCriteriaCoreService);
    }

    async canExecute(agentWorkflow: AgentWorkflow): Promise<boolean> {
        // TODO: add validation based on workflow status/metadata
        if (agentWorkflow.agentWorkflowType !== AgentWorkflowVendorType.VENDORS_CRITERIA) {
            return false;
        }
        return true;
    }

    async execute(
        account: Account,
        workflow: AgentWorkflow,
        step: AgentWorkflowStep,
    ): Promise<void> {
        this.logAccountAndWorkflow(account, workflow, 'execute');
        await this.hideMessages(
            workflow,
            AgentWorkflowVendorCriteriaStepName.REVIEW_AND_MODIFY_CRITERIA,
        );
        // TODO: Execute restore default Drata criteria logic
        // This step represents restoring the default Drata vendor criteria
        step.status = AgentWorkflowStepStatus.COMPLETED;
        await this.saveStep(step);
    }

    async nextStep(
        account: Account,
        agentWorkflow: AgentWorkflow,
    ): Promise<AgentWorkflowStepName | null> {
        this.logAccountAndWorkflow(account, agentWorkflow, 'nextStep');

        // After restoring default criteria, the workflow is complete
        return AgentWorkflowGeneralStepName.FINALIZE;
    }
}

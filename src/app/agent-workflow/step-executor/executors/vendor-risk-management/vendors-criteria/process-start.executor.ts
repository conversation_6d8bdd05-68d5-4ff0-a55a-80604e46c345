import { Injectable } from '@nestjs/common';
import { AgentWorkflowStep } from 'app/agent-workflow/entities/agent-workflow-step.entity';
import { AgentWorkflow } from 'app/agent-workflow/entities/agent-workflow.entity';
import {
    AgentWorkflowStepName,
    AgentWorkflowStepStatus,
    AgentWorkflowVendorCriteriaStepName,
    AgentWorkflowVendorType,
} from 'app/agent-workflow/enums';
import { VendorCriteriaStepExecutorBase } from 'app/agent-workflow/step-executor/base/vendor-criteria-step-executor-base';
import { VendorAssessmentCriteriaCoreService } from 'app/users/vendors/services/vendor-assessment-criteria-core.service';
import { Account } from 'auth/entities/account.entity';
import { TenancyContext } from 'tenancy/contexts/tenancy.context';

@Injectable()
export class CriteriaCreationProcessStartExecutor extends VendorCriteriaStepExecutorBase {
    constructor(
        tenant: TenancyContext,
        vendorAssessmentCriteriaCoreService: VendorAssessmentCriteriaCoreService,
    ) {
        super(tenant, vendorAssessmentCriteriaCoreService);
    }

    async canExecute(agentWorkflow: AgentWorkflow): Promise<boolean> {
        // Validate that this is a vendor criteria workflow
        if (agentWorkflow.agentWorkflowType !== AgentWorkflowVendorType.VENDORS_CRITERIA) {
            return false;
        }
        return true;
    }

    async execute(
        account: Account,
        workflow: AgentWorkflow,
        step: AgentWorkflowStep,
    ): Promise<void> {
        this.logAccountAndWorkflow(account, workflow, 'execute');
        step.status = AgentWorkflowStepStatus.COMPLETED;
        await this.saveStep(step);
    }

    async nextStep(
        account: Account,
        agentWorkflow: AgentWorkflow,
    ): Promise<AgentWorkflowStepName | null> {
        this.logAccountAndWorkflow(account, agentWorkflow, 'nextStep');

        // TODO: Check if vendor has questionnaires uploaded
        // For now, we'll default to checking if questionnaires exist
        // If questionnaires exist, transition to SEND_CRITERIA_QUESTIONNAIRE_TO_AI
        // Otherwise, transition to CREATE_CRITERIA_MISSING_QUESTIONNAIRES

        // Placeholder logic - should be replaced with actual questionnaire check
        const hasQuestionnaires = true; // TODO: Implement actual check

        if (hasQuestionnaires) {
            return AgentWorkflowVendorCriteriaStepName.SEND_CRITERIA_QUESTIONNAIRE_TO_AI;
        } else {
            return AgentWorkflowVendorCriteriaStepName.CREATE_CRITERIA_MISSING_QUESTIONNAIRES;
        }
    }
}

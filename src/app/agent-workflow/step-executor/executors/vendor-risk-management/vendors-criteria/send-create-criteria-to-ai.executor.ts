import { Injectable } from '@nestjs/common';
import { EventBus } from '@nestjs/cqrs';
import { AgentWorkflowStep } from 'app/agent-workflow/entities/agent-workflow-step.entity';
import { AgentWorkflow } from 'app/agent-workflow/entities/agent-workflow.entity';
import {
    AgentWorkflowStepName,
    AgentWorkflowStepStatus,
    AgentWorkflowVendorType,
} from 'app/agent-workflow/enums';
import { AgentWorkflowStepUpdatedEvent } from 'app/agent-workflow/observables/events/agent-workflow-step-updated.event';
import { VendorCriteriaStepExecutorBase } from 'app/agent-workflow/step-executor/base/vendor-criteria-step-executor-base';
import { AiExecutionGroupRequestDto } from 'app/ai/dtos/ai-execution-group-request.dto';
import { VendorCriteriaQuestionsCreationStrategy } from 'app/ai/strategies/vendor-criteria-questions-creation.strategy';
import { VendorAssessmentCriteriaCoreService } from 'app/users/vendors/services/vendor-assessment-criteria-core.service';
import { Account } from 'auth/entities/account.entity';
import { ProcessFeature } from 'commons/enums/process-feature.enum';
import { ProcessType } from 'commons/enums/process-type.enum';
import { chunk } from 'lodash';
import { TenancyContext } from 'tenancy/contexts/tenancy.context';

@Injectable()
export class SendCreateCriteriaToAiExecutor extends VendorCriteriaStepExecutorBase {
    constructor(
        tenant: TenancyContext,
        vendorAssessmentCriteriaCoreService: VendorAssessmentCriteriaCoreService,
        private readonly vendorCriteriaQuestionsCreationStrategy: VendorCriteriaQuestionsCreationStrategy,
        private readonly eventBus: EventBus,
    ) {
        super(tenant, vendorAssessmentCriteriaCoreService);
    }

    async canExecute(agentWorkflow: AgentWorkflow): Promise<boolean> {
        if (agentWorkflow.agentWorkflowType !== AgentWorkflowVendorType.VENDORS_CRITERIA) {
            return false;
        }
        return true;
    }

    async execute(
        account: Account,
        workflow: AgentWorkflow,
        step: AgentWorkflowStep,
    ): Promise<void> {
        this.logAccountAndWorkflow(account, workflow, 'execute');
        try {
            // Retrieve the security review data using the base class method
            const criteria = await this.getCriteria();

            // TODO: Get chunk size from config
            const chunkSize = 100;

            // Split criteria into chunks for processing
            const criteriaChunks = chunk(criteria, chunkSize);

            this.logAccountAndWorkflow(account, workflow, 'execute');
            this.logger.log(
                `Processing ${criteria.length} questionnaires in ${criteriaChunks.length} chunks of size ${chunkSize}`,
            );

            // Process each chunk sequentially
            for (let chunkIndex = 0; chunkIndex < criteriaChunks.length; chunkIndex++) {
                const criteriaChunk = criteriaChunks[chunkIndex];

                this.logger.log(
                    `Processing chunk ${chunkIndex + 1}/${criteriaChunks.length} with ${criteriaChunk.length} criteria items`,
                );

                // Create the AI execution request with security review data for this chunk
                const request = new AiExecutionGroupRequestDto();
                request.processType = ProcessType.AGENT;
                request.processFeature = ProcessFeature.VENDOR_CRITERIA_QUESTIONS_CREATION;
                request.featureId = workflow.workflowTypeId;
                request.data = JSON.stringify({
                    workflowId: workflow.id,
                    stepId: step.id,
                    tenantId: account.id,
                    tenantName: account.companyName,
                    createCriteriaData: {
                        criteria: criteriaChunk,
                    },
                });

                // Execute the AI strategy for this chunk
                // eslint-disable-next-line no-await-in-loop
                await this.vendorCriteriaQuestionsCreationStrategy.execute(account, request, null);
            }

            // Store result data with the required information
            const resultData = {
                numberOfCriteria: criteria.length,
                numberOfChunks: criteriaChunks.length,
                chunkSize,
            };
            step.resultData = JSON.stringify({ request: resultData });
            step.progress = 0;
            await this.saveStep(step);

            // Emit step updated event
            const event = new AgentWorkflowStepUpdatedEvent(account, workflow, step);
            await this.eventBus.publish(event);
        } catch (error) {
            // Handle errors and mark step as failed if needed
            step.status = AgentWorkflowStepStatus.FAILED;
            await this.saveStep(step);
            const event = new AgentWorkflowStepUpdatedEvent(account, workflow, step);
            await this.eventBus.publish(event);
            throw error;
        }
    }

    async nextStep(
        account: Account,
        agentWorkflow: AgentWorkflow,
    ): Promise<AgentWorkflowStepName | null> {
        this.logAccountAndWorkflow(account, agentWorkflow, 'nextStep');
        return null;
    }

    private async getCriteria(): Promise<any[]> {
        return [];
    }
}

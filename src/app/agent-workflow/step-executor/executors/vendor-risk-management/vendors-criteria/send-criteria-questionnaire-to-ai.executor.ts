import { Injectable } from '@nestjs/common';
import { EventBus } from '@nestjs/cqrs';
import { AgentWorkflowStep } from 'app/agent-workflow/entities/agent-workflow-step.entity';
import { AgentWorkflow } from 'app/agent-workflow/entities/agent-workflow.entity';
import {
    AgentWorkflowStepName,
    AgentWorkflowStepStatus,
    AgentWorkflowVendorCriteriaStepName,
    AgentWorkflowVendorType,
} from 'app/agent-workflow/enums';
import { AgentWorkflowStepUpdatedEvent } from 'app/agent-workflow/observables/events/agent-workflow-step-updated.event';
import { VendorCriteriaStepExecutorBase } from 'app/agent-workflow/step-executor/base/vendor-criteria-step-executor-base';
import { AiExecutionGroupRequestDto } from 'app/ai/dtos/ai-execution-group-request.dto';
import { VendorCriteriaCreationStrategy } from 'app/ai/strategies/vendor-criteria-creation.strategy';
import { VendorAssessmentCriteriaCoreService } from 'app/users/vendors/services/vendor-assessment-criteria-core.service';
import { Account } from 'auth/entities/account.entity';
import { ProcessFeature } from 'commons/enums/process-feature.enum';
import { ProcessType } from 'commons/enums/process-type.enum';
import { chunk } from 'lodash';
import { TenancyContext } from 'tenancy/contexts/tenancy.context';

@Injectable()
export class SendCriteriaQuestionnaireToAiExecutor extends VendorCriteriaStepExecutorBase {
    constructor(
        tenant: TenancyContext,
        vendorAssessmentCriteriaCoreService: VendorAssessmentCriteriaCoreService,
        private readonly vendorCriteriaCreationStrategy: VendorCriteriaCreationStrategy,
        private readonly eventBus: EventBus,
    ) {
        super(tenant, vendorAssessmentCriteriaCoreService);
    }

    async canExecute(agentWorkflow: AgentWorkflow): Promise<boolean> {
        // TODO: add validation based on workflow status/metadata
        if (agentWorkflow.agentWorkflowType !== AgentWorkflowVendorType.VENDORS_CRITERIA) {
            return false;
        }
        return true;
    }

    async execute(
        account: Account,
        workflow: AgentWorkflow,
        step: AgentWorkflowStep,
    ): Promise<void> {
        this.logAccountAndWorkflow(account, workflow, 'execute');
        await this.hideMessages(
            workflow,
            AgentWorkflowVendorCriteriaStepName.CREATE_CRITERIA_MISSING_QUESTIONNAIRES,
        );
        try {
            // Retrieve the security review data using the base class method
            const tenantQuestionnaires = await this.getQuestionnaires();

            // TODO: Get chunk size from config
            const chunkSize = 100;

            // Split criteria into chunks for processing
            const questionnaireChunks = chunk(tenantQuestionnaires, chunkSize);

            // Process each chunk sequentially
            for (let chunkIndex = 0; chunkIndex < questionnaireChunks.length; chunkIndex++) {
                const questionnaireChunk = questionnaireChunks[chunkIndex];

                this.logger.log(
                    `Processing chunk ${chunkIndex + 1}/${questionnaireChunks.length} with ${questionnaireChunk.length} questionnaires`,
                );

                // Create the AI execution request with security review data for this chunk
                const request = new AiExecutionGroupRequestDto();
                request.processType = ProcessType.AGENT;
                request.processFeature = ProcessFeature.VENDOR_CRITERIA_CREATION;
                request.featureId = workflow.workflowTypeId;
                request.data = JSON.stringify({
                    workflowId: workflow.id,
                    stepId: step.id,
                    tenantId: account.id,
                    tenantName: account.companyName,
                    createCriteriaData: {
                        questionnaires: questionnaireChunk,
                    },
                });

                // Execute the AI strategy for this chunk
                // eslint-disable-next-line no-await-in-loop
                await this.vendorCriteriaCreationStrategy.execute(account, request, null);
            }

            // Store result data with the required information
            const resultData = {
                numberOfQuestionnaires: tenantQuestionnaires.length,
                numberOfChunks: questionnaireChunks.length,
                chunkSize,
            };
            step.resultData = JSON.stringify({ request: resultData });
            step.progress = 0;
            await this.saveStep(step);

            // Emit step updated event
            const event = new AgentWorkflowStepUpdatedEvent(account, workflow, step);
            await this.eventBus.publish(event);
        } catch (error) {
            // Handle errors and mark step as failed if needed
            step.status = AgentWorkflowStepStatus.FAILED;
            await this.saveStep(step);
            const event = new AgentWorkflowStepUpdatedEvent(account, workflow, step);
            await this.eventBus.publish(event);
            throw error;
        }
    }

    async nextStep(
        account: Account,
        agentWorkflow: AgentWorkflow,
    ): Promise<AgentWorkflowStepName | null> {
        this.logAccountAndWorkflow(account, agentWorkflow, 'nextStep');

        // After we send the criteria to AI, we go into a wait state for the AI to analyze
        return null;
    }

    private async getQuestionnaires(): Promise<any[]> {
        return [];
    }
}

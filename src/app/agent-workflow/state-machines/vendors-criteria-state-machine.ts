import { AgentWorkflowGeneralStepName } from 'app/agent-workflow/enums/agent-workflow-general-step-name.enum';
import { AgentWorkflowStepName } from 'app/agent-workflow/enums/agent-workflow-step-name.enum';
import { AgentWorkflowVendorCriteriaStepName } from 'app/agent-workflow/enums/agent-workflow-vendor-criteria-step-name.enum';
import { AgentWorkflowStateMachine } from 'app/agent-workflow/state-machines/agent-workflow-state-machine.abstract';

/**
 * Concrete implementation of the agent workflow state machine for Vendors Criteria workflows.
 */
export class VendorsCriteriaStateMachine extends AgentWorkflowStateMachine {
    /**
     * Valid transitions map for Vendors Criteria workflow.
     */
    protected readonly validTransitions: Record<AgentWorkflowStepName, AgentWorkflowStepName[]> = {
        [AgentWorkflowGeneralStepName.PROCESS_START]: [
            AgentWorkflowVendorCriteriaStepName.SEND_CRITERIA_QUESTIONNAIRE_TO_AI,
            AgentWorkflowVendorCriteriaStepName.CREATE_CRITERIA_MISSING_QUESTIONNAIRES,
        ],
        [AgentWorkflowVendorCriteriaStepName.CREATE_CRITERIA_MISSING_QUESTIONNAIRES]: [
            AgentWorkflowGeneralStepName.PROCESS_START,
        ],
        [AgentWorkflowVendorCriteriaStepName.SEND_CRITERIA_QUESTIONNAIRE_TO_AI]: [
            AgentWorkflowVendorCriteriaStepName.PROCESSING_QUESTIONNAIRES,
            AgentWorkflowGeneralStepName.CANCELLED,
        ],
        [AgentWorkflowVendorCriteriaStepName.PROCESSING_QUESTIONNAIRES]: [
            AgentWorkflowVendorCriteriaStepName.PROCESSING_QUESTIONS,
            AgentWorkflowGeneralStepName.CANCELLED,
        ],
        [AgentWorkflowVendorCriteriaStepName.PROCESSING_QUESTIONS]: [
            AgentWorkflowVendorCriteriaStepName.CREATING_CRITERIA,
            AgentWorkflowGeneralStepName.CANCELLED,
        ],
        [AgentWorkflowVendorCriteriaStepName.CREATING_CRITERIA]: [
            AgentWorkflowVendorCriteriaStepName.CRITERIA_CREATED,
            AgentWorkflowGeneralStepName.CANCELLED,
        ],
        [AgentWorkflowVendorCriteriaStepName.CRITERIA_CREATED]: [
            AgentWorkflowVendorCriteriaStepName.REVIEW_AND_MODIFY_CRITERIA,
        ],
        [AgentWorkflowVendorCriteriaStepName.REVIEW_AND_MODIFY_CRITERIA]: [
            AgentWorkflowVendorCriteriaStepName.SAVE_GENERATED_CRITERIA,
            AgentWorkflowVendorCriteriaStepName.RESTORE_DRATA_CRITERIA,
        ],
        [AgentWorkflowVendorCriteriaStepName.SAVE_GENERATED_CRITERIA]: [
            AgentWorkflowVendorCriteriaStepName.SEND_CREATE_CRITERIA_TO_AI,
        ],
        [AgentWorkflowVendorCriteriaStepName.RESTORE_DRATA_CRITERIA]: [
            AgentWorkflowGeneralStepName.FINALIZE,
        ],
        [AgentWorkflowVendorCriteriaStepName.SEND_CREATE_CRITERIA_TO_AI]: [
            AgentWorkflowVendorCriteriaStepName.CRITERIA_QUESTIONS_CREATED,
        ],
        [AgentWorkflowVendorCriteriaStepName.CRITERIA_QUESTIONS_CREATED]: [
            AgentWorkflowGeneralStepName.FINALIZE,
        ],
        [AgentWorkflowGeneralStepName.FINALIZE]: [],
    } as any;

    constructor(currentStep: AgentWorkflowStepName = AgentWorkflowGeneralStepName.PROCESS_START) {
        super(currentStep);
    }
}

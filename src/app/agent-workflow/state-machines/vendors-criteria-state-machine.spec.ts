import { AgentWorkflowGeneralStepName } from 'app/agent-workflow/enums/agent-workflow-general-step-name.enum';
import { AgentWorkflowVendorCriteriaStepName } from 'app/agent-workflow/enums/agent-workflow-vendor-criteria-step-name.enum';
import { VendorsCriteriaStateMachine } from 'app/agent-workflow/state-machines/vendors-criteria-state-machine';

describe('VendorsCriteriaStateMachine', () => {
    describe('constructor', () => {
        it('should create instance with provided current step', () => {
            const stateMachine = new VendorsCriteriaStateMachine(
                AgentWorkflowVendorCriteriaStepName.PROCESSING_QUESTIONNAIRES,
            );

            expect(stateMachine.getCurrentStep()).toBe(
                AgentWorkflowVendorCriteriaStepName.PROCESSING_QUESTIONNAIRES,
            );
        });
    });

    describe('constructor', () => {
        it('should create new instance starting from PROCESS_START by default', () => {
            const stateMachine = new VendorsCriteriaStateMachine();

            expect(stateMachine.getCurrentStep()).toBe(AgentWorkflowGeneralStepName.PROCESS_START);
        });

        it('should create instance from specific step when provided', () => {
            const stateMachine = new VendorsCriteriaStateMachine(
                AgentWorkflowGeneralStepName.FINALIZE,
            );

            expect(stateMachine.getCurrentStep()).toBe(AgentWorkflowGeneralStepName.FINALIZE);
        });
    });

    describe('canTransitionTo', () => {
        it('should not allow any transitions from FINALIZE (terminal state)', () => {
            const stateMachine = new VendorsCriteriaStateMachine(
                AgentWorkflowGeneralStepName.FINALIZE,
            );

            expect(stateMachine.canTransitionTo(AgentWorkflowGeneralStepName.PROCESS_START)).toBe(
                false,
            );
            expect(stateMachine.canTransitionTo(AgentWorkflowGeneralStepName.FINALIZE)).toBe(false);
            expect(stateMachine.canTransitionTo(AgentWorkflowGeneralStepName.CANCELLED)).toBe(
                false,
            );
        });

        it('should not allow any transitions from CANCELLED (terminal state)', () => {
            const stateMachine = new VendorsCriteriaStateMachine(
                AgentWorkflowGeneralStepName.CANCELLED,
            );

            expect(stateMachine.canTransitionTo(AgentWorkflowGeneralStepName.PROCESS_START)).toBe(
                false,
            );
            expect(stateMachine.canTransitionTo(AgentWorkflowGeneralStepName.FINALIZE)).toBe(false);
            expect(stateMachine.canTransitionTo(AgentWorkflowGeneralStepName.CANCELLED)).toBe(
                false,
            );
        });
    });

    describe('transitionTo', () => {
        it('should actually update the internal state when transitioning', () => {
            const stateMachine = new VendorsCriteriaStateMachine();

            // Verify initial state
            expect(stateMachine.getCurrentStep()).toBe(AgentWorkflowGeneralStepName.PROCESS_START);

            // Perform transition
            stateMachine.transitionTo(
                AgentWorkflowVendorCriteriaStepName.SEND_CRITERIA_QUESTIONNAIRE_TO_AI,
            );

            // Verify state has changed
            expect(stateMachine.getCurrentStep()).toBe(
                AgentWorkflowVendorCriteriaStepName.SEND_CRITERIA_QUESTIONNAIRE_TO_AI,
            );
        });

        it('should not change state when invalid transition is attempted', () => {
            const stateMachine = new VendorsCriteriaStateMachine(
                AgentWorkflowVendorCriteriaStepName.PROCESSING_QUESTIONNAIRES,
            );

            // Verify initial state
            expect(stateMachine.getCurrentStep()).toBe(
                AgentWorkflowVendorCriteriaStepName.PROCESSING_QUESTIONNAIRES,
            );

            // Attempt invalid transition (PROCESSING_QUESTIONNAIRES can only go to PROCESSING_QUESTIONS or CANCELLED)
            expect(() => {
                stateMachine.transitionTo(
                    AgentWorkflowVendorCriteriaStepName.RESTORE_DRATA_CRITERIA,
                );
            }).toThrow();

            // Verify state has NOT changed
            expect(stateMachine.getCurrentStep()).toBe(
                AgentWorkflowVendorCriteriaStepName.PROCESSING_QUESTIONNAIRES,
            );
        });

        it('should throw error for transition from FINALIZE terminal state', () => {
            const stateMachine = new VendorsCriteriaStateMachine(
                AgentWorkflowGeneralStepName.FINALIZE,
            );

            expect(() => {
                stateMachine.transitionTo(AgentWorkflowGeneralStepName.PROCESS_START);
            }).toThrow(
                'Invalid transition from FINALIZE to PROCESS_START. ' +
                    'Valid transitions from FINALIZE are: none',
            );
        });

        it('should throw error for transition from CANCELLED terminal state', () => {
            const stateMachine = new VendorsCriteriaStateMachine(
                AgentWorkflowGeneralStepName.CANCELLED,
            );

            expect(() => {
                stateMachine.transitionTo(AgentWorkflowGeneralStepName.PROCESS_START);
            }).toThrow(
                'Invalid transition from CANCELLED to PROCESS_START. ' +
                    'Valid transitions from CANCELLED are: none',
            );
        });
    });

    describe('getCurrentStep', () => {
        it('should return current step', () => {
            const stateMachine = new VendorsCriteriaStateMachine(
                AgentWorkflowGeneralStepName.FINALIZE,
            );

            expect(stateMachine.getCurrentStep()).toBe(AgentWorkflowGeneralStepName.FINALIZE);
        });
    });
});

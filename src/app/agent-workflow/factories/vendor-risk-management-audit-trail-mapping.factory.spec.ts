import { AgentWorkflowStepMessageRef } from 'app/agent-workflow/enums/agent-workflow-step-message-ref.enum';
import { AgentWorkflowVendorType } from 'app/agent-workflow/enums/agent-workflow-vendor-type.enum';
import { VendorRiskManagementAuditTrailMappingFactory } from 'app/agent-workflow/factories/vendor-risk-management-audit-trail-mapping.factory';

describe('VendorRiskManagementAuditTrailMappingFactory', () => {
    let factory: VendorRiskManagementAuditTrailMappingFactory;

    beforeEach(() => {
        factory = new VendorRiskManagementAuditTrailMappingFactory();
    });

    describe('getMapping', () => {
        it('should return vendor assessment mapping for VENDOR_ASSESSMENT', () => {
            // Act
            const mapping = factory.getMapping(AgentWorkflowVendorType.VENDOR_ASSESSMENT);

            // Assert
            expect(mapping).toBeDefined();
            expect(mapping.size).toBeGreaterThan(0);
            expect(mapping.has('PROCESS_START')).toBe(true);
            expect(mapping.has('SAFEBASE_ACCESS_GRANTED')).toBe(true);
            expect(mapping.has('SAFEBASE_SEND_DOCUMENTS_TO_AI')).toBe(true);
            expect(mapping.has('RETRIEVE_SECURITY_POSTURE')).toBe(true);
            expect(mapping.has('SECURITY_QUESTIONNAIRE_SENT')).toBe(true);
            expect(mapping.size).toBe(5);
        });

        it('should return vendor criteria mapping for VENDORS_CRITERIA', () => {
            // Act
            const mapping = factory.getMapping(AgentWorkflowVendorType.VENDORS_CRITERIA);

            // Assert
            expect(mapping).toBeDefined();
            expect(mapping.size).toBeGreaterThan(0);
            expect(mapping.has('PROCESSING_QUESTIONNAIRES')).toBe(true);
            expect(mapping.has('PROCESSING_QUESTIONS')).toBe(true);
            expect(mapping.has('CREATING_CRITERIA')).toBe(true);
            expect(mapping.size).toBe(3);
        });

        it('should throw error for unsupported workflow type', () => {
            // Act & Assert
            expect(() => {
                factory.getMapping('UNSUPPORTED_WORKFLOW_TYPE' as any);
            }).toThrow(
                'Unsupported workflow type for Vendor Risk Management audit trail: UNSUPPORTED_WORKFLOW_TYPE',
            );
        });
    });

    describe('display names', () => {
        it('should have correct display names for all vendor assessment steps', () => {
            // Act
            const mapping = factory.getMapping(AgentWorkflowVendorType.VENDOR_ASSESSMENT);

            // Assert
            expect(mapping.get('PROCESS_START')?.displayName).toBe('Started security review');
            expect(mapping.get('SAFEBASE_ACCESS_GRANTED')?.displayName).toBe('Access Granted');
            expect(mapping.get('SAFEBASE_SEND_DOCUMENTS_TO_AI')?.displayName).toBe(
                'Documents collected',
            );
            expect(mapping.get('RETRIEVE_SECURITY_POSTURE')?.displayName).toBe(
                'Criteria assessment results',
            );
            expect(mapping.get('SECURITY_QUESTIONNAIRE_SENT')?.displayName).toBe(
                'Follow up questionnaire',
            );
        });

        it('should have correct ref values for vendor assessment steps', () => {
            // Act
            const mapping = factory.getMapping(AgentWorkflowVendorType.VENDOR_ASSESSMENT);

            // Assert
            expect(mapping.get('PROCESS_START')?.ref).toBe(
                AgentWorkflowStepMessageRef.SECURITY_REVIEW_ASSESSMENT,
            );
            expect(mapping.get('SAFEBASE_ACCESS_GRANTED')?.ref).toBeUndefined();
            expect(mapping.get('SAFEBASE_SEND_DOCUMENTS_TO_AI')?.ref).toBe(
                AgentWorkflowStepMessageRef.SECURITY_REVIEW_DOCUMENTS,
            );
            expect(mapping.get('RETRIEVE_SECURITY_POSTURE')?.ref).toBe(
                AgentWorkflowStepMessageRef.SECURITY_REVIEW_ASSESSMENT,
            );
            expect(mapping.get('SECURITY_QUESTIONNAIRE_SENT')?.ref).toBe(
                AgentWorkflowStepMessageRef.SECURITY_REVIEW_QUESTIONNAIRE,
            );
        });
    });
});

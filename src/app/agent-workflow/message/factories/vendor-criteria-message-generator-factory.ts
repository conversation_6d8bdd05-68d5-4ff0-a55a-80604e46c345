import { Injectable } from '@nestjs/common';
import { AgentWorkflowStepName } from 'app/agent-workflow/enums/agent-workflow-step-name.enum';
import { AgentWorkflowVendorCriteriaStepName } from 'app/agent-workflow/enums/agent-workflow-vendor-criteria-step-name.enum';
import { CreateCriteriaMissingQuestionnairesMessageGenerator } from 'app/agent-workflow/message/generators/vendor-risk-management/vendors-criteria/create-criteria-missing-questionnaires.generator';
import { CreatingCriteriaMessageGenerator } from 'app/agent-workflow/message/generators/vendor-risk-management/vendors-criteria/creating-criteria.generator';
import { CriteriaCreatedMessageGenerator } from 'app/agent-workflow/message/generators/vendor-risk-management/vendors-criteria/criteria-created.generator';
import { CriteriaQuestionsCreatedMessageGenerator } from 'app/agent-workflow/message/generators/vendor-risk-management/vendors-criteria/criteria-questions-created.generator';
import { ProcessingQuestionnairesMessageGenerator } from 'app/agent-workflow/message/generators/vendor-risk-management/vendors-criteria/processing-questionnaires.generator';
import { ProcessingQuestionsMessageGenerator } from 'app/agent-workflow/message/generators/vendor-risk-management/vendors-criteria/processing-questions.generator';
import { RestoreDrataCriteriaMessageGenerator } from 'app/agent-workflow/message/generators/vendor-risk-management/vendors-criteria/restore-drata-criteria.generator';
import { ReviewAndModifyCriteriaMessageGenerator } from 'app/agent-workflow/message/generators/vendor-risk-management/vendors-criteria/review-and-modify-criteria.generator';
import { SavedGeneratedCriteriaMessageGenerator } from 'app/agent-workflow/message/generators/vendor-risk-management/vendors-criteria/saved-generated-criteria.generator';
import { SendCreateCriteriaToAiMessageGenerator } from 'app/agent-workflow/message/generators/vendor-risk-management/vendors-criteria/send-create-criteria-to-ai.generator';
import { SendCriteriaQuestionnaireToAiMessageGenerator } from 'app/agent-workflow/message/generators/vendor-risk-management/vendors-criteria/send-criteria-questionnaire-to-ai.generator';
import { IMessageGenerator } from 'app/agent-workflow/message/types/message-generator.interface';

/**
 * Factory for creating vendor criteria message generators.
 * Uses dependency injection to provide message generators for different vendor criteria steps.
 * Follows the same pattern as AI strategy factories.
 */
@Injectable()
export class VendorCriteriaMessageGeneratorFactory {
    constructor(
        private readonly createCriteriaMissingQuestionnairesMessageGenerator: CreateCriteriaMissingQuestionnairesMessageGenerator,
        private readonly sendCriteriaQuestionnaireToAiMessageGenerator: SendCriteriaQuestionnaireToAiMessageGenerator,
        private readonly processingQuestionnairesMessageGenerator: ProcessingQuestionnairesMessageGenerator,
        private readonly processingQuestionsMessageGenerator: ProcessingQuestionsMessageGenerator,
        private readonly creatingCriteriaMessageGenerator: CreatingCriteriaMessageGenerator,
        private readonly criteriaCreatedMessageGenerator: CriteriaCreatedMessageGenerator,
        private readonly reviewAndModifyCriteriaMessageGenerator: ReviewAndModifyCriteriaMessageGenerator,
        private readonly savedGeneratedCriteriaMessageGenerator: SavedGeneratedCriteriaMessageGenerator,
        private readonly restoreDrataCriteriaMessageGenerator: RestoreDrataCriteriaMessageGenerator,
        private readonly sendCreateCriteriaToAiMessageGenerator: SendCreateCriteriaToAiMessageGenerator,
        private readonly criteriaQuestionsCreatedMessageGenerator: CriteriaQuestionsCreatedMessageGenerator,
    ) {}

    /**
     * Gets the appropriate message generator for a vendor criteria step.
     * @param stepName - The vendor criteria step name
     * @returns The message generator for the step, or null if not found
     */
    getMessageGenerator(stepName: AgentWorkflowStepName): IMessageGenerator | null {
        switch (stepName) {
            case AgentWorkflowVendorCriteriaStepName.CREATE_CRITERIA_MISSING_QUESTIONNAIRES:
                return this.createCriteriaMissingQuestionnairesMessageGenerator;
            case AgentWorkflowVendorCriteriaStepName.SEND_CRITERIA_QUESTIONNAIRE_TO_AI:
                return this.sendCriteriaQuestionnaireToAiMessageGenerator;
            case AgentWorkflowVendorCriteriaStepName.PROCESSING_QUESTIONNAIRES:
                return this.processingQuestionnairesMessageGenerator;
            case AgentWorkflowVendorCriteriaStepName.PROCESSING_QUESTIONS:
                return this.processingQuestionsMessageGenerator;
            case AgentWorkflowVendorCriteriaStepName.CREATING_CRITERIA:
                return this.creatingCriteriaMessageGenerator;
            case AgentWorkflowVendorCriteriaStepName.CRITERIA_CREATED:
                return this.criteriaCreatedMessageGenerator;
            case AgentWorkflowVendorCriteriaStepName.REVIEW_AND_MODIFY_CRITERIA:
                return this.reviewAndModifyCriteriaMessageGenerator;
            case AgentWorkflowVendorCriteriaStepName.SAVE_GENERATED_CRITERIA:
                return this.savedGeneratedCriteriaMessageGenerator;
            case AgentWorkflowVendorCriteriaStepName.RESTORE_DRATA_CRITERIA:
                return this.restoreDrataCriteriaMessageGenerator;
            case AgentWorkflowVendorCriteriaStepName.SEND_CREATE_CRITERIA_TO_AI:
                return this.sendCreateCriteriaToAiMessageGenerator;
            case AgentWorkflowVendorCriteriaStepName.CRITERIA_QUESTIONS_CREATED:
                return this.criteriaQuestionsCreatedMessageGenerator;
            default:
                return null;
        }
    }
}

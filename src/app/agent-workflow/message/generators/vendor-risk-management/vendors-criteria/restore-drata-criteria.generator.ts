import { Injectable } from '@nestjs/common';
import { AgentWorkflow } from 'app/agent-workflow/entities';
import {
    AgentWorkflowStepMessageAction,
    AgentWorkflowStepMessageActionType,
    AgentWorkflowStepMessageCaller,
} from 'app/agent-workflow/enums';
import { MessageGeneratorBase } from 'app/agent-workflow/message/generators/message-generator-base';
import { AgentWorkflowStepMessageDataType } from 'app/agent-workflow/types';
import { Account } from 'auth/entities/account.entity';

/**
 * Message generator for the RESTORE_DRATA_CRITERIA step in vendor criteria process.
 * Generates appropriate message data for when users restore the default Drata criteria template.
 */
@Injectable()
export class RestoreDrataCriteriaMessageGenerator extends MessageGeneratorBase {
    /**
     * Generates message data for the RESTORE_DRATA_CRITERIA step.
     * @param account - The account context for the workflow
     * @param agentWorkflow - The agent workflow containing context and state
     * @returns Promise<Array of message data objects for the step>
     */
    async generate(
        account: Account,
        agentWorkflow: AgentWorkflow,
    ): Promise<AgentWorkflowStepMessageDataType[]> {
        this.logGenerate(account, agentWorkflow);
        return [
            {
                caller: AgentWorkflowStepMessageCaller.USER,
                title: [],
                body: [],
                actions: [
                    {
                        type: AgentWorkflowStepMessageActionType.PRESSED_BUTTON,
                        text: 'Restore Drata’s criteria template',
                        action: AgentWorkflowStepMessageAction.RESTORE_DRATA_CRITERIA,
                    },
                ],
            },
        ];
    }
}

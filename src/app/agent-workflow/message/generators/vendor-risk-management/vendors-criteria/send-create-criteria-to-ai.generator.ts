import { Injectable } from '@nestjs/common';
import { AgentWorkflow } from 'app/agent-workflow/entities';
import { MessageGeneratorBase } from 'app/agent-workflow/message/generators/message-generator-base';
import { AgentWorkflowStepMessageDataType } from 'app/agent-workflow/types';
import { Account } from 'auth/entities/account.entity';

@Injectable()
export class SendCreateCriteriaToAiMessageGenerator extends MessageGeneratorBase {
    /**
     * Generates message data for the SEND_CREATE_CRITERIA_TO_AI step.
     * @param account - The account context for the workflow
     * @param agentWorkflow - The agent workflow containing context and state
     * @returns Promise<Array of message data objects for the step>
     */
    async generate(
        account: Account,
        agentWorkflow: AgentWorkflow,
    ): Promise<AgentWorkflowStepMessageDataType[]> {
        this.logGenerate(account, agentWorkflow);
        return [];
    }
}

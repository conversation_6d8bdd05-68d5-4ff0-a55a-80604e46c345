import { Injectable } from '@nestjs/common';
import { AgentWorkflow } from 'app/agent-workflow/entities';
import {
    AgentWorkflowStepMessageAction,
    AgentWorkflowStepMessageActionType,
    AgentWorkflowStepMessageCaller,
} from 'app/agent-workflow/enums';
import { MessageGeneratorBase } from 'app/agent-workflow/message/generators/message-generator-base';
import { AgentWorkflowStepMessageDataType } from 'app/agent-workflow/types';
import { Account } from 'auth/entities/account.entity';

/**
 * Message generator for the CREATE_CRITERIA_MISSING_QUESTIONNAIRES step in vendor criteria process.
 * Generates appropriate message data for when vendor needs to upload questionnaires.
 */
@Injectable()
export class CreateCriteriaMissingQuestionnairesMessageGenerator extends MessageGeneratorBase {
    /**
     * Generates message data for the CREATE_CRITERIA_MISSING_QUESTIONNAIRES step.
     * @param account - The account context for the workflow
     * @param agentWorkflow - The agent workflow containing context and state
     * @returns Promise<Array of message data objects for the step>
     */
    async generate(
        account: Account,
        agentWorkflow: AgentWorkflow,
    ): Promise<AgentWorkflowStepMessageDataType[]> {
        this.logGenerate(account, agentWorkflow);
        return [
            {
                caller: AgentWorkflowStepMessageCaller.AGENT,
                title: [],
                body: [
                    {
                        text: 'Drata can generate tailored criteria based on questionnaires that you send to your vendors.',
                    },
                ],
                actions: [],
            },
            {
                caller: AgentWorkflowStepMessageCaller.USER,
                title: [],
                body: [],
                actions: [
                    {
                        type: AgentWorkflowStepMessageActionType.BUTTON,
                        text: 'Get started',
                        action: AgentWorkflowStepMessageAction.SEND_VENDOR_CRITERIA_QUESTIONNAIRES,
                    },
                ],
            },
        ];
    }
}

import { SocketEvent } from '@drata/enums';
import { Injectable } from '@nestjs/common';
import { AiExecutionGroupRequestDto } from 'app/ai/dtos/ai-execution-group-request.dto';
import { IExecutionStrategy } from 'app/ai/interfaces/IExecutionStrategy';
import { AiAgentApiService } from 'app/ai/services/ai-agent-api.service';
import { WebhookStrategyBase } from 'app/ai/strategies/webhook-base.strategy';
import { VendorGenerateCriteriaQuestionsData } from 'app/ai/types/agent/vendor-generate-criteria-questions/vendor-generate-criteria-questions-data.type';
import { Account } from 'auth/entities/account.entity';
import { PolloAdapter } from 'commons/adapters/pollo.adapter';
import { Llm } from 'dependencies/llm/llm';
import { Socket } from 'dependencies/socket/socket';
import { PolloMessage } from 'pollo-logger/classes/pollo.message';
import { TenancyContext } from 'tenancy/contexts/tenancy.context';

@Injectable()
export class VendorCriteriaQuestionsCreationStrategy
    extends WebhookStrategyBase
    implements IExecutionStrategy
{
    override socketEvent = SocketEvent.VENDOR_CRITERIA_CREATION;

    constructor(
        tenant: TenancyContext,
        socket: Socket,
        private readonly llm: Llm,
        private readonly aiAgentApiService: AiAgentApiService,
    ) {
        super(tenant, socket);
    }

    async parseRequest(executionId: string, request: AiExecutionGroupRequestDto) {
        return {
            executionId,
            data: request.data,
        };
    }

    async generateData(account: Account, data: VendorGenerateCriteriaQuestionsData): Promise<any> {
        this.logger.log(
            PolloAdapter.acct(
                'Calling AI agent API for vendor criteria questions creation',
                account,
                this.constructor.name,
            ).setIdentifier({
                tenantId: data.tenantId,
                tenantName: data.tenantName,
                jobId: data.jobId,
            }),
        );

        try {
            const response = await this.aiAgentApiService.generateQuestions(data);

            this.logger.log(
                PolloAdapter.acct(
                    'AI agent API call completed successfully',
                    account,
                    this.constructor.name,
                ).setIdentifier({
                    jobId: data.jobId,
                    responseJobId: response.jobId,
                }),
            );

            return response;
        } catch (error) {
            this.logger.error(
                PolloMessage.msg('AI Agent API call failed')
                    .setContext(this.constructor.name)
                    .setIdentifier({
                        jobId: data.jobId,
                        tenantId: data.tenantId,
                        tenantName: data.tenantName,
                        error: error.message,
                        responseData: error.response?.data,
                        requestData: data,
                    }),
            );
            throw error;
        }
    }
}

import { SocketEvent } from '@drata/enums';
import { Injectable } from '@nestjs/common';
import { AiExecutionGroupRequestDto } from 'app/ai/dtos/ai-execution-group-request.dto';
import { IExecutionStrategy } from 'app/ai/interfaces/IExecutionStrategy';
import {
    AiAgentApiService,
    AIImmediateResponseInterface,
} from 'app/ai/services/ai-agent-api.service';
import { WebhookStrategyBase } from 'app/ai/strategies/webhook-base.strategy';
import { VendorSecurityReviewAssessmentData } from 'app/ai/types/agent/vendor-assessment/vendor-security-review-assessment-data.type';
import { Account } from 'auth/entities/account.entity';
import { PolloAdapter } from 'commons/adapters/pollo.adapter';
import { Llm } from 'dependencies/llm/llm';
import { Socket } from 'dependencies/socket/socket';
import { PolloMessage } from 'pollo-logger/classes/pollo.message';
import { TenancyContext } from 'tenancy/contexts/tenancy.context';

@Injectable()
export class VendorSecurityReviewAssessmentStrategy
    extends WebhookStrategyBase
    implements IExecutionStrategy
{
    override socketEvent = SocketEvent.VENDOR_SECURITY_REVIEW_ASSESSMENT;

    constructor(
        tenant: TenancyContext,
        socket: Socket,
        private readonly llm: Llm,
        private readonly aiAgentApiService: AiAgentApiService,
    ) {
        super(tenant, socket);
    }

    async parseRequest(executionId: string, request: AiExecutionGroupRequestDto) {
        const data = request.data ? JSON.parse(request.data) : {};
        const { vendorAssessmentData = {} } = data;
        const requestData: VendorSecurityReviewAssessmentData = {
            criteriaWithQuestions: vendorAssessmentData.criteriaWithQuestions,
            jobId: executionId,
            productId: String(data.productId),
            productName: data.productName,
            tenantId: data.tenantId,
            tenantName: data.tenantName,
            vendorDocumentIndexId: vendorAssessmentData.documentIndexId,
            vendorDocuments: vendorAssessmentData.documents,
            vendorId: '123e4567-e89b-12d3-a456-************', // This is not getting used anyway but API service is expecting a UUID
            vendorName: vendorAssessmentData.vendorName,
            vendorSecurityReviewId: String(vendorAssessmentData.securityReviewId),
        };
        return requestData;
    }

    async generateData(
        account: Account,
        data: VendorSecurityReviewAssessmentData,
    ): Promise<AIImmediateResponseInterface> {
        this.logger.log(
            PolloAdapter.acct(
                'Calling AI agent API for vendor security review assessment',
                account,
                this.constructor.name,
            ).setIdentifier({
                vendorId: data.vendorId,
                vendorSecurityReviewId: data.vendorSecurityReviewId,
                jobId: data.jobId,
                tenantId: data.tenantId,
                tenantName: data.tenantName,
            }),
        );

        try {
            const response = await this.aiAgentApiService.assessVendorSecurityReview(data);

            this.logger.log(
                PolloAdapter.acct(
                    'AI agent API call completed successfully',
                    account,
                    this.constructor.name,
                ).setIdentifier({
                    jobId: data.jobId,
                    responseJobId: response.jobId,
                }),
            );

            return response;
        } catch (error) {
            this.logger.error(
                PolloMessage.msg('AI Agent API call failed')
                    .setContext(this.constructor.name)
                    .setIdentifier({
                        jobId: data.jobId,
                        tenantId: data.tenantId,
                        tenantName: data.tenantName,
                        error: error.message,
                        responseData: error.response?.data,
                        requestData: data,
                    }),
            );
            throw error;
        }
    }
}

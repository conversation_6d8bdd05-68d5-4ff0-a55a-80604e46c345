import { SocketEvent } from '@drata/enums';
import { Injectable } from '@nestjs/common';
import { AiExecutionGroupRequestDto } from 'app/ai/dtos/ai-execution-group-request.dto';
import { AiExecutionGroup } from 'app/ai/entities/ai-execution-group.entity';
import { AiExecutionErrorFactory } from 'app/ai/factories/ai-execution-error-factory';
import { IExecutionStrategy } from 'app/ai/interfaces/IExecutionStrategy';
import { StrategyBase } from 'app/ai/strategies/strategy-base.strategy';
import { Account } from 'auth/entities/account.entity';
import { PolloAdapter } from 'commons/adapters/pollo.adapter';
import { ExecutionStatus } from 'commons/enums/execution-status.enum';
import { getUuid } from 'commons/helpers/identity.helper';
import { Socket } from 'dependencies/socket/socket';
import { isNil } from 'lodash';
import { TenancyContext } from 'tenancy/contexts/tenancy.context';

@Injectable()
export abstract class WebhookStrategyBase extends StrategyBase implements IExecutionStrategy {
    protected socketEvent: SocketEvent;
    protected group: AiExecutionGroup;

    constructor(
        protected readonly tenant: TenancyContext,
        protected readonly socket: Socket,
    ) {
        super(tenant, socket);
    }

    async execute(
        account: Account,
        request: AiExecutionGroupRequestDto,
        aiExecutionGroup?: AiExecutionGroup | null,
    ): Promise<AiExecutionGroup> {
        if (isNil(aiExecutionGroup)) {
            this.group = await this.createExecutionGroup(request);
        } else {
            this.group = aiExecutionGroup;
        }

        // Create execution with UUID in advance
        const executionId = this.generateJobId(account, request);
        const newAiExecution = await this.createExecution(this.group, executionId);

        try {
            await this.emitSocketMessage(account, ExecutionStatus.INPROGRESS, newAiExecution);

            const data = await this.parseRequest(executionId, request);
            await this.callGenerateSummary(account, data);

            // Return 200 with jobId immediately - webhook will handle the actual response later
            await this.emitSocketMessage(account, ExecutionStatus.INPROGRESS, newAiExecution);
        } catch (error) {
            this.logger.log(
                PolloAdapter.acct(
                    'Something went wrong during AI webhook execution',
                    account,
                    this.constructor.name,
                )
                    .setError(error)
                    .setIdentifier({
                        executionId: newAiExecution.executionId,
                    }),
            );

            const aiExecutionErrorFactory = new AiExecutionErrorFactory();
            const errorCode = aiExecutionErrorFactory.getErrorCode(error);
            await this.updateExecution(newAiExecution.id, null, errorCode);
            await this.emitSocketMessage(account, ExecutionStatus.ERROR, newAiExecution);
        }

        return this.getAiExecutionGroup(this.group.id);
    }

    /**
     * Generate a jobId for the AI execution
     * Can be overridden by concrete implementations for custom jobId generation
     * @param account
     * @param request
     * @returns
     */
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    protected generateJobId(account: Account, request: AiExecutionGroupRequestDto): string {
        // Generate a log for the account and the request
        this.logger.log(
            PolloAdapter.acct(
                'Generating job id for AI execution',
                account,
                this.constructor.name,
            ).setIdentifier({
                request,
            }),
        );
        return getUuid();
    }

    abstract parseRequest(executionId: string, request: AiExecutionGroupRequestDto): any;
}

import { Global, MiddlewareConsumer, Module } from '@nestjs/common';
import { Ai<PERSON>ontroller } from 'app/ai/ai.controller';
import { AiService } from 'app/ai/ai.service';
import { AiExecutionGroupFeedbackGroupsController } from 'app/ai/controllers/ai-execution-group-feedback-groups.controller';
import { AiExecutionGroupController } from 'app/ai/controllers/ai-execution-group.controller';
import { AgentStrategyFactory } from 'app/ai/factories/agent-strategy-factory';
import { FeedbackStrategyFactory } from 'app/ai/factories/feedback-strategy-factory';
import { OpenKnowledgeFactory } from 'app/ai/factories/open-knowledge-factory';
import { QuestionnaireStrategyFactory } from 'app/ai/factories/questionnaire-strategy-factory';
import { StrategyFactory } from 'app/ai/factories/strategy-factory';
import { SummaryStrategyFactory } from 'app/ai/factories/summary-strategy-factory';
import { AiEnabledMiddleware } from 'app/ai/middlewares/ai-enable.middleware';
import { AiExecutionGroupsHandler } from 'app/ai/observables/handlers/ai-execution-groups.handler';
import { AiAgentApiService } from 'app/ai/services/ai-agent-api.service';
import { AiExecutionGroupService } from 'app/ai/services/ai-execution-group.service';
import { AiExecutionService } from 'app/ai/services/ai-execution.service';

import { ControlTestInstanceInstructionsStrategy } from 'app/ai/strategies/control-test-instance-instructions.strategy';
import { ControlTestTemplateInstructionsStrategy } from 'app/ai/strategies/control-test-template-instructions.strategy';
import { InboundSecurityQuestionnaireAnsweringStrategy } from 'app/ai/strategies/inbound-security-questionnaire-answering-strategy';
import { InboundSecurityQuestionnaireExtractionStrategy } from 'app/ai/strategies/inbound-security-questionnaire-extraction-strategy';
import { OpenKnowledgeAnsweringStrategy } from 'app/ai/strategies/open-knowledge-answering.strategy';
import { QuestionnaireSummaryStrategy } from 'app/ai/strategies/questionnaire-summary.strategy';
import { TestFailureSummariesStrategy } from 'app/ai/strategies/test-failure-summaries.strategy';
import { TestFailureSummaryStrategy } from 'app/ai/strategies/test-failure-summary.strategy';
import { TestLogicSummaryStrategy } from 'app/ai/strategies/test-logic-summary.strategy';
import { VendorCriteriaCreationStrategy } from 'app/ai/strategies/vendor-criteria-creation.strategy';
import { VendorCriteriaQuestionsCreationStrategy } from 'app/ai/strategies/vendor-criteria-questions-creation.strategy';
import { VendorSecurityReviewAssessmentStrategy } from 'app/ai/strategies/vendor-security-review-assessment.strategy';
import { ModuleType, ModuleTypes } from 'commons/decorators/module-type.decorator';
import { isLocal } from 'commons/helpers/environment.helper';
import { TypeOrmExtensionsModule } from 'database/typeorm/typeorm.extensions.module';
import { ControlTestTemplateRepository } from 'site-admin/repositories/control-test-template.repository';

@ModuleType(ModuleTypes.CORE)
@Global()
@Module({
    imports: [TypeOrmExtensionsModule.forGlobalCustomRepository([ControlTestTemplateRepository])],
    controllers: [
        ...(isLocal() ? [AiController] : []),
        AiExecutionGroupController,
        AiExecutionGroupFeedbackGroupsController,
    ],
    providers: [
        AiService,
        AiExecutionService,
        AiAgentApiService,
        AiExecutionGroupService,
        QuestionnaireSummaryStrategy,
        OpenKnowledgeAnsweringStrategy,
        TestLogicSummaryStrategy,
        TestFailureSummariesStrategy,
        TestFailureSummaryStrategy,
        ControlTestInstanceInstructionsStrategy,
        ControlTestTemplateInstructionsStrategy,
        SummaryStrategyFactory,
        FeedbackStrategyFactory,
        StrategyFactory,
        OpenKnowledgeFactory,
        QuestionnaireStrategyFactory,
        InboundSecurityQuestionnaireExtractionStrategy,
        InboundSecurityQuestionnaireAnsweringStrategy,
        VendorCriteriaCreationStrategy,
        VendorCriteriaQuestionsCreationStrategy,
        VendorSecurityReviewAssessmentStrategy,
        AgentStrategyFactory,
        AiExecutionGroupsHandler,
    ],
    exports: [
        AiService,
        AiExecutionGroupService,
        VendorSecurityReviewAssessmentStrategy,
        VendorCriteriaCreationStrategy,
        VendorCriteriaQuestionsCreationStrategy,
    ],
})
export class AiModule {
    configure(consumer: MiddlewareConsumer) {
        consumer.apply(AiEnabledMiddleware).forRoutes(AiExecutionGroupController);
    }
}

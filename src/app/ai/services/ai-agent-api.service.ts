import { HttpService } from '@nestjs/axios';
import { Injectable } from '@nestjs/common';
import { VendorSecurityReviewAssessmentData } from 'app/ai/types/agent/vendor-assessment/vendor-security-review-assessment-data.type';
import { VendorCreateCriteriaData } from 'app/ai/types/agent/vendor-create-criteria/vendor-create-criteria-data.type';
import { VendorGenerateCriteriaQuestionsData } from 'app/ai/types/agent/vendor-generate-criteria-questions/vendor-generate-criteria-questions-data.type';
import { AxiosResponse } from 'axios';
import { BaseService } from 'commons/services/base.service';
import config from 'config';
import { PolloMessage } from 'pollo-logger/classes/pollo.message';
import { firstValueFrom } from 'rxjs';

export interface AIImmediateResponseInterface {
    jobId: string;
    tenantId: string;
    tenantName: string;
}

@Injectable()
export class AiAgentApiService extends BaseService {
    private readonly baseUrl: string;

    constructor(private readonly httpService: HttpService) {
        super();
        this.baseUrl = config.get('aiAgent.api.baseUrl', '');
    }

    async assessVendorSecurityReview(
        request: VendorSecurityReviewAssessmentData,
    ): Promise<AIImmediateResponseInterface> {
        const endpoint = '/api/v1/vrm/questionnaire/criteria/assessment';
        const url = `${this.baseUrl}${endpoint}`;

        this.logger.log(
            PolloMessage.msg('Calling AI Agent API for vendor security review assessment')
                .setContext(this.constructor.name)
                .setIdentifier({
                    url,
                    vendorId: request.vendorId,
                    vendorSecurityReviewId: request.vendorSecurityReviewId,
                    jobId: request.jobId,
                }),
        );

        try {
            const response: AxiosResponse<AIImmediateResponseInterface> = await firstValueFrom(
                this.httpService.post(url, request, {
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    timeout: 30000,
                }),
            );

            this.logger.log(
                PolloMessage.msg('AI Agent API call successful')
                    .setContext(this.constructor.name)
                    .setIdentifier({
                        jobId: request.jobId,
                        tenantId: request.tenantId,
                        tenantName: request.tenantName,
                    }),
            );

            return response.data;
        } catch (error) {
            this.logger.error(
                PolloMessage.msg('AI Agent API call failed')
                    .setContext(this.constructor.name)
                    .setIdentifier({
                        jobId: request.jobId,
                        tenantId: request.tenantId,
                        tenantName: request.tenantName,
                        error: error.message,
                    }),
            );
            throw error;
        }
    }

    async generateCriteria(
        request: VendorCreateCriteriaData,
    ): Promise<AIImmediateResponseInterface> {
        const endpoint = '/api/v1/vrm/questionnaire/criteria';
        const url = `${this.baseUrl}${endpoint}`;

        this.logger.log(
            PolloMessage.msg('Calling AI Agent API for generate criteria')
                .setContext(this.constructor.name)
                .setIdentifier({
                    url,
                    jobId: request.jobId,
                    tenantId: request.tenantId,
                    tenantName: request.tenantName,
                }),
        );

        try {
            const response: AxiosResponse<AIImmediateResponseInterface> = await firstValueFrom(
                this.httpService.post(url, request, {
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    timeout: 30000,
                }),
            );

            this.logger.log(
                PolloMessage.msg('AI Agent API call successful for generate criteria')
                    .setContext(this.constructor.name)
                    .setIdentifier({
                        jobId: request.jobId,
                        tenantId: request.tenantId,
                        tenantName: request.tenantName,
                    }),
            );

            return response.data;
        } catch (error) {
            this.logger.error(
                PolloMessage.msg('AI Agent API call failed for generate criteria')
                    .setContext(this.constructor.name)
                    .setIdentifier({
                        jobId: request.jobId,
                        tenantId: request.tenantId,
                        tenantName: request.tenantName,
                        error: error.message,
                    }),
            );
            throw error;
        }
    }

    async generateQuestions(
        request: VendorGenerateCriteriaQuestionsData,
    ): Promise<AIImmediateResponseInterface> {
        const endpoint = '/api/v1/vrm/questionnaire/criteria/questions';
        const url = `${this.baseUrl}${endpoint}`;

        this.logger.log(
            PolloMessage.msg('Calling AI Agent API for generate criteria questions')
                .setContext(this.constructor.name)
                .setIdentifier({
                    url,
                    jobId: request.jobId,
                    tenantId: request.tenantId,
                    tenantName: request.tenantName,
                }),
        );

        try {
            const response: AxiosResponse<AIImmediateResponseInterface> = await firstValueFrom(
                this.httpService.post(url, request, {
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    timeout: 30000,
                }),
            );

            this.logger.log(
                PolloMessage.msg('AI Agent API call successful for generate criteria questions')
                    .setContext(this.constructor.name)
                    .setIdentifier({
                        jobId: request.jobId,
                        tenantId: request.tenantId,
                        tenantName: request.tenantName,
                    }),
            );

            return response.data;
        } catch (error) {
            this.logger.error(
                PolloMessage.msg('AI Agent API call failed for generate criteria questions')
                    .setContext(this.constructor.name)
                    .setIdentifier({
                        jobId: request.jobId,
                        tenantId: request.tenantId,
                        tenantName: request.tenantName,
                        error: error.message,
                    }),
            );
            throw error;
        }
    }
}

import { Injectable } from '@nestjs/common';
import { IExecutionStrategy } from 'app/ai/interfaces/IExecutionStrategy';
import { VendorCriteriaCreationStrategy } from 'app/ai/strategies/vendor-criteria-creation.strategy';
import { VendorCriteriaQuestionsCreationStrategy } from 'app/ai/strategies/vendor-criteria-questions-creation.strategy';
import { VendorSecurityReviewAssessmentStrategy } from 'app/ai/strategies/vendor-security-review-assessment.strategy';
import { ProcessFeature } from 'commons/enums/process-feature.enum';

@Injectable()
export class AgentStrategyFactory {
    constructor(
        private readonly vendorCriteriaCreationStrategy: VendorCriteriaCreationStrategy,
        private readonly vendorCriteriaQuestionsCreationStrategy: VendorCriteriaQuestionsCreationStrategy,
        private readonly vendorSecurityReviewAssessmentStrategy: VendorSecurityReviewAssessmentStrategy,
    ) {}

    getStrategy(processFeature: ProcessFeature): IExecutionStrategy {
        switch (processFeature) {
            case ProcessFeature.VENDOR_CRITERIA_CREATION:
                return this.vendorCriteriaCreationStrategy;
            case ProcessFeature.VENDOR_CRITERIA_QUESTIONS_CREATION:
                return this.vendorCriteriaQuestionsCreationStrategy;
            case ProcessFeature.VENDOR_SECURITY_REVIEW_ASSESSMENT:
                return this.vendorSecurityReviewAssessmentStrategy;
            default:
                throw new Error(`No strategy found for process feature: ${processFeature}`);
        }
    }
}

import { ApiProperty } from '@nestjs/swagger';

export class CreateTrainingRequestDto {
    @ApiProperty({
        type: 'string',
        example: '<EMAIL>',
        description: 'Email of the personnel that completed the training',
        required: true,
    })
    email: string;

    @ApiProperty({
        type: 'string',
        format: 'date-time',
        example: '2021-01-01T00:00:00.000Z',
        description: 'Completion date',
        required: true,
    })
    completionDate: Date;

    @ApiProperty({
        type: 'string',
        format: 'binary',
        description: 'Evidence file',
        required: true,
    })
    evidence: string; // TODO: https://drata.atlassian.net/browse/ENG-75075
}

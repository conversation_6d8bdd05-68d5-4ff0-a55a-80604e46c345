/* eslint-disable no-await-in-loop */
import { DescribeTasksCommandOutput, ECS, RunTaskCommandInput } from '@aws-sdk/client-ecs';
import { CheckStatus, RegionType } from '@drata/enums';
import { BadRequestException, ConflictException, Injectable } from '@nestjs/common';
import { AutopilotTaskService } from 'app/autopilot/autopilot-task.service';
import { AutopilotAccountTaskBuilder } from 'app/autopilot/builders/autopilot-account-task.builder';
import { AutopilotDatabaseType } from 'app/autopilot/enums/autopilot-database.enum';
import { AutopilotRunType } from 'app/autopilot/enums/autopilot-run-type.enum';
import { AutopilotTraceState } from 'app/autopilot/enums/autopilot-trace-state.enum';
import { Stage } from 'app/autopilot/enums/stage.enum';
import { AutopilotRunnerArgs } from 'app/autopilot/interfaces/autopilot-cli.interface';
import { IAutopilotTask } from 'app/autopilot/interfaces/autopilot-task.interface';
import { AutopilotTrace } from 'app/autopilot/interfaces/autopilot-trace.interface';
import { Autopilot } from 'app/autopilot/runner/instances/autopilot';
import { AutopilotTask } from 'app/autopilot/tasks/autopilot.task';
import { Product } from 'app/companies/products/entities/product.entity';
import { WorkspacesCoreService } from 'app/companies/products/services/workspaces-core.service';
import { GrcCoreService } from 'app/grc/core/services/grc-core.service';
import { CodeReviewService } from 'app/iac-scanning/services/code-review/code-review.service';
import { MonitorInstance } from 'app/monitors/entities/monitor-instance.entity';
import { updateMonitoringSummaryResultsIndexForAccount } from 'app/monitors/helpers/monitoring-summary-indexing.helper';
import { MonitoringSummaryIndexingService } from 'app/monitors/services/monitoring-summary-indexing.service';
import { TaskRuntimeStatService } from 'app/stats/services/task-runtime-stat.service';
import { BackgroundCheckSynchronizationService } from 'app/synchronizations/services/background-check/background-check-synchronization.service';
import { CspmSynchronizationService } from 'app/synchronizations/services/cloud-security-posture-management/cspm-synchronization.service';
import { CodebaseRepoSynchronizationService } from 'app/synchronizations/services/codebase/codebase-repo-synchronization.service';
import { CyberInsuranceSynchronizationService } from 'app/synchronizations/services/cyber-insurance/cyber-insurance-synchronizations.service';
import { EDRSynchronizationService } from 'app/synchronizations/services/endpoint-detection-response/edr-synchronization.service';
import { HrisBackgroundCheckSynchronizationService } from 'app/synchronizations/services/hris/hris-background-check-synchronization.service';
import { IdentityGroupsSynchronizationService } from 'app/synchronizations/services/identity/identity-groups-synchronizations.service';
import { IdentitySynchronizationService } from 'app/synchronizations/services/identity/identity-synchronization.service';
import { InfrastructureSynchronizationService } from 'app/synchronizations/services/infrastructure/infrastructure-synchronization.service';
import { InfrastructureWarmupService } from 'app/synchronizations/services/infrastructure/infrastructure-warmup.service';
import { MobileDeviceManagementSynchronizationService } from 'app/synchronizations/services/mobile-device-management/mobile-device-management-synchronization.service';
import { ObservabilitySynchronizationService } from 'app/synchronizations/services/observability/observability-synchronization.service';
import { PolicySynchronizationService } from 'app/synchronizations/services/policy/policy-synchronization.service';
import { SecurityTrainingSynchronizationService } from 'app/synchronizations/services/security-training/security-training-synchronization.service';
import { OffboardingSynchronizationService } from 'app/synchronizations/services/ticketing/offboarding-synchronization.service';
import { TicketAutomateSynchronizationService } from 'app/synchronizations/services/ticketing/ticket-automate-synchronization.service';
import { UserAccessReviewSynchronizationService } from 'app/synchronizations/services/uar/user-access-review-synchronization.service';
import { VersionControlSynchronizationService } from 'app/synchronizations/services/version-control/version-control-synchronization.service';
import { VulnerabilitySynchronizationService } from 'app/synchronizations/services/vulnerability/vulnerability-synchronization.service';
import { Account } from 'auth/entities/account.entity';
import { AccountsService } from 'auth/services/accounts.service';
import { CacheBusterWithPrefix } from 'cache/cache.decorator';
import { CacheService } from 'cache/cache.service';
import { SyncCacheBusterWithPrefix } from 'cache/decorators/sync-cache-buster-with-prefix';
import { PolloAdapter } from 'commons/adapters/pollo.adapter';
import { Benchmark } from 'commons/benchmark/benchmark';
import { AccountStack } from 'commons/classes/account-stack.class';
import { DrataDataSource } from 'commons/classes/drata-data-source.class';
import { deprecatedControlTestIds } from 'commons/configs/deprecated-control-tests.config';
import { AccountStatus } from 'commons/enums/auth/account-status.enum';
import { ClientType } from 'commons/enums/auth/client-type.enum';
import { ProviderType } from 'commons/enums/auth/provider-type.enum';
import { Caches } from 'commons/enums/cache.enum';
import { Color } from 'commons/enums/color.enum';
import { LogIdentifierEvent } from 'commons/enums/log-identifier-event.enum';
import { LogIdentifierRecordType } from 'commons/enums/log-identifier-record-type.enum';
import { RunMode } from 'commons/enums/run-mode.enum';
import { ConnectionFactory } from 'commons/factories/connection.factory';
import { timeDiff } from 'commons/helpers/date.helper';
import {
    getEnvironmentName,
    getServerRegion,
    isLocal,
    isProd,
} from 'commons/helpers/environment.helper';
import { AxiosClientRetry } from 'commons/helpers/http.helper';
import { startLightRun } from 'commons/helpers/lightrun.helper';
import { setLoggingContext } from 'commons/helpers/logging-context.helper';
import { getMemoryUsageString } from 'commons/helpers/memory.helper';
import { getProductId } from 'commons/helpers/products.helper';
import { sleep } from 'commons/helpers/sleep.helper';
import { getDatadogAutopilotLogUrl } from 'commons/helpers/url.helper';
import { BaseService } from 'commons/services/base.service';
import config from 'config';
import { Communication } from 'dependencies/communication/communication';
import { FeatureFlagCategory } from 'feature-flags/enums/feature-flag-category.enum';
import { FeatureFlag } from 'feature-flags/feature-flag.enum';
import { FeatureFlagService } from 'feature-flags/feature-flag.service';
import { every, isEmpty, isNil, pick } from 'lodash';
import { ClsServiceManager, UseCls } from 'nestjs-cls';
import pluralize from 'pluralize';
import { PolloMessage } from 'pollo-logger/classes/pollo.message';
import { setTenancyContextForConnection } from 'tenancy/helpers/tenancy.helper';
import { APTestId } from 'tests/autopilot-suite-set/classes/ap-test-id.enum';
import { format } from 'util';
import { v4 as uuidv4 } from 'uuid';

@Injectable()
export class AutopilotRunnerService extends BaseService {
    private static RDS_BATCH_SIZE = config.get('api.autopilotTenantBatchSize');
    private static AURORA_BATCH_SIZE = config.get('api.autopilotAuroraTenantBatchSize');
    private static DATABASE_TYPE: AutopilotDatabaseType;
    private static WAIT_FOR = 1000;

    /**
     * The number of milliseconds that we wait to poll the ECS tasks
     * to determine if it has reached the target state
     */
    private static ECS_POLL_INTERVAL = 30000;

    /**
     * The number if iterations to poll the ECS tasks before we timeout
     * (60 = 30 minutes)
     * (480 = 4 hours)
     */
    private static ECS_POLL_MAX_COUNT = 480;
    private static ECS_STOPPED_STATE = 'STOPPED';

    private accountCountDownLatch = new Map<string, Account | null>();

    private static DEFAULT_ACCOUNT_LATCH_ENTRY = '0';

    private EcsConfig = class {
        cluster: string;
        taskDefinition: string;
        taskRevision?: number;

        async load() {
            if (!isNil(this.cluster)) {
                return this;
            }

            if (!isNil(config.get('aws.ecsContainerMetadataUri'))) {
                const metadataUri = config.get('aws.ecsContainerMetadataUri');
                const taskUrl = `${metadataUri}/task`;
                try {
                    /**
                     * Axios is kind enough to parse the response body as JSON since the metadata
                     * service gives a content type of application/json. Example response body:
                     *
                     * {"Cluster":"arn:aws:ecs:us-west-2:************:cluster/tyler-metadata-testing","TaskARN":"arn:aws:ecs:us-west-2:************:task/tyler-metadata-testing/fccb013daed3482c97cf62fb74063098","Family":"tyler-metadata-testing","Revision":"2","DesiredStatus":"RUNNING","KnownStatus":"RUNNING","Limits":{"CPU":0.25,"Memory":512},"PullStartedAt":"2021-11-03T16:27:42.020280047Z","PullStoppedAt":"2021-11-03T16:28:15.214243416Z","AvailabilityZone":"us-west-2a","Containers":[{"DockerId":"fccb013daed3482c97cf62fb74063098-4072158283","Name":"metadata-service-testing","DockerName":"metadata-service-testing","Image":"tpickett66/ecs-testing:ac97b28","ImageID":"sha256:4218b92663527d7ef499c3633c5da19eb4fb3c38d209f65123641c0f295838ce","Labels":{"com.amazonaws.ecs.cluster":"arn:aws:ecs:us-west-2:************:cluster/tyler-metadata-testing","com.amazonaws.ecs.container-name":"metadata-service-testing","com.amazonaws.ecs.task-arn":"arn:aws:ecs:us-west-2:************:task/tyler-metadata-testing/fccb013daed3482c97cf62fb74063098","com.amazonaws.ecs.task-definition-family":"tyler-metadata-testing","com.amazonaws.ecs.task-definition-version":"2"},"DesiredStatus":"RUNNING","KnownStatus":"RUNNING","Limits":{"CPU":2},"CreatedAt":"2021-11-03T16:28:19.830144873Z","StartedAt":"2021-11-03T16:28:19.830144873Z","Type":"NORMAL","Networks":[{"NetworkMode":"awsvpc","IPv4Addresses":["*************"],"AttachmentIndex":0,"MACAddress":"02:13:21:67:6a:d5","IPv4SubnetCIDRBlock":"***********/20","DomainNameServers":["**********"],"DomainNameSearchList":["us-west-2.compute.internal"],"PrivateDNSName":"ip-172-31-21-254.us-west-2.compute.internal","SubnetGatewayIpv4Address":"***********/20"}],"ContainerARN":"arn:aws:ecs:us-west-2:************:container/tyler-metadata-testing/fccb013daed3482c97cf62fb74063098/2699c114-f837-47fd-81bd-92921303a8df","LogOptions":{"awslogs-group":"/ecs/tyler-metadata-testing","awslogs-region":"us-west-2","awslogs-stream":"ecs/metadata-service-testing/fccb013daed3482c97cf62fb74063098"},"LogDriver":"awslogs"}],"LaunchType":"FARGATE","ClockDrift":{"ClockErrorBound":0.4249465,"ReferenceTimestamp":"2021-11-03T16:28:19Z","ClockSynchronizationStatus":"SYNCHRONIZED"}}
                     *
                     */
                    const responseData = (await AxiosClientRetry().get(taskUrl)).data;

                    this.cluster = responseData.Cluster;
                    this.taskDefinition = responseData.Family;
                    this.taskRevision = responseData.Revision;
                } catch (e) {
                    throw new Error(
                        `Error fetching task configuration from metadata service: ${e.message}`,
                    );
                }

                return this;
            } else {
                throw new Error(
                    'Unable to construct ECS run configuration, need values from config yaml or ECS_CONTAINER_METADATA_URI env var.',
                );
            }
        }
    };

    constructor(
        private readonly accountsService: AccountsService,
        private readonly communication: Communication,
        private readonly personnelSyncService: IdentitySynchronizationService,
        private readonly groupsSyncService: IdentityGroupsSynchronizationService,
        private readonly usersVersionControlSyncService: VersionControlSynchronizationService,
        private readonly infrastructureSync: InfrastructureSynchronizationService,
        private readonly mobileDeviceManagementSync: MobileDeviceManagementSynchronizationService,
        private readonly securityTrainingSync: SecurityTrainingSynchronizationService,
        private readonly observabilitySyncService: ObservabilitySynchronizationService,
        private readonly autopilot: AutopilotTask<any>,
        private readonly autopilotTaskService: AutopilotTaskService,
        private readonly hrisBackgroundCheckSync: HrisBackgroundCheckSynchronizationService,
        private readonly workspacesCoreService: WorkspacesCoreService,
        private readonly offboardingSyncService: OffboardingSynchronizationService,
        private readonly policySynchronizationService: PolicySynchronizationService,
        private readonly vulnerabilitySynchronizationService: VulnerabilitySynchronizationService,
        private readonly ticketAutomateSynchronizationService: TicketAutomateSynchronizationService,
        private readonly featureFlag: FeatureFlagService,
        private readonly cacheService: CacheService,
        private readonly edrSynchronizationService: EDRSynchronizationService,
        private readonly cspmSynchronizationService: CspmSynchronizationService,
        private readonly backgroundCheckSynchronizationService: BackgroundCheckSynchronizationService,
        private readonly userAccessReviewSynchronizationService: UserAccessReviewSynchronizationService,
        private readonly codebaseRepositorySynchronizationService: CodebaseRepoSynchronizationService,
        private readonly infrastructureWarmupService: InfrastructureWarmupService,
        private readonly cyberInsuranceSyncService: CyberInsuranceSynchronizationService,
        private readonly grcCoreService: GrcCoreService,
        private readonly monitoringSummaryIndexingService: MonitoringSummaryIndexingService,
        private readonly codeReviewService: CodeReviewService,
        private readonly taskRuntimeStatService: TaskRuntimeStatService,
    ) {
        super();
    }

    /**
     *
     * @param atomic
     * @param testIds
     * @param accountIds
     * @param accountDomains
     * @param productIds
     * @param runId
     * @param runTypeAuto
     * @param async
     * @param isRetest
     * @param tenantDb
     * @param tenantBatchSize
     */
    @UseCls<[AutopilotRunnerArgs]>({
        generateId: true,
        idGenerator: function (this: AutopilotRunnerService) {
            return `drata-ap-${uuidv4()}`;
        },
    })
    async start({
        atomic,
        testIds = [],
        accountIds = [],
        accountDomains = [],
        productIds = [],
        runId = null,
        runTypeAuto = false,
        async = false,
        isRetest = false,
        tenantDb = null,
        tenantBatchSize = null,
        auroraTenantBatchSize = null,
    }: AutopilotRunnerArgs): Promise<void> {
        if (!isLocal() && !config.get('temporal.enabled')) {
            throw new Error('Temporal must be enabled to run Autopilot');
        }

        /**
         * Log the parameters for the start
         */
        this.log(`Autopilot: start`, null, {
            atomic,
            testIds,
            accountIds,
            accountDomains,
            productIds,
            runId,
            runTypeAuto,
            async,
            isRetest,
            tenantDb,
            tenantBatchSize,
            auroraTenantBatchSize,
        });

        this.sanityChecks(
            atomic,
            isRetest,
            accountIds,
            accountDomains,
            tenantDb,
            tenantBatchSize,
            auroraTenantBatchSize,
        );

        const benchmark = new Benchmark();

        // run individual ECS task for each specified domain or when is autopilot launcher
        const shouldRunAtomicTasks = atomic && !isLocal();
        const isTenantAutopilotLauncher = !!(tenantDb && !isLocal());
        const autopilotTrace = this.getAutopilotTrace(isRetest, runTypeAuto, runId);
        const hasQueryValues = !isEmpty(accountDomains) || !isEmpty(accountIds);

        if (isTenantAutopilotLauncher) {
            this.log(
                'Autopilot: Will Launch a ECS task!',
                undefined,
                {
                    tenantDb,
                    autopilotTrace: {
                        ...autopilotTrace,
                        runMarker: AutopilotTraceState.MARKER_TENANT_LAUNCH,
                    },
                },
                benchmark,
            );
            AutopilotRunnerService.DATABASE_TYPE = tenantDb.toLowerCase().includes('aurora')
                ? AutopilotDatabaseType.AURORA
                : AutopilotDatabaseType.RDS;
        }

        this.autopilot.setRunType(autopilotTrace.runType);
        this.autopilot.setRunId(autopilotTrace.runId);

        // when autopilot launcher
        if (shouldRunAtomicTasks && !hasQueryValues) {
            await this.sendAutopilotKickOffMessage(autopilotTrace.runId);
            await this.createTenantLauncherEcsTasks(
                autopilotTrace,
                async,
                productIds,
                autopilotTrace.runId,
                testIds,
                tenantBatchSize,
                auroraTenantBatchSize,
            );

            this.log('Autopilot: FINISHED!', null, {
                autopilotTrace: {
                    ...autopilotTrace,
                    runMarker: AutopilotTraceState.MARKER_LAUNCH,
                },
            });

            return;
        }

        const { queryBy, queryValues } = this.getQueryParameters(accountDomains, accountIds);

        const accounts = await this.getAccountsForAutopilotRun(
            queryBy,
            queryValues,
            tenantDb,
            autopilotTrace,
            isTenantAutopilotLauncher,
        );

        let successfulRun = false;

        if (!isEmpty(accounts)) {
            if (accounts.length > 1 && productIds.length > 1) {
                const error = new Error(
                    'Only one account can be given when multiple productIds provided.',
                );
                this.error(error);
                throw error;
            }

            await this.doAutopilot(
                autopilotTrace,
                isTenantAutopilotLauncher || shouldRunAtomicTasks,
                accounts,
                productIds,
                testIds,
                queryValues,
                async,
                autopilotTrace.runId,
            );

            this.log('Autopilot: Waiting latch to reach zero!', undefined, {
                autopilotTrace: {
                    ...autopilotTrace,
                    runMarker: AutopilotTraceState.MARKER_LAUNCH,
                },
            });

            /**
             * Wait until the latch is empty
             */
            await this.halt();

            successfulRun = true;

            benchmark.end();
            autopilotTrace.runTime = benchmark.time();

            this.log(
                'Autopilot: FINISHED!',
                undefined,
                {
                    tenantDb,
                    autopilotTrace: {
                        ...autopilotTrace,
                        runMarker: AutopilotTraceState.MARKER_FINISHED,
                    },
                },
                benchmark,
            );
        }

        if (
            this.launcherFinished(
                testIds,
                queryValues,
                shouldRunAtomicTasks && !hasQueryValues,
                isTenantAutopilotLauncher,
            )
        ) {
            if (successfulRun) {
                await this.communication.sendMessage(
                    '',
                    this.getFinishedSlackMessageAttachment(
                        accounts,
                        testIds,
                        benchmark.timeString(),
                        autopilotTrace.runId,
                    ),
                );
                return;
            }

            await this.communication.sendMessage(
                'There are no accounts! Uh Oh! Time to raise more $$$',
                null,
            );
        }
    }

    /**
     *
     * @param isRetest
     * @param accountIds
     * @param accountDomains
     * @param tenantBatchSize
     */
    sanityChecks(
        atomic: boolean,
        isRetest: boolean,
        accountIds: string[],
        accountDomains: string[],
        tenantDb: string,
        tenantBatchSize: number,
        auroraTenantBatchSize: number,
    ) {
        if (atomic && !isEmpty(tenantDb)) {
            const error = new Error('--atomic cannot be used when tenant db is specified');
            this.error(error);
            throw error;
        }

        if (!isRetest && !config.get('api.autopilotEnabled')) {
            const error = new Error('Autopilot is not enabled for this environment');
            this.error(error);
            throw error;
        }

        if (every([accountIds, accountDomains], arr => !isEmpty(arr))) {
            const error = new Error('Only one account query type is supported');
            this.error(error);
            throw error;
        }

        if (tenantBatchSize) {
            if (tenantBatchSize > AutopilotRunnerService.RDS_BATCH_SIZE) {
                const error = new Error(
                    `Warning: Max ${AutopilotRunnerService.RDS_BATCH_SIZE} concurrent accounts can be run per RDS db host. ` +
                        `Please choose ${AutopilotRunnerService.RDS_BATCH_SIZE} or less.`,
                );
                this.error(error);
                throw error;
            }
            AutopilotRunnerService.RDS_BATCH_SIZE = tenantBatchSize;
        }

        if (auroraTenantBatchSize) {
            if (tenantBatchSize > AutopilotRunnerService.AURORA_BATCH_SIZE) {
                const error = new Error(
                    `Warning: Max ${AutopilotRunnerService.AURORA_BATCH_SIZE} concurrent accounts can be run per Aurora db host. ` +
                        `Please choose ${AutopilotRunnerService.AURORA_BATCH_SIZE} or less.`,
                );
                this.error(error);
                throw error;
            }
            AutopilotRunnerService.AURORA_BATCH_SIZE = auroraTenantBatchSize;
        }
    }

    /**
     *
     * @param isRetest
     * @param runTypeAuto
     * @param runId
     */
    getAutopilotTrace(isRetest: boolean, runTypeAuto: boolean, runId: string): AutopilotTrace {
        /**
         * https://drata.atlassian.net/browse/ENG-4685
         */
        let runType = AutopilotRunType.CLI;

        /**
         * We are retesting so this is from the app
         */
        if (isRetest) {
            runType = AutopilotRunType.APP;
        } else {
            if (runTypeAuto) {
                runType = AutopilotRunType.AUTO;
            }
        }

        const autopilotRunId = runId || this.autopilot.getId();

        return {
            testId: null,
            runId: autopilotRunId,
            accountRunId: null,
            taskRunId: null,
            runTime: null,
            accountRunTime: null,
            taskRunTime: null,
            runType,
            runMarker: null,
            productId: null,
        };
    }

    /**
     *
     * @param autopilotTrace
     * @param async
     * @param productIds
     * @param autopilotRunId
     * @param testIds
     * @param tenantBatchSize
     */
    async createTenantLauncherEcsTasks(
        autopilotTrace: AutopilotTrace,
        async: boolean,
        productIds: number[],
        autopilotRunId: string,
        testIds: any[],
        tenantBatchSize: number,
        auroraTenantBatchSize: number,
    ): Promise<void> {
        const tenantDbs = await this.accountsService.getAccountsHostsByStatus(AccountStatus.ACTIVE);

        this.log('Autopilot: creating tenant db ECS tasks', null, {
            autopilotTrace: {
                ...autopilotTrace,
                runMarker: AutopilotTraceState.MARKER_LAUNCH,
                tenantDbs: tenantDbs,
            },
        });

        if (!isEmpty(tenantDbs)) {
            const tasksToStart = [];

            for (const dbHost of tenantDbs) {
                tasksToStart.push(
                    this.runEcsTask(
                        null,
                        async,
                        productIds,
                        autopilotRunId,
                        testIds,
                        dbHost,
                        tenantBatchSize,
                        auroraTenantBatchSize,
                    ),
                );
            }

            try {
                const tasks = await (Promise as any).allSettled(tasksToStart);

                for (const finishedTask of tasks) {
                    if (finishedTask.status === 'rejected') {
                        this.error(finishedTask.reason, null, {
                            autopilotTrace: {
                                ...autopilotTrace,
                                runMarker: AutopilotTraceState.MARKER_LAUNCH,
                                launchFailed: true,
                            },
                        });
                    }
                }
            } catch (err) {
                this.error(err, null, {
                    autopilotTrace: {
                        ...autopilotTrace,
                        runMarker: AutopilotTraceState.MARKER_LAUNCH,
                        reason: err,
                        launchFailed: true,
                    },
                });
            }
        } else {
            const message = 'There are no tenant databases! Where did all the tenant dbs go?';
            await this.communication.sendMessage(message, null);
        }
    }

    /**
     *
     * @param accountDomains
     * @param accountIds
     */
    getQueryParameters(
        accountDomains: string[],
        accountIds: string[],
    ): { queryBy: 'id' | 'domain'; queryValues: string[] } {
        let queryBy: any = 'domain';
        let queryValues = accountDomains;

        if (!isEmpty(accountIds)) {
            queryBy = 'id';
            queryValues = accountIds;
        }

        return { queryBy, queryValues };
    }

    /**
     *
     * @param queryBy
     * @param queryValues
     * @param tenantDb
     * @param autopilotTrace
     * @param isTenantAutopilotLauncher
     */
    async getAccountsForAutopilotRun(
        queryBy: 'domain' | 'id',
        queryValues: string[],
        tenantDb: string,
        autopilotTrace: AutopilotTrace,
        isTenantAutopilotLauncher: boolean,
    ): Promise<Account[]> {
        if (tenantDb) {
            this.log(`Autopilot: getting all accounts for tenant ${tenantDb}`, null, {
                tenantDb,
                autopilotTrace: {
                    ...autopilotTrace,
                    runMarker: AutopilotTraceState.MARKER_TENANT_LAUNCH,
                },
            });

            return this.accountsService.getActiveAccountsByColumn(
                queryBy,
                queryValues,
                tenantDb,
                undefined,
                undefined,
                false,
            );
        } else {
            this.log(`Autopilot: getting all accounts for this autopilot run`, null, {
                autopilotTrace: {
                    ...autopilotTrace,
                    runMarker: isTenantAutopilotLauncher
                        ? AutopilotTraceState.MARKER_TENANT_LAUNCH
                        : AutopilotTraceState.MARKER_START,
                    queryValues: queryValues,
                },
            });

            return this.accountsService.getActiveAccountsByColumn(
                queryBy,
                queryValues,
                undefined,
                undefined,
                undefined,
                false,
            );
        }
    }

    /**
     *
     * @param autopilotTrace
     * @param isTenantAutopilotLauncher
     * @param accounts
     * @param productIds
     * @param testIds
     * @param queryValues
     * @param async
     * @param autopilotRunId
     */
    async doAutopilot(
        autopilotTrace: AutopilotTrace,
        isTenantAutopilotLauncher: boolean,
        accounts: Account[],
        productIds: number[],
        testIds: number[],
        queryValues: string[],
        async: boolean,
        autopilotRunId: string,
    ) {
        const runMarker = isTenantAutopilotLauncher
            ? AutopilotTraceState.MARKER_TENANT_LAUNCH
            : AutopilotTraceState.MARKER_START;

        this.log('Autopilot: STARTED!', undefined, {
            tenantDb: accounts[0].databaseHost,
            autopilotTrace: {
                ...autopilotTrace,
                runMarker,
            },
        });

        if (isEmpty(testIds) && isEmpty(queryValues) && isTenantAutopilotLauncher) {
            await this.communication.sendMessage(
                '',
                this.getStartedSlackMessageAttachment(accounts, productIds, autopilotTrace.runId),
            );
        }

        let autopilotSerializedGroupDomains = null;
        if (config.has('api.autopilotSerializedGroupDomains')) {
            autopilotSerializedGroupDomains = config.get('api.autopilotSerializedGroupDomains');
        }
        const accountStack = new AccountStack(accounts, autopilotSerializedGroupDomains);
        accountStack.setLoggerResources(this.logger, autopilotTrace);

        while (accountStack.hasItems()) {
            /**
             * Get the next account to run/launch
             */
            const activeDomains = Array.from(this.accountCountDownLatch.keys());
            const item = accountStack.pop(activeDomains);
            if (!item) {
                this.log(`Autopilot: No account ready to process, waiting..`, undefined, {
                    autopilotTrace: {
                        ...autopilotTrace,
                        runMarker,
                    },
                });
                // only serialized accounts remain, but one is actively running already
                await sleep(AutopilotRunnerService.WAIT_FOR);
                continue;
            }
            const { account } = item;
            autopilotTrace.accountRunId = account.id;
            this.log(`Autopilot: Next account to process`, account, {
                autopilotTrace: {
                    ...autopilotTrace,
                    domain: account.domain,
                    runMarker,
                },
            });

            /**
             * Required for CLS context - we are not able to await in the run method.
             */
            const connection = await ConnectionFactory.getConnection(account);

            this.runOrLaunch(
                testIds,
                isTenantAutopilotLauncher,
                account,
                async,
                productIds,
                autopilotRunId,
                autopilotTrace,
                connection,
            );
            await this.wait();

            this.log(`Autopilot: Latch has opened, continuing to next account`, undefined, {
                autopilotTrace: {
                    ...autopilotTrace,
                    runMarker,
                },
            });
        }
        this.log(`Autopilot: Account stack drained..`, undefined, {
            autopilotTrace: {
                ...autopilotTrace,
                runMarker,
            },
        });
    }

    /**
     *
     * @param testIds
     * @param isTenantAutopilotLauncher
     * @param account
     * @param async
     * @param productIds
     * @param autopilotRunId
     * @param autopilotTrace
     */
    runOrLaunch(
        testIds: number[],
        shouldCreateEcsTaskForEachDomain: boolean,
        account: Account,
        async: boolean,
        productIds: number[],
        autopilotRunId: string,
        autopilotTrace: AutopilotTrace,
        connection: DrataDataSource,
    ) {
        if (isEmpty(testIds)) {
            if (shouldCreateEcsTaskForEachDomain) {
                this.log(`Autopilot: Launching ECS task for ${account.domain}`, null, {
                    tenantDb: account.databaseHost,
                    autopilotTrace: {
                        ...autopilotTrace,
                        runMarker: AutopilotTraceState.MARKER_TENANT_LAUNCH,
                    },
                });

                void this.runEcsTask(
                    account,
                    async,
                    productIds,
                    autopilotRunId,
                    null,
                    null,
                    null,
                    null,
                    shouldCreateEcsTaskForEachDomain,
                );
            } else {
                this.log(`Autopilot: Running autopilot for ${account.domain}`, null, {
                    tenantDb: account.databaseHost,
                    autopilotTrace: {
                        ...autopilotTrace,
                        runMarker: AutopilotTraceState.MARKER_START,
                    },
                });

                const cls = ClsServiceManager.getClsService();
                cls.run(() => {
                    setLoggingContext(cls, {
                        traceId: cls.getId(),
                        domain: account.domain,
                        tenantHost: account.getDatabaseHost(),
                    });

                    setTenancyContextForConnection(cls, account, connection, cls.getId());
                    void this.run(account, null, productIds, autopilotTrace, async);
                });
            }
        } else {
            if (shouldCreateEcsTaskForEachDomain) {
                this.log(`Autopilot: Launching ECS task for ${account.domain}`, null, {
                    tenantDb: account.databaseHost,
                    autopilotTrace: {
                        ...autopilotTrace,
                        runMarker: AutopilotTraceState.MARKER_TENANT_LAUNCH,
                    },
                });

                void this.runEcsTask(
                    account,
                    async,
                    productIds,
                    autopilotRunId,
                    testIds,
                    null,
                    null,
                    null,
                    shouldCreateEcsTaskForEachDomain,
                );
            } else {
                for (const testId of testIds) {
                    this.log(
                        `Autopilot: Running autopilot for ${account.domain}, Test ID: ${testId}`,
                        null,
                        {
                            tenantDb: account.databaseHost,
                            autopilotTrace: {
                                ...autopilotTrace,
                                runMarker: AutopilotTraceState.MARKER_START,
                            },
                        },
                    );

                    if (deprecatedControlTestIds.includes(testId)) {
                        this.logger.warn(
                            PolloAdapter.acct(
                                `[AutopilotTest]: ${APTestId[testId]} NOT AVAILABLE`,
                                account,
                            ),
                        );
                        continue;
                    }

                    const cls = ClsServiceManager.getClsService();

                    cls.run(() => {
                        setLoggingContext(cls, {
                            traceId: cls.getId(),
                            domain: account.domain,
                            tenantHost: account.getDatabaseHost(),
                        });

                        setTenancyContextForConnection(cls, account, connection, cls.getId());
                        void this.run(account, testId, productIds, autopilotTrace, async);
                    });
                }
            }
        }
    }

    async sendAutopilotKickOffMessage(autopilotRunId: string) {
        if (!isProd()) {
            return;
        }
        try {
            const commitId = process.env.COMMIT_ID;
            let region: string;

            if (getServerRegion() === RegionType.NA) {
                region = 'US :flag-us:';
            } else if (getServerRegion() === RegionType.APAC) {
                region = 'AU :flag-au:';
            } else {
                region = 'EU :flag-eu:';
            }
            if (config.has('slack.prodDeploymentWebhookUrl')) {
                await this.communication.sendMessage(
                    '',
                    this.communication.buildMessageAttachment(
                        [
                            {
                                header: 'Autopilot 1.0 started :rocket:',
                                item: '',
                            },
                            {
                                header: 'Region',
                                item: region,
                            },
                            {
                                header: 'Commit ID',
                                item: commitId,
                            },
                        ],
                        'Keep that in mind when running backfills and migrations',
                        'View logs',
                        getDatadogAutopilotLogUrl(false, autopilotRunId),
                        Color.SAPPHIRE_BLUE,
                    ),
                    config.get('slack.prodDeploymentWebhookUrl'),
                );
            }
        } catch (error) {
            this.logger.error(
                PolloMessage.msg(
                    'Failed to send slack notification about Autopilot start',
                    this.constructor.name,
                )
                    .setIdentifier({ runId: autopilotRunId })
                    .setError(error),
            );
        }
    }

    /**
     *
     * @param accounts
     * @param productIds
     */
    getStartedSlackMessageAttachment(
        accounts: Account[],
        productIds: number[],
        autopilotRunId: string,
    ) {
        return this.communication.buildMessageAttachment(
            [
                {
                    header: 'Accounts',
                    item: accounts.length.toString(),
                },
                { header: 'Test IDs', item: '-' },
                {
                    header: 'Product IDs',
                    item: isEmpty(productIds) ? '-' : productIds.join(', '),
                },
                {
                    header: 'Database Host',
                    item: accounts[0].databaseHost,
                },
            ],
            `Starting Autopilot for ${pluralize('account', accounts.length, true)}!`,
            'Datadog Logs',
            getDatadogAutopilotLogUrl(false, autopilotRunId),
            Color.SAPPHIRE_BLUE,
        );
    }

    /**
     *
     * @param accounts
     * @param testIds
     */
    getFinishedSlackMessageAttachment(
        accounts: Account[],
        testIds: number[],
        timeUsed: string,
        autopilotRunId: string,
    ) {
        return this.communication.buildMessageAttachment(
            [
                {
                    header: 'Accounts',
                    item: accounts.length.toString(),
                },
                {
                    header: 'Test IDs',
                    item: isEmpty(testIds) ? '-' : testIds.join(', '),
                },
                {
                    header: 'Database Host',
                    item: accounts[0].databaseHost,
                },
            ],
            `Finished running Autopilot for ${pluralize(
                'account',
                accounts.length,
                true,
            )} in ${timeUsed}!`,
            'Datadog Logs',
            getDatadogAutopilotLogUrl(false, autopilotRunId),
            Color.GREEN_PANTONE,
        );
    }

    async sendSlackErrorMessage(message: string, account?: Account, runId?: string): Promise<void> {
        const NA = 'N/A';
        await this.communication.sendMessage(
            '',
            this.communication.buildMessageAttachment(
                [
                    {
                        header: 'Account ID',
                        item: account?.id || NA,
                    },
                    {
                        header: 'Account Domain',
                        item: account?.domain || NA,
                    },
                    {
                        header: 'Run Id',
                        item: runId || NA,
                    },
                ],
                message,
                'Datadog Logs',
                getDatadogAutopilotLogUrl(true),
                Color.IMPERIAL_RED,
            ),
        );
    }

    /**
     *
     * @param testIds
     * @param queryValues
     * @param isAutopilotLauncher
     * @param isTenantAutopilotLauncher
     */
    launcherFinished(
        testIds: number[],
        queryValues: string[],
        isAutopilotLauncher: boolean,
        isTenantAutopilotLauncher: boolean,
    ) {
        return (
            isEmpty(testIds) &&
            isEmpty(queryValues) &&
            (isAutopilotLauncher || isTenantAutopilotLauncher)
        );
    }

    /**
     *
     * @param account
     * @param testId
     * @param productIds
     * @param autopilotTrace
     * @param async
     * @returns
     */
    async run(
        account: Account,
        testId: number,
        productIds: number[],
        autopilotTrace: AutopilotTrace,
        async: boolean,
    ): Promise<void> {
        /**
         * Tell the latch we've started
         */
        this.started(account);

        const totalBenchmark = new Benchmark();
        this.log(`Start Autopilot for ${account.domain}`, account, {
            event: LogIdentifierEvent.autopilotStart,
            testId,
            tenantDb: account.databaseHost,
            autopilotTrace: {
                ...autopilotTrace,
                accountRunMarker: AutopilotTraceState.MARKER_START,
            },
        });

        const id = await this.taskRuntimeStatService.logTaskStart(
            {
                traceId: autopilotTrace.taskRunId,
                benchmark: totalBenchmark,
                runId: autopilotTrace.runId,
                accountId: account.id,
                logIdentifier: LogIdentifierRecordType.AP,
            },
            account,
        );

        /**
         * This call returns all products or the primary product for an account
         */
        const products = await this.workspacesCoreService.getProductsForAccount(
            account,
            productIds,
        );

        try {
            const syncBenchmark = new Benchmark();
            if (isNil(testId)) {
                // reset tests stuck in testing state beyond max allowed
                await this.autopilotTaskService.resetTestsStuckInTesting(account);

                // set this context for ap auto sync tracing only
                const cls = ClsServiceManager.getClsService();
                cls.set('runId', autopilotTrace.runId);
                await this.runSyncs(account, autopilotTrace, products);
            }

            await this.warmupServices(account, products);

            syncBenchmark.endTime();
            const monitorBenchmark = new Benchmark();

            const domain = account.domain;
            let shouldUpdateMonitoringResultsIndexForAccount = false;

            for (const product of products) {
                const monitors: Array<MonitorInstance> = [];
                account.setCurrentProduct(product);

                /**
                 * We need to ensure that the autopilot process is thread-safe here. If the products
                 * count is greater that 1 we are in a multi-product run. The con-current nature of
                 * AP deems setting the product within the account as not thread-local. If we are in
                 * a multi-product run we are required to clone the account for each product run.
                 */
                const accountWithProduct = account.cloneWithProduct(product);
                autopilotTrace.productId = getProductId(accountWithProduct);

                /**
                 * Build the monitors we need here from the service as some
                 * of the monitors might be disabled or excused from the
                 * autopilot run - check for enabled as true.
                 */
                const allMonitors = await this.autopilotTaskService.getMonitors(
                    accountWithProduct,
                    testId,
                    false, //includeDrafts
                );

                /**
                 * https://drata.atlassian.net/browse/ENG-30950
                 *
                 * Remove any monitors when AP2 is enabled for the account and the monitors are adapted
                 * for AP2 as we do not want AP1 to handle these mapped assessments - these will be
                 * handled in the AP2 flow
                 */

                const connections =
                    await this.autopilotTaskService.getActiveConnectionsWithExclusions(
                        accountWithProduct,
                    );

                this.log('Connections count', accountWithProduct, {
                    count: connections.length,
                    autopilotTrace,
                });

                for (const monitor of allMonitors) {
                    const { controlTestInstance } = monitor;
                    const { runMode } = controlTestInstance;

                    const runModeToSkip = [RunMode.AP2, RunMode.AP2_SHADOW_TEST];

                    const monitorRunMode = Autopilot.getMonitorAPRunMode(
                        connections,
                        controlTestInstance,
                    );
                    const shouldSkipAP1Run = runModeToSkip.includes(monitorRunMode);

                    if (shouldSkipAP1Run) {
                        this.log(
                            'Monitor/Control Instance will run in AP2, skipping AP1 for this assessment',
                            accountWithProduct,
                            {
                                assessmentId: controlTestInstance.testId,
                                monitorAp2EnabledAt: controlTestInstance.ap2EnabledAt,
                                runMode: RunMode[runMode],
                                autopilotTrace,
                            },
                        );
                    } else {
                        monitors.push(monitor);
                    }
                }

                if (isEmpty(monitors)) {
                    this.warn(
                        `No active monitors found for ${domain} and productId ${product.id}`,
                        accountWithProduct,
                        {
                            monitorCount: monitors.length,
                            productId: product.id,
                            autopilotTrace,
                        },
                    );
                } else {
                    shouldUpdateMonitoringResultsIndexForAccount = true;
                    this.log('Running AP1 for monitors of a product', accountWithProduct, {
                        monitors: monitors.map(monitor =>
                            pick(monitor, [
                                'id',
                                'autopilotTaskType',
                                'controlTestInstance.testId',
                                'runMode',
                            ]),
                        ),
                        monitorCount: monitors.length,
                        productId: product.id,
                        autopilotTrace,
                    });

                    let accountTask: IAutopilotTask<any>;

                    try {
                        accountTask = await AutopilotAccountTaskBuilder.for(
                            accountWithProduct,
                        ).fill(
                            monitors,
                            connections,
                            this.autopilot,
                            this.featureFlag,
                            this.codeReviewService,
                        );
                    } catch (error) {
                        this.error(error, account, autopilotTrace);
                        continue;
                    }

                    await this.autopilot.add(accountTask);

                    await this.autopilotTaskService.setControlTestInstancesState(
                        accountWithProduct,
                        monitors.map(monitor => monitor.controlTestInstance),
                    );

                    /**
                     * We are able to simply populate the tree here and wait until all
                     * accounts are pulled in via a latch - but in order to maintain
                     * concurrency and add future support for task clustering - start
                     * the account tasks as they are received and do not wait for full
                     * identity download for all accounts to start work ...
                     */

                    const count = accountTask.getTasks().length;

                    if (isNil(testId)) {
                        await this.autopilotTaskService.syncComplianceCheckExclusions(account);
                    }

                    this.log(
                        `Starting ${count} account tasks for (domain, productId) = (${domain}, ${product.id})`,
                        accountWithProduct,
                        { autopilotTrace },
                    );

                    if (await this.shouldRunInBatches(account)) {
                        await this.autopilot.startInBatches(accountTask);
                    } else {
                        if (async) {
                            await this.autopilot.start(accountTask);
                        } else {
                            await this.autopilot.startAndWait(accountTask);
                        }
                    }
                }

                if (isNil(testId)) {
                    // running ticket sync for each product when productIds has more than one value
                    this.log(
                        `Running ticket automate sync for ${account.domain} and product ${product.name}`,
                        account,
                        { autopilotTrace },
                    );

                    this.logger.log(
                        PolloAdapter.acct('Ticket automate sync memory usage', account)
                            .setContext('Before')
                            .setIdentifier(getMemoryUsageString()),
                    );

                    try {
                        await this.ticketAutomateSynchronizationService.start(account);
                    } catch (error) {
                        void this.onCaughtSyncError(
                            error,
                            account,
                            autopilotTrace,
                            Stage.TICKET_AUTOMATE,
                        );
                    }

                    this.logger.log(
                        PolloAdapter.acct('Ticket automate sync memory usage', account)
                            .setContext('After')
                            .setIdentifier(getMemoryUsageString()),
                    );
                }
            }

            if (isNil(testId)) {
                await this.autopilotTaskService.emailControlOwners(account, products);
                await this.autopilotTaskService.emailControlTestErrors(account, products);
            }

            if (shouldUpdateMonitoringResultsIndexForAccount && isNil(testId)) {
                /**
                 * can't delegate to another thread (via eventbus) because app will end before indexing happens.
                 */
                await updateMonitoringSummaryResultsIndexForAccount(
                    this.monitoringSummaryIndexingService,
                    account,
                    {
                        calculateFindingsAndExclusionsCounts: false,
                    },
                );
            }

            monitorBenchmark.end();

            if (isNil(testId)) {
                const uarBenchmark = new Benchmark();

                this.log(`Running User Access Review sync for ${account.domain}`, account, {
                    autopilotTrace,
                });

                this.logger.log(
                    PolloAdapter.acct('User Access Review sync memory usage', account)
                        .setContext('Before')
                        .setIdentifier(getMemoryUsageString()),
                );

                try {
                    await this.userAccessReviewSynchronizationService.start(account);
                } catch (error) {
                    await this.onCaughtSyncError(error, account, autopilotTrace, Stage.UAR);
                }

                this.logger.log(
                    PolloAdapter.acct('User Access Review sync memory usage', account)
                        .setContext('After')
                        .setIdentifier(getMemoryUsageString()),
                );

                uarBenchmark.end();
                totalBenchmark.end();
                autopilotTrace.accountRunTime = totalBenchmark.time();

                this.log(
                    `Finished Autopilot with UAR for ${domain}`,
                    account,
                    {
                        event: LogIdentifierEvent.autopilotAndUarComplete,
                        tenantDb: account.databaseHost,
                        syncDuration: syncBenchmark.time(),
                        monitorDuration: monitorBenchmark.time(),
                        uarDuration: uarBenchmark.time(),
                        autopilotTrace: {
                            ...autopilotTrace,
                            accountRunMarker: AutopilotTraceState.MARKER_FINISHED,
                        },
                    },
                    totalBenchmark,
                );
            } else {
                totalBenchmark.end();

                this.log(
                    `Finished Autopilot for ${domain}`,
                    account,
                    {
                        event: LogIdentifierEvent.autopilotComplete,
                        syncDuration: syncBenchmark.time(),
                        monitorDuration: monitorBenchmark.time(),
                        tenantDb: account.databaseHost,
                        autopilotTrace: {
                            ...autopilotTrace,
                            accountRunMarker: AutopilotTraceState.MARKER_FINISHED,
                        },
                    },
                    totalBenchmark,
                );
            }
        } catch (error) {
            this.logger.warn(
                PolloAdapter.acct(error?.message ?? String(error), account)
                    .setIdentifier({
                        autopilotTrace: {
                            ...autopilotTrace,
                            accountRunMarker: AutopilotTraceState.MARKER_ERROR,
                        },
                    })
                    .setError(error),
            );
        } finally {
            await this.grcCoreService.controlReadinessCalculation(account);

            await this.taskRuntimeStatService.logTaskComplete(
                {
                    benchmark: totalBenchmark,
                    accountId: account.id,
                    data: { id },
                    logIdentifier: LogIdentifierRecordType.AP,
                },
                account,
            );

            /**
             * Tell the latch we're done
             */
            this.notify(account);

            if (isNil(testId)) {
                /**
                 * https://drata.atlassian.net/browse/ENG-9168
                 *
                 * Finalize the sync and remove the maps only if syncs
                 * were activated, i.e. autopilot was run in full-mode
                 */
                await this.personnelSyncService.finalize(account);
                await this.usersVersionControlSyncService.finalize(account);
                await this.infrastructureSync.finalize(account);
            }
            await this.cleanCache(account);
        }
    }

    private async warmupServices(account: Account, products: Array<Product>) {
        try {
            await this.infrastructureWarmupService.warmup(account, products);
        } catch (error) {
            this.logger.warn(
                PolloAdapter.acct('Infrastructure sync warmup failed', account).setError(error),
            );
        }
    }

    private async runSyncs(
        account: Account,
        autopilotTrace: AutopilotTrace,
        products: Array<Product>,
    ) {
        this.log(`Running personnel sync for ${account.domain}`, account, {
            autopilotTrace,
        });

        let stage = Stage.IDP;

        try {
            // one more db call :/
            const idpConnection = await this.autopilotTaskService.getConnection(
                account,
                ProviderType.IDENTITY,
            );

            const identityClientType = idpConnection?.clientType ?? null;

            switch (identityClientType) {
                case ClientType.CSV_IDP:
                    /**
                     * Users sourced from the CSV/JSON IDP connection are processed during the 'create/update connection' operations.
                     * No updates occur between these operations and an AP run or a manual resync.
                     * Therefore, there is no need to invoke personnel sync here when the tenant has
                     * established this IDP connection, as there are no updates to process at this point in time.
                     */
                    break;
                default:
                    this.logger.log(
                        PolloAdapter.acct('Personnel sync memory usage', account)
                            .setContext('Before')
                            .setIdentifier(getMemoryUsageString()),
                    );

                    /**
                     * call the service to pull the personnel records from the identity source.
                     * This process should error out if the following imperative steps fail:
                     *  - Identity provider API service setup fails
                     *  - HRIS API service setup fails if the client has it setup
                     */
                    try {
                        await this.personnelSyncService.start(account);
                    } catch (error) {
                        await this.onCaughtSyncError(error, account, autopilotTrace, stage);
                    }
                    this.logger.log(
                        PolloAdapter.acct('Personnel sync memory usage', account)
                            .setContext('After')
                            .setIdentifier(getMemoryUsageString()),
                    );
            }

            stage = Stage.GROUP;

            this.log(`Running IdP Groups sync for ${account.domain}`, account, {
                autopilotTrace,
            });

            this.logger.log(
                PolloAdapter.acct('IdP Groups sync memory usage', account)
                    .setContext('Before')
                    .setIdentifier(getMemoryUsageString()),
            );

            try {
                await this.groupsSyncService.start(account);
            } catch (error) {
                await this.onCaughtSyncError(error, account, autopilotTrace, stage);
            }

            this.logger.log(
                PolloAdapter.acct('IdP Groups sync memory usage', account)
                    .setContext('After')
                    .setIdentifier(getMemoryUsageString()),
            );

            stage = Stage.HRIS;

            this.log(`Running HRIS background check sync for ${account.domain}`, account, {
                autopilotTrace,
            });

            this.logger.log(
                PolloAdapter.acct('HRIS background check sync memory usage', account)
                    .setContext('Before')
                    .setIdentifier(getMemoryUsageString()),
            );

            try {
                await this.hrisBackgroundCheckSync.start(account);
            } catch (error) {
                await this.onCaughtSyncError(error, account, autopilotTrace, stage);
            }

            this.logger.log(
                PolloAdapter.acct('HRIS background check sync memory usage', account)
                    .setContext('After')
                    .setIdentifier(getMemoryUsageString()),
            );

            stage = Stage.BACKGROUND_CHECK;

            this.log(`Running background check sync for ${account.domain}`, account, {
                autopilotTrace,
            });

            this.logger.log(
                PolloAdapter.acct('Background check sync memory usage', account)
                    .setContext('Before')
                    .setIdentifier(getMemoryUsageString()),
            );

            try {
                await this.backgroundCheckSynchronizationService.start(account, null);
            } catch (error) {
                await this.onCaughtSyncError(error, account, autopilotTrace, stage);
            }

            this.logger.log(
                PolloAdapter.acct('Background check sync memory usage', account)
                    .setContext('After')
                    .setIdentifier(getMemoryUsageString()),
            );

            stage = Stage.INFRASTRUCTURE;

            this.log(`Running infrastructure sync for ${account.domain}`, account, {
                autopilotTrace,
            });

            this.logger.log(
                PolloAdapter.acct('Infrastructure sync sync memory usage', account)
                    .setContext('Before')
                    .setIdentifier(getMemoryUsageString()),
            );

            try {
                this.logger.log(PolloAdapter.acct(`Running infrastructure sync`, account));
                await this.infrastructureSync.start(account, null, products);
            } catch (error) {
                await this.onCaughtSyncError(error, account, autopilotTrace, stage);
            }

            this.logger.log(
                PolloAdapter.acct('Infrastructure sync sync memory usage', account)
                    .setContext('After')
                    .setIdentifier(getMemoryUsageString()),
            );

            stage = Stage.VCS;

            this.log(`Running VCS sync for ${account.domain}`, account, {
                autopilotTrace,
            });

            this.logger.log(
                PolloAdapter.acct('VCS sync sync memory usage', account)
                    .setContext('Before')
                    .setIdentifier(getMemoryUsageString()),
            );

            try {
                this.logger.log(PolloAdapter.acct(`Running VCS sync sync`, account));
                await this.usersVersionControlSyncService.start(account, null, products);
            } catch (error) {
                await this.onCaughtSyncError(error, account, autopilotTrace, stage);
            }

            this.logger.log(
                PolloAdapter.acct('VCS sync sync memory usage', account)
                    .setContext('After')
                    .setIdentifier(getMemoryUsageString()),
            );

            stage = Stage.MDM;

            this.log(`Running MDM sync for ${account.domain}`, account, {
                autopilotTrace,
            });

            this.logger.log(
                PolloAdapter.acct('MDM sync sync memory usage', account)
                    .setContext('Before')
                    .setIdentifier(getMemoryUsageString()),
            );

            try {
                await this.mobileDeviceManagementSync.start(account);
            } catch (error) {
                await this.onCaughtSyncError(error, account, autopilotTrace, stage);
            }

            this.logger.log(
                PolloAdapter.acct('MDM sync sync memory usage', account)
                    .setContext('After')
                    .setIdentifier(getMemoryUsageString()),
            );

            stage = Stage.SECURITY_TRAINING;

            this.log(`Running Security Training sync for ${account.domain}`, account, {
                autopilotTrace,
            });

            this.logger.log(
                PolloAdapter.acct('Security Training sync memory usage', account)
                    .setContext('Before')
                    .setIdentifier(getMemoryUsageString()),
            );

            try {
                await this.securityTrainingSync.start(account);
            } catch (error) {
                await this.onCaughtSyncError(error, account, autopilotTrace, stage);
            }

            this.logger.log(
                PolloAdapter.acct('Security Training sync memory usage', account)
                    .setContext('After')
                    .setIdentifier(getMemoryUsageString()),
            );

            stage = Stage.OFFBOARDING;

            this.log(`Running offboarding sync for ${account.domain}`, account, {
                autopilotTrace,
            });

            this.logger.log(
                PolloAdapter.acct('Offboarding sync memory usage', account)
                    .setContext('Before')
                    .setIdentifier(getMemoryUsageString()),
            );

            try {
                await this.offboardingSyncService.start(account);
            } catch (error) {
                await this.onCaughtSyncError(error, account, autopilotTrace, stage);
            }

            this.logger.log(
                PolloAdapter.acct('Offboarding sync memory usage', account)
                    .setContext('After')
                    .setIdentifier(getMemoryUsageString()),
            );

            this.log(`Running policy sync for ${account.domain}`, account, {
                autopilotTrace,
            });

            this.logger.log(
                PolloAdapter.acct('Policy sync memory usage', account)
                    .setContext('Before')
                    .setIdentifier(getMemoryUsageString()),
            );

            stage = Stage.POLICY;

            try {
                await this.policySynchronizationService.start(account);
            } catch (error) {
                await this.onCaughtSyncError(error, account, autopilotTrace, stage);
            }

            this.logger.log(
                PolloAdapter.acct('Policy sync memory usage', account)
                    .setContext('After')
                    .setIdentifier(getMemoryUsageString()),
            );

            this.log(`Running observability sync for ${account.domain}`, account, {
                autopilotTrace,
            });

            this.logger.log(
                PolloAdapter.acct('Observability sync memory usage', account)
                    .setContext('Before')
                    .setIdentifier(getMemoryUsageString()),
            );

            stage = Stage.OBSERVABILITY;

            try {
                await this.observabilitySyncService.start(account);
            } catch (error) {
                await this.onCaughtSyncError(error, account, autopilotTrace, stage);
            }

            this.logger.log(
                PolloAdapter.acct('Observability sync memory usage', account)
                    .setContext('After')
                    .setIdentifier(getMemoryUsageString()),
            );

            this.log(`Running vulnerability sync for ${account.domain}`, account, {
                autopilotTrace,
            });

            this.logger.log(
                PolloAdapter.acct('Vulnerability sync memory usage', account)
                    .setContext('Before')
                    .setIdentifier(getMemoryUsageString()),
            );

            stage = Stage.VULNERABILITY;

            try {
                await this.vulnerabilitySynchronizationService.start(account);
            } catch (error) {
                await this.onCaughtSyncError(error, account, autopilotTrace, stage);
            }

            this.logger.log(
                PolloAdapter.acct('Vulnerability sync memory usage', account)
                    .setContext('After')
                    .setIdentifier(getMemoryUsageString()),
            );

            this.log(`Running Security sync for ${account.domain}`, account, {
                autopilotTrace,
            });

            this.logger.log(
                PolloAdapter.acct('Security sync memory usage', account)
                    .setContext('Before')
                    .setIdentifier(getMemoryUsageString()),
            );

            stage = Stage.SECURITY;

            try {
                await this.edrSynchronizationService.start(account);
            } catch (error) {
                await this.onCaughtSyncError(error, account, autopilotTrace, stage);
            }
            this.logger.log(
                PolloAdapter.acct('Security sync memory usage', account)
                    .setContext('After')
                    .setIdentifier(getMemoryUsageString()),
            );

            stage = Stage.CODEBASE_REPO;

            this.log(`Running Codebase Repository sync for ${account.domain}`, account, {
                autopilotTrace,
            });

            this.logger.log(
                PolloAdapter.acct('Codebase Repository sync memory usage', account)
                    .setContext('Before')
                    .setIdentifier(getMemoryUsageString()),
            );

            try {
                await this.codebaseRepositorySynchronizationService.start(account);
            } catch (error) {
                await this.onCaughtSyncError(error, account, autopilotTrace, stage);
            }

            this.logger.log(
                PolloAdapter.acct('Codebase Repository sync memory usage', account)
                    .setContext('After')
                    .setIdentifier(getMemoryUsageString()),
            );

            this.log(`Running CSPM sync for ${account.domain}`, account, {
                autopilotTrace,
            });

            this.logger.log(
                PolloAdapter.acct('CSPM sync memory usage', account)
                    .setContext('Before')
                    .setIdentifier(getMemoryUsageString()),
            );

            stage = Stage.CSPM;

            try {
                await this.cspmSynchronizationService.start(account);
            } catch (error) {
                await this.onCaughtSyncError(error, account, autopilotTrace, stage);
            }
            this.logger.log(
                PolloAdapter.acct('CSPM sync memory usage', account)
                    .setContext('After')
                    .setIdentifier(getMemoryUsageString()),
            );

            this.log(`Running CyberInsurance sync for ${account.domain}`, account, {
                autopilotTrace,
            });

            this.logger.log(
                PolloAdapter.acct('CyberInsurance sync memory usage', account)
                    .setContext('Before')
                    .setIdentifier(getMemoryUsageString()),
            );

            stage = Stage.CYBER_INSURANCE;

            try {
                await this.cyberInsuranceSyncService.start(account);
            } catch (error) {
                await this.onCaughtSyncError(error, account, autopilotTrace, stage);
            }
            this.logger.log(
                PolloAdapter.acct('CyberInsurance sync memory usage', account)
                    .setContext('After')
                    .setIdentifier(getMemoryUsageString()),
            );
        } catch (error) {
            await this.onCaughtSyncError(error, account, autopilotTrace, stage);
        }
    }

    /**
     *
     * @param account
     * @param async
     * @param productIds
     * @param runId
     * @param testIds
     * @param tenantDb
     * @param tenantBatchSize
     */
    async runEcsTask(
        account: Account,
        async: boolean,
        productIds: number[],
        runId: string = null,
        testIds: number[] = [],
        tenantDb: string = null,
        tenantBatchSize: number = null,
        auroraTenantBatchSize: number = null,
        runAtomicForSpecificTenants: boolean = false,
    ): Promise<void> {
        /**
         * Tell the latch we've started
         */
        this.started(account);
        const taskErrors = [];

        const ecsConfig = new this.EcsConfig();
        await ecsConfig.load();

        const cluster = ecsConfig.cluster;
        let taskDefinition: string;
        if (isNil(ecsConfig.taskRevision)) {
            taskDefinition = ecsConfig.taskDefinition;
        } else {
            taskDefinition = `${ecsConfig.taskDefinition}:${ecsConfig.taskRevision}`;
        }

        const commandArr = config.get('aws.runner.commands.autopilot');

        const command = [commandArr[0], commandArr[1]];

        if (!isEmpty(account)) {
            command.push(format(commandArr[2], account.domain));
        }

        if (!isEmpty(testIds)) {
            command.push(format(commandArr[3], testIds.join(' ')));
        }

        if (!isEmpty(productIds)) {
            command.push(format(commandArr[4], productIds.join(' ')));
        }

        if (!isNil(runId)) {
            command.push(format(commandArr[5], runId));
        }

        if (async) {
            command.push(commandArr[6]);
        }

        if (!isNil(tenantDb)) {
            command.push(format(commandArr[7], tenantDb));
        }

        if (!isNil(tenantBatchSize)) {
            command.push(format(commandArr[8], tenantBatchSize));
        }

        if (!isNil(auroraTenantBatchSize)) {
            command.push(format(commandArr[9], auroraTenantBatchSize));
        }

        // ignore the confirmation in the ecs enviroment
        if (runAtomicForSpecificTenants) {
            command.push('--confirmed=true');
        }

        const cliMode =
            process.env.TASK_STARTED_BY == 'scheduled_task' || process.env.CLI_MODE == 'cron'
                ? 'cron'
                : 'manual';

        if (cliMode === 'manual') {
            await startLightRun([`autopilot_${getEnvironmentName()}`]);
        }

        const ddAutopilotType = isNil(tenantDb) ? 'tenant' : 'cluster';
        // setup the env
        const params: RunTaskCommandInput = {
            taskDefinition,
            cluster,
            enableExecuteCommand: config.get('aws.runner.enableExecuteCommand'),
            propagateTags: 'TASK_DEFINITION',
            tags: [
                {
                    key: 'drata:operations:component',
                    value: 'ap1',
                },
                {
                    key: 'drata:terraform:managed',
                    value: 'false',
                },
            ],
            launchType: config.get('aws.runner.launchType'),
            networkConfiguration: {
                awsvpcConfiguration: {
                    subnets: config.get('aws.runner.subnets'),
                    securityGroups: config.get('aws.runner.securityGroups').split(','),
                    assignPublicIp: config.get('aws.runner.assignPublicIp'),
                },
            },
            overrides: {
                containerOverrides: [
                    {
                        command,
                        name: config.get('aws.runner.name'),
                        environment: [
                            { name: 'AUTOPILOT_ENABLED', value: 'true' },
                            { name: 'TASK_STARTED_BY', value: 'Autopilot 1.0' },
                            { name: 'CLI_MODE', value: cliMode },
                        ],
                    },
                    {
                        name: 'log-router',
                        environment: [
                            {
                                name: 'DD_TAGS_FIRELENS',
                                value: `mode:autopilot,autopilot:${ddAutopilotType},env:${process.env.DD_ENV},cli_mode:${cliMode}`,
                            },
                        ],
                    },
                    {
                        name: 'datadog-agent',
                        environment: [
                            {
                                name: 'DD_TAGS',
                                value: `mode:autopilot autopilot:${ddAutopilotType} env:${process.env.DD_ENV} cli_mode:${cliMode}`,
                            },
                        ],
                    },
                ],
            },
        };

        try {
            let status: string | undefined;
            let pendingTasks: DescribeTasksCommandOutput | undefined;

            const ecs = new ECS({
                region: config.get('aws.runner.region'),
            });

            const startTime = new Date();

            const runTaskResponse = await ecs.runTask(params);
            const tasksArr = runTaskResponse.tasks || [];
            const failures = runTaskResponse.failures || [];
            const tasks = tasksArr.map(task => task.taskArn).filter(arn => arn !== undefined);

            this.log(`ECS Task STARTED`, account, {
                params,
                tasks,
                failures,
                ecsTask: {
                    action: 'started',
                    startTime: startTime.toUTCString(),
                },
            });

            if (isEmpty(tasks)) {
                taskErrors.push({ params, tasks, failures });
                const errorMsg = 'ECS Task failed to start';
                await this.sendSlackErrorMessage(
                    `${errorMsg}. Command: ${command.join(' ')}`,
                    account,
                    runId,
                );
                throw new Error(errorMsg);
            }

            let pollCount = 0;
            while (status !== AutopilotRunnerService.ECS_STOPPED_STATE) {
                await sleep(AutopilotRunnerService.ECS_POLL_INTERVAL);

                pendingTasks = await ecs.describeTasks({ tasks, cluster });
                status = pendingTasks.tasks?.[0]?.lastStatus;

                if (
                    ++pollCount >= AutopilotRunnerService.ECS_POLL_MAX_COUNT &&
                    status !== AutopilotRunnerService.ECS_STOPPED_STATE
                ) {
                    break;
                }
            }
            const stopTime = new Date();
            const logParams = {
                params,
                tasks: pendingTasks,
                ecsTask: {
                    action: 'stopped',
                    startTime: startTime.toUTCString(),
                    stopTime: stopTime.toUTCString(),
                    duration: timeDiff(startTime, stopTime),
                },
            };

            if (
                pollCount >= AutopilotRunnerService.ECS_POLL_MAX_COUNT &&
                status !== AutopilotRunnerService.ECS_STOPPED_STATE
            ) {
                this.warn('ECS Task Time exceeded limit', account, {
                    ...logParams,
                    ecsTask: { ...logParams.ecsTask, action: 'timeout' },
                });
            } else if (pollCount >= AutopilotRunnerService.ECS_POLL_MAX_COUNT) {
                this.warn('ECS Poll max count exceeded', account, {
                    logParams,
                });
            } else {
                this.log(`ECS Task STOPPED`, account, logParams);
            }
        } catch (error) {
            this.error(error, account, taskErrors);
        } finally {
            /**
             * Tell the latch we're done
             */
            this.notify(account);
        }
    }

    /**
     *
     * @param account
     */
    @CacheBusterWithPrefix<void>({
        stores: [Caches.FRAMEWORK, Caches.LIST_CONTROLS, Caches.REQUIREMENTS],
    })
    async retest(account: Account, testId: number): Promise<void> {
        const test = await this.autopilotTaskService.getControlTestInstanceByTestId(
            account,
            testId,
        );

        if (test.checkStatus === CheckStatus.TESTING) {
            throw new ConflictException('The test is already running');
        }

        if (test.checkStatus !== CheckStatus.ENABLED) {
            throw new BadRequestException('The test must be in an enabled state to run');
        }

        /**
         * Setting to testing before task starts to match the FE state
         * This DOES notify the FE as only TEST NOW events will use pusher
         */
        await this.autopilotTaskService.startTestingControlTestInstanceForRetest(account, test);

        const productId = getProductId(account);

        this.log(`Autopilot: Starting non-atomic retest for test ID ${testId}`, account, {
            productId,
        });

        this.start({
            atomic: false,
            testIds: [testId],
            accountIds: [],
            accountDomains: [account.domain],
            runId: undefined,
            runTypeAuto: false,
            isRetest: true,
            productIds: productId ? [productId] : [],
        }).catch(error => {
            this.error(error, account);
        });
    }

    /**
     *
     * @param account
     */
    @CacheBusterWithPrefix<void>({
        stores: [Caches.FRAMEWORK, Caches.LIST_CONTROLS, Caches.REQUIREMENTS],
    })
    async bulkRetestRemote(account: Account, testIds: Array<number>): Promise<void> {
        const productId = getProductId(account);
        this.log(`Autopilot: Starting atomic bulk-retest`, account, {
            productId,
            testIds,
        });

        /**
         * Set all the test as TESTING because the ECS task take a few minutes to start, this will let the user
         * know that something is happening, AP1 Picks ENABLED and TESTING test
         */

        await Promise.allSettled(
            testIds.map(async testId => {
                const test = await this.autopilotTaskService.getControlTestInstanceByTestId(
                    account,
                    testId,
                );
                await this.autopilotTaskService.startTestingControlTestInstanceForRetest(
                    account,
                    test,
                );
            }),
        );

        this.start({
            atomic: false,
            testIds: testIds,
            accountIds: [account.id],
            tenantDb: account.databaseHost,
            accountDomains: [],
            runId: undefined,
            runTypeAuto: false,
            isRetest: true,
            productIds: productId ? [productId] : [],
        }).catch(error => {
            this.error(error, account);
        });
    }
    /**
     *
     */
    private started(account: Account): void {
        if (isNil(account)) {
            /**
             * Runner mode is the initial ECS task launcher
             */
            this.accountCountDownLatch.set(
                AutopilotRunnerService.DEFAULT_ACCOUNT_LATCH_ENTRY,
                null,
            );
        } else {
            this.accountCountDownLatch.set(account.domain, account);
        }
    }

    /**
     *
     */
    private async wait() {
        while (this.isWaiting()) {
            await sleep(AutopilotRunnerService.WAIT_FOR);
        }
    }

    /**
     *
     */
    private isWaiting(): boolean {
        const limit =
            AutopilotRunnerService.DATABASE_TYPE === AutopilotDatabaseType.RDS
                ? AutopilotRunnerService.RDS_BATCH_SIZE
                : AutopilotRunnerService.AURORA_BATCH_SIZE;
        return this.accountCountDownLatch.size >= limit;
    }

    /**
     *
     */
    private notify(account: Account) {
        if (isNil(account)) {
            /**
             * Runner mode is the initial ECS task launcher
             */
            this.accountCountDownLatch.delete(AutopilotRunnerService.DEFAULT_ACCOUNT_LATCH_ENTRY);
        } else {
            this.accountCountDownLatch.delete(account.domain);
        }
    }

    /**
     *
     */
    private async halt() {
        while (this.isHalting()) {
            await sleep(AutopilotRunnerService.WAIT_FOR);
        }
    }

    /**
     *
     */
    private isHalting(): boolean {
        return this.accountCountDownLatch.size > 0;
    }

    /**
     *
     * @param error
     * @param account
     * @param autopilotTrace
     * @param stage
     */
    private async onCaughtSyncError(
        error: any,
        account: Account,
        autopilotTrace: any,
        stage: Stage,
    ): Promise<void> {
        this.logger.warn(
            PolloAdapter.acct(error?.message ?? String(error), account)
                .setIdentifier({ autopilotTrace, stage })
                .setError(error),
        );
    }

    @SyncCacheBusterWithPrefix<void>({
        stores: [Caches.FRAMEWORK, Caches.LIST_CONTROLS, Caches.REQUIREMENTS],
    })
    private cleanCache(account: Account): Promise<void> {
        return Promise.resolve();
    }

    /**
     * Temporary function to evaluate temporary feature flag.
     * @param account Tenant account.
     * @returns `true` if the feature flag is enabled, `false` otherwise.
     */
    private shouldRunInBatches(account: Account): Promise<boolean> {
        return this.featureFlag.evaluateAsTenant(
            {
                category: FeatureFlagCategory.NONE,
                name: FeatureFlag.POC_AUTOPILOT_TASKS_RUN_IN_BATCHES,
                defaultValue: false,
            },
            account,
        );
    }
}

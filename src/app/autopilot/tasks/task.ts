/* eslint-disable @typescript-eslint/no-misused-promises */
import { Data } from 'app/autopilot/classes/data.class';
import { MonitorData } from 'app/autopilot/classes/monitor-data.class';
import { MonitorResult } from 'app/autopilot/classes/monitor-result.class';
import { TaskData } from 'app/autopilot/classes/task-data.class';
import { TaskResponse } from 'app/autopilot/classes/task-response.class';
import { TaskResult } from 'app/autopilot/classes/task-result.class';
import { UserTaskData } from 'app/autopilot/classes/user-task-data.class';
import { MonitorDataType } from 'app/autopilot/enums/monitor-data-type.enum';
import { taskErrorSeverityFromError } from 'app/autopilot/helper/task-error-severity.helper';
import { IAutopilotTask } from 'app/autopilot/interfaces/autopilot-task.interface';
import { AutopilotTrace } from 'app/autopilot/interfaces/autopilot-trace.interface';
import { IResolver } from 'app/autopilot/interfaces/resolver.interface';
import { AutopilotTask } from 'app/autopilot/tasks/autopilot.task';
import { AutopilotErrorType } from 'app/autopilot/types/autopilot-error.type';
import { AutopilotTaskResponseType } from 'app/autopilot/types/autopilot-task-response.type';
import { TaskErrorSeverity } from 'app/autopilot/types/task-error-severity.enum';
import { ConnectionEntity } from 'app/companies/connections/entities/connection.entity';
import { ControlTestInstanceHistory } from 'app/monitors/entities/control-test-instance-history.entity';
import { MonitorInstanceExclusion as Exclusion } from 'app/monitors/entities/monitor-instance-exclusion.entity';
import { UserIdentityEntityType } from 'app/users/entities/user-identity-entity.type';
import { User } from 'app/users/entities/user.entity';
import { Policy } from 'app/users/policies/entities/policy.entity';
import { Account } from 'auth/entities/account.entity';
import { PolloAdapter } from 'commons/adapters/pollo.adapter';
import { Benchmark } from 'commons/benchmark/benchmark';
import { LatchedMutexBatch } from 'commons/classes/latched-mutex-batch.class';
import { ApiDataSource } from 'commons/enums/api-data-source.enum';
import { AssetProvider } from 'commons/enums/asset-provider.enum';
import { AssetReferenceType } from 'commons/enums/asset-reference-type.enum';
import { AssetType } from 'commons/enums/asset-type.enum';
import { ClientType } from 'commons/enums/auth/client-type.enum';
import { ProviderType } from 'commons/enums/auth/provider-type.enum';
import { AutopilotTaskResponseStatus as ResponseStatus } from 'commons/enums/autopilot/autopilot-task-response-status.enum';
import { AutopilotTaskState } from 'commons/enums/autopilot/autopilot-task-state.enum';
import { AutopilotTaskType as TaskType } from 'commons/enums/autopilot/autopilot-task-type.enum';
import { LogIdentifierEvent } from 'commons/enums/log-identifier-event.enum';
import { LogIdentifierRecordType } from 'commons/enums/log-identifier-record-type.enum';
import { MobileDeviceManagementSourceType } from 'commons/enums/mdm-source-type.enum';
import { getUuid } from 'commons/helpers/identity.helper';
import { getProductId } from 'commons/helpers/products.helper';
import { fullName } from 'commons/helpers/user.helper';
import config from 'config';
import { TaskStore } from 'dependencies/autopilot/task.store';
import { chunk, clone, get, has, isEmpty, isNil } from 'lodash';
import { PolloMessage } from 'pollo-logger/classes/pollo.message';
export abstract class Task<T extends Task<T>> implements IAutopilotTask<T> {
    /**
     *
     */
    private autopilotTrace: AutopilotTrace = {
        testId: null,
        runId: null,
        accountRunId: null,
        taskRunId: null,
        runTime: null,
        accountRunTime: null,
        taskRunTime: null,
        runType: null,
        runMarker: null,
        productId: null,
    };

    /**
     *
     */
    protected context: AutopilotTask<T>;

    /**
     *
     */
    protected account: Account;

    /**
     *
     */
    protected testId: number;

    /**
     *
     */
    protected type: TaskType;

    /**
     *
     */
    protected id: string;

    /**
     *
     */
    protected name: string;

    /**
     *
     */
    protected state: AutopilotTaskState;

    /**
     *
     */
    protected metadata: string;

    /**
     *
     */
    protected request: any;

    /**
     *
     */
    response: any;

    /**
     *
     */
    protected completedAt: Date;

    /**
     *
     */
    protected time: number;

    /**
     *
     */
    protected tasks: T[] = [];

    /**
     *
     */
    protected errors: AutopilotErrorType[] = [];

    /**
     *
     */
    protected user: User;

    /**
     *
     */
    protected results: TaskResult<any> = new TaskResult<any>();

    /**
     *
     */
    protected resolver: IResolver<any>;

    /**
     *
     */
    protected connection: ConnectionEntity;

    /**
     *
     */
    protected source: ApiDataSource;

    /**
     *
     */
    protected controlTestInstanceHistory: ControlTestInstanceHistory;

    /**
     * The set of task that are grouped together due multi-connections common
     * dependencies
     */
    protected taskSet: Task<T>[] | null;

    /**
     * The mutext batch shared across the the task set used to synchronize
     * operation when running things asynchronously
     */
    protected mutexBatch: LatchedMutexBatch;

    // ENG-58645: temporary
    protected taskGroups: Map<TaskType, string[]>;

    // ENG-58645: temporary
    protected asyncGroupTasksEnabled = false;

    /**
     *
     * @param context
     * @param account
     * @param type
     * @param testId
     */
    constructor(
        context: AutopilotTask<T>,
        account: Account,
        type: TaskType,
        testId: number,
        controlTestInstanceHistory: ControlTestInstanceHistory,
    ) {
        this.context = context;
        this.account = account;
        this.type = type;
        this.testId = testId;
        this.controlTestInstanceHistory = controlTestInstanceHistory;
        this.name = this.getTaskName();

        this.autopilotTrace.testId = this.testId;

        /**
         * We have to make sure this isn't on boot
         */
        if (!isNil(this.account)) {
            this.autopilotTrace.accountRunId = this.account.id;
            this.autopilotTrace.productId = getProductId(this.account);
        }

        /**
         * We have to make sure this isn't on boot
         */
        if (!isNil(this.context)) {
            this.autopilotTrace.runId = this.context.runId;
            this.autopilotTrace.runType = this.context.runType;
        }

        this.taskSet = [];
        this.mutexBatch = null;
        // ENG-58645: temporary
        this.taskGroups = new Map<TaskType, string[]>();
    }

    /**
     *
     */
    getId(): string {
        return this.id;
    }

    /**
     *
     * @param id
     */
    setId(id: string): void {
        this.id = id;
    }

    /**
     *
     */
    getType(): TaskType {
        return this.type;
    }

    /**
     *
     */
    getContextId(): string {
        return this.context.id;
    }

    /**
     *
     */
    getName(): string {
        return this.name;
    }

    /**
     *
     */
    getState(): AutopilotTaskState {
        return this.state;
    }

    /**
     *
     */
    getMetadata(): string {
        return this.metadata;
    }

    /**
     *
     */
    getRequest(): any {
        return this.request;
    }

    /**
     *
     */
    getResponse(): any {
        return this.response;
    }

    /**
     *
     */
    formatResponse(): any {
        if (!isNil(this.response)) {
            return JSON.stringify(this.response);
        }

        return null;
    }

    /**
     *
     */
    getCompletedAt(): Date {
        return this.completedAt;
    }

    /**
     *
     */
    getTime(): number {
        return this.time;
    }

    /**
     *
     */
    getTasks(): IAutopilotTask<T>[] {
        return this.tasks;
    }

    /**
     *
     */
    getStore(): TaskStore<T> {
        return this.context.taskStore;
    }

    /**
     *
     */
    getErrors(): AutopilotErrorType[] {
        return this.errors;
    }

    /**
     *
     */
    formatErrors(): any {
        if (!isEmpty(this.errors)) {
            return JSON.stringify(this.errors);
        }

        return null;
    }

    /**
     *
     */
    getAccount(): Account {
        return this.account;
    }

    /**
     *
     */
    getUser(): User {
        return this.user;
    }

    /**
     *
     */
    getResolver(): IResolver<any> {
        return this.resolver;
    }

    /**
     *
     */
    getTestId(): number {
        return this.testId;
    }

    /**
     *
     * @returns
     */
    getConnection(): ConnectionEntity | null {
        return this.connection ?? null;
    }

    /**
     *
     */
    getConnectionId(): number | null {
        return !isNil(this.connection) ? this.connection.id : null;
    }

    /**
     *
     * @returns
     */
    getConnectionClientId(): string | null {
        return !isNil(this.connection) ? this.connection.clientId : null;
    }

    /**
     *
     * @returns
     */
    getConnectionClientAlias(): string | null {
        return !isNil(this.connection) ? this.connection.clientAlias : null;
    }

    /**
     *
     */
    getConnectionClientType(): ClientType | null {
        return !isNil(this.connection) ? this.connection.clientType : null;
    }

    /**
     *
     */
    getConnectionProviderType(): ProviderType | null {
        return !isNil(this.connection) ? this.connection.providerType : null;
    }

    /**
     *
     */
    getConnectionProviderTypes(): ProviderType[] {
        return (this.connection?.connectionProviderTypes ?? [])
            .filter(cpt => cpt.enabledAt)
            .map(cpt => cpt.providerType);
    }

    /**
     *
     * @returns
     */
    getControlTestInstanceHistory(): ControlTestInstanceHistory {
        return this.controlTestInstanceHistory;
    }

    /**
     *
     */
    setControlTestInstanceHistory(controlTestInstanceHistory: ControlTestInstanceHistory): void {
        this.controlTestInstanceHistory = controlTestInstanceHistory;
    }

    /**
     *
     */
    hasMonitorMetadata(): boolean {
        return !isNil(this.getMonitorDataType());
    }

    /**
     *
     * @returns copy of current AutopilotTrace
     */
    getCurrentAutopilotTrace() {
        return clone(this.autopilotTrace);
    }

    // ENG-58645: temporary
    addTaskGroup(autopilotTaskType: TaskType, taskGroup: string[]): void {
        this.taskGroups.set(autopilotTaskType, taskGroup);
    }

    // ENG-58645: temporary
    setAsyncGroupTasksEnabled(asyncGroupTasksEnabled: boolean): void {
        this.asyncGroupTasksEnabled = asyncGroupTasksEnabled;
    }

    /**
     *
     * @param task
     */
    async add(task: T): Promise<string> {
        task.id = getUuid();
        task.state = AutopilotTaskState.WAITING;
        task.autopilotTrace.taskRunId = task.id;

        try {
            await this.getStore().write(task);

            this.tasks.push(task);

            const connection = task.getConnection();
            const connectionId = !isNil(connection) ? connection.id : 'Not Set';
            const testId = !isNil(task.testId) ? task.testId : 'Not Set';
            const description = `(testId, productId, connectionId) = (${testId}, ${getProductId(
                this.account,
            )}, ${connectionId})`;

            /**
             * We need to increment the latch
             */
            this.context.started();

            this.writeLog(`Added Task - ${task.name}: ${description}`, task);
        } catch (error) {
            this.setStateError(task);
            this.onCaughtError(error);
        }

        return task.id;
    }

    /**
     *
     */
    async run(wait?: boolean, inBatches?: boolean): Promise<void> {
        if (wait) {
            await this.do(async () => {
                for (const tsk of this.tasks) {
                    this.willStart(tsk);
                    // eslint-disable-next-line no-await-in-loop
                    await tsk.run(wait);
                }
            });
        } else if (inBatches) {
            await this.do(async () => {
                const batchSize = config.get('api.autopilotTasksBatchSize');

                const taskChunks = chunk(this.tasks, batchSize);

                this.log(`Running tasks in batches of ${batchSize}`, {
                    totalTasks: this.tasks.length,
                    totalBatches: taskChunks.length,
                });

                for (const [batchIndex, batch] of taskChunks.entries()) {
                    this.log(`Running batch of ${batch.length} tasks`, {
                        batchIndex,
                        data: batch.map(tsk => {
                            return {
                                id: tsk.getId(),
                                name: tsk.getName(),
                                testId: tsk.getTestId(),
                            };
                        }),
                    });

                    const promises = batch.map(async (tsk: T) => {
                        this.willStart(tsk);
                        await tsk.run();
                    });

                    // eslint-disable-next-line no-await-in-loop
                    const results = await Promise.allSettled(promises);

                    const rejectedResults = results
                        .filter(result => result.status === 'rejected')
                        .map(result => (result as PromiseRejectedResult).reason);

                    if (!isEmpty(rejectedResults)) {
                        this.log('Some tasks failed in batch processing', { rejectedResults });
                    }

                    if (global.gc) {
                        global.gc();
                    }
                }
            });
        } else {
            this.do(async () => {
                // ENG-58645: temporary
                if (this.asyncGroupTasksEnabled) {
                    this.log(
                        `asyncGroupTasksEnabled flag enabled, running groups of tasks in parallel, and groups synchronously..`,
                    );

                    /**
                     * Execute per task group, but async across the group.
                     */
                    for (const [autopilotTaskType, taskIds] of this.taskGroups) {
                        this.log(
                            `Resolving task group for autopilot task type ${TaskType[autopilotTaskType]}`,
                            {
                                taskIds,
                                length: taskIds.length,
                            },
                        );
                        const tasksForGroup = this.tasks.filter(task =>
                            taskIds.includes(task.getId()),
                        );

                        tasksForGroup.forEach(task => {
                            this.log(
                                `Resolved task group item for autopilot task type ${TaskType[autopilotTaskType]}`,
                                {
                                    name: task.getTaskName(),
                                    testId: task.testId,
                                    autopilotTaskTypeNumber: autopilotTaskType,
                                    autopilotTaskTypeString: TaskType[autopilotTaskType],
                                    connectionId: task.getConnectionId(),
                                    productId: getProductId(task.getAccount()),
                                },
                            );
                        });

                        this.log(`Starting task in parallel for ${TaskType[autopilotTaskType]}`, {
                            autopilotTaskTypeNumber: autopilotTaskType,
                            autopilotTaskTypeString: TaskType[autopilotTaskType],
                            length: tasksForGroup.length,
                        });
                        const promises: Array<Promise<any>> = [];
                        for (const task of tasksForGroup) {
                            promises.push(
                                task.run().catch(error => {
                                    this.log('Unhandled promise rejection', { error });
                                }),
                            );
                        }
                        // eslint-disable-next-line no-await-in-loop
                        const results = await Promise.allSettled(promises);
                        this.log(
                            `Completed tasks in parallel, promise results for ${TaskType[autopilotTaskType]}`,
                            {
                                results,
                            },
                        );
                    }
                } else {
                    this.log(`asyncGroupTasksEnabled flag disabled, running tasks in parallel..`);

                    /**
                     * Default async behavior across all tasks.
                     */
                    for (const tsk of this.tasks) {
                        this.willStart(tsk);
                        tsk.run().catch(error => {
                            this.log('Unhandled promise rejection', { error });
                        });
                    }
                }
            }).catch(error => {
                this.log('Unhandled promise rejection', { error });
            });
        }
    }

    /**
     *
     * @param task
     */
    async startAndWait(task?: T): Promise<void> {
        if (this.getId() === task.getId()) {
            this.willStart(task);
            await this.run(true);
        } else {
            for (const tsk of this.tasks) {
                // eslint-disable-next-line no-await-in-loop
                await tsk.startAndWait(task);
            }
        }
    }

    /**
     *
     * @param task
     */
    start(task?: T): void {
        if (this.getId() === task.getId()) {
            this.willStart(task);
            // eslint-disable-next-line @typescript-eslint/no-floating-promises
            this.run();
        } else {
            for (const tsk of this.tasks) {
                tsk.start(task);
            }
        }
    }

    /**
     * Starts the task in batches.
     * @param task Optional. Defaults to `this.tasks`.
     */
    async startInBatches(task?: T): Promise<void> {
        if (this.getId() === task?.getId()) {
            this.willStart(task);
            await this.run(false, true);
        } else {
            for (const tsk of this.tasks) {
                // eslint-disable-next-line no-await-in-loop
                await tsk.startInBatches(task);
            }
        }
    }

    /**
     *
     * @param work
     */
    protected async do(work: () => Promise<void>): Promise<void> {
        const benchmark = new Benchmark();

        this.setStateRunning(this);

        try {
            await (work as any)();
        } catch (error) {
            this.onCaughtError(error);
        }

        await this.completed(benchmark);
    }

    /**
     *
     * @param benchmark
     */
    private async completed(benchmark: Benchmark): Promise<void> {
        if (isEmpty(this.errors)) {
            this.setStateFinished(this);
        } else {
            this.setStateError(this);
        }

        benchmark.end();

        this.completedAt = benchmark.getCompletedAt();
        this.time = benchmark.time();
        this.autopilotTrace.taskRunTime = this.time;

        const connection = this.getConnection();
        const connectionId = !isNil(connection) ? connection.id : 'Not Set';
        const testId = !isNil(this.testId) ? this.testId : 'Not Set';
        const description = `(testId, productId, connectionId) = (${testId}, ${getProductId(
            this.account,
        )}, ${connectionId})`;

        this.writeLog(
            `Completed Task - ${this.name}: ${description}`,
            this,
            LogIdentifierEvent.autopilotTaskComplete,
        );

        try {
            await this.getStore().update(this as any);

            /**
             * Do not notify on super-tasks
             */
            if (!isNil(this.getTestId())) {
                /**
                 * Grouped tasks have to wait for the mutex to finish
                 */
                if (this.isInGroupedTask()) {
                    /**
                     * Check that all tasks have been run in this task set
                     */
                    if (this.mutexBatch.completed()) {
                        await this.context.runner.notifyTestStateChange(this.account, this.testId);
                    }
                } else {
                    await this.context.runner.notifyTestStateChange(this.account, this.testId);
                }
            }
        } catch (error) {
            this.setStateError(this);
            this.onCaughtError(error);
        } finally {
            /**
             * We need to decrement the latch
             */
            this.context.notify();
        }
    }

    /**
     *
     * @param users
     */
    protected failUsers(users: User[]): void {
        const data = users.map((user: User) => {
            const taskData = new UserTaskData();

            const identities = get(user, 'identities', []);
            const identity = identities.find(i => !isNil(i.avatarSource)) ?? null;

            taskData.id = isNil(user.id) ? '' : String(user.id);
            taskData.name = isNil(user.id) ? '' : fullName(user);
            taskData.email = user.email;
            taskData.avatar = user.avatar;
            taskData.avatarSource = !isNil(identity) ? identity.avatarSource : null;
            taskData.raw = null;
            taskData.policiesMissingAcceptance = user.policies;

            if (!isNil(user.agentData)) {
                const { deviceName, operationalState, serialNumber } = user.agentData;
                const { uniqueId } = user.assets[0];
                taskData.raw = {
                    agentData: {
                        serialNumber,
                        operationalState,
                    },
                    drataDevice: {
                        deviceName,
                        serialNumber: uniqueId,
                    },
                };
            }

            if (!isNil(user['devices'])) {
                taskData.raw = {
                    devicesCount: user['devices'].length,
                    devices: user['devices'].map(device => ({
                        id: device.id,
                        osVersion: device.osVersion,
                        serialNumber: device.serialNumber,
                        model: device.model,
                        sourceType: MobileDeviceManagementSourceType[device.sourceType],
                        appsCount: device.appsCount,
                        asset: !isNil(device.asset)
                            ? {
                                  id: device.asset.id,
                                  name: device.asset.name,
                                  type: AssetType[device.asset.assetType],
                                  description: device.asset.description,
                                  assetProvider: AssetProvider[device.asset.assetProvider],
                                  assetReferenceType: !isNil(device.asset.assetReferenceType)
                                      ? AssetReferenceType[device.asset.assetReferenceType]
                                      : null,
                                  assetClassTypes: device.asset.assetClassTypes,
                              }
                            : null,
                    })),
                };
            }

            return taskData;
        });

        this.results.fail = data;
    }

    /**
     *
     * @param users
     * @param policy
     */
    protected failPolicyUsers(users: User[], policy?: Policy): void {
        this.failUsers(users);

        if (!isNil(policy)) {
            for (const failed of this.results.fail as Array<UserTaskData>) {
                failed.groups = [];

                for (const policyGroup of policy.groups) {
                    const group = policyGroup.group;
                    const groupPersonnel = get(group, 'personnel', []);

                    for (const gp of groupPersonnel) {
                        const currentPersonnel = gp.personnel;
                        const user = get(currentPersonnel, 'user', null);

                        if (!isNil(user) && failed.id === String(user.id)) {
                            failed.groups.push(group.name);
                        }
                    }
                }
            }
        }
    }

    /**
     *
     * @param users
     */
    protected passUsers(users: User[]): void {
        const data = users.map((user: User) => {
            const taskData = new UserTaskData();

            const identities = get(user, 'identities', []);
            const identity = identities.find(i => !isNil(i.avatarSource)) ?? null;

            taskData.id = String(user.id);
            taskData.name = fullName(user);
            taskData.email = user.email;
            taskData.avatar = user.avatar;
            taskData.avatarSource = !isNil(identity) ? identity.avatarSource : null;
            taskData.raw = user.agentData;

            return taskData;
        });

        this.results.pass = data;
    }

    /**
     *
     * @param versions
     */
    protected failPolicies(policies: Policy[]): void {
        const data = policies.map((policy: Policy) => {
            return {
                id: String(policy.id),
                name: policy.name,
                description: policy.currentDescription,
                raw: null,
                request: null,
            };
        });

        this.results.fail = data;
    }

    /**
     *
     * @param identities
     */
    protected failIdentities(identities: UserIdentityEntityType[]): void {
        this.results.fail = this.processIdentity(identities);

        if (isEmpty(this.results.fail)) {
            this.results.passed = true;
        }
    }

    /**
     *
     * @param identities
     */
    protected passIdentities(identities: UserIdentityEntityType[]): void {
        this.results.pass = this.processIdentity(identities);
    }

    /**
     *
     * @param identities
     */
    private processIdentity(identities: UserIdentityEntityType[]): Data[] {
        return identities.map(({ userIdentity, ...identity }: UserIdentityEntityType) => {
            if (has(userIdentity, 'connection')) {
                delete userIdentity.connection;
            }

            if (!userIdentity.username || !userIdentity.email) {
                this.context.logger.warn(
                    PolloAdapter.acct(
                        `The userIdentity ${userIdentity.id} doesn't have a username or email`,
                        this.account,
                        this.constructor.name,
                    )
                        .setSubContext(this.account.companyName)
                        .setProvider(this.source)
                        .setIdentifier({
                            userIdentity: {
                                id: userIdentity.id,
                                identityId: userIdentity.identityId,
                            },
                        }),
                );
            }

            const name = userIdentity.username ?? userIdentity.email ?? userIdentity.identityId;

            if ('resourceArn' in identity && !isNil(identity.resourceArn)) {
                return {
                    id: String(userIdentity.identityId),
                    name: String(name),
                    raw: userIdentity,
                    accountId: identity.resourceArn.split(':')[4],
                    ...(identity.resourceArn && { resourceArn: identity.resourceArn }),
                };
            }

            return {
                id: String(userIdentity.identityId),
                name: String(name),
                raw: userIdentity,
                accountId: this.connection.clientId,
                accountName: this.connection.clientAlias,
            };
        });
    }

    /**
     *
     * @param results
     */
    passedTemplate<K extends Data>(data: TaskData<K>): TaskResponse<K> {
        return {
            status: ResponseStatus.PASSED,
            data,
            controlTestInstanceHistory: this.controlTestInstanceHistory,
            connection: this.connection ?? null,
            createdAt: new Date(),
        };
    }

    /**
     *
     * @param data
     */
    failedTemplate<K extends Data>(data: TaskData<K>): TaskResponse<K> {
        const status = ResponseStatus.FAILED;
        const message = this.getFailMessage();
        const createdAt = new Date();

        return {
            status,
            data,
            message,
            controlTestInstanceHistory: this.controlTestInstanceHistory,
            connection: this.connection ?? null,
            createdAt,
        };
    }

    /**
     *
     * @param data
     * @param source
     */
    toMonitorResult<K extends MonitorData>(
        pass: Data[],
        fail: Data[],
        source: ApiDataSource,
        exlusions: Exclusion[],
        excluded?: Data[],
        connectionId?: number,
        clientId?: string,
        clientAlias?: string,
        errors?: unknown,
        clientType?: ClientType,
        message?: string,
        policyScope?: string,
        policyName?: string,
        groups?: { id: number; name: string }[],
    ): MonitorResult<K> {
        const type = this.getMonitorDataType();

        if (isNil(type)) {
            return null;
        }

        const monitorResult = new MonitorResult<MonitorData>();

        monitorResult.pass = pass.map((result: Data) => new MonitorData(result));

        monitorResult.fail = fail.map((result: Data) => new MonitorData(result));

        if (!isNil(excluded)) {
            monitorResult.excluded = excluded.map((result: Data) => new MonitorData(result));
        }

        monitorResult.type = type;
        monitorResult.source = source;
        monitorResult.exclusions = exlusions;
        monitorResult.connectionId = connectionId;
        monitorResult.clientId = clientId;
        monitorResult.clientAlias = clientAlias;
        monitorResult.errors = errors;
        monitorResult.clientType = clientType;
        monitorResult.message = message;
        monitorResult.policyScope = policyScope;
        monitorResult.policyName = policyName;
        monitorResult.groups = groups;
        return monitorResult as MonitorResult<K>;
    }

    /**
     *
     * @param response
     * @returns
     */
    buildEventMetadata<K extends Data>(response: TaskResponse<K>): AutopilotTaskResponseType<K> {
        response.data = this.buildEventMetadataData(response);
        return response as any;
    }

    /**
     *
     * @param response
     * @returns
     */
    buildEventMetadataData<K extends Data>(response: TaskResponse<K>): TaskData<K> {
        return response.data;
    }

    /**
     *
     * @param error
     */
    onCaughtError(error: Error, identifier?: any): void {
        if (isNil(this.account) || isNil(this.testId)) {
            this.logErrorLevelBasedOnSeverity(
                error,
                PolloMessage.msg(
                    `Caught error in task before account and test id were set ${error.message}`,
                )
                    .setContext(this.constructor.name)
                    .setSubContext(this.onCaughtError.name)
                    .setError(error)
                    .setIdentifier({
                        event: LogIdentifierEvent.autopilotTaskError,
                        autopilotTrace: this.autopilotTrace,
                        testId: this.getTestId(),
                        name: this.getName(),
                        identifier,
                    }),
            );
            return;
        }

        let message = get(error, 'message', null);

        if (isNil(message)) {
            message = get(error, 'name', null);
        }

        const polloMessage = PolloAdapter.acct(message, this.account, this.constructor.name)
            .setSubContext(this.account.companyName)
            .setIdentifier({
                event: LogIdentifierEvent.autopilotTaskError,
                autopilotTrace: this.autopilotTrace,
                testId: this.getTestId(),
                name: this.getName(),
                identifier,
                clientType: ClientType[this.getConnectionClientType()],
                providerType: ProviderType[this.getConnectionProviderType()],
                providerTypes: this.getConnectionProviderTypes().map(type => ProviderType[type]),
                connectionId: this.getConnectionId(),
                connectionAlias: this.getConnectionClientAlias(),
            })
            .setProvider(this.source)
            .setExecutionTime(this.time)
            .setError(error);

        this.logErrorLevelBasedOnSeverity(error, polloMessage);

        this.errors.push({
            message: error.message,
        });
    }

    /**
     *
     * @param taskSet
     */
    setTaskSet(taskSet: Task<T>[]): void {
        this.taskSet = taskSet;
    }

    /**
     *
     * @param mutexBatch
     */
    setMutexBatch(mutexBatch: LatchedMutexBatch): void {
        this.mutexBatch = mutexBatch;
    }

    /**
     *
     * @returns
     */
    getTaskSet(): Task<T>[] | null {
        return this.taskSet;
    }

    /**
     *
     * @returns
     */
    isInGroupedTask(): boolean {
        return this.taskSet.length > 1;
    }

    /**
     *
     * @returns
     */
    async getMutexLock(): Promise<number> {
        if (isNil(this.mutexBatch)) {
            this.writeLog(`Grouped Task Sync does not have a shared mutex, continuing..`, this);
            return;
        }

        const description = this.buildDescription();
        this.writeLog(
            `Grouped Task attaining mutex for (name, description) = (${this.name}, ${description})`,
            this,
        );

        const mutex = await this.mutexBatch.lock();
        this.writeLog(
            `Grouped Task mutex attained for (name, description, mutex) = (${this.name}, ${description}, ${mutex})`,
            this,
        );
        // return the mutex
        return mutex;
    }

    /**
     *
     * @param mutex
     */
    releaseMutex(mutex: number): void {
        this.mutexBatch.unlock(mutex);
        const description = this.buildDescription();
        this.writeLog(
            `Grouped Task mutex released for (name, description, mutex) = (${this.name}, ${description}, ${mutex})`,
            this,
        );
    }

    /**
     *
     * @returns
     */
    protected buildDescription(): string {
        const connection = this.getConnection();
        const connectionId = !isNil(connection) ? connection.id : 'Not Set';
        const testId = !isNil(this.testId) ? this.testId : 'Not Set';
        return `(testId, productId, connectionId) = (${testId}, ${getProductId(
            this.account,
        )}, ${connectionId})`;
    }

    /**
     *
     */
    protected getMonitorDataType(): MonitorDataType {
        return null;
    }

    /**
     *
     * Override this method in the child task if it needs to store a
     * fail message in the monitor instance entity - null ignores update
     */
    protected getTaskName(): string {
        return this.constructor.name;
    }

    /**
     *
     * @param message
     * @param task
     */
    protected log(message: string, identifier: object = {}): void {
        const polloMessage = PolloAdapter.acct(message, this.account, this.constructor.name)
            .setSubContext(this.account.companyName)
            .setIdentifier(identifier)
            .setProvider(this.source)
            .setExecutionTime(this.time);

        this.context.logger.log(polloMessage);
    }

    /**
     *
     * @param message
     * @param task
     */
    protected writeLog(message: string, task: Task<any>, event?: LogIdentifierEvent): void {
        if (isNil(this.account) || isNil(task.testId)) {
            return;
        }

        this.log(message, {
            event,
            autopilotTrace: task.autopilotTrace,
            testId: task.getTestId(),
            name: task.getTaskName(),
            clientType: ClientType[task.getConnectionClientType()],
            providerType: ProviderType[task.getConnectionProviderType()],
            providerTypes: task.getConnectionProviderTypes().map(type => ProviderType[type]),
            connectionId: task.getConnectionId(),
            connectionAlias: task.getConnectionClientAlias(),
        });
    }

    protected logTaskProgress(
        connection: ConnectionEntity,
        recordType: LogIdentifierRecordType,
        recordCount?: number,
    ): void {
        if (!recordCount) {
            // Skiping logging if recordCount is 0 or false
            return;
        }
        const clientType = ClientType[connection.clientType];
        this.log(`Fetched ${recordCount} ${recordType} records from ${clientType}`, {
            event: LogIdentifierEvent.autopilotTaskProgress,
            clientType,
            connectionId: this.getConnectionId(),
            connectionAlias: this.getConnectionClientAlias(),
            recordType,
            recordCount: recordCount,
            testId: this.getTestId(),
        });
    }

    /**
     *
     * @param task
     */
    private setStateRunning(task: Task<T>): void {
        task.state = AutopilotTaskState.RUNNING;
    }

    /**
     *
     * @param task
     */
    private setStateFinished(task: Task<T>): void {
        task.state = AutopilotTaskState.DONE;
    }

    /**
     *
     * @param task
     */
    private setStateError(task: Task<T>): void {
        task.state = AutopilotTaskState.ERROR;
    }

    /**
     *
     * @param task
     */
    private willStart(task: Task<any>): void {
        this.setStateRunning(task);

        this.writeLog(
            `Running Task - ${task.name}: ${isNil(task.testId) ? 'Not Set' : task.testId}`,
            task,
            LogIdentifierEvent.autopilotTaskStart,
        );
    }

    /**
     *
     */
    protected abstract getFailMessage(): string;

    /**
     * Will log with level based on severity determined by taskErrorSeverityFromError
     * @param error
     * @param polloMessage
     */
    protected logErrorLevelBasedOnSeverity(error: unknown, polloMessage: PolloMessage) {
        if (taskErrorSeverityFromError(error) !== TaskErrorSeverity.HIGH) {
            this.context.logger.warn(polloMessage);
        } else {
            this.context.logger.error(polloMessage);
        }
    }
}

import { Injectable } from '@nestjs/common';
import { ApiClientService } from 'app/api-client/api-client.service';
import { AutopilotTaskService } from 'app/autopilot/autopilot-task.service';
import { AutopilotCluster } from 'app/autopilot/autopilot.cluster';
import { AutopilotRunType } from 'app/autopilot/enums/autopilot-run-type.enum';
import { Task } from 'app/autopilot/tasks/task';
import { AutopilotTaskState } from 'commons/enums/autopilot/autopilot-task-state.enum';
import { getUuid } from 'commons/helpers/identity.helper';
import { sleep } from 'commons/helpers/sleep.helper';
import { TaskStore } from 'dependencies/autopilot/task.store';
import { Uploader } from 'dependencies/uploader/uploader';
import { isEmpty } from 'lodash';
import { PolloMessage } from 'pollo-logger/classes/pollo.message';
import { PolloLogger } from 'pollo-logger/pollo.logger';

@Injectable()
export class AutopilotTask<T extends Task<T>> extends Task<T> {
    /**
     *
     */
    countdownLatch = 0;

    /**
     *
     */
    runId: string;

    /**
     *
     */
    runType: AutopilotRunType;

    /**
     *
     * @param cluster
     * @param taskStore
     * @param runner
     * @param external
     * @param uploader
     * @param logger
     */
    constructor(
        private readonly cluster: AutopilotCluster<T>,
        readonly taskStore: TaskStore<T>,
        readonly runner: AutopilotTaskService,
        readonly external: ApiClientService,
        readonly uploader: Uploader,
        readonly logger: PolloLogger<PolloMessage>,
    ) {
        super(null, null, null, null, null);

        this.reset();
    }

    reset(): void {
        this.id = getUuid();
        this.context = this;
        this.name = `${this.constructor.name}`;
        this.state = AutopilotTaskState.WAITING;
        this.tasks = [];
        this.completedAt = null;
        this.time = null;
        this.countdownLatch = 0;
        this.runType = null;
        this.runId = null;
    }

    /**
     *
     * @param runId
     */
    setRunId(runId: string): void {
        this.runId = runId;
    }

    /**
     *
     * @param runType
     */
    setRunType(runType: AutopilotRunType): void {
        this.runType = runType;
    }

    /**
     *
     */
    async run(): Promise<void> {
        await this.do(async () => {
            for (const tsk of this.tasks) {
                this.state = AutopilotTaskState.RUNNING;
                this.cluster.run(tsk);

                /**
                 * This will be removed when clustering is implemented for the autopilot
                 * tasks - we will not receive the immediate response here obviously - but
                 * will wait on a callback to update this data - each account will be sent
                 * to a secondary server for handling and will issue a callback of the
                 * resulting tree ...
                 */

                if (isEmpty(this.errors)) {
                    this.state = AutopilotTaskState.DONE;
                } else {
                    this.errors = this.errors.concat(this.errors);
                    this.state = AutopilotTaskState.ERROR;
                }
            }
        });
    }

    /**
     *
     * @param task
     */
    async startAndWait(task?: T): Promise<void> {
        this.state = AutopilotTaskState.RUNNING;

        for (const tsk of this.tasks) {
            // eslint-disable-next-line no-await-in-loop
            await tsk.startAndWait(task);
        }

        if (this.countdownLatch === 0) {
            this.state = AutopilotTaskState.DONE;
        }
    }

    /**
     *
     * @param task
     */
    async start(task?: T): Promise<void> {
        this.state = AutopilotTaskState.RUNNING;

        for (const tsk of this.tasks) {
            tsk.start(task);
        }

        while (this.waiting()) {
            // eslint-disable-next-line no-await-in-loop
            await this.wait();
        }

        this.state = AutopilotTaskState.DONE;
    }

    async startInBatches(task?: T): Promise<void> {
        this.state = AutopilotTaskState.RUNNING;

        for (const tsk of this.tasks) {
            // eslint-disable-next-line no-await-in-loop
            await tsk.startInBatches(task);
        }

        if (this.countdownLatch === 0) {
            this.state = AutopilotTaskState.DONE;
        }
    }

    /**
     *
     * @returns
     */
    isWaiting(): boolean {
        return this.getState() === AutopilotTaskState.WAITING;
    }

    /**
     *
     * @returns
     */
    isRunning(): boolean {
        return this.getState() === AutopilotTaskState.RUNNING;
    }

    /**
     *
     */
    isDone(): boolean {
        return this.getState() === AutopilotTaskState.DONE;
    }

    /**
     *
     */
    isError(): boolean {
        return this.getState() === AutopilotTaskState.ERROR;
    }

    /**
     *
     */
    started(): void {
        ++this.countdownLatch;
    }

    /**
     *
     */
    waiting(): boolean {
        return this.countdownLatch > 0;
    }

    /**
     *
     */
    async wait(): Promise<void> {
        await sleep(1000);
    }

    /**
     *
     */
    notify(): void {
        --this.countdownLatch;
    }

    /**
     *
     * @returns
     */
    protected getFailMessage(): string {
        return null;
    }
}

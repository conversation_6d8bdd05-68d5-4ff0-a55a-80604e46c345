import {
    BulkImportEntityEnum,
    BulkImportSubCategoryEnum,
} from 'app/bulk-import/enums/bulk-import.entity.enum';
import { decrypt, encrypt } from 'commons/helpers/security.helper';
import { AccountIdType, toAccountIdType } from 'commons/types/account-id.type';

export class SpaceMetadata {
    accountId: AccountIdType;
    userId: number | string;
    entityType: BulkImportEntityEnum;
    subEntityType?: BulkImportSubCategoryEnum;

    static encryptMetadata(metadata: SpaceMetadata): string {
        let encryptedAccountId: string = '';
        if (metadata.accountId) {
            encryptedAccountId = encrypt(metadata.accountId);
        }

        let encryptedUserId: string = '';
        if (metadata.userId) {
            encryptedUserId = encrypt(JSON.stringify(metadata.userId));
        }

        let encryptedEntityType: string = '';
        if (metadata.entityType) {
            encryptedEntityType = encrypt(JSON.stringify(metadata.entityType));
        }

        let encryptedSubEntityType: string = '';
        if (metadata.subEntityType) {
            encryptedSubEntityType = encrypt(JSON.stringify(metadata.subEntityType));
        }

        return JSON.stringify({
            accountId: encryptedAccountId,
            userId: encryptedUserId,
            entityType: encryptedEntityType,
            subEntityType: encryptedSubEntityType,
        });
    }

    static parse(encryptedMetadata: string): SpaceMetadata {
        let metadata: SpaceMetadata;

        try {
            metadata = JSON.parse(encryptedMetadata);

            if (metadata.accountId) {
                metadata.accountId = toAccountIdType(decrypt(metadata.accountId));
            }

            if (metadata.userId && typeof metadata.userId === 'string') {
                metadata.userId = JSON.parse(decrypt(metadata.userId));
            }

            if (metadata.entityType && typeof metadata.entityType === 'string') {
                metadata.entityType = JSON.parse(decrypt(metadata.entityType));
            }

            if (metadata.subEntityType && typeof metadata.subEntityType === 'string') {
                metadata.subEntityType = JSON.parse(decrypt(metadata.subEntityType));
            }
        } catch (error) {
            throw new Error(error);
        }
        return metadata;
    }
}

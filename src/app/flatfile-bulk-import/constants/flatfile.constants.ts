/**
 * Constants for Flatfile integration
 */

import { BulkImportType } from 'app/flatfile-bulk-import/enums/entity-type.enum';
import { FileValidationType } from 'commons/enums/file-validation-type.enum';
import { Role } from 'commons/enums/users/role.enum';
import { FileTypes } from 'commons/maps/file-extensions-and-mime-types-validation.map';

export const bulkImportTypeRoleMap = new Map([
    [BulkImportType.RISK, [Role.ADMIN, Role.WORKSPACE_ADMINISTRATOR, Role.RISK_MANAGER]],
    [
        BulkImportType.CONTROL,
        [Role.ADMIN, Role.WORKSPACE_ADMINISTRATOR, Role.CONTROL_MANAGER, Role.TECHGOV],
    ],
    [
        BulkImportType.TRAINING,
        [Role.ADMIN, Role.WORKSPACE_ADMINISTRATOR, Role.CONTROL_MANAGER, Role.TECHGOV],
    ],
]);

export enum FlatFileResource {
    SPACE = 'space',
    WORKBOOK = 'workbook',
    SHEET = 'sheet',
    RECORD = 'record',
}

export enum FlatFileWorkbookName {
    RISK = 'Risk',
    ASSET = 'Asset',
    VENDOR = 'Vendor',
}

export const DEFAULT_FLATFILE_ACTIONS = [
    {
        operation: 'submitAction',
        mode: 'foreground',
        label: 'Submit',
        type: 'string',
        description: 'Submit data for processing',
        constraints: [{ type: 'hasAllValid' }, { type: 'hasData' }],
        primary: true,
    },
];

const EXCLUDED_FILE_EXTENSIONS = ['.md', '.markdown', '.doc'];

export const allowedFileTypes = [
    FileTypes.get(FileValidationType.DOCUMENT),
    FileTypes.get(FileValidationType.IMAGE),
    FileTypes.get(FileValidationType.SCANNABLE),
]
    .filter((type): type is NonNullable<typeof type> => type !== undefined)
    .flat()
    .map(type => type.extension)
    .filter(extension => !EXCLUDED_FILE_EXTENSIONS.includes(extension));

export const FILES_ATTACHMENT_SHEET_SLUG = 'files-attachments';
export const CATEGORIES_SHEET_SLUG = 'categories-sheet';

export const DEFAULT_FILE_SIZE = 25 * 1_000_000; // 25MB in decimal (25,000,000 bytes)
export const DEFAULT_ZIP_FILE_SIZE = 100 * 1_000_000; // 100MB in decimal (100,000,000 bytes)

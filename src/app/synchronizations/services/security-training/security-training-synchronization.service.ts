/* eslint-disable no-await-in-loop */
import { Injectable, InternalServerErrorException } from '@nestjs/common';
import { ApiClientService } from 'app/api-client/api-client.service';
import { ApiResponse } from 'app/apis/classes/api/api-response.class';
import { SecurityTrainingAssignment } from 'app/apis/classes/security-training/security-training-assignment.class';
import { SecurityTrainingData } from 'app/apis/classes/security-training/security-training-data.class';
import { ISecurityTrainingServices } from 'app/apis/interfaces/security-training-services.interface';
import { SecurityTrainingSyncedEvent } from 'app/autopilot/observables/events/security-training-synced.event';
import { ConnectionEntity } from 'app/companies/connections/entities/connection.entity';
import { ConnectionsCoreService } from 'app/companies/connections/services/connections-core.service';
import { Company } from 'app/companies/entities/company.entity';
import { TrainingType } from 'app/companies/entities/training-type.enum';
import { Product } from 'app/companies/products/entities/product.entity';
import { CompaniesCoreService } from 'app/companies/services/companies-core.service';
import { TrainingCampaign } from 'app/security-training/entities/training-campaign.entity';
import { getServiceForTrainingType } from 'app/security-training/helpers/security-training.helper';
import { SecurityTrainingConstants } from 'app/security-training/security-training.constants';
import { SecurityTrainingComplianceChecksCoreService } from 'app/security-training/services/security-training-compliance-checks-core.service';
import { SecurityTrainingCoreService } from 'app/security-training/services/security-training-core.service';
import { SettingsCoreService } from 'app/settings/services/settings-core.service';
import { AutoResetConfig } from 'app/settings/types/auto-reset-config.type';
import { ISecurityTrainingSynchronization } from 'app/synchronizations/interfaces/security-training/security-training-synchronization.interface';
import { ISynchronization } from 'app/synchronizations/interfaces/synchronization.interface';
import { BaseSynchronizationService } from 'app/synchronizations/services/base-synchronization.service';
import { AutoResetSecurityTrainingService } from 'app/synchronizations/services/security-training/auto-reset-security-training.service';
import { UserDocumentTypeByTrainingCampaignType } from 'app/users/entities/user-document-training-campaign-type.map';
import { UserDocument } from 'app/users/entities/user-document.entity';
import { User } from 'app/users/entities/user.entity';
import { ComplianceCheckTypeNames } from 'app/users/personnel/entities/compliance-check-type-names.map';
import { Personnel } from 'app/users/personnel/entities/personnel.entity';
import { ComplianceChecksOrchestrationService } from 'app/users/personnel/services/compliance-checks-orchestration.service';
import { PersonnelCoreService } from 'app/users/personnel/services/personnel-core.service';
import { UsersCoreService } from 'app/users/services/users-core.service';
import { Account } from 'auth/entities/account.entity';
import { ClientTypeDescription } from 'auth/entities/client-type-description-map.enum';
import {
    StackOneSecurityTrainingClientTypes,
    StackOneSecurityTrainingTypes,
} from 'auth/helpers/stack-one/stack-one-provider-type.helper';
import { PolloAdapter } from 'commons/adapters/pollo.adapter';
import { Benchmark } from 'commons/benchmark/benchmark';
import { ClientType } from 'commons/enums/auth/client-type.enum';
import { ProviderType } from 'commons/enums/auth/provider-type.enum';
import { HipaaTrainingType } from 'commons/enums/hipaa-training-type.enum';
import { LogIdentifierRecordType } from 'commons/enums/log-identifier-record-type.enum';
import { NistAITrainingType } from 'commons/enums/nistai-training-type.enum';
import { SecurityTrainingType } from 'commons/enums/security-training-type.enum';
import { SettingType } from 'commons/enums/settings/settings-type.enum';
import {
    TrainingCampaignType,
    TrainingCampaignTypeComplianceCheckTypeMap,
} from 'commons/enums/training-campaign-type.enum';
import { UploadType } from 'commons/enums/upload-type.enum';
import { areUserDocumentsCompliant } from 'commons/helpers/compliance-check.helper';
import { fileNameDate } from 'commons/helpers/date.helper';
import { forEachTokenPage } from 'commons/helpers/pagination/pagination.helper';
import { TokenPagination } from 'commons/helpers/pagination/token.pagination';
import { isInAFormerStatus } from 'commons/helpers/personnel.helper';
import { fileNameNoExt, sanitizeFileName } from 'commons/helpers/upload.helper';
import { fullName } from 'commons/helpers/user.helper';
import config from 'config';
import { Downloader } from 'dependencies/downloader/downloader';
import { DownloaderPayloadType } from 'dependencies/downloader/types/downloader-payload.interface';
import { HtmlToPdfConverter } from 'dependencies/html-to-pdf-converter/html-to-pdf-converter';
import { Uploader } from 'dependencies/uploader/uploader';
import fs from 'fs';
import hbs from 'hbs';
import { includes, isEmpty, isNil, uniq } from 'lodash';
import moment from 'moment';
import path from 'path';
import { tenantWrapper } from 'tenancy/contexts/tenant-wrapper';

@Injectable()
export class SecurityTrainingSynchronizationService
    extends BaseSynchronizationService
    implements ISynchronization
{
    constructor(
        private readonly provider: ApiClientService,
        private readonly connectionsCoreService: ConnectionsCoreService,
        private readonly personnelCoreService: PersonnelCoreService,
        private readonly complianceChecksOrchestrationService: ComplianceChecksOrchestrationService,
        private readonly securityTrainingComplianceChecksCoreService: SecurityTrainingComplianceChecksCoreService,
        private readonly companiesCoreService: CompaniesCoreService,
        private readonly securityTrainingCoreService: SecurityTrainingCoreService,
        private readonly settingsCoreService: SettingsCoreService,
        private readonly usersCoreService: UsersCoreService,
        private readonly autoResetSecurityTrainingService: AutoResetSecurityTrainingService,
        private readonly uploader: Uploader,
        private readonly downloader: Downloader,
        private readonly htmlToPdfConverter: HtmlToPdfConverter,
    ) {
        super();
    }

    getProviderType(): ProviderType {
        return ProviderType.SECURITY_TRAINING;
    }

    /**
     * @param {Account} account
     * @param {ConnectionEntity[]} targetConnections
     * @param products
     * @param {boolean} isInitialSync
     */
    protected async runSync(
        account: Account,
        targetConnections?: ConnectionEntity[],
        products?: Product[],
        isInitialSync = false,
    ): Promise<void> {
        this.finalize(account);
        await this.willStartSynchronization(account);

        if (isEmpty(targetConnections)) {
            this.log(`No target connections provided, fetching all`, account);

            targetConnections = await this.getConnections(account, ProviderType.SECURITY_TRAINING);
        }

        await this.cleanupComplianceCheckExclusions(account);
        // TS doesn't understand isEmpty
        if (targetConnections && !isEmpty(targetConnections)) {
            this.logSyncStart(account, targetConnections);
            const benchmark = new Benchmark();

            for (const connection of targetConnections) {
                const connectionBenchmark = new Benchmark();
                try {
                    await this.logConnectionSyncStart(
                        account,
                        connection,
                        benchmark,
                        LogIdentifierRecordType.SECURITY_TRAINING,
                    );

                    await this.startForConnection(account, connection, isInitialSync);

                    await this.logConnectionSyncSuccess(
                        account,
                        connection,
                        connectionBenchmark,
                        LogIdentifierRecordType.SECURITY_TRAINING,
                    );
                } catch (error) {
                    await this.logConnectionSyncFailure(
                        error,
                        account,
                        connection,
                        connectionBenchmark,
                        LogIdentifierRecordType.SECURITY_TRAINING,
                    );
                    throw error;
                }
            }

            this.logSyncFinish(account, targetConnections, benchmark);

            this.didFinishSynchronization(account);
        } else {
            this.log(
                `No connections of provider type ${
                    ProviderType[ProviderType.SECURITY_TRAINING]
                } connections found, evaluating compliance based on user documents`,
                account,
            );

            await this.evaluateComplianceForBuiltInEvidenceUpload(account);
        }

        await this.autoResetSecurityTrainingService.run(account);
    }

    /**
     * @param {Account} account
     * @param {ProviderType} providerType
     */
    getConnections(account: Account, providerType: ProviderType): Promise<ConnectionEntity[]> {
        return this.connectionsCoreService.getConnectionsByProviderType(providerType, account);
    }

    /**
     * @param {Account} account
     */
    async willStartSynchronization(account: Account): Promise<void> {}

    /**
     * @param {ConnectionEntity} connection
     */
    async getProvider(
        account: Account,
        connection: ConnectionEntity,
    ): Promise<ISecurityTrainingServices> {
        // eslint-disable-next-line @typescript-eslint/no-unnecessary-type-assertion
        return (await this.provider.api(connection, account)) as ISecurityTrainingServices;
    }

    /**
     * @param {Account} account
     */
    didFinishSynchronization(account: Account): void {
        this.finalize(account);
    }

    /**
     * Evaluate security training compliance for accounts using Drata's built-in evidence upload
     * This runs when no security training connections are established
     */
    private async evaluateComplianceForBuiltInEvidenceUpload(account: Account): Promise<void> {
        try {
            this.log('Starting compliance evaluation for built-in evidence upload method', account);

            const evaluateSecurityTrainingBuiltInEvidence = async (
                personnelIterator: AsyncGenerator<Personnel[], void, unknown>,
                autoResetConfig?: AutoResetConfig | null,
            ) => {
                try {
                    // Check which training types are enabled
                    const isHipaaFrameworkEnabled =
                        await this.companiesCoreService.getIsTrainingEnabled(
                            TrainingType.HIPAA_TRAINING,
                        );
                    const isNistAiFrameworkEnabled =
                        await this.companiesCoreService.getIsTrainingEnabled(
                            TrainingType.NIST_AI_TRAINING,
                        );

                    for await (const personnelBatch of personnelIterator) {
                        await Promise.all(
                            personnelBatch.map(personnel => {
                                const promises: Promise<void>[] = [];

                                // Always evaluate security training as default
                                promises.push(
                                    this.evaluatePersonnelComplianceFromDocuments(
                                        account,
                                        personnel,
                                        autoResetConfig,
                                        TrainingCampaignType.SECURITY_TRAINING,
                                    ),
                                );

                                // Evaluate HIPAA training if enabled
                                if (isHipaaFrameworkEnabled) {
                                    promises.push(
                                        this.evaluatePersonnelComplianceFromDocuments(
                                            account,
                                            personnel,
                                            autoResetConfig,
                                            TrainingCampaignType.HIPAA_TRAINING,
                                        ),
                                    );
                                }

                                // Evaluate NIST AI training if enabled
                                if (isNistAiFrameworkEnabled) {
                                    promises.push(
                                        this.evaluatePersonnelComplianceFromDocuments(
                                            account,
                                            personnel,
                                            autoResetConfig,
                                            TrainingCampaignType.NIST_AI_TRAINING,
                                        ),
                                    );
                                }

                                return Promise.all(promises);
                            }),
                        );
                    }
                } catch (error) {
                    this.warn(error, account, {
                        message: `Error evaluating compliance for built-in evidence upload: ${error.message}`,
                    });
                }
            };

            const autoResetConfig = await this.getAutoResetConfigForSecurityTraining(account);
            await evaluateSecurityTrainingBuiltInEvidence(
                this.personnelCoreService.getAllCurrentPersonnelIterator(),
                autoResetConfig,
            );

            this.log(
                'Completed compliance evaluation for built-in evidence upload method',
                account,
            );
        } catch (error) {
            this.error(error, account, {
                message: `Error evaluating compliance for built-in evidence upload: ${error.message}`,
            });
        }
    }

    /**
     * Evaluate individual personnel compliance based on their uploaded documents
     */
    private async evaluatePersonnelComplianceFromDocuments(
        account: Account,
        personnel: Personnel,
        autoResetConfig: AutoResetConfig | null = null,
        trainingType: TrainingCampaignType = TrainingCampaignType.SECURITY_TRAINING,
    ): Promise<void> {
        try {
            if (!personnel.user) {
                this.log(
                    `Skipping ${TrainingCampaignType[trainingType]} compliance evaluation for personnel ${personnel.id} - no associated user`,
                    account,
                );
                return;
            }

            // Get the appropriate document type for the training type
            const documentType = UserDocumentTypeByTrainingCampaignType.get(trainingType);
            if (!documentType) {
                this.warn(`Unknown training type: ${TrainingCampaignType[trainingType]}`, account, {
                    trainingType,
                    personnelId: personnel.id,
                });
                return;
            }

            // Get the appropriate compliance check type for the training type
            const complianceCheckType =
                TrainingCampaignTypeComplianceCheckTypeMap.get(trainingType);
            if (!complianceCheckType) {
                this.warn(
                    `Unknown compliance check type for training: ${TrainingCampaignType[trainingType]}`,
                    account,
                    { trainingType, personnelId: personnel.id },
                );
                return;
            }

            const trainingUserDocuments =
                (await this.usersCoreService.listAllDocumentsOfType(
                    personnel.user.id,
                    documentType,
                )) || [];

            const isCompliant = areUserDocumentsCompliant(trainingUserDocuments, autoResetConfig);

            if (isCompliant) {
                const completionDate = this.getLatestDocumentCompletionDate(trainingUserDocuments);

                await this.complianceChecksOrchestrationService.passComplianceCheck(
                    personnel.user,
                    complianceCheckType,
                    completionDate,
                    true,
                );

                this.log(
                    `Passed ${TrainingCampaignType[trainingType]} compliance for ${
                        personnel.user.email
                    } based on user documents (completion: ${completionDate.toISOString()})`,
                    account,
                );
            } else {
                await this.complianceChecksOrchestrationService.failComplianceCheck(
                    personnel.user,
                    complianceCheckType,
                );

                this.log(
                    `Failed ${TrainingCampaignType[trainingType]} compliance for ${
                        personnel.user.email
                    } based on user documents`,
                    account,
                );
            }
        } catch (error) {
            this.error(error, account, {
                message: `Error evaluating ${TrainingCampaignType[trainingType]} compliance for personnel ${
                    personnel.user?.email || 'unknown'
                }: ${error.message}`,
            });
        }
    }

    /**
     * Get the latest document completion date from user documents
     */
    private getLatestDocumentCompletionDate(userDocuments: UserDocument[]): Date {
        if (isEmpty(userDocuments)) {
            return new Date();
        }

        userDocuments.sort((a, b) => (b.createdAt?.getTime() ?? 0) - (a.createdAt?.getTime() ?? 0));
        const latestDocument = userDocuments[0];
        return latestDocument ? new Date(latestDocument.createdAt) : new Date();
    }

    /**
     * Determine if auto-reset settings for security training
     */
    private async getAutoResetConfigForSecurityTraining(
        account: Account,
    ): Promise<AutoResetConfig | null> {
        try {
            const settings = await tenantWrapper(account, () => {
                return this.settingsCoreService.getSettingBySettingType(
                    SettingType.SECURITY_TRAINING,
                );
            });

            if (isNil(settings)) {
                return null;
            }

            const autoResetConfig = settings.configObject as AutoResetConfig;
            return autoResetConfig;
        } catch {
            return null;
        }
    }

    /**
     * @param {Account} account
     * @param {ConnectionEntity} connection
     * @param {boolean} isInitialSync
     */
    private async startForConnection(
        account: Account,
        connection: ConnectionEntity,
        isInitialSync: boolean,
    ): Promise<void> {
        const securityTrainingApi = await this.getSecurityTrainingSynchronizationApi(
            connection,
            account,
        );

        if (!isNil(securityTrainingApi)) {
            this.logger.log(
                PolloAdapter.acct(
                    `Security training synchronization for connection with ID ${connection.id} has started.`,
                    account,
                )
                    .setContext(this.constructor.name)
                    .setSubContext(this.startForConnection.name)
                    .setIdentifier({
                        connection,
                    }),
            );

            await this.startSecurityTrainingSync(
                securityTrainingApi,
                account,
                connection,
                isInitialSync,
            );

            this._eventBus.publish(new SecurityTrainingSyncedEvent(account, connection));
        }
    }

    /**
     * @param {ISecurityTrainingSynchronization} securityTrainingServiceApi
     * @param {Account} account
     * @param {ConnectionEntity} connection
     * @param {boolean} isInitialSync
     */
    private async startSecurityTrainingSync(
        securityTrainingApi: ISecurityTrainingSynchronization,
        account: Account,
        connection: ConnectionEntity,
        isInitialSync: boolean,
    ): Promise<void> {
        try {
            const company = await this.getCompanyByAccount(account);

            await this.validateAndUpdateCampaignsName(securityTrainingApi, account, connection);
            const activeCampaigns = await this.getActiveCampaigns(account, connection);
            const personnelWithTrainings: { [key: number]: Personnel } = {};
            const personnelChecksToExcludeFromDelete: {
                [key: number]: number[];
            } = {};
            for (const campaign of activeCampaigns) {
                personnelChecksToExcludeFromDelete[campaign.id] = [];
                await forEachTokenPage(
                    (nextPageToken: string) => {
                        return securityTrainingApi.getData(
                            TokenPagination.paged(nextPageToken, config.get('sync.maxResults')),
                            account,
                            campaign.trainingId,
                        );
                    },
                    async (data: SecurityTrainingData[], response: ApiResponse<any>) => {
                        this.logger.log(
                            PolloAdapter.acct(`Raw API response`, account)
                                .setContext(this.constructor.name)
                                .setSubContext(this.startSecurityTrainingSync.name)
                                .setIdentifier({
                                    data: response.data,
                                    raw: response.raw,
                                }),
                        );
                        this.log(
                            `Processing ${data.length} users of ${campaign.name} campaign`,
                            account,
                        );
                        const processedPersonnel: number[] = [];
                        for (const securityTrainingData of data) {
                            this.logger.log(
                                PolloAdapter.acct(
                                    `Raw API response for ${securityTrainingData.identity ?? '(missing identity)'}`,
                                    account,
                                )
                                    .setContext(this.constructor.name)
                                    .setSubContext(this.startSecurityTrainingSync.name)
                                    .setIdentifier({
                                        securityTrainingData,
                                    }),
                            );
                            const personnel = await this.processUserCampaignData(
                                campaign,
                                securityTrainingData,
                                account,
                                connection,
                                company,
                                isInitialSync,
                            );
                            if (!isNil(personnel)) {
                                processedPersonnel.push(personnel.id);
                            }

                            if (!isNil(personnel) && isNil(personnelWithTrainings[personnel.id])) {
                                personnelWithTrainings[personnel.id] = personnel;
                            }
                        }
                        personnelChecksToExcludeFromDelete[campaign.id].push(...processedPersonnel);
                    },
                );
            }
            const personnelToUpdate = Object.values(personnelWithTrainings);
            this.log(
                `Updating security training compliance check of ${personnelToUpdate.length} users`,
            );

            await this.deleteTrainingChecksByPersonnel(account, personnelChecksToExcludeFromDelete);

            const isHipaaFrameworkEnabled = await this.companiesCoreService.getIsTrainingEnabled(
                TrainingType.HIPAA_TRAINING,
            );

            const isNistAiFrameworkEnabled = await this.companiesCoreService.getIsTrainingEnabled(
                TrainingType.NIST_AI_TRAINING,
            );

            await this.setSecurityTrainingComplianceCheckForPersonnel(
                account,
                connection,
                personnelToUpdate,
                company,
                isHipaaFrameworkEnabled,
                isNistAiFrameworkEnabled,
            );
        } catch (error) {
            this.logger.error(
                PolloAdapter.acct(`${ClientType[connection.clientType]}: ${error.message}`, account)
                    .setError(error)
                    .setContext(this.constructor.name)
                    .setSubContext(this.startSecurityTrainingSync.name),
            );
            return;
        }
    }

    /**
     *
     * @param {SecurityTrainingData} securityTrainingData
     * @param {Account} account
     * @param {ConnectionEntity} connection
     * @param {Company} company
     * @param {boolean} isInitialSync
     */
    private async processUserCampaignData(
        campaign: TrainingCampaign,
        securityTrainingData: SecurityTrainingData,
        account: Account,
        connection: ConnectionEntity,
        company: Company,
        isInitialSync: boolean,
    ): Promise<Personnel> {
        const { identity, isCompliance, completedAt, assignment } = securityTrainingData;
        const { securityTraining, hipaaTraining, nistaiTraining } = company;
        const { url } = securityTrainingData;

        /**
         * Legacy code dictates that completedAt may be null here - this is resulting in bad
         * dates and behavior. We need to add a check here and verify we don't break anything
         */
        const completedAtDate = new Date(completedAt);

        /**
         * CompletedAt may be null here so we get very bizarre dates; ie 1970 dates.
         */
        const completedAtPlusTwoYears = new Date(
            completedAtDate.setFullYear(completedAtDate.getFullYear() + 2),
        );

        const domainsToIgnore = ['churnzero.net', 'socpilot.com'];

        /**
         * Sniff out if this is StackOne security training and sanity check the completedAt
         * date here ... support the legacy code until this is sussed out. The completedAt
         * date comes back as ~1970 when the date is null - this should not be expected ...
         */
        if (StackOneSecurityTrainingClientTypes.includes(connection.clientType)) {
            if (
                /**
                 * Check that we have a valid completedAt date.
                 */
                !isNil(completedAt) &&
                completedAtPlusTwoYears < new Date() &&
                !domainsToIgnore.includes(account.domain)
            ) {
                this.log(
                    'Campaign not processed for the user since it was completed more than a year ago.',
                    account,
                    {
                        assignment,
                        identity,
                        isCompliance,
                        completedAt,
                        clientType: ClientType[connection.clientType],
                    },
                );

                return;
            }
        } else {
            /**
             * Support the legacy behavior for non-stack one clients. Due to the null competedAt issue
             * above any training data with a null completedAt date will enter this block. This will
             * result in no processing of the data within `parseAndSaveSecurityTrainingData` ...
             */
            if (completedAtPlusTwoYears < new Date() && !domainsToIgnore.includes(account.domain)) {
                this.log(
                    'Campaign not processed for the user since it was completed more than a year ago.',
                    account,
                    {
                        assignment,
                        identity,
                        isCompliance,
                        completedAt,
                        clientType: ClientType[connection.clientType],
                    },
                );

                return;
            }
        }

        if (isNil(url)) {
            this.log(`Certificate URL not available to ${identity}`, account);
        }

        if (!isNil(identity)) {
            try {
                const personnel = await this.personnelCoreService.getPersonnelByEmail(identity);

                const isFormerOrOutOfScopeStatus = isInAFormerStatus(personnel.employmentStatus);

                const isSecurityTrainingSynchronizable = includes(
                    [
                        SecurityTrainingType.CURRICULA,
                        SecurityTrainingType.KNOWBE4,
                        ...StackOneSecurityTrainingTypes,
                    ],
                    securityTraining,
                );

                const isHipaaTrainingSynchronizable = includes(
                    [HipaaTrainingType.KNOWBE4],
                    hipaaTraining,
                );
                const isNistAiTrainingSynchronizable = includes(
                    [NistAITrainingType.KNOWBE4],
                    nistaiTraining,
                );

                // Add other providers to Security Training
                const isSynchronizable =
                    (isSecurityTrainingSynchronizable ||
                        isHipaaTrainingSynchronizable ||
                        isNistAiTrainingSynchronizable) &&
                    !isFormerOrOutOfScopeStatus;
                if (!isSynchronizable) {
                    this.log('This personnel is not synchronizable', account, {
                        identity,
                        securityTraining,
                    });
                    return;
                }

                const needsCustomCertificate =
                    (includes([SecurityTrainingType.KNOWBE4], securityTraining) ||
                        isHipaaTrainingSynchronizable ||
                        isNistAiTrainingSynchronizable) &&
                    isCompliance &&
                    isNil(url);

                this.log(`Syncing data for personnel on campaign ${campaign.name}`, account, {
                    identity,
                });

                await this.parseAndSaveSecurityTrainingData(
                    account,
                    personnel,
                    campaign,
                    securityTrainingData,
                    connection,
                    company,
                    needsCustomCertificate,
                    isInitialSync,
                );

                return personnel;
            } catch (error) {
                this.log(`Personnel not found for identity: ${identity}`, account, error);
            }
        } else {
            this.log("Can't create personnel data for this user", account, {
                identity,
            });
        }
    }

    private async parseAndSaveSecurityTrainingData(
        account: Account,
        personnel: Personnel,
        trainingCampaign: TrainingCampaign,
        securityTrainingData: SecurityTrainingData,
        connection: ConnectionEntity,
        company: Company,
        needsCustomCertificate: boolean,
        isInitialSync: boolean,
    ): Promise<void> {
        let fileName: string = null;
        let fileNameWithoutExtension: string = null;

        if (!securityTrainingData.isCompliance) {
            this.log('The campaign has not been completed by the user.');
        }

        if (securityTrainingData.isCompliance) {
            this.log(
                `The campaign has been completed by the user${
                    needsCustomCertificate ? ', attempting to create custom certificate' : ''
                }.`,
            );

            fileName = sanitizeFileName(
                `${ClientType[connection.clientType]}-${fullName(personnel.user)}-${connection.id}-${
                    trainingCampaign.trainingId
                }.pdf`,
            );

            fileNameWithoutExtension = !isNil(fileName) ? fileNameNoExt(fileName) : null;

            if (needsCustomCertificate) {
                const certificate = await this.generateCertificatePdfDownloadUrl(
                    account,
                    personnel.user,
                    securityTrainingData,
                    fileNameWithoutExtension,
                    company,
                    isInitialSync,
                    trainingCampaign.type,
                );
                if (!isEmpty(certificate?.signedUrl)) {
                    securityTrainingData.url = certificate.signedUrl;
                }
            }
        }

        return this.complianceChecksOrchestrationService.saveSecurityTrainingData(
            personnel,
            account,
            trainingCampaign,
            securityTrainingData,
            fileName,
            fileNameWithoutExtension,
            company,
            false,
        );
    }

    private async validateAndUpdateCampaignsName(
        securityTrainingSynchronizationService: ISecurityTrainingSynchronization,
        account: Account,
        connection: ConnectionEntity,
    ) {
        const assignments = await securityTrainingSynchronizationService.assignmentList(account);
        const activeCampaigns =
            await this.securityTrainingCoreService.getCampaignsByConnection(connection);

        for (const campaign of activeCampaigns) {
            const assignment = this.getCampaignNameById(assignments, campaign.trainingId);
            if (isNil(assignment)) {
                await this.securityTrainingCoreService.deleteTrainingCampaignAndTrainingChecks(
                    campaign.id,
                );
                this.log(
                    `Delete campaign ${campaign.id}-${campaign.trainingId} with name "${campaign.name}"`,
                );
            } else {
                await this.securityTrainingCoreService.updateTrainingCampaignName(
                    assignment.name,
                    campaign.trainingId,
                );
                this.log(
                    `Updated campaign ${campaign.id}-${campaign.trainingId} with name "${assignment.name}"`,
                );
            }
        }
    }

    private getCampaignNameById(
        assignmentList: SecurityTrainingAssignment[],
        id: string,
    ): SecurityTrainingAssignment {
        return assignmentList.find(assignment => assignment.id === id);
    }

    async getActiveCampaigns(
        account: Account,
        connection: ConnectionEntity,
    ): Promise<TrainingCampaign[]> {
        const trainingCampaignTypes = await this.getSyncableCompanyTrainings(account);

        if (isEmpty(trainingCampaignTypes)) {
            this.logger.log(
                PolloAdapter.acct(
                    `No active company trainings to be synced.`,
                    account,
                    this.constructor.name,
                )
                    .setSubContext(this.getActiveCampaigns.name)
                    .setIdentifier({
                        connection: ClientTypeDescription[connection.clientType],
                    }),
            );
            return [];
        }

        this.logger.log(
            PolloAdapter.acct(
                `Getting active company trainings to be synced.`,
                account,
                this.constructor.name,
            )
                .setSubContext(this.getActiveCampaigns.name)
                .setIdentifier({
                    connection: ClientTypeDescription[connection.clientType],
                    trainingCampaignTypes: trainingCampaignTypes.map(type => {
                        // just print the name of the training type, HIPAA i.e.
                        const complianceCheckType =
                            TrainingCampaignTypeComplianceCheckTypeMap.get(type);

                        return complianceCheckType
                            ? ComplianceCheckTypeNames.get(complianceCheckType)
                            : 'Unknown';
                    }),
                }),
        );
        return this.securityTrainingCoreService.getCampaignsByConnectionAndTypes(
            connection,
            trainingCampaignTypes,
        );
    }

    /**
     * @summary get the training campaign types that are eligible to be synced.
     * we want to know which training types are eligible to be synced
     * based on the company preferences. For example for security training if
     * customer picked Drata's embedded we won't sync security training campaigns.
     * @param account account
     * @returns array of training campaign types
     */
    async getSyncableCompanyTrainings(account: Account): Promise<TrainingCampaignType[]> {
        const company = await this.getCompanyByAccount(account);
        const { securityTraining, hipaaTraining, nistaiTraining } = company;
        const trainingTypes: TrainingCampaignType[] = [];

        if (
            !isNil(securityTraining) &&
            [
                SecurityTrainingType.CURRICULA,
                SecurityTrainingType.KNOWBE4,
                ...StackOneSecurityTrainingTypes,
            ].includes(securityTraining)
        ) {
            trainingTypes.push(TrainingCampaignType.SECURITY_TRAINING);
        }

        if (!isNil(hipaaTraining) && [HipaaTrainingType.KNOWBE4].includes(hipaaTraining)) {
            trainingTypes.push(TrainingCampaignType.HIPAA_TRAINING);
        }

        if (!isNil(nistaiTraining) && [NistAITrainingType.KNOWBE4].includes(nistaiTraining)) {
            trainingTypes.push(TrainingCampaignType.NIST_AI_TRAINING);
        }

        return trainingTypes;
    }

    /**
     * Generates a PDF representing a security training certificate based on
     * the data provided by a security training provider. This function is intended
     * to be used whenever security training providers are not able to provide a
     * certificate URL from their APIs.
     * @param account
     * @param user
     * @param securityTrainingData
     * @param type
     * @param isInitialSync
     * @returns Certificate URL
     */
    async generateCertificatePdfDownloadUrl(
        account: Account,
        user: User,
        trainingData: SecurityTrainingData,
        fileNameWithoutExtension: string,
        company: Company,
        isInitialSync: boolean,
        trainingCampaignType: TrainingCampaignType,
    ): Promise<DownloaderPayloadType> {
        try {
            const userHasValidTrainingDocument =
                await this.complianceChecksOrchestrationService.userHasValidTrainingDocument(
                    trainingCampaignType,
                    user,
                    fileNameWithoutExtension,
                );

            /**
             * The user already has a valid document. It is not necessary to generate a new one.
             */
            if (userHasValidTrainingDocument && !isInitialSync) {
                this.logger.log(
                    PolloAdapter.acct(
                        `Skipping certificate creation since user has already a valid one.`,
                        account,
                    ),
                );
                return;
            }

            const source = fs.readFileSync(
                path.join(__dirname, SecurityTrainingConstants.HANDLEBARS_CERTIFICATE_PATH),
                'utf8',
            );

            const template = hbs.handlebars.compile(source);

            if (isEmpty(trainingData?.url)) {
                // Remove url property in case the value is null or empty
                delete trainingData.url;
            }

            const service = getServiceForTrainingType(trainingCampaignType, company);

            const html = template({
                companyName: account.companyName,
                userName: !isNil(user) ? fullName(user) : trainingData.identity,
                timestamp: !isNil(trainingData.completedAt)
                    ? moment
                          .utc(trainingData.completedAt)
                          .format(SecurityTrainingConstants.TIME_FORMAT)
                    : moment.utc().format(SecurityTrainingConstants.TIME_FORMAT),
                description: SecurityTrainingConstants.EVIDENCE_DESCRIPTION,
                service,
                accountId: user.id,
                createdAt: moment.utc().format(SecurityTrainingConstants.TIME_FORMAT),
                json: JSON.stringify(trainingData, null, 2),
            });

            if (isNil(html)) {
                throw new InternalServerErrorException();
            }

            const { data, mimetype } = await this.htmlToPdfConverter.convertToPdfBuffer(
                html,
                account,
            );

            const fileName = sanitizeFileName(
                `${fileNameDate()}-${ProviderType[ProviderType.SECURITY_TRAINING]}-${service}-${
                    trainingData.id
                }.pdf`,
            );

            const uploadedFile = await this.uploader.uploadPrivateFileFromBuffer(
                account.id,
                UploadType.EVIDENCE,
                data,
                fileName,
                mimetype,
            );

            return await this.downloader.getDownloadUrl(uploadedFile.key);
        } catch (error) {
            this.logger.error(
                PolloAdapter.acct(error.message, account)
                    .setError(error)
                    .setContext(this.constructor.name)
                    .setSubContext(this.generateCertificatePdfDownloadUrl.name),
            );

            throw error;
        }
    }

    /**
     * @param {ConnectionEntity} connection
     * @param {Account} account
     */
    private async getSecurityTrainingSynchronizationApi(
        connection: ConnectionEntity,
        account: Account,
    ): Promise<ISecurityTrainingSynchronization> {
        const provider = await this.getProvider(account, connection);
        if (isNil(provider)) {
            this.log(
                `No API client found for connection with id ${connection.id}, skipping...`,
                account,
            );
            return;
        }
        return provider;
    }

    /**
     * @param {Account} account
     */
    private getCompanyByAccount(account: Account): Promise<Company> {
        return this.companiesCoreService.findCompanyByAccountId(account.id);
    }

    /**
     * @param {Account} account
     */
    finalize(account: Account): void {}

    private async deleteTrainingChecksByPersonnel(
        account: Account,
        personnelToExcludeDeleteChecks: {
            [key: number]: number[];
        },
    ): Promise<void> {
        for await (const [key, value] of Object.entries(personnelToExcludeDeleteChecks)) {
            try {
                await this.securityTrainingComplianceChecksCoreService.deleteSecurityTrainingChecksByPersonnel(
                    parseInt(key),
                    uniq(value),
                );
            } catch (error) {
                this.logger.error(
                    PolloAdapter.acct(
                        `Something went wrong while deleting training checks by personnel for campaign with id ${key}`,
                        account,
                    )
                        .setContext(this.constructor.name)
                        .setSubContext(this.deleteTrainingChecksByPersonnel.name)
                        .setError(error),
                );
            }
        }
    }

    private setSecurityTrainingComplianceCheckForPersonnel(
        account: Account,
        connection: ConnectionEntity,
        personnel: Personnel[],
        company: Company,
        isHipaaFrameworkEnabled: boolean,
        isNistAiFrameworkEnabled: boolean,
    ): Promise<void> {
        const { securityTraining, hipaaTraining, nistaiTraining } = company;
        return (Promise as any).allSettled(
            personnel.map(person => {
                const promises: Promise<void>[] = [];
                if (securityTraining) {
                    promises.push(
                        this.complianceChecksOrchestrationService.updateSecurityTrainingComplianceCheck(
                            account,
                            connection,
                            person,
                            securityTraining,
                        ),
                    );
                }
                if (isHipaaFrameworkEnabled && hipaaTraining) {
                    promises.push(
                        this.complianceChecksOrchestrationService.updateHipaaTrainingComplianceCheck(
                            account,
                            connection,
                            person,
                            hipaaTraining,
                        ),
                    );
                }
                if (isNistAiFrameworkEnabled && nistaiTraining) {
                    promises.push(
                        this.complianceChecksOrchestrationService.updateNistAiTrainingComplianceCheck(
                            account,
                            connection,
                            person,
                            nistaiTraining,
                        ),
                    );
                }
                return Promise.all(promises);
            }),
        );
    }

    /**
     * Cleans up compliance check exclusions for a given account.
     * This method archives expired exclusions, removes obsolete group exclusions,
     * and resets stale exclusion statuses.
     *
     * @param account - The account for which to cleanup compliance check exclusions
     * @returns A Promise that resolves when the cleanup is complete
     */
    private async cleanupComplianceCheckExclusions(account: Account): Promise<void> {
        try {
            this.log('Starting compliance check exclusions cleanup', account);

            await this.complianceChecksOrchestrationService.archiveExpiredOrObsoleteExclusions(
                account,
            );
            await this.complianceChecksOrchestrationService.cleanupStaleExclusionStatuses(account);

            this.log('Completed compliance check exclusions cleanup', account);
        } catch (error) {
            this.error(error, account, 'Failed to cleanup compliance check exclusions');
            // Don't throw - allow sync to continue
        }
    }
}

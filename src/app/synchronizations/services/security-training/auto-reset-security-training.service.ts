import { ComplianceCheckType } from '@drata/enums';
import { Injectable } from '@nestjs/common';
import { Company } from 'app/companies/entities/company.entity';
import { CompaniesCoreService } from 'app/companies/services/companies-core.service';
import { AutoResetTypeNames } from 'app/security-training/enums/auto-reset-type-names.map';
import {
    AutoResetTrainingSettingsType,
    AutoResetTrainingType,
} from 'app/security-training/types/auto-reset-training.type';
import { Setting } from 'app/settings/entities/setting.entity';
import { SettingsCoreService } from 'app/settings/services/settings-core.service';
import { AutoResetConfig } from 'app/settings/types/auto-reset-config.type';
import { User } from 'app/users/entities/user.entity';
import { ComplianceCheckTypeNames } from 'app/users/personnel/entities/compliance-check-type-names.map';
import { Personnel } from 'app/users/personnel/entities/personnel.entity';
import { ComplianceChecksOrchestrationService } from 'app/users/personnel/services/compliance-checks-orchestration.service';
import { PersonnelCoreService } from 'app/users/personnel/services/personnel-core.service';
import { Account } from 'auth/entities/account.entity';
import { AutoResetType } from 'commons/enums/security-training/auto-reset-type.enum';
import { SettingType } from 'commons/enums/settings/settings-type.enum';
import { TrainingCampaignType } from 'commons/enums/training-campaign-type.enum';
import { UserDocumentType } from 'commons/enums/users/user-document-type.enum';
import { asyncForOf } from 'commons/helpers/array.helper';
import { isDocumentValidAfterDate } from 'commons/helpers/compliance-check.helper';
import { formatDateToReadable } from 'commons/helpers/date.helper';
import { AppService } from 'commons/services/app.service';
import { has, isEmpty, isNil } from 'lodash';
import moment from 'moment';
import { PolloMessage } from 'pollo-logger/classes/pollo.message';
import { tenantWrapper } from 'tenancy/contexts/tenant-wrapper';

@Injectable()
export class AutoResetSecurityTrainingService extends AppService {
    constructor(
        private readonly companiesCoreService: CompaniesCoreService,
        private readonly settingsCoreService: SettingsCoreService,
        private readonly personnelCoreService: PersonnelCoreService,
        private readonly complianceChecksOrchestrationService: ComplianceChecksOrchestrationService,
    ) {
        super();
    }

    /**
     * will execute the configured auto reset type
     */
    async run(account: Account): Promise<void> {
        this.logger.log(
            PolloMessage.msg(`Starting Auto Reset Training.`, this.constructor.name)
                .setSubContext('autoResetTrainings')
                .setDomain(account.domain)
                .setAccountId(account.id),
        );

        const company = await this.companiesCoreService.getCompanyByAccountId(account.id);

        /**
         * get all active trainings from the company table
         */
        const trainings = this.getActiveCompanyTrainings(company);

        if (isEmpty(trainings)) {
            this.logger.log(
                PolloMessage.msg(
                    `No enabled trainings were found, finishing Auto Reset Training for account.`,
                    this.constructor.name,
                )
                    .setSubContext('autoResetTrainings')
                    .setDomain(account.domain)
                    .setAccountId(account.id),
            );
            return;
        }

        await asyncForOf(trainings, async (training: AutoResetTrainingType) => {
            let settings: Setting;

            try {
                settings = await tenantWrapper(account, () => {
                    return this.settingsCoreService.getSettingBySettingType(training.settingType);
                });
            } catch {
                settings = null;
            }

            if (isNil(settings)) {
                return;
            }

            const autoResetConfig = settings.configObject as AutoResetConfig;
            const complianceCheckType = training.complianceCheckType;

            const autoResetTrainingType: AutoResetTrainingSettingsType = {
                ...training,
                settings,
            };

            let autoResetType: number | null = null;

            if (has(autoResetConfig, 'type')) {
                autoResetType = Number(autoResetConfig.type);
            }

            if (!isNil(autoResetType)) {
                this.logger.log(
                    PolloMessage.msg(
                        `Executing ${AutoResetTypeNames.get(
                            autoResetType,
                        )} for ${ComplianceCheckTypeNames.get(complianceCheckType)}`,
                        this.constructor.name,
                    )
                        .setSubContext('autoResetTrainings')
                        .setDomain(account.domain)
                        .setAccountId(account.id),
                );
            }

            switch (autoResetType) {
                case AutoResetType.ROLLING_RESET:
                    await this.doRollingReset(account, autoResetTrainingType);
                    break;
                case AutoResetType.GLOBAL_RESET:
                    await this.doGlobalReset(account, autoResetTrainingType);
                    break;
                case AutoResetType.NO_AUTOMATION:
                    this.doNoAutomationReset(account);
                    break;
                default:
                    this.notImplemented(account);
                    break;
            }
        });

        this.logger.log(
            PolloMessage.msg(`Finished Auto Reset Training.`, this.constructor.name)
                .setSubContext('autoResetTrainings')
                .setDomain(account.domain)
                .setAccountId(account.id),
        );
    }

    /**
     * get active trainings based on company
     * @param company
     * @returns
     */
    private getActiveCompanyTrainings(company: Company): AutoResetTrainingType[] {
        const trainings = [];

        if (!isNil(company.hipaaTraining)) {
            trainings.push({
                trainingCampaignType: TrainingCampaignType.HIPAA_TRAINING,
                settingType: SettingType.HIPAA_TRAINING,
                complianceCheckType: ComplianceCheckType.HIPAA_TRAINING,
            });
        }

        if (!isNil(company.nistaiTraining)) {
            trainings.push({
                trainingCampaignType: TrainingCampaignType.NIST_AI_TRAINING,
                settingType: SettingType.NIST_AI_TRAINING,
                complianceCheckType: ComplianceCheckType.NIST_AI_TRAINING,
            });
        }

        if (!isNil(company.securityTraining)) {
            trainings.push({
                trainingCampaignType: TrainingCampaignType.SECURITY_TRAINING,
                settingType: SettingType.SECURITY_TRAINING,
                complianceCheckType: ComplianceCheckType.SECURITY_TRAINING,
            });
        }

        return trainings;
    }

    /**
     * Reset 12 months after each personnel’s last completion date
     * @param account
     * @param autoResetTrainingType
     */
    async doRollingReset(
        account: Account,
        autoResetTrainingType: AutoResetTrainingType,
    ): Promise<void> {
        const currentDate = new Date();
        const oneYearAgoUtcDate = moment.utc(currentDate).subtract(1, 'year');
        const { complianceCheckType } = autoResetTrainingType;
        const limit = 1_000;
        let nextPageToken = 1;

        /**
         * reset personnel in batches
         */
        do {
            const paginatedPersonnel =
                // eslint-disable-next-line no-await-in-loop
                await this.personnelCoreService.getActivePersonnelByComplianceCheckType(
                    { page: nextPageToken, limit },
                    complianceCheckType,
                );

            /**
             * get personnel with expired training
             */
            const personnelToReset = paginatedPersonnel.data.filter(personnel => {
                /**
                 * get personnel compliance check
                 */
                const personnelComplianceCheck = personnel.complianceChecks.find(
                    complianceCheck => complianceCheck.type === complianceCheckType,
                );

                if (isNil(personnelComplianceCheck?.completionDate)) {
                    return false;
                }

                const completionDate = new Date(personnelComplianceCheck.completionDate);
                const userDocuments = personnel?.user?.documents || [];

                const validUserDocuments = userDocuments
                    .filter(document =>
                        [
                            UserDocumentType.SEC_TRAINING,
                            UserDocumentType.HIPAA_TRAINING_EVIDENCE,
                            UserDocumentType.NIST_AI_TRAINING_EVIDENCE,
                        ].includes(document.type),
                    )
                    .filter(({ renewalDate, createdAt }) =>
                        isDocumentValidAfterDate(
                            new Date(renewalDate),
                            createdAt,
                            oneYearAgoUtcDate.toDate(),
                        ),
                    );
                /**
                 * add 1 year to completion date and compare with current date
                 */
                completionDate.setFullYear(completionDate.getFullYear() + 1);

                const isTrainingsExpired = isEmpty(validUserDocuments);
                const isCompletionDateExpired = currentDate > completionDate;
                // If the completion date is expired and the training is expired, we need to reset
                return isCompletionDateExpired && isTrainingsExpired;
            });

            if (isEmpty(personnelToReset)) {
                this.logger.log(
                    PolloMessage.msg(
                        `Found ${personnelToReset.length} personnel with expired trainings, skipping reset for now.`,
                        this.constructor.name,
                    )
                        .setSubContext('autoResetTrainings')
                        .setDomain(account.domain)
                        .setAccountId(account.id),
                );
                return;
            }

            this.logger.log(
                PolloMessage.msg(
                    `Found ${personnelToReset.length} personnel with expired trainings, reset training for them.`,
                    this.constructor.name,
                )
                    .setSubContext('autoResetTrainings')
                    .setDomain(account.domain)
                    .setAccountId(account.id),
            );

            /**
             * reset training individually
             */
            const personnelEmailsToReset: string[] = [];

            // eslint-disable-next-line no-await-in-loop
            await asyncForOf(personnelToReset, async (personnel: Personnel) => {
                const extendedPersonnel =
                    await this.complianceChecksOrchestrationService.resetTraining(
                        account,
                        {} as User, // we don't require a user here since it's used to create an event
                        complianceCheckType,
                        personnel.id,
                    );

                personnelEmailsToReset.push(extendedPersonnel.user.email);
            });

            /**
             * log a summary of the rolling reset
             */
            this.logger.log(
                PolloMessage.msg(
                    `Reset ${ComplianceCheckTypeNames.get(complianceCheckType)} for ${
                        personnelEmailsToReset.length
                    } personnel.`,
                    this.constructor.name,
                )
                    .setSubContext('autoResetTrainings')
                    .setDomain(account.domain)
                    .setAccountId(account.id),
            );

            /**
             * set page token according to the results
             */
            nextPageToken = paginatedPersonnel.data.length < limit ? null : nextPageToken + 1;
        } while (!isNil(nextPageToken));
    }

    /**
     * Reset on the same date each year for all personnel
     * @param account
     * @param autoResetTrainingType
     */
    async doGlobalReset(
        account: Account,
        autoResetTrainingType: AutoResetTrainingSettingsType,
    ): Promise<void> {
        const { complianceCheckType } = autoResetTrainingType;
        const currentDate = new Date();
        const config = autoResetTrainingType.settings.configObject as AutoResetConfig;
        const { day, month } = config;

        /**
         * create renewal date using customer preferences (month and day)
         */
        const renewalDate = new Date(new Date().getFullYear(), month - 1, day);
        /**
         * we want to reset training globally just that exact day
         */
        const shouldResetTraining = moment(currentDate).isSame(renewalDate, 'day');
        const renewalMessage = shouldResetTraining
            ? 'resetting training now'
            : 'skipping reset for now';

        this.logger.log(
            PolloMessage.msg(
                `Renewal date for ${ComplianceCheckTypeNames.get(
                    complianceCheckType,
                )} is ${formatDateToReadable(renewalDate)}, ${renewalMessage}.`,
                this.constructor.name,
            )
                .setSubContext('autoResetTrainings')
                .setDomain(account.domain)
                .setAccountId(account.id),
        );

        if (shouldResetTraining) {
            await this.complianceChecksOrchestrationService.resetTrainingForMultiple(
                account,
                {} as User,
                complianceCheckType,
            );

            this.logger.log(
                PolloMessage.msg(
                    `Reset ${ComplianceCheckTypeNames.get(complianceCheckType)} for all personnel.`,
                    this.constructor.name,
                )
                    .setSubContext('autoResetTrainings')
                    .setDomain(account.domain)
                    .setAccountId(account.id),
            );
        }
    }

    /**
     * No automation required
     * @param account
     */
    doNoAutomationReset(account: Account): void {
        this.logger.log(
            PolloMessage.msg(
                `Auto Reset is disabled for this type of training.`,
                this.constructor.name,
            )
                .setSubContext('autoResetTrainings')
                .setDomain(account.domain)
                .setAccountId(account.id),
        );
    }

    /**
     * Default response for invalid option
     * @param account
     */
    notImplemented(account: Account): void {
        this.logger.log(
            PolloMessage.msg(`Auto Reset Option is not implemented.`, this.constructor.name)
                .setSubContext('autoResetTrainings')
                .setDomain(account.domain)
                .setAccountId(account.id),
        );
    }
}

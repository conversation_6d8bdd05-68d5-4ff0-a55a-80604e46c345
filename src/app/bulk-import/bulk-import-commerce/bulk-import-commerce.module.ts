import { Module } from '@nestjs/common';
import { BulkImportCommerceService } from 'app/bulk-import/bulk-import-commerce/bulk-import-commerce.service';
import { BulkImportControlFileProcessor } from 'app/bulk-import/bulk-import-commerce/file-processors/bulk-import-control-file-processor';
import { BulkImportTrainingFileProcessor } from 'app/bulk-import/bulk-import-commerce/file-processors/bulk-import-training-file-processor';
import { ControlsBlueprint } from 'app/bulk-import/bulk-import-commerce/schemas/controls/bulk-import.controls.blueprint';
import { TrainingBlueprint } from 'app/bulk-import/bulk-import-commerce/schemas/training/bulk-import.training.blueprint';
import { BulkImportFileProcessorFactoryService } from 'app/bulk-import/bulk-import-commerce/services/bulk-import-file-processor-factory.service';
import { BulkImportFlatfileService } from 'app/bulk-import/bulk-import-commerce/services/bulk-import-flatfile.service';
import { ControlsBlueprintService } from 'app/bulk-import/bulk-import-commerce/services/controls-blueprint.service';
import { FlatFileBlueprintService } from 'app/bulk-import/bulk-import-commerce/services/flatfile-blueprint.service';
import { ControlValidationService } from 'app/bulk-import/bulk-import-commerce/validations/controls/control-validations.service';
import { CustomFieldValidationService } from 'app/bulk-import/bulk-import-commerce/validations/custom-fields/custom-field-validation.service';
import { ControlOrchestrationModule } from 'app/control/control-orchestration.module';
import { ControlModule } from 'app/control/control.module';
import { FrameworksCoreModule } from 'app/frameworks/frameworks-core.module';
import { RequirementsCoreModule } from 'app/frameworks/requirements-core.module';
import { NotesOrchestrationModule } from 'app/notes/notes-orchestration.module';
import { RiskOrchestrationModule } from 'app/risk-management/risk-orchestration.module';
import { UsersCoreModule } from 'app/users/users-core.module';
import { AccountsCoreModule } from 'auth/accounts-core-module';
import { ModuleType, ModuleTypes } from 'commons/decorators/module-type.decorator';

@ModuleType(ModuleTypes.COMMERCE)
@Module({
    imports: [
        RiskOrchestrationModule,
        UsersCoreModule,
        AccountsCoreModule,
        FrameworksCoreModule,
        RequirementsCoreModule,
        NotesOrchestrationModule,
        ControlModule,
        ControlOrchestrationModule,
    ],

    providers: [
        BulkImportCommerceService,
        BulkImportControlFileProcessor,
        BulkImportFileProcessorFactoryService,
        BulkImportFlatfileService,
        ControlsBlueprint,
        ControlsBlueprintService,
        ControlValidationService,
        FlatFileBlueprintService,
        CustomFieldValidationService,
        TrainingBlueprint,
        BulkImportTrainingFileProcessor,
    ],
    exports: [
        BulkImportCommerceService,
        BulkImportControlFileProcessor,
        BulkImportTrainingFileProcessor,
        BulkImportFileProcessorFactoryService,
        BulkImportFlatfileService,
    ],
})
export class BulkImportCommerceModule {}

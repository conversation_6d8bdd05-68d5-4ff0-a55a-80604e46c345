import { SheetConfig } from '@flatfile/api/api';
import { Injectable } from '@nestjs/common';
import { FlatfileBlueprint } from 'app/bulk-import/bulk-import-commerce/classes/flatfile-blueprint';
import { TrainingB<PERSON>printField } from 'app/bulk-import/bulk-import-commerce/schemas/training/bulk-import.training.fields';
import { BulkImportEntityEnum } from 'app/bulk-import/enums/bulk-import.entity.enum';
import { Account } from 'auth/entities/account.entity';

@Injectable()
export class TrainingBlueprint extends FlatfileBlueprint {
    EVIDENCE_FILE_REFERENCE_SHEET = 'training-references';

    constructor() {
        super(BulkImportEntityEnum.TRAINING);
    }

    async getBlueprints(_account: Account): Promise<SheetConfig[]> {
        return [
            {
                name: 'Training list',
                slug: 'training',
                fields: [
                    {
                        key: TrainingBlueprintField.EMAIL,
                        type: 'string',
                        label: 'Personnel Email',
                        description: 'Email of the personnel.',
                        constraints: [{ type: 'required' }],
                    },
                    {
                        key: TrainingBlueprintField.DATE,
                        type: 'date',
                        label: 'Completion Date',
                        description: 'Date on which the training was completed.',
                        constraints: [{ type: 'required' }],
                    },
                    {
                        key: TrainingBlueprintField.EVIDENCE,
                        type: 'string', // this should change to "reference" [ENG-75075]
                        label: 'Evidence',
                        description: 'Training completion evidence file.',
                        constraints: [{ type: 'required' }],
                        // config: { [ENG-75075] will implement this
                        //     ref: this.EVIDENCE_FILE_REFERENCE_SHEET,
                        //     key: 'filename',
                        // },
                    },
                ],
            },
        ];
    }
}

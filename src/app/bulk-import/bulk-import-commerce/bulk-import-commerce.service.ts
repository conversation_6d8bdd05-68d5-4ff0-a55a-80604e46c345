import { BadRequestException, Injectable } from '@nestjs/common';
import { FlatfileTemporalRecord } from 'app/bulk-import/bulk-import-commerce/classes/flatfile-temporal.record.class';
import { BulkImportFileProcessorFactoryService } from 'app/bulk-import/bulk-import-commerce/services/bulk-import-file-processor-factory.service';
import { BulkImportFlatfileService } from 'app/bulk-import/bulk-import-commerce/services/bulk-import-flatfile.service';
import { FlatFileSdk } from 'app/bulk-import/bulk-import-commerce/services/flat-file.sdk';
import { CustomFieldValidationService } from 'app/bulk-import/bulk-import-commerce/validations/custom-fields/custom-field-validation.service';
import {
    BulkImportEntityEnum,
    BulkImportSubCategoryEnum,
} from 'app/bulk-import/enums/bulk-import.entity.enum';
import { EntityTypeCustomFieldMap } from 'app/bulk-import/enums/entity-type-custom-field.map';
import { BulkImportOperation } from 'app/bulk-import/types/bulk-import-operation.type';
import { BulkImportPreview } from 'app/bulk-import/types/bulk-import-preview.type';
import { BulkImportRecordUpdateSummary } from 'app/bulk-import/types/bulk-import-record-update-summary.type';
import { BulkImportSpace } from 'app/bulk-import/types/bulk-import-space.type';
import { CustomFieldLocation } from 'app/custom-fields/entities/custom-field-location.entity';
import { isCustomFieldsEnabled } from 'app/custom-fields/helpers/custom-fields.helper';
import { CustomFieldsLocationsRepository } from 'app/custom-fields/repositories/custom-fields-locations.repository';
import { UsersCoreService } from 'app/users/services/users-core.service';
import { Account } from 'auth/entities/account.entity';
import { AccountsCoreService } from 'auth/services/accounts-core.service';
import { ChainsConfig } from 'bulk-import-validation/classes/validation-chain-builder';
import { Benchmark } from 'commons/benchmark/benchmark';
import { AppService } from 'commons/services/app.service';
import { WorkspacesBaseService } from 'commons/services/workspaces-base.service';
import { AccountIdType } from 'commons/types/account-id.type';
import { isNil } from 'lodash';
import { PolloMessage } from 'pollo-logger/classes/pollo.message';

@Injectable()
export class BulkImportCommerceService extends AppService {
    private readonly flatFileSdk: FlatFileSdk;
    constructor(
        private readonly processorService: BulkImportFileProcessorFactoryService,
        private readonly flatfileService: BulkImportFlatfileService,
        private readonly accountCoreService: AccountsCoreService,
        private readonly usersCoreService: UsersCoreService,
        private readonly workspacesBaseService: WorkspacesBaseService,
        private readonly customFieldValidationService: CustomFieldValidationService,
    ) {
        super();
        this.flatFileSdk = new FlatFileSdk();
    }

    /**
     *
     * @param bulkImportOperation
     */
    async createSpace(
        account: Account,
        workspaceId: number,
        entityType: BulkImportEntityEnum,
        userId: number,
        subEntityType?: BulkImportSubCategoryEnum,
    ): Promise<BulkImportSpace> {
        const blueprintEntity = this.processorService.getBlueprint(entityType);
        const [principalSheet, ...sheets] = await blueprintEntity.getBlueprints(
            account,
            workspaceId,
        );

        return this.flatFileSdk.createSpace({
            name: principalSheet.name,
            documents: blueprintEntity.getDocuments(),
            accountId: account.id,
            blueprints: [principalSheet, ...sheets],
            entityType,
            workspaceId,
            userId,
            subEntityType,
        });
    }

    /**
     * TODO: implementation pending
     * @param bulkImportOperation
     */
    async transform(bulkImportOperation: BulkImportOperation): Promise<void> {
        const { entityType, context } = bulkImportOperation;
        const fileProcessor = this.processorService.getFileProcessor(entityType);

        await this.flatfileService.reportRecords(
            context.sheetId,
            fileProcessor.transformRecords(
                this.flatfileService.getRecords(
                    context.sheetId,
                    this.processorService.getFields(entityType),
                ),
            ),
        );
    }

    /**
     *
     * @param bulkImportOperationDto
     */
    async validate(bulkImportOperationDto: BulkImportOperation): Promise<void> {
        const { entityType, accountId, context } = bulkImportOperationDto;

        if (!accountId) {
            throw new BadRequestException('AccountId is required');
        }

        this.logger.log(
            PolloMessage.msg('Starting bulk import validation pipeline for sheet').setIdentifier({
                sheetId: context.sheetId,
            }),
        );

        const benchmark = new Benchmark();

        try {
            const fileProcessor = this.processorService.getFileProcessor(entityType);

            const workspace = await this.workspacesBaseService.getProductById(
                Number(context.workspaceId),
            );

            const account = await this.accountCoreService.getAccountById(accountId);

            if (!workspace) {
                throw new BadRequestException('Workspace not found');
            }

            if (!account) {
                throw new BadRequestException('Account is required');
            }

            /**
             * custom fields validations are generated here before iterations
             * to avoid generating them for each record
             */
            const customFieldsValidations = await this.getCustomFieldValidationsForEntity(
                account,
                entityType,
            );

            context.account = account;
            context.workspaceName = workspace.name;
            fileProcessor.setContext(context);
            fileProcessor.setCustomFieldValidations(customFieldsValidations);

            const summary = await this.flatfileService.reportRecords(
                context.sheetId,
                fileProcessor.validateRecords(
                    this.flatfileService.getRecords(
                        context.sheetId,
                        this.processorService.getFields(entityType),
                    ),
                ),
            );

            this.logger.log(
                PolloMessage.msg('Bulk import validation pipeline for sheet completed.')
                    .setIdentifier({
                        sheetId: context.sheetId,
                        summary,
                    })
                    .setExecutionTime(benchmark.endTime()),
            );
        } catch (error) {
            this.logger.error(
                PolloMessage.msg(
                    'Something went wrong while running validation pipeline for sheet.',
                )
                    .setIdentifier({
                        sheetId: context.sheetId,
                    })
                    .setError(error)
                    .setExecutionTime(benchmark.endTime()),
            );
        }
    }

    /**
     *
     * @param bulkImportOperationDto
     */
    async save(
        entityType: BulkImportEntityEnum,
        spaceId: string,
        sheetId: string,
        accountId: AccountIdType,
        userId: number,
        workspaceId?: number,
    ): Promise<BulkImportRecordUpdateSummary> {
        const isValid = await this.flatfileService.validateSpaceEntityType(spaceId, entityType);

        if (!isValid) {
            throw new BadRequestException('Invalid entity type for space');
        }

        const processor = this.processorService.getFileProcessor(entityType);
        processor.setWorkspaceId(workspaceId);
        const targetEntityDto = processor.dtoClass;
        const account = await this.accountCoreService.getAccountById(accountId);
        const user = await this.usersCoreService.getUserById(userId); // Get user by ID

        this.logger.log(
            PolloMessage.msg('Starting bulk import save pipeline for sheet.').setIdentifier({
                sheetId,
                spaceId,
            }),
        );

        const benchmark = new Benchmark();

        try {
            const saveGenerator = processor.saveOrReportErrors(
                account,
                user,
                processor.convertToDto(
                    targetEntityDto,
                    this.flatfileService.getRecords(
                        sheetId,
                        this.processorService.getFields(entityType),
                        { includeMessages: true },
                    ),
                ),
            );

            const errorRecords: FlatfileTemporalRecord[] = [];
            const errorMessages: string[] = [];
            let successfullyCreated = 0;
            let totalProcessed = 0;

            for await (const result of saveGenerator) {
                totalProcessed++;
                if (result.errors) {
                    errorRecords.push(result.record);
                    const errorMessage =
                        result.errors.map(e => e.message).join('; ') || 'Validation error';
                    errorMessages.push(errorMessage);
                } else {
                    successfullyCreated++;
                }
            }

            const summary: BulkImportRecordUpdateSummary = {
                totalRecords: totalProcessed,
                recordsWithErrors: errorRecords.length,
                successfullyCreated: successfullyCreated,
                errorSummaries: [...new Set(errorMessages)],
            };

            this.logger.log(
                PolloMessage.msg('Bulk import save pipeline for sheet completed.')
                    .setIdentifier({
                        sheetId: sheetId,
                        summary,
                        totalCreated: summary.successfullyCreated,
                    })
                    .setExecutionTime(benchmark.endTime()),
            );

            return summary;
        } catch (error) {
            this.logger.error(
                PolloMessage.msg('Something went wrong while running save pipeline for sheet.')
                    .setIdentifier({
                        sheetId: sheetId,
                        spaceId: spaceId,
                    })
                    .setError(error)
                    .setExecutionTime(benchmark.endTime()),
            );

            return {
                totalRecords: 0,
                recordsWithErrors: 0,
                successfullyCreated: 0,
            };
        }
    }

    async getPreview(bulkImportOperation: BulkImportOperation): Promise<BulkImportPreview> {
        const { entityType, context } = bulkImportOperation;
        const processor = this.processorService.getFileProcessor(entityType);

        processor.setContext(context);
        processor.cleanSummary();

        try {
            return await processor.generatePreview(
                this.flatfileService.getRecords(
                    context.sheetId,
                    this.processorService.getFields(entityType),
                    { includeMessages: true },
                ),
            );
        } catch (error) {
            throw new BadRequestException(`Unable to generate import summary: ${error.message}`);
        }
    }

    private async getCustomFieldsLocationByEntity(
        entityType: BulkImportEntityEnum,
        account: Account,
    ): Promise<CustomFieldLocation[]> {
        const customFieldEnabled = await isCustomFieldsEnabled(account);

        if (!customFieldEnabled) {
            return [];
        }

        const customFieldEntityType = EntityTypeCustomFieldMap.get(entityType);

        if (isNil(customFieldEntityType)) {
            return [];
        }

        return this.customFieldsLocationsRepository.getCustomFieldLocationsByEntity(
            customFieldEntityType,
        );
    }

    private async getCustomFieldValidationsForEntity(
        account: Account,
        entityType: BulkImportEntityEnum,
    ) {
        let customFieldsValidations: ChainsConfig<unknown> = {};

        if (account) {
            const customFields = await this.getCustomFieldsLocationByEntity(entityType, account);

            customFieldsValidations =
                await this.customFieldValidationService.generateChainsConfig(customFields);
        }

        return customFieldsValidations;
    }

    private get customFieldsLocationsRepository(): CustomFieldsLocationsRepository {
        return this.getCustomTenantRepository(CustomFieldsLocationsRepository);
    }
}

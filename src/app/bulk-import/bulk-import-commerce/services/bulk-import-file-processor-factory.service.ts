import { Injectable } from '@nestjs/common';
import { BulkImportFileProcessor } from 'app/bulk-import/bulk-import-commerce/classes/bulk-import-file-processor.class';
import { FlatfileBlueprint } from 'app/bulk-import/bulk-import-commerce/classes/flatfile-blueprint';
import { BulkImportControlFileProcessor } from 'app/bulk-import/bulk-import-commerce/file-processors/bulk-import-control-file-processor';
import { BulkImportTrainingFileProcessor } from 'app/bulk-import/bulk-import-commerce/file-processors/bulk-import-training-file-processor';
import { ControlsBlueprint } from 'app/bulk-import/bulk-import-commerce/schemas/controls/bulk-import.controls.blueprint';
import { ControlBlueprintField } from 'app/bulk-import/bulk-import-commerce/schemas/controls/bulk-import.controls.fields';
import { TrainingBlueprint } from 'app/bulk-import/bulk-import-commerce/schemas/training/bulk-import.training.blueprint';
import { TrainingBlueprintField } from 'app/bulk-import/bulk-import-commerce/schemas/training/bulk-import.training.fields';
import { BulkImportEntityEnum } from 'app/bulk-import/enums/bulk-import.entity.enum';
import { AppService } from 'commons/services/app.service';

type EntityRegistry = Partial<Record<BulkImportEntityEnum, EntityRegistryItem>>;

type EntityRegistryItem = {
    fields: Record<string, string>;
    blueprint: FlatfileBlueprint;
    processor: BulkImportFileProcessor;
};

@Injectable()
export class BulkImportFileProcessorFactoryService extends AppService {
    constructor(
        private readonly controlsFileProcessor: BulkImportControlFileProcessor,
        private readonly controlsBlueprint: ControlsBlueprint,
        private readonly trainingFileProcessor: BulkImportTrainingFileProcessor,
        private readonly trainingBlueprint: TrainingBlueprint,
    ) {
        super();
    }

    /**
     * register new entities here
     */
    private registry: EntityRegistry = {
        [BulkImportEntityEnum.CONTROL]: {
            fields: ControlBlueprintField,
            blueprint: this.controlsBlueprint,
            processor: this.controlsFileProcessor,
        },
        [BulkImportEntityEnum.TRAINING]: {
            fields: TrainingBlueprintField,
            blueprint: this.trainingBlueprint,
            processor: this.trainingFileProcessor,
        },
    };

    getFields(entity: BulkImportEntityEnum): Record<string, string> {
        if (!this.registry[entity]) {
            throw new Error('Entity not found.');
        }

        return this.registry[entity].fields;
    }

    getFileProcessor(entity: BulkImportEntityEnum): BulkImportFileProcessor {
        if (!this.registry[entity]) {
            throw new Error('Entity not found.');
        }

        return this.registry[entity].processor;
    }

    getBlueprint(entity: BulkImportEntityEnum): FlatfileBlueprint {
        if (!this.registry[entity]) {
            throw new Error('Blueprint not found.');
        }

        return this.registry[entity].blueprint;
    }

    get<K extends keyof EntityRegistryItem>(
        entity: BulkImportEntityEnum,
        prop: K,
    ): EntityRegistryItem[K] {
        if (this.registry[entity]) {
            return this.registry[entity][prop];
        } else {
            throw new Error('Entity not found.');
        }
    }
}

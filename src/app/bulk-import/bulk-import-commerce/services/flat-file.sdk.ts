import { Flatfile, FlatfileClient } from '@flatfile/api';
import {
    Action,
    DocumentConfig,
    DocumentResponse,
    EnumPropertyOption,
    FileResponse,
    GetRecordsResponse,
    Property,
    RecordData,
    SheetConfig,
    SpaceResponse,
    Success,
    VersionResponse,
} from '@flatfile/api/api';
import { GetRecordsRequestOptions, JsonlRecord } from '@flatfile/api/v2/records/types';
import { AuthorizedService } from 'app/apis/services/authorized.service';
import { FlatfileTemporalRecord } from 'app/bulk-import/bulk-import-commerce/classes/flatfile-temporal.record.class';
import {
    BulkImportEntityEnum,
    BulkImportSubCategoryEnum,
} from 'app/bulk-import/enums/bulk-import.entity.enum';
import { BulkImportSpace } from 'app/bulk-import/types/bulk-import-space.type';
import { SpaceMetadata } from 'app/flatfile-bulk-import/classes/space-metadata';
import {
    DEFAULT_FLATFILE_ACTIONS,
    FlatFileResource,
} from 'app/flatfile-bulk-import/constants/flatfile.constants';
import { drataTheme } from 'app/flatfile-bulk-import/util/drata-theme';
import { generateTranslationDataUrl } from 'app/flatfile-bulk-import/util/drata-translations';
import { PolloAdapter } from 'commons/adapters/pollo.adapter';
import { Retry } from 'commons/decorators/retry.decorator';
import { getEnvironmentName } from 'commons/helpers/environment.helper';
import { forEachTokenPage } from 'commons/helpers/pagination/pagination.helper';
import { capitalize } from 'commons/helpers/string.helper';
import { AccountIdType } from 'commons/types/account-id.type';
import config from 'config';
import { first } from 'lodash';
import Stream from 'stream';

const defaultRetryOptions = {
    maxRetries: 3,
    retryWait: 2000,
    exponential: true,
};
export class FlatFileSdk extends AuthorizedService {
    client: FlatfileClient;
    constructor() {
        super();
        this.client = new FlatfileClient({
            token: config.get('flatfile.apiKey'),
            environment: config.get('flatfile.environmentId'),
            apiUrl: config.get('flatfile.baseUrl'),
        });
    }

    async createSpace(params: {
        name: string;
        userId: number;
        accountId: AccountIdType;
        blueprints: SheetConfig[];
        workbookName?: string;
        documents?: DocumentConfig[];
        entityType: BulkImportEntityEnum;
        workspaceId: number;
        subEntityType?: BulkImportSubCategoryEnum;
    }): Promise<BulkImportSpace> {
        const {
            name,
            userId,
            accountId,
            blueprints,
            documents,
            entityType,
            workspaceId,
            subEntityType,
        } = params;
        const workbookName = `Bulk Import ${capitalize(entityType, true)}`;

        const namespace = config.get('flatfile.namespace');

        try {
            const spaceMetadata = SpaceMetadata.encryptMetadata({
                entityType,
                accountId,
                userId,
                subEntityType,
            });

            // Generate data URL with embedded JSON
            const translationsPath = generateTranslationDataUrl(capitalize(entityType, true));

            // Create a space in Flatfile
            const space = await this.client.spaces.create({
                name,
                metadata: {
                    spaceMetadata,
                    theme: drataTheme,
                    environment: getEnvironmentName(),
                    workspaceId,
                },
                languageOverride: 'en',
                translationsPath,
                namespace,
            });

            this.logger.log(
                PolloAdapter.acct(
                    `Created Flatfile ${FlatFileResource.SPACE}: ${space.data.id}`,
                    null,
                    this.constructor.name,
                ).setIdentifier({ spaceName: name, namespace }),
            );

            // Create a workbook in the space
            const workbook = await this.client.workbooks.create({
                spaceId: space.data.id,
                name: workbookName,
                namespace,
                sheets: blueprints,
                actions: DEFAULT_FLATFILE_ACTIONS as unknown as Action[],
            });

            this.logger.log(
                PolloAdapter.acct(
                    `Created Flatfile ${FlatFileResource.WORKBOOK}: ${workbook.data.id}`,
                    null,
                    this.constructor.name,
                ).setIdentifier({ spaceId: space.data.id, namespace }),
            );

            const sheet = workbook.data.sheets?.[0];
            const sheetId = sheet?.id;

            if (!sheetId) {
                throw new Error('Failed to create sheet');
            }

            let defaultDocumentId: null | string | undefined = null;

            if (documents && documents.length > 0) {
                const createdDocuments = await this.createDocuments(space.data.id, documents);
                defaultDocumentId = first(createdDocuments)?.data.id;
            }

            if (defaultDocumentId) {
                await this.updateSpaceMetadata(space.data.id, {
                    ...space.data.metadata,
                    sidebarConfig: {
                        defaultPage: {
                            documentId: defaultDocumentId,
                        },
                    },
                });
            }

            return {
                space: space.data,
                spaceId: space.data.id,
                accessToken: space.data.accessToken,
                sheetId,
            };
        } catch (error) {
            // Determine which operation failed based on error context
            const operation = error.message?.includes(FlatFileResource.WORKBOOK)
                ? FlatFileResource.WORKBOOK
                : FlatFileResource.SPACE;

            const identifier =
                operation === FlatFileResource.WORKBOOK
                    ? { spaceId: error.config?.params?.spaceId || 'unknown', namespace }
                    : { spaceName: name, namespace };

            this.logger.error(
                PolloAdapter.acct(
                    `Failed to create Flatfile ${operation}`,
                    null,
                    this.constructor.name,
                )
                    .setError(error)
                    .setIdentifier(identifier),
            );
            throw error;
        }
    }

    @Retry(defaultRetryOptions)
    async getSheetRecords(sheetId: string, pageNumber: number): Promise<GetRecordsResponse> {
        return this.client.records.get(sheetId, {
            pageSize: config.get('flatfile.pageSize') || 100,
            pageNumber,
        });
    }

    /**
     * This method is used to get the records from a sheet in a streaming fashion.
     * It is useful when we want to process the records in batches.
     * TODO: Add error handling ENG-72091
     *
     * @param sheetId
     * @returns AsyncGenerator<JsonlRecord>
     */
    getSheetRecordsV2(
        sheetId: string,
        options?: GetRecordsRequestOptions,
    ): AsyncGenerator<JsonlRecord> {
        return this.client.records.v2.getRawStreaming(sheetId, options);
    }

    async deleteSpace(spaceId: string): Promise<void> {
        const namespace = config.get('flatfile.namespace');

        try {
            await this.client.spaces.delete(spaceId);

            this.logger.log(
                PolloAdapter.acct(
                    `Deleted Flatfile ${FlatFileResource.SPACE}: ${spaceId}`,
                    null,
                    this.constructor.name,
                ).setIdentifier({ spaceId, namespace }),
            );
        } catch (error) {
            this.logger.error(
                PolloAdapter.acct(
                    `Failed to delete Flatfile ${FlatFileResource.SPACE}`,
                    null,
                    this.constructor.name,
                )
                    .setError(error)
                    .setIdentifier({ spaceId, namespace }),
            );
            throw error;
        }
    }

    @Retry(defaultRetryOptions)
    async getSheetRecordsByQuery(
        sheetId: string,
        query: string,
        pageNumber: number,
    ): Promise<GetRecordsResponse> {
        return this.client.records.get(sheetId, {
            pageSize: config.get('pagination.limit'),
            pageNumber,
            q: query,
        });
    }

    async insertRecords(sheetId: string, records: RecordData[]): Promise<Success> {
        return this.client.records.insert(sheetId, records);
    }

    async updateRecords(sheetId: string, records: Flatfile.Records): Promise<VersionResponse> {
        return this.client.records.update(sheetId, records);
    }

    async updateSpaceMetadata(
        spaceId: string,
        metadata: Record<string, unknown>,
    ): Promise<SpaceResponse> {
        return this.client.spaces.update(spaceId, { metadata });
    }

    async deleteSheetRecord(sheetId: string, recordIds: string | string[]): Promise<Success> {
        return this.client.records.delete(sheetId, {
            ids: recordIds,
        });
    }

    @Retry(defaultRetryOptions)
    async getSpaceWorkbooks(spaceId: string): Promise<Flatfile.Workbook[]> {
        const { data: workbooks } = await this.client.workbooks.list({ spaceId });
        return workbooks;
    }

    @Retry(defaultRetryOptions)
    async getWorkbookSheets(workbookId: string): Promise<Flatfile.Sheet[]> {
        const { data: workbook } = await this.client.workbooks.get(workbookId);
        return workbook.sheets || [];
    }

    @Retry(defaultRetryOptions)
    async getSpace(spaceId: string): Promise<SpaceResponse> {
        return this.client.spaces.get(spaceId);
    }

    async deleteFile(fileId: string): Promise<Success> {
        return this.client.files.delete(fileId);
    }

    @Retry(defaultRetryOptions)
    async downloadFile(fileId: string): Promise<Stream.Readable> {
        return this.client.files.download(fileId);
    }

    @Retry(defaultRetryOptions)
    async getFile(fileId: string): Promise<FileResponse> {
        return this.client.files.get(fileId);
    }

    @Retry(defaultRetryOptions)
    async getFiles(spaceId: string, page: number): Promise<Flatfile.File_[]> {
        const { data: files } = await this.client.files.list({
            spaceId,
            pageNumber: page,
            pageSize: 100,
        });
        return files;
    }

    async createDocument(
        spaceId: string,
        documentConfig: DocumentConfig,
    ): Promise<DocumentResponse> {
        return this.client.documents.create(spaceId, documentConfig);
    }

    async getJob(jobId: string): Promise<Flatfile.JobResponse> {
        return this.client.jobs.get(jobId);
    }

    async createDocuments(
        spaceId: string,
        documentConfigs: DocumentConfig[],
    ): Promise<DocumentResponse[]> {
        return Promise.all(documentConfigs.map(document => this.createDocument(spaceId, document)));
    }

    async getSheetById(sheetId: string): Promise<Flatfile.SheetResponse> {
        return this.client.sheets.get(sheetId);
    }

    async processSheetRecordsInBatches(
        sheetId: string,
        processor: (records: any[], response: any) => Promise<void>,
    ): Promise<void> {
        return forEachTokenPage(async (_nextPageToken, page = 1) => {
            // Get records from FlatFile
            const response = await this.getSheetRecords(sheetId, page);

            // Transform the response to match expected format
            return {
                data: {
                    page: response.data.records || [],
                    // If we have more records than current page size, set nextPageToken to next page number
                    nextPageToken: response.data.records?.length > 0 ? String(page + 1) : null,
                },
            };
        }, processor);
    }

    async getFrameworkFieldsAndOptions(sheetId: string): Promise<{
        frameworkFieldsNames: Map<string, string>;
        frameworkRequirementsMap: Map<string, EnumPropertyOption[]>;
    }> {
        try {
            const {
                data: { config: sheetConfig },
            } = await this.client.sheets.get(sheetId);
            const frameworkFieldsNames: Map<string, string> = new Map<string, string>();
            const frameworkRequirementsMap = new Map<string, EnumPropertyOption[]>();
            const frameworkConfigFields = sheetConfig.fields.filter(
                field =>
                    field.type === 'enum-list' &&
                    field.key.startsWith(FlatfileTemporalRecord.FRAMEWORK_FIELD_PREFIX),
            ) as Property.EnumList[];

            for (const frameworkField of frameworkConfigFields) {
                frameworkFieldsNames.set(
                    frameworkField.key,
                    frameworkField.metadata.frameworkName ?? '',
                );
                frameworkRequirementsMap.set(frameworkField.key, frameworkField.config.options);
            }
            return {
                frameworkFieldsNames,
                frameworkRequirementsMap,
            };
        } catch (error) {
            this.logger.error(
                PolloAdapter.acct(
                    `Failed to get framework fields and options`,
                    null,
                    this.constructor.name,
                )
                    .setError(error)
                    .setIdentifier({ sheetId }),
            );
            throw error;
        }
    }

    async getSheetRecordCount(sheetId: string) {
        try {
            const response = await this.client.records.get(sheetId, {});
            return response.data.records.length;
        } catch (error) {
            this.logger.error(
                PolloAdapter.acct(`Failed to get sheet record count`, null, this.constructor.name)
                    .setError(error)
                    .setIdentifier({ sheetId }),
            );
        }
    }
}

import { Injectable } from '@nestjs/common';
import { BulkImportFileProcessor } from 'app/bulk-import/bulk-import-commerce/classes/bulk-import-file-processor.class';
import { FlatfileTemporalRecord } from 'app/bulk-import/bulk-import-commerce/classes/flatfile-temporal.record.class';
import { BulkImportPreview } from 'app/bulk-import/types/bulk-import-preview.type';
import { BulkImportRecordWithDto } from 'app/bulk-import/types/bulk-import-record-with-dto.type';
import { CustomFieldSubmissionsRequestDto } from 'app/custom-fields/dtos/custom-field-submissions-request.dto';
import { CreateTrainingRequestDto } from 'app/grc/dtos/create-training-request.dto';
import { User } from 'app/users/entities/user.entity';
import { Account } from 'auth/entities/account.entity';

@Injectable()
export class BulkImportTrainingFileProcessor extends BulkImportFileProcessor {
    dtoClass = CreateTrainingRequestDto;

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    preprocess(_records: FlatfileTemporalRecord[]): Promise<void> {
        throw new Error('Method not implemented.');
    }

    getPreview(): BulkImportPreview {
        throw new Error('Method not implemented.');
    }

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    transform(_record: FlatfileTemporalRecord): Promise<FlatfileTemporalRecord> {
        throw new Error('Method not implemented.');
    }
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    validate(_record: FlatfileTemporalRecord): Promise<FlatfileTemporalRecord> {
        throw new Error('Method not implemented.');
    }

    save(
        account: Account,
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        _user: User,
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        _recordsWithDto: BulkImportRecordWithDto<CustomFieldSubmissionsRequestDto>[],
    ): Promise<number> {
        throw new Error('Method not implemented.');
    }

    setupValidationContext(): Promise<void> {
        throw new Error('Method not implemented.');
    }
}

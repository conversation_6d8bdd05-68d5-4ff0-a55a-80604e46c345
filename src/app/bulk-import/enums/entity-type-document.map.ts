import { DocumentConfig } from '@flatfile/api/api';
import { BulkImportEntityEnum } from 'app/bulk-import/enums/bulk-import.entity.enum';
import * as fs from 'fs';
import * as path from 'path';

function loadDocumentContent(filename: string): string {
    try {
        const filePath = path.join('views/bulk-import', filename);
        return fs.readFileSync(filePath, 'utf-8');
    } catch (error) {
        return `# ${filename.replace('.md', '').replace('-', ' ')} Instructions\n\nInstructions will be available soon.`;
    }
}

export const EntityTypeDocumentMap = new Map<BulkImportEntityEnum, DocumentConfig[]>([
    [
        BulkImportEntityEnum.RISK,
        [
            {
                title: 'Create custom risks or update risks in bulk',
                body: loadDocumentContent('risk-instructions.md'),
            },
        ],
    ],
    [
        BulkImportEntityEnum.CONTROL,
        [
            {
                title: 'Control Bulk Import Process',
                body: loadDocumentContent('control-instructions.md'),
            },
        ],
    ],
    [
        BulkImportEntityEnum.TRAINING,
        [
            {
                title: 'Training Bulk Import Process',
                body: 'TBD',
            },
        ],
    ],
]);

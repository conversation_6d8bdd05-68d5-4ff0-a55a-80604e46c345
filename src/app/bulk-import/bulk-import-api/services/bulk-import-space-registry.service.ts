import { Space } from '@flatfile/api/api';
import { Injectable } from '@nestjs/common';
import { WorkflowExecutionAlreadyStartedError } from '@temporalio/client';
import {
    BulkImportEntityEnum,
    BulkImportSubCategoryEnum,
} from 'app/bulk-import/enums/bulk-import.entity.enum';
import {
    flatfileSpaceMonitorWorkflow,
    spaceClosedSignal,
} from 'app/worker/workflows/bulk-import/flatfile-space-monitor.v1.workflow';
import { Account } from 'auth/entities/account.entity';
import { getTemporalClient, TemporalClient } from 'commons/helpers/temporal/client';
import config from 'config';
import { PolloMessage } from 'pollo-logger/classes/pollo.message';
import { PolloLogger } from 'pollo-logger/pollo.logger';

export type SpaceRegistrationResult = {
    success: boolean;
    workflowId: string;
    alreadyExists: boolean;
    error?: string;
};

type AccountContext = Pick<Account, 'id' | 'domain'>;

/**
 * Service for managing Flatfile space registration and auto-recovery.
 * This service:
 * 1. Registers spaces for monitoring by starting Temporal workflows
 * 2. Handles auto-recovery after server restarts
 * 3. Queries active space workflows from Temporal
 * 4. Manages space lifecycle (creation, monitoring, closure)
 */
@Injectable()
export class BulkImportSpaceRegistryService {
    private temporalClient: TemporalClient;
    private logger: PolloLogger<PolloMessage>;

    constructor() {
        this.logger = PolloLogger.logger(this.constructor.name);
    }

    /**
     * Initialize the service and Temporal client
     */
    async initialize(): Promise<void> {
        try {
            this.temporalClient = await getTemporalClient();
            this.logger.log(PolloMessage.msg('Space registry service initialized'));
        } catch (error) {
            this.logger.error(
                PolloMessage.msg('Failed to initialize space registry service').setError(error),
            );
            throw error;
        }
    }

    /**
     * Register a space for monitoring by starting a Temporal workflow.
     * This creates a long-running workflow that will survive server restarts.
     */
    async registerSpace(
        space: Space,
        entityType: BulkImportEntityEnum,
        workspaceId: number,
        { id: accountId, domain }: AccountContext,
        subEntityType?: BulkImportSubCategoryEnum,
    ): Promise<SpaceRegistrationResult> {
        const spaceId = space.id;
        try {
            if (!this.temporalClient) {
                await this.initialize();
            }

            const workflowId = `flatfileSpaceMonitor-${space.id}`;
            this.logger.log(
                PolloMessage.msg('Registering space for monitoring').setIdentifier({
                    spaceId,
                    accountId,
                    workflowId,
                }),
            );

            try {
                await this.temporalClient.startWorkflow(flatfileSpaceMonitorWorkflow, {
                    workflowId,
                    taskQueue: config.get('temporal.taskQueues.temporal-default'),
                    args: [spaceId, accountId],
                    memo: {
                        accountId,
                        domain,
                        spaceId,
                        entityType,
                        subEntityType,
                        workspaceId,
                    },
                });

                this.logger.log(
                    PolloMessage.msg('Space registered successfully').setIdentifier({
                        spaceId,
                        workflowId,
                    }),
                );

                return {
                    success: true,
                    workflowId,
                    alreadyExists: false,
                };
            } catch (error) {
                if (error instanceof WorkflowExecutionAlreadyStartedError) {
                    // Space already being monitored - that's fine
                    this.logger.log(
                        PolloMessage.msg('Space already registered for monitoring').setIdentifier({
                            spaceId,
                            workflowId,
                        }),
                    );

                    return {
                        success: true,
                        workflowId,
                        alreadyExists: true,
                    };
                } else {
                    throw error;
                }
            }
        } catch (error) {
            this.logger.error(
                PolloMessage.msg('Failed to register space')
                    .setError(error)
                    .setIdentifier({ spaceId, accountId }),
            );

            return {
                success: false,
                workflowId: `flatfileSpaceMonitor-${spaceId}`,
                alreadyExists: false,
                error: error.message,
            };
        }
    }

    /**
     * Close a space by signaling its monitor workflow to stop
     */
    async closeSpace(spaceId: string): Promise<void> {
        try {
            if (!this.temporalClient) {
                await this.initialize();
            }

            const workflowId = `flatfileSpaceMonitor-${spaceId}`;

            this.logger.log(
                PolloMessage.msg('Closing space').setIdentifier({ spaceId, workflowId }),
            );

            try {
                // For now, use a simple approach to signal the workflow
                await this.temporalClient.sendSignal(workflowId, spaceClosedSignal, []);

                this.logger.log(
                    PolloMessage.msg('Space closed successfully').setIdentifier({
                        spaceId,
                        workflowId,
                    }),
                );
            } catch (error) {
                if (error.message?.includes('not found')) {
                    // Workflow doesn't exist - space is already closed
                    this.logger.log(
                        PolloMessage.msg('Space workflow not found (already closed)').setIdentifier(
                            { spaceId, workflowId },
                        ),
                    );
                }
                throw error;
            }
        } catch (error) {
            this.logger.error(
                PolloMessage.msg('Failed to close space')
                    .setError(error)
                    .setIdentifier({ spaceId }),
            );
        }
    }
}

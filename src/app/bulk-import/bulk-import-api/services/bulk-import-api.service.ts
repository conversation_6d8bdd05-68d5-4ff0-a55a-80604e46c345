import { Injectable } from '@nestjs/common';
import { BulkImportCreateSpaceRequestDto } from 'app/bulk-import/bulk-import-api/dtos/bulk-import-create-space-request.dto';
import { BulkImportSpaceRegistryService } from 'app/bulk-import/bulk-import-api/services/bulk-import-space-registry.service';
import { BulkImportCommerceService } from 'app/bulk-import/bulk-import-commerce/bulk-import-commerce.service';
import { BulkImportEntityEnum } from 'app/bulk-import/enums/bulk-import.entity.enum';
import { BulkImportOperation } from 'app/bulk-import/types/bulk-import-operation.type';
import { BulkImportPreview } from 'app/bulk-import/types/bulk-import-preview.type';
import { BulkImportRecordUpdateSummary } from 'app/bulk-import/types/bulk-import-record-update-summary.type';
import { BulkImportSpace } from 'app/bulk-import/types/bulk-import-space.type';
import { Product } from 'app/companies/products/entities/product.entity';
import { Account } from 'auth/entities/account.entity';
import { PolloAdapter } from 'commons/adapters/pollo.adapter';
import { BadRequestException } from 'commons/exceptions/bad-request.exception';
import { AppService } from 'commons/services/app.service';
import { WorkspacesBaseService } from 'commons/services/workspaces-base.service';
import { isNil } from 'lodash';
import { PolloMessage } from 'pollo-logger/classes/pollo.message';

@Injectable()
export class BulkImportApiService extends AppService {
    constructor(
        private readonly bulkImportCommerceService: BulkImportCommerceService,
        private readonly workspacesBaseService: WorkspacesBaseService,
        private readonly flatfileSpaceRegistryService: BulkImportSpaceRegistryService,
    ) {
        super();
    }

    /**
     *
     * @param bulkImportOperation
     * @returns
     */
    async createSpace(
        account: Account,
        dto: BulkImportCreateSpaceRequestDto,
        entityType: BulkImportEntityEnum,
        userId: number,
    ): Promise<BulkImportSpace> {
        this.logger.log(
            PolloAdapter.acct('Creating bulk import space', account, this.constructor.name)
                .setContext(this.constructor.name)
                .setSubContext(this.createSpace.name)
                .setIdentifier({ userId, entityType, dto }),
        );

        try {
            if (!account) {
                throw new BadRequestException('Account is required to create space');
            }

            const { workspaceId, subEntityType } = dto;
            let workspace: Product | null;

            if (workspaceId && [BulkImportEntityEnum.CONTROL].includes(entityType)) {
                workspace = await this.workspacesBaseService.getProductById(workspaceId);
            } else {
                workspace = await this.workspacesBaseService.getPrimaryProduct();
            }

            if (!isNil(workspace)) {
                account.setCurrentProduct(workspace);
            } else {
                throw new BadRequestException('No primary product found');
            }

            if (!entityType) {
                throw new BadRequestException('Entity type is required to create space');
            }

            if (!userId) {
                this.logger.error(
                    PolloMessage.msg('User is required to create space')
                        .setContext(this.constructor.name)
                        .setSubContext(this.createSpace.name)
                        .setIdentifier({ account, userId, entityType, subEntityType }),
                );
                throw new BadRequestException('User is required to create space');
            }

            const bulkImportSpace = await this.bulkImportCommerceService.createSpace(
                account,
                workspace.id,
                entityType,
                userId,
                subEntityType,
            );

            await this.flatfileSpaceRegistryService.registerSpace(
                bulkImportSpace.space,
                entityType,
                workspace.id,
                {
                    id: account.id,
                    domain: account.domain,
                },
                subEntityType,
            );

            return bulkImportSpace;
        } catch (error) {
            const message = error.message ?? 'Failed to create space';

            this.logger.error(
                PolloMessage.msg(message)
                    .setContext(this.constructor.name)
                    .setSubContext(this.createSpace.name)
                    .setIdentifier({ account, userId, entityType })
                    .setError(error),
            );
            throw error;
        }
    }

    /**
     * @param bulkImportOperation
     * @returns
     */
    async importRecords(
        bulkImportOperation: BulkImportOperation,
    ): Promise<BulkImportRecordUpdateSummary> {
        const { entityType, context } = bulkImportOperation;
        const { sheetId, spaceId, account, userId, workspaceId } = context;

        if (!account) {
            throw new BadRequestException('Account is required to import records');
        }

        if (!userId) {
            throw new BadRequestException('User ID is required to import records');
        }

        if (!workspaceId && [BulkImportEntityEnum.CONTROL].includes(entityType)) {
            throw new BadRequestException('Workspace ID is required to import records');
        }

        return this.bulkImportCommerceService.save(
            entityType,
            spaceId,
            sheetId,
            account.id,
            userId,
            workspaceId,
        );
    }

    /**
     *
     * @param bulkImportOperation
     * @returns
     */
    async getPreview(bulkImportOperation: BulkImportOperation): Promise<BulkImportPreview> {
        return this.bulkImportCommerceService.getPreview(bulkImportOperation);
    }
}

import { ApiProperty } from '@nestjs/swagger';
import { BulkImportSubCategoryEnum } from 'app/bulk-import/enums/bulk-import.entity.enum';
import { IsEnum, IsNotEmpty, IsOptional } from 'class-validator';

export class BulkImportCreateSpaceRequestDto {
    @ApiProperty({
        required: false,
        description: 'Current workspace id being used',
        example: '1',
        type: 'number',
    })
    @IsNotEmpty()
    workspaceId?: number;

    @ApiProperty({
        required: false,
        description: 'Sub entity type used to differentiate between different types of training',
        example: 'SECURITY_TRAINING',
        type: 'string',
        enum: BulkImportSubCategoryEnum,
    })
    @IsOptional()
    @IsNotEmpty()
    @IsEnum(BulkImportSubCategoryEnum)
    subEntityType?: BulkImportSubCategoryEnum;
}

import { TestingModule } from '@nestjs/testing';
import { RiskRepository } from 'app/risk-management/repositories/risk.repository';
import { VendorImpactLevel } from 'app/users/vendors/enums/vendor-impact-level.enum';
import { VendorRepository } from 'app/users/vendors/repositories/vendor.repository';
import { VendorSummary } from 'app/vendors/entities/vendors-summary.opensearch.entity';
import { VendorsSourceDataService } from 'app/vendors/services/vendors-source-data.service';
import { VendorWithSecurityReview } from 'app/vendors/types/vendor-with-security-review.type';
import { Account } from 'auth/entities/account.entity';
import { VendorCategory } from 'commons/enums/vendor/vendor-category.enum';
import { VendorStatus } from 'commons/enums/vendor/vendor-status.enum';
import { VendorType } from 'commons/enums/vendor/vendor-type.enum';
import { createAppTestingModule } from 'commons/helpers/app-testing-module.helper';
import { WorkspacesBaseService } from 'commons/services/workspaces-base.service';
import { TenancyContextMock } from 'tenancy/contexts/__mocks__/tenancy.context';
import { TenancyContext } from 'tenancy/contexts/tenancy.context';

describe('VendorsSourceDataService', () => {
    let service: VendorsSourceDataService;
    let vendorRepository: jest.Mocked<VendorRepository>;
    let riskRepository: jest.Mocked<RiskRepository>;
    let tenancyContextMock: ReturnType<typeof TenancyContextMock>;

    const mockAccount = { id: 1 } as unknown as Account;
    const mockWorkspaceId = 100;

    beforeEach(async () => {
        tenancyContextMock = TenancyContextMock();

        // Create mock repositories
        vendorRepository = {
            getVendorsWithSecurityReviews: jest.fn(),
            getVendorRisks: jest.fn(),
            getVendorCustomFields: jest.fn(),
            getRiskCustomFields: jest.fn(),
        } as any;

        riskRepository = {
            getVendorRiskCount: jest.fn(),
        } as any;

        const module: TestingModule = await createAppTestingModule({
            providers: [
                VendorsSourceDataService,
                {
                    provide: WorkspacesBaseService,
                    useValue: {
                        getPrimaryProductId: jest.fn().mockResolvedValue(mockWorkspaceId),
                    },
                },
                {
                    provide: TenancyContext,
                    useValue: tenancyContextMock,
                },
            ],
        }).compile();

        service = module.get<VendorsSourceDataService>(VendorsSourceDataService);

        // Mock getCustomTenantRepository to return the correct repository based on type
        jest.spyOn(service as any, 'getCustomTenantRepository').mockImplementation(
            repositoryType => {
                if (repositoryType === VendorRepository) {
                    return vendorRepository;
                }
                if (repositoryType === RiskRepository) {
                    return riskRepository;
                }
                return vendorRepository; // fallback
            },
        );
    });

    it('should be defined', () => {
        expect(service).toBeDefined();
    });

    describe('getVendors', () => {
        it('should return empty array when no vendors are found', async () => {
            vendorRepository.getVendorsWithSecurityReviews.mockResolvedValue([]);

            const result = await service.getVendors(mockAccount);

            expect(result).toEqual([]);
        });

        it('should transform vendors with security reviews into VendorSummary objects', async () => {
            const mockVendors: VendorWithSecurityReview[] = [
                {
                    id: 1,
                    name: 'Vendor 1',
                    status: VendorStatus.ACTIVE,
                    type: VendorType.VENDOR,
                    category: VendorCategory.FINANCE,
                    impactLevel: VendorImpactLevel.MAJOR,
                    isSubProcessor: false,
                    risk: 3,
                    scheduledQuestionnaireStatus: null,
                    securityReviewStatus: 1,
                    lastSocReportReviewAt: new Date(),
                    securityOwner: '<EMAIL>',
                    requestedAt: new Date(),
                    reviewDeadlineAt: new Date(),
                    updatedAt: new Date(),
                    customFieldSubmission: null,
                    scheduleConfigurationId: null,
                    scheduleConfigurationStatus: null,
                },
            ];

            const mockCustomFields = [
                {
                    vendorId: 1,
                    fieldName: 'custom_field_1',
                    submissionData: JSON.stringify({ value: 'Custom Value 1', fieldType: 'TEXT' }),
                },
                {
                    vendorId: 1,
                    fieldName: 'custom_field_options',
                    submissionData: JSON.stringify({
                        value: 2,
                        metadata: { optionSelected: 'Option B' },
                        fieldType: 'OPTIONS',
                    }),
                },
                {
                    vendorId: 1,
                    fieldName: 'custom_field_currency',
                    submissionData: JSON.stringify({
                        value: 15000,
                        metadata: { currencyCode: 'USD' },
                        fieldType: 'CURRENCY',
                    }),
                },
            ];

            jest.spyOn(vendorRepository, 'getVendorsWithSecurityReviews').mockResolvedValue(
                mockVendors,
            );
            jest.spyOn(vendorRepository, 'getVendorCustomFields').mockResolvedValue(
                mockCustomFields,
            );
            jest.spyOn(riskRepository, 'getVendorRiskCount').mockResolvedValue({
                riskCount: 1,
                risksWithAnticipatedCompletionDateCount: 0,
            });

            const result = await service.getVendors(mockAccount);

            expect(result).toHaveLength(1);
            expect(result[0]).toBeInstanceOf(VendorSummary);
            expect(result[0].vendorId).toBe(1);
            expect(result[0].name).toBe('Vendor 1');
            expect(riskRepository.getVendorRiskCount).toHaveBeenCalledWith(1);
            expect(result[0].riskCount).toBe(1);
            expect(result[0].securityReviewStatus).toBe('NOT_YET_STARTED');
            expect(result[0].securityOwner).toBe('<EMAIL>');
            expect(result[0].customFields).toEqual({
                custom_field_1: {
                    value: 'Custom Value 1',
                    type: 'TEXT',
                },
                custom_field_options: {
                    value: 'Option B', // Should use optionSelected from metadata
                    type: 'OPTIONS',
                },
                custom_field_currency: {
                    value: 150, // Should be divided by 100 (15000 / 100)
                    type: 'CURRENCY',
                },
            });
        });

        it('should handle errors when retrieving vendor risks', async () => {
            const mockVendors: VendorWithSecurityReview[] = [
                {
                    id: 1,
                    name: 'Vendor 1',
                    status: VendorStatus.ACTIVE,
                    type: VendorType.VENDOR,
                    category: VendorCategory.FINANCE,
                    impactLevel: VendorImpactLevel.MAJOR,
                    isSubProcessor: false,
                    risk: 1,
                    scheduledQuestionnaireStatus: null,
                    securityReviewStatus: 1,
                    lastSocReportReviewAt: null,
                    securityOwner: null,
                    requestedAt: null,
                    reviewDeadlineAt: null,
                    updatedAt: new Date(),
                    customFieldSubmission: null,
                    scheduleConfigurationId: null,
                    scheduleConfigurationStatus: null,
                },
            ];

            jest.spyOn(vendorRepository, 'getVendorsWithSecurityReviews').mockResolvedValue(
                mockVendors,
            );
            jest.spyOn(vendorRepository, 'getVendorCustomFields').mockResolvedValue([]);
            jest.spyOn(riskRepository, 'getVendorRiskCount').mockRejectedValue(
                new Error('Risk count error'),
            );

            const result = await service.getVendors(mockAccount);

            expect(result).toHaveLength(1);
            expect(result[0].riskCount).toBe(0); // Should default to 0 on error
        });

        it('should handle invalid JSON in custom fields', async () => {
            const mockVendors: VendorWithSecurityReview[] = [
                {
                    id: 1,
                    name: 'Vendor 1',
                    status: VendorStatus.ACTIVE,
                    type: VendorType.VENDOR,
                    category: VendorCategory.FINANCE,
                    impactLevel: VendorImpactLevel.MAJOR,
                    isSubProcessor: false,
                    risk: 1,
                    scheduledQuestionnaireStatus: null,
                    securityReviewStatus: 1,
                    lastSocReportReviewAt: null,
                    securityOwner: null,
                    requestedAt: null,
                    reviewDeadlineAt: null,
                    updatedAt: new Date(),
                    customFieldSubmission: null,
                    scheduleConfigurationId: null,
                    scheduleConfigurationStatus: null,
                },
            ];

            const mockCustomFields = [
                {
                    vendorId: 1,
                    fieldName: 'invalid_field',
                    submissionData: '{invalid json}',
                },
            ];

            jest.spyOn(vendorRepository, 'getVendorsWithSecurityReviews').mockResolvedValue(
                mockVendors,
            );
            jest.spyOn(vendorRepository, 'getVendorRisks').mockResolvedValue([]);
            jest.spyOn(vendorRepository, 'getVendorCustomFields').mockResolvedValue(
                mockCustomFields,
            );
            const result = await service.getVendors(mockAccount);

            expect(result).toHaveLength(1);
            expect(result[0].customFields).toEqual({
                invalid_field: {
                    value: null,
                    type: null,
                },
            });
        });

        it('should respect indexingOptions.includeRiskData=false', async () => {
            const mockVendors: VendorWithSecurityReview[] = [
                {
                    id: 1,
                    name: 'Vendor 1',
                    status: VendorStatus.ACTIVE,
                    type: VendorType.VENDOR,
                    category: VendorCategory.FINANCE,
                    impactLevel: VendorImpactLevel.MAJOR,
                    isSubProcessor: false,
                    risk: 1,
                    scheduledQuestionnaireStatus: null,
                    securityReviewStatus: 1,
                    lastSocReportReviewAt: null,
                    securityOwner: null,
                    requestedAt: null,
                    reviewDeadlineAt: null,
                    updatedAt: new Date(),
                    customFieldSubmission: null,
                    scheduleConfigurationId: null,
                    scheduleConfigurationStatus: null,
                },
            ];

            jest.spyOn(vendorRepository, 'getVendorsWithSecurityReviews').mockResolvedValue(
                mockVendors,
            );
            jest.spyOn(vendorRepository, 'getVendorCustomFields').mockResolvedValue([]);
            jest.spyOn(vendorRepository, 'getRiskCustomFields').mockResolvedValue([]);

            const result = await service.getVendors(mockAccount);

            expect(result).toHaveLength(1);
            expect(vendorRepository.getVendorRisks).not.toHaveBeenCalled();
        });

        it('should respect indexingOptions.includeCustomFields=false', async () => {
            const mockVendors: VendorWithSecurityReview[] = [
                {
                    id: 1,
                    name: 'Vendor 1',
                    status: VendorStatus.ACTIVE,
                    type: VendorType.VENDOR,
                    category: VendorCategory.FINANCE,
                    impactLevel: VendorImpactLevel.MAJOR,
                    isSubProcessor: false,
                    risk: 1,
                    scheduledQuestionnaireStatus: null,
                    securityReviewStatus: 1,
                    lastSocReportReviewAt: null,
                    securityOwner: null,
                    requestedAt: null,
                    reviewDeadlineAt: null,
                    updatedAt: new Date(),
                    customFieldSubmission: null,
                    scheduleConfigurationId: null,
                    scheduleConfigurationStatus: null,
                },
            ];

            jest.spyOn(vendorRepository, 'getVendorsWithSecurityReviews').mockResolvedValue(
                mockVendors,
            );
            jest.spyOn(vendorRepository, 'getVendorRisks').mockResolvedValue([]);
            jest.spyOn(vendorRepository, 'getVendorCustomFields').mockResolvedValue([]);
            jest.spyOn(vendorRepository, 'getRiskCustomFields').mockResolvedValue([]);

            const result = await service.getVendors(mockAccount);

            expect(result).toHaveLength(1);
            expect(result[0].customFields).toEqual({});
        });

        it('should handle vendor with empty security owner', async () => {
            const mockVendors: VendorWithSecurityReview[] = [
                {
                    id: 1,
                    name: 'Vendor 1',
                    status: VendorStatus.ACTIVE,
                    type: VendorType.VENDOR,
                    category: VendorCategory.FINANCE,
                    impactLevel: VendorImpactLevel.MAJOR,
                    isSubProcessor: false,
                    risk: 1,
                    scheduledQuestionnaireStatus: null,
                    securityReviewStatus: 1,
                    lastSocReportReviewAt: null,
                    securityOwner: '', // Empty string should become null
                    requestedAt: null,
                    reviewDeadlineAt: null,
                    updatedAt: new Date(),
                    customFieldSubmission: null,
                    scheduleConfigurationId: null,
                    scheduleConfigurationStatus: null,
                },
            ];

            jest.spyOn(vendorRepository, 'getVendorsWithSecurityReviews').mockResolvedValue(
                mockVendors,
            );
            jest.spyOn(riskRepository, 'getVendorRiskCount').mockResolvedValue({
                riskCount: 0,
                risksWithAnticipatedCompletionDateCount: 0,
            });
            jest.spyOn(vendorRepository, 'getVendorCustomFields').mockResolvedValue([]);

            const result = await service.getVendors(mockAccount);

            expect(result).toHaveLength(1);
            expect(result[0].securityOwner).toBeNull(); // Empty string should be converted to null
            expect(result[0].riskCount).toBe(0);
        });
    });
});

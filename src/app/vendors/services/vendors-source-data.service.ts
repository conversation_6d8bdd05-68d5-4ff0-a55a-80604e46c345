import { CustomFieldType } from '@drata/expression-evaluator';
import { Injectable } from '@nestjs/common';
import { CUSTOM_FIELDS_CURRENCY_CONVERTER } from 'app/custom-fields/constants/custom-fields-currency.constants';
import { RiskRepository } from 'app/risk-management/repositories/risk.repository';
import { VendorImpactLevel } from 'app/users/vendors/enums/vendor-impact-level.enum';
import { VendorSecurityReviewDecision } from 'app/users/vendors/enums/vendor-security-review-decision.enum';
import { VendorRepository } from 'app/users/vendors/repositories/vendor.repository';
import {
    generateVendorDocumentId,
    VendorSummary,
} from 'app/vendors/entities/vendors-summary.opensearch.entity';
import { VendorWithSecurityReview } from 'app/vendors/types/vendor-with-security-review.type';
import { Account } from 'auth/entities/account.entity';
import { PolloAdapter } from 'commons/adapters/pollo.adapter';
import { ScheduledQuestionnaireStatus } from 'commons/enums/scheduled-questionnaire-status-enum';
import { VendorCategory } from 'commons/enums/vendor/vendor-category.enum';
import { VendorRisk } from 'commons/enums/vendor/vendor-risk.enum';
import { VendorSecurityReviewStatus } from 'commons/enums/vendor/vendor-security-review-status.enum';
import { VendorStatus } from 'commons/enums/vendor/vendor-status.enum';
import { VendorType } from 'commons/enums/vendor/vendor-type.enum';
import { safeJsonParse } from 'commons/helpers/utility.helper';
import { AppService } from 'commons/services/app.service';
import { WorkspacesBaseService } from 'commons/services/workspaces-base.service';
import { isEmpty, isNil } from 'lodash';
import { Span } from 'nestjs-ddtrace';

@Injectable()
export class VendorsSourceDataService extends AppService {
    constructor(private readonly workspacesBaseService: WorkspacesBaseService) {
        super();
    }

    private get vendorRepository(): VendorRepository {
        return this.getCustomTenantRepository(VendorRepository);
    }

    private get riskRepository(): RiskRepository {
        return this.getCustomTenantRepository(RiskRepository);
    }

    @Span()
    async getVendors(account: Account, vendorIds?: number[]): Promise<VendorSummary[]> {
        try {
            // Get primary workspace ID
            const workspaceId = await this.workspacesBaseService.getPrimaryProductId();

            // Get vendors with security reviews from repository
            const vendors = await this.vendorRepository.getVendorsWithSecurityReviews(
                account,
                vendorIds,
            );

            // Get custom fields from repository
            const customFields = !isEmpty(vendors)
                ? await this.vendorRepository.getVendorCustomFields(
                      vendors.map(v => v.id),
                      account,
                  )
                : [];

            // Group custom fields by vendorId
            const customFieldsByVendorId = customFields.reduce(
                (acc: Record<number, Record<string, any>>, cf: any) => {
                    if (!acc[cf.vendorId]) {
                        acc[cf.vendorId] = {};
                    }

                    if (!isEmpty(acc[cf.vendorId][cf.fieldName])) {
                        return acc;
                    }

                    // Parse the JSON submission data using helper function
                    const parsedData = safeJsonParse(cf.submissionData);

                    // Check if parsing was successful (returns object) or failed (returns string)
                    const submissionData =
                        typeof parsedData === 'object'
                            ? (parsedData as { value?: any; fieldType?: string; metadata?: any })
                            : null;

                    if (submissionData) {
                        let processedValue = submissionData.value ?? null;

                        switch (submissionData.fieldType) {
                            case CustomFieldType.OPTIONS:
                                // For OPTIONS type, use optionSelected from metadata instead of numeric value
                                processedValue =
                                    submissionData.metadata?.optionSelected ?? processedValue;
                                break;
                            case CustomFieldType.CURRENCY:
                                // Apply currency converter for CURRENCY type custom fields
                                if (!isNil(processedValue) && typeof processedValue === 'number') {
                                    processedValue =
                                        processedValue / CUSTOM_FIELDS_CURRENCY_CONVERTER;
                                }
                                break;
                            case CustomFieldType.OPTIONS_NUMERIC:
                                if (!isNil(processedValue)) {
                                    processedValue = Number(cf.valueNumber);
                                }
                                break;
                            default:
                                break;
                        }

                        // Convert to object with value and type properties
                        acc[cf.vendorId][cf.fieldName] = {
                            value: processedValue,
                            type: submissionData.fieldType || null,
                        };
                    } else {
                        // Fallback for parsing failures
                        acc[cf.vendorId][cf.fieldName] = {
                            value: null,
                            type: null,
                        };
                    }

                    return acc;
                },
                {},
            );

            // Transform vendors to VendorSummary
            return await Promise.all(
                vendors.map(async vendor => {
                    // Add custom fields to vendor
                    vendor.customFieldSubmission = customFieldsByVendorId[vendor.id] || {};

                    return this.mapVendorToResultSummary(vendor, account, workspaceId);
                }),
            );
        } catch (error) {
            this.logger.error(
                PolloAdapter.acct(
                    `Error retrieving vendor results: ${error.message}`,
                    account,
                ).setError(error),
            );
            throw error;
        }
    }

    private async mapVendorToResultSummary(
        vendor: VendorWithSecurityReview,
        account: Account,
        workspaceId: number,
    ): Promise<VendorSummary> {
        const result = new VendorSummary();

        // Set identification fields
        result.vendorId = vendor.id;
        result.accountId = account.id;
        result.workspaceId = workspaceId;

        // Generate document ID
        result.id = generateVendorDocumentId(account.id, workspaceId, vendor.id);

        // Set basic vendor fields
        result.name = vendor.name;
        result.status = !isNil(vendor.status) ? VendorStatus[vendor.status] : '';
        result.type = !isNil(vendor.type) ? VendorType[vendor.type] : '';
        result.category = !isNil(vendor.category) ? VendorCategory[vendor.category] : '';
        result.impactLevel = !isNil(vendor.impactLevel)
            ? VendorImpactLevel[vendor.impactLevel]
            : '';
        result.isSubProcessor = Boolean(vendor.isSubProcessor);
        result.risk = !isNil(vendor.risk) ? VendorRisk[vendor.risk] : '';

        // Set scheduled questionnaire status based on scheduleConfiguration
        result.scheduledQuestionnaireStatus = vendor.scheduleConfigurationId
            ? ScheduledQuestionnaireStatus[ScheduledQuestionnaireStatus.ENABLED]
            : ScheduledQuestionnaireStatus[ScheduledQuestionnaireStatus.DISABLED];

        // Set security review related fields from SQL query
        result.securityReviewStatus = !isNil(vendor.securityReviewStatus)
            ? VendorSecurityReviewStatus[vendor.securityReviewStatus]
            : '';
        // Set security owner
        result.securityOwner =
            !isNil(vendor.securityOwner) && vendor.securityOwner.trim() !== ''
                ? vendor.securityOwner
                : null;
        result.requestedAt = vendor.requestedAt;
        result.reviewDeadlineAt = vendor.reviewDeadlineAt;
        result.lastSocReportReviewAt = vendor.lastSocReportReviewAt;
        result.riskCount = await this.getVendorRiskCount(vendor.id);
        result.securityReviewId = vendor.securityReviewId || null;
        result.securityReviewDecision = !isNil(vendor.securityReviewDecision)
            ? VendorSecurityReviewDecision[vendor.securityReviewDecision]
            : '';
        result.customFields = vendor.customFieldSubmission || {};
        result.updatedAt = vendor.updatedAt;

        return result;
    }

    /**
     * Get the count of risks for a specific vendor
     */
    private async getVendorRiskCount(vendorId: number): Promise<number> {
        try {
            const { riskCount } = await this.riskRepository.getVendorRiskCount(vendorId);
            return riskCount;
        } catch (error) {
            return 0;
        }
    }
}

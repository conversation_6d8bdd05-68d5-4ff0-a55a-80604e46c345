import { AccountContractNotificationStage } from 'app/plan-and-usage/enums/notification-stage';

export function computeNotificationStage(
    daysRenewed: number,
    daysLeft: number,
    daysOppRenewed: number,
    isAutoRenewalOn: boolean,
    isFirstContract: boolean,
    isOpportunityWon?: boolean,
) {
    const fulfillsSevenDaysRenewed =
        // Is a contract with a renewal opportunity that has been won within the last 7 days
        (!isAutoRenewalOn && isOpportunityWon && daysOppRenewed >= -7 && daysOppRenewed <= 0) ||
        // Is a contract that has been renewed within the last 7 days and is not the first contract
        (!isFirstContract && daysRenewed >= -7 && daysRenewed <= 0);

    // Check contract state when opportunity is closed
    const isContractLost = daysOppRenewed < 0 && !isOpportunityWon;
    const isContractWon = daysOppRenewed < 0 && isOpportunityWon;

    switch (true) {
        case fulfillsSevenDaysRenewed:
            return AccountContractNotificationStage.SEVEN_DAYS_RENEWED;
        case isContractLost:
            return AccountContractNotificationStage.CONTRACT_LOST;
        case isContractWon:
            return AccountContractNotificationStage.CONTRACT_WON;
        case daysLeft === 0:
            return AccountContractNotificationStage.ON_RENEWAL_DAY;
        case daysLeft < 0:
            return AccountContractNotificationStage.RENEWAL_DAY_PASSED;
        case daysLeft <= 3:
            return AccountContractNotificationStage.THREE_DAYS_LEFT;
        case daysLeft <= 7:
            return AccountContractNotificationStage.SEVEN_DAYS_LEFT;
        case daysLeft <= 30:
            return AccountContractNotificationStage.THIRTY_DAYS_LEFT;
        case daysLeft <= 60:
            return AccountContractNotificationStage.SIXTY_DAYS_LEFT;
        case daysLeft <= 90:
            return AccountContractNotificationStage.NINETY_DAYS_LEFT;
        default:
            return AccountContractNotificationStage.MORE_THAN_NINETY_DAYS_LEFT;
    }
}

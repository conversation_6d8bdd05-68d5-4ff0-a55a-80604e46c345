import { bootstrapNest } from 'cli/bootstrap-nest';
import { program } from 'commander';
import { runAutopilot } from 'commands/autopilot';
import { numberProcess, stringProcess } from 'commons/helpers/cli.helper';
import config from 'config';
import { isEmpty } from 'lodash';
import { PolloMessage } from 'pollo-logger/classes/pollo.message';
import { PolloLogger } from 'pollo-logger/pollo.logger';
import 'source-map-support/register';

const RDS_BATCH_SIZE = config.get('api.autopilotTenantBatchSize');
const AURORA_BATCH_SIZE = config.get('api.autopilotAuroraTenantBatchSize');

let uncaughtExceptionLogMetadata;

// This CLI will run autopilot
// for CLI options, run: dist/cli.js -h
async function autopilot() {
    program
        .option(
            '-t, --test-ids [test-ids...]',
            'One or more tests to run (space-delimited)',
            numberProcess,
            [],
        )
        .option(
            '-i, --account-ids [account-ids...]',
            'One or more account ids to limit the tests to run for (space-delimited)',
            stringProcess,
            [],
        )
        .option(
            '-d, --account-domains [account-domains...]',
            'One or more account domains to limit the tests to run for (space-delimited)',
            stringProcess,
            [],
        )
        .option(
            '-p, --product-ids [product-ids...]',
            'One or more product ids to target (space-delimited). When used, only one account id/domain must be given.',
            numberProcess,
            [],
        )
        .option('-r, --run-id <runId>', 'The Autopilot Run ID to use for the current run')
        .option('--tenant-db <db_host>', 'The tenant database to limit the test run for')
        .option(
            '--tenant-batch-size <num>',
            'The number of concurrent accounts to run per RDS db host.' +
                ' Default in config -> api -> autopilotTenantBatchSize',
        )
        .option(
            '--aurora-tenant-batch-size <num>',
            'The number of concurrent accounts to run per Aurora db host.' +
                ' Default in config -> api -> autopilotAuroraTenantBatchSize',
        )
        .option('--atomic', 'Run each accounts tests in a distinct ECS task')
        .option('--run-type-auto', 'This is the automated process')
        .option('--async', 'Run each accounts tests asynchronously ')
        .option(
            '--confirmed=true',
            'Run the --atomic flag for specific domains with confirmation. Only used to skip confirmation inside aws containers',
        )
        .parse(process.argv);

    uncaughtExceptionLogMetadata = program;

    const {
        atomic,
        testIds,
        accountIds,
        accountDomains,
        productIds,
        runId,
        runTypeAuto,
        async,
        tenantDb,
        tenantBatchSize,
        auroraTenantBatchSize,
    } = program;

    // let's not allow --account-ids AND --account-domains
    if (!isEmpty(accountIds) && !isEmpty(accountDomains)) {
        console.error('Warning: Only one account query type is supported');
        process.exit(1);
    }

    if (!isEmpty(tenantBatchSize) && tenantBatchSize > RDS_BATCH_SIZE) {
        console.error(
            `Warning: Maximum ${RDS_BATCH_SIZE} concurrent accounts can be run per RDS db host. ` +
                `Please choose ${RDS_BATCH_SIZE} or less.`,
        );
        process.exit(1);
    }

    if (!isEmpty(auroraTenantBatchSize) && auroraTenantBatchSize > AURORA_BATCH_SIZE) {
        console.error(
            `Warning: Maximum ${AURORA_BATCH_SIZE} concurrent accounts can be run per Aurora db host. ` +
                `Please choose ${AURORA_BATCH_SIZE} or less.`,
        );
        process.exit(1);
    }

    try {
        const app = await bootstrapNest();
        await runAutopilot({
            app,
            atomic,
            testIds,
            accountIds,
            accountDomains,
            productIds,
            runId,
            runTypeAuto,
            async,
            tenantDb,
            tenantBatchSize,
            auroraTenantBatchSize,
        });
    } catch (error) {
        console.error('Autopilot CLI Error: ', error);
        process.exit(1);
    }

    process.exit(0);
}

const logger = PolloLogger.logger();

process.on('unhandledRejection', error => {
    const logMessage = PolloMessage.msg('Unhandled rejection detected').setSubContext(
        'unhandledRejectionEventHandler',
    );

    const metadata = uncaughtExceptionLogMetadata;

    if (error instanceof Error) {
        logMessage.setError(error);
    } else {
        metadata.unhandledRejection = error;
    }

    logMessage.setMetadata(metadata);

    logger.error(logMessage);
    process.exit(1);
});

process.on('uncaughtException', error => {
    logger.error(
        PolloMessage.msg('Uncaught exception detected')
            .setSubContext('uncaughtExceptionEventHandler')
            .setMetadata(uncaughtExceptionLogMetadata)
            .setError(error),
    );
    process.exit(1);
});

(async () => {
    await autopilot();
})().catch(error => {
    console.error('Autopilot CLI Error: ', error);
    process.exit(1);
});

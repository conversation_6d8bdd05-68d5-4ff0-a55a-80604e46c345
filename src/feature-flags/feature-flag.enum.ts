/**
 * Private ENUM: This enum was found to be used only in this repository during https://drata.atlassian.net/browse/ENG-60010.
 * Move to shared package in https://github.com/drata/platform/tree/release/packages/enums, if sharing is required.
 **/
/**
 * Lets keep flag names in one place
 */
export enum FeatureFlag {
    FLAG_EXAMPLE = 'flag-example',
    RELEASE_AHA_CONNECTION = 'release-aha-connection',
    RELEASE_BASECAMP_CONNECTION = 'release-basecamp-connection',
    RELEASE_BITBUCKET_CONNECTION = 'release-bitbucket-connection',
    RELEASE_CONFLUENCE_POLICY_INTEGRATION = 'release-confluence-policy-integration',
    RELEASE_CUSTOM_POLICY_WYSIWYG = 'release-custom-policy-wysiwyg',
    RELEASE_SERVICE_NOW_TICKETING = 'release-service-now-ticketing',
    RELEASE_FRESHDESK_CONNECTION = 'release-freshdesk-connection',
    RELEASE_FRESHSERVICE_CONNECTION = 'release-freshservice-connection',
    RELEASE_HEIGHT_CONNECTION = 'release-height-connection',
    RELEASE_HIVE_CONNECTION = 'release-hive-connection',
    RELEASE_TEAMWORK_CONNECTION = 'release-teamwork-connection',
    RELEASE_WINDOWS_DISK_ENCRYPTION = 'release-windows-disk-encryption',
    RELEASE_FILENAME_ABBREVIATION = 'release-filename-abbreviation',
    RELEASE_WRIKE_CONNECTION = 'release-wrike-connection',
    RELEASE_ZENDESK_CONNECTION = 'release-zendesk-connection',
    RELEASE_CUSTOM_RISK_SCORING = 'release-custom-risk-scoring',
    RELEASE_SCANNING = 'release-support-file-formats',
    RELEASE_AZURE_VIRTUAL_ASSETS_V1 = 'release-azure-virtual-assets-v-1',
    RELEASE_GITLAB_ISSUES_WRITE_ACCESS = 'release-gitlab-issues-write-access',
    RELEASE_TRUST_CENTER_FOR_WORKSPACES = 'release-trust-center-for-workspaces',
    RELEASE_TRUST_CENTER_ANALYTICS_REPORT_EXPORT_DOWNLOAD_WAAS = 'release-trust-center-analytics-report-export-download-waas',
    RELEASE_TRUST_CENTER_WEB_ANALYTICS = 'release-trust-center-web-analytics',
    RELEASE_TRUST_CENTER_SECTION_AND_DOCUMENT_REORDERING = 'release-trust-center-section-and-document-reordering',
    RELEASE_LINKING_CONTROL_INFO_AND_EVIDENCE = 'release-linking-control-info-and-evidence',
    RELEASE_CONTROL_APPROVALS = 'release-control-approvals',
    RELEASE_SET_SOC_2_TSC_SCOPE = 'release-set-soc-2-tsc-scope',
    CONFIGURE_DO_NOT_DISABLE_CONTROLS = 'configure-do-not-disable-controls',
    RELEASE_SQA_FOR_WORKSPACES = 'release-security-questionnaires-automate-answering-security-questionnaires',
    RELEASE_AI_OPEN_KNOWLEDGE_BASE = 'release-ai-open-knowledge-base',
    RELEASE_EXTENDED_TPRM_RISK_OBJECTS = 'release-extended-tprm-risk-objects',
    RELEASE_AWS_QUICK_LAUNCH_SQS = 'release-aws-quicklaunch-sqs',
    RELEASE_CUSTOM_VC_AUTO_MATCH_ON_UPDATE = 'release-vc-custom-sync',

    //HRIS expansion
    RELEASE_MERGEDEV_PAYCOR = 'RELEASE_MERGEDEV_PAYCOR',
    RELEASE_MERGEDEV_NAMELY = 'RELEASE_MERGEDEV_NAMELY',
    RELEASE_MERGEDEV_INSPERITY_PREMIER = 'RELEASE_MERGEDEV_INSPERITY_PREMIER',
    RELEASE_MERGEDEV_CERIDIAN_DAYFORCE = 'RELEASE_MERGEDEV_CERIDIAN_DAYFORCE',
    RELEASE_MERGEDEV_ALEXISHR = 'RELEASE_MERGEDEV_ALEXISHR',
    RELEASE_MERGEDEV_BREATHE = 'RELEASE_MERGEDEV_BREATHE',
    RELEASE_MERGEDEV_CHARLIE = 'RELEASE_MERGEDEV_CHARLIE',
    RELEASE_MERGEDEV_CHARTHOP = 'RELEASE_MERGEDEV_CHARTHOP',
    RELEASE_MERGEDEV_DEEL = 'RELEASE_MERGEDEV_DEEL',
    RELEASE_MERGEDEV_FACTORIAL = 'RELEASE_MERGEDEV_FACTORIAL',
    RELEASE_MERGEDEV_INTELLIHR = 'RELEASE_MERGEDEV_INTELLIHR',
    RELEASE_MERGEDEV_KEKA = 'RELEASE_MERGEDEV_KEKA',
    RELEASE_MERGEDEV_LUCCA = 'RELEASE_MERGEDEV_LUCCA',
    RELEASE_MERGEDEV_OFFICIENT = 'RELEASE_MERGEDEV_OFFICIENT',
    RELEASE_MERGEDEV_PAYCHEX = 'RELEASE_MERGEDEV_PAYCHEX',
    RELEASE_MERGEDEV_PEOPLE_HR = 'RELEASE_MERGEDEV_PEOPLE_HR',
    RELEASE_MERGEDEV_HR_PARTNER = 'RELEASE_MERGEDEV_HR_PARTNER',
    RELEASE_MERGEDEV_HUMAANS = 'RELEASE_MERGEDEV_HUMAANS',
    RELEASE_MERGEDEV_OYSTER_HR = 'RELEASE_MERGEDEV_OYSTER_HR',
    RELEASE_MERGEDEV_SAGE_HR = 'RELEASE_MERGEDEV_SAGE',
    RELEASE_MERGEDEV_SAP_SUCCESSFACTORS = 'RELEASE_MERGEDEV_SAP_SUCCESSFACTORS',
    RELEASE_MERGEDEV_SAPLING = 'RELEASE_MERGEDEV_SAPLING',
    RELEASE_MERGEDEV_UKG_READY = 'RELEASE_MERGEDEV_UKG_READY',
    RELEASE_MERGEDEV_EMPLOYMENT_HERO = 'release-mergedev-employment-hero',
    RELEASE_MERGEDEV_ZOHO_PEOPLE = 'release-mergedev-zoho-people',
    RELEASE_MERGEDEV_CYBERARK = 'release-cyber-ark',
    RELEASE_MERGEDEV_PINGONE = 'release-pingone-connection',
    RELEASE_MERGEDEV_LATTICE_HRIS = 'release-mergedev-lattice-hris',
    RELEASE_MERGEDEV_DARWINBOX = 'release-mergedev-darwinbox',
    RELEASE_MERGEDEV_PAYCOM = 'release-mergedev-paycom',

    //UAR connections
    RELEASE_UAR_CHECKR = 'release-uar-checkr',
    RELEASE_UAR_ZENDESK = 'release-uar-zendesk',
    RELEASE_UAR_LEVER = 'release-uar-lever',
    RELEASE_UAR_AZURE_DEVOPS = 'release-uar-azure-devops',
    RELEASE_UAR_AHA = 'release-uar-aha',
    RELEASE_UAR_BITBUCKET = 'release-uar-bitbucket',
    RELEASE_UAR_FRESHDESK = 'release-uar-freshdesk',
    RELEASE_UAR_FRESHSERVICE = 'release-uar-freshservice',
    RELEASE_UAR_HIVE = 'release-uar-hive',
    RELEASE_UAR_WRIKE = 'release-uar-wrike',
    RELEASE_UAR_FRONT = 'release-uar-front',
    RELEASE_UAR_LATTICE = 'release-uar-lattice',
    RELEASE_UAR_WEBFLOW = 'release-uar-webflow',
    RELEASE_UAR_SCORO = 'release-uar-scoro',
    UAR_AWS_GRANULAR_DATA = 'uar-aws-granular-data',
    // Temp
    ACCESS_REVIEW_USER_LIST_PERFORMANCE = 'access-review-query-optimization-rollout',
    RELEASE_MERGEDEV_FRONT = 'release-mergedev-front',

    RELEASE_JIRA_EVIDENCE_LIBRARY_INTEGRATION_V1 = 'release-jira-evidence-library-integration-v-1',
    // Size limit validation
    EVIDENCE_LIBRARY_TICKET_SIZE_LIMIT = 'evidence-library-ticket-size-limit',

    // RBAC Risk Manager Extended To Vendors
    RELEASE_RBAC_RISK_MANAGER_EXTENDED_TO_VENDORS = 'release-rbac-risk-manager-extended-to-vendors',

    // Connection Google Idp Enhancements
    RELEASE_IDP_CONNECTION_ENHANCEMENTS_GW = 'release-idp-connection-enhancements-gw',
    // Multi Write Ticketing
    RELEASE_MULTI_WRITE_JIRA = 'release-multi-write-jira',
    // Risk Assessment v2
    RELEASE_RISK_ASSESSMENT_V2 = 'release-risk-assessment-v-2',
    // AI TPRM
    RELEASE_TPRM_QUESTIONNAIRE_AI_SUMMARY = 'release-tprm-questionnaire-ai-summary',
    // CUSTOM-FIELDS
    RELEASE_CUSTOM_FIELDS_FOR_RISK_MANAGEMENT = 'release-custom-fields-for-risk-management',
    // Notion external policy manager
    RELEASE_NOTION_POLICIES = 'release-notion-policies',
    /**
     * @deprecated use RELEASE_EMAIL_POOLS_V2
     */
    RELEASE_EMAIL_POOLS = 'release-email-pools',
    RELEASE_EMAIL_POOLS_V2 = 'release-email-pools-v2',
    RELEASE_CUSTOM_QUESTIONNAIRE_LIMIT = 'release-custom-questionnaire-limit',
    // Begin WaaS Feature Flags
    PING_AS_WORKFLOW = 'ping-as-workflow',
    CONTROL_EVIDENCE_PACKAGE_AS_WORKFLOW = 'control-evidence-package-as-workflow',
    RELEASE_POLICY_ASSIGNMENT_WORKFLOW = 'release-policy-assignment-workflow',
    RELEASE_SALESFORCE_AUTOMATED_TENANT_CREATION = 'release-salesforce-automated-tenant-creation',

    PERSONNEL_BULK_ACTION_VALIDATE_EMPLOYMENT_WORKFLOW = 'personnel-bulk-action-validate-employment-workflow',
    RISK_BULK_ACTIONS_WORKFLOW = 'risk-bulk-actions-workflow',
    // End WaaS Feature Flags

    // SafeBase feature flags
    RELEASE_TRUST_CENTER_SAFE_BASE_DATA_SYNC = 'release-trust-center-safe-base-data-sync',
    RELEASE_TRUST_CENTER_SAFE_BASE_EVIDENCE_LIBRARY = 'release-trust-center-safe-base-evidence-library',

    // VULNERABILITY MONITORING,
    RELEASE_ONBOARDING_PHASE_1_NEW_WELCOME_EXPERIENCE = 'release-onboarding-phase-1-new-welcome-experience',

    // VULNERABILITY MONITORING,
    RELEASE_VULNERABILITY_MONITORING = 'release-vulnerability-monitoring',

    // Background Check Manual Upload Providers
    RELEASE_BGC_STERLING = 'release-bgc-sterling',
    RELEASE_BGC_HIRERIGHT = 'release-bgc-hire-right',

    // AWS Identity Center Users
    AWS_IDENTITY_CENTER = 'aws-identity-center',

    // Leen
    RELEASE_TENABLE_VIA_LEEN = 'release-tenable-via-leen',
    RELEASE_QUALYS_VIA_LEEN = 'release-qualys-via-leen',
    RELEASE_SEMGREP_VIA_LEEN = 'release-semgrep-via-leen',
    RELEASE_SNYK_VIA_LEEN = 'release-snyk-via-leen',
    RELEASE_MICROSOFT_DEFENDER_VM_VIA_LEEN = 'release-microsoft-defender-vm-via-leen',
    RELEASE_SENTINEL_ONE_VULNERABILITY_MANAGEMENT_VIA_LEEN = 'release-sentinel-one-vulnerability-management-via-leen',
    RELEASE_CROWD_STRIKE_SPOTLIGHT_VIA_LEEN = 'release-crowd-strike-spotlight-via-leen',
    RELEASE_RAPID7_VIA_LEEN = 'release-rapid-7-via-leen',
    RELEASE_ARNICA_VIA_LEEN = 'release-arnica-via-leen',
    RELEASE_AIKIDO_VIA_LEEN = 'release-aikido-via-leen',
    RELEASE_WIZ_VMS_VIA_LEEN = 'release-wiz-vms-via-leen',
    RELEASE_WIZ_CODE_VIA_LEEN = 'release-wiz-code-via-leen',

    RELEASE_EVIDENCE_GUIDANCE = 'release-evidence-guidance',

    // Custom Workflows
    RELEASE_CUSTOM_WORKFLOWS = 'release-custom-workflows',

    // Salesforce WAAS Automation
    DRY_RUN_SALESFORCE_ACCOUNT_DELETION = 'experiment-salesforce-account-deletion-workflow-dry-run',

    RELEASE_SCALING_RISK_FOUNDATION = 'release-scaling-risk-foundation',
    RELEASE_RISK_STATUS = 'release-closed-status-for-risk',
    RELEASE_COSMOS_TABLE_FOR_RISK = 'release-cosmos-table-for-risk',
    RELEASE_OPENSEARCH_FOR_COSMOS_TABLE_FOR_RISK = 'release-opensearch-for-cosmos-table-for-risk',

    // Exception Management
    RELEASE_EXCEPTION_MANAGEMENT = 'release-exception-management',

    // Release Vendors Opensearch
    RELEASE_VENDORS_OPENSEARCH = 'release-vendors-opensearch',

    // Bamboo native connection
    RELEASE_BAMBOO_HR_NO_MERGE_DEV = 'release-bamboo-hr-no-merge-dev',

    /*****  Start: Connection Run Test  *****/
    RELEASE_AWS_AUTO_RUN_TESTS = 'release-aws-auto-run-tests',
    RELEASE_INFRA_AUTO_RUN_TESTS = 'release-infra-auto-run-tests',
    RELEASE_IDENTITY_AUTO_RUN_TESTS = 'release-identify-auto-run-tests',
    RELEASE_VERSIONING_AUTO_RUN_TESTS = 'release-versioning-auto-run-tests',
    RELEASE_TICKETING_AUTO_RUN_TESTS = 'release-ticketing-auto-run-tests',
    /*****  End: Connection Run Test  *****/

    // Infrastructure sync concurrent connections
    RELEASE_INFRA_SYNC_BATCH_CONCURRENT_CONNECTIONS = 'release-infra-sync-batch-concurrent-connections',
    // Events Migration to S3 https://drata.atlassian.net/browse/ENG-50964
    MIGRATION_EVENTS_WRITE_TO_S3 = 'migration-events-write-to-s3',
    MIGRATION_EVENTS_READ_FROM_S3 = 'migration-events-read-from-s3',
    MIGRATION_EVENTS_LOGGING_S3 = 'migration-events-logging-s3',
    MIGRATION_EVENTS_WRITE_TO_SQL = 'migration-events-write-to-sql',
    // Multiple IDP Global enablement feature flag
    RELEASE_MULTI_IDP = 'release-multi-idp',
    // Multiple HRIS enablement feature flag
    RELEASE_MULTI_HRIS = 'release-multi-hris',

    // Vendor enable suggestion for esential users
    RELEASE_ENABLING_SUGGESTION_VIA_CONNECTIONS_FOR_ESSENTIALS = 'release-enabling-suggestions-via-connections-for-essential',

    // AWS Org Units support for test 111
    RELEASE_TEST_111_SUPPORT_FOR_AWS_ORG_UNITS = 'release-test-111-support-for-aws-org-units',
    RELEASE_AWS_RESOURCE_SCOPING = 'release-aws-resource-scoping',

    // AWS Org Units support for Virtual Assets Synchronization
    RELEASE_AWS_ORGS_VIRTUAL_ASSETS = 'release-aws-orgs-virtual-assets',

    // AWS Org Units Parallelized Asset Synchronization
    RELEASE_AWS_ORGS_PARALLELIZED_ASSET_SYNC = 'release-aws-orgs-parallelized-asset-sync',

    // AWS Org Units getActiveOrganizationalAccounts v2
    RELEASE_AWS_ORGS_GET_ACTIVE_ORGANIZATIONAL_ACCOUNTS_V2 = 'release-aws-orgs-get-active-organizational-accounts-v-2',

    // New Policy statuses
    RELEASE_NEW_POLICY_STATUSES = 'release-new-policy-statuses',

    // Autopilot Queries Refactor
    RELEASE_AP_PERSONNEL_WITH_FAILING_MFA_REFACTOR = 'ap-personnel-with-failing-mfa-queries-refactor',
    RELEASE_AP_PERSONNEL_WITH_DEVICES_QUERIES_REFACTOR = 'ap-personnel-with-devices-queries-refactor',
    RELEASE_AP_PERSONNEL_QUERIES_REFACTOR = 'ap-personnel-queries-refactor',
    RELEASE_AP_ASSETS_QUERIES_REFACTOR_V2 = 'release-auto-pilot-assets-query-refactor-v2',

    // Autopilot Queries Refactor
    RELEASE_AP_MONITORING_TEST_INSTANCE_ENDPOINT_REFRACTOR = 'monitoring-test-instance-endpoint',

    // controls what oauth resources to return the FE for msft idp customers
    RELEASE_MICROSOFT_TENANT_LOGIN_PAGE = 'release-microsoft-tenant-login-page',

    // Knock App notifications migration
    RELEASE_COURIER_KNOCK_MIGRATION = 'release-courier-to-knock-migration',

    // Multiple IDP separate user personnel feature flag
    RELEASE_MULTI_IDP_SEPARATE_USER_PERSONNEL = 'release-multi-idp-separate-user-personnel',
    // controls AP1 async behavior - all tasks async or all group tasks async but groups are sync
    RELEASE_AP1_ASYNC_GROUP_TASKS = 'release-ap-1-async-group-tasks',
    // Group sync API call reduction
    EXPERIMENT_GROUP_SYNC_API_CALL_REDUCTION = 'experiment-group-sync-api-call-reduction',

    // Personnel endpoint optimized
    RELEASE_SINGLE_PERSONNEL_ENDPOINT_OPTIMIZED = 'release-single-personnel-endpoint-optimized',

    // Allows Beacon to bypass the temporal for Scheduler
    RECURRING_TASKS_SKIP_TEMPORAL = 'recurring-tasks-skip-temporal',

    // Evidence Library
    RELEASE_MERGE_TEST_EVIDENCE_INTO_EL = 'release-merge-test-evidence-into-el',
    RELEASE_SHOW_STATUS_FILTER_TEST_EVIDENCE = 'release-show-status-filter-test-evidence',
    RELEASE_AUTOPILOT_TEST_EVIDENCE_FLOW = 'release-autopilot-test-evidence-flow',
    RELEASE_HIDE_TEST_EVIDENCES = 'release-hide-test-evidences',

    // Policy Multi-approvers
    POLICY_MULTI_APPROVERS = 'policy-multi-approvers',

    // Q1 optimization endpoints - policy-center
    RELEASE_OPTIMIZE_POLICIES_OVERVIEW_ENDPOINT = 'release-optimize-policies-overview-endpoint',

    // vendors endpoint optimization
    RELEASE_OPTIMIZED_VENDORS_ENDPOINT = 'release-optimized-vendors-endpoint',

    // vendors review endpoint optimization
    RELEASE_OPTIMIZED_VENDORS_REVIEW_ENDPOINT = 'release-optimized-vendors-review-endpoint',

    // auditor auth endpoint optimization
    RELEASE_AUDITOR_AUTH_ENDPOINT = 'release-optimized-auditor-auth-endpoint',

    RELEASE_MDM_INTUNE_API_BATCH_CALLS = 'release-mdm-intune-api-batch-calls',

    RELEASE_GROUP_ENDPOINT_IMPROVEMENT = 'release-group-endpoint-improvement',

    // Release Tasks Notifications
    RELEASE_TASKS_NOTIFICATIONS = 'release-tasks-notifications',

    // Release Security Report Performance
    RELEASE_SECURITY_REPORT_PERFORMANCE = 'release-security-report-performance',

    // Release Optimized Audit Hub Audits Endpoint
    RELEASE_OPTIMIZED_AUDIT_HUB_AUDITS_ENDPOINT = 'release-optimized-audit-hub-audits-endpoint',

    RELEASE_DEBUG_BUCKET_SERVICE_LOGS = 'release-debug-bucket-service-logs',
    // Release AwsSdkV3
    RELEASE_AWS_SDK_V3 = 'release-aws-sdk-v3',

    // Release List Vendor Performance
    RELEASE_LIST_VENDOR_PERFORMANCE = 'release-list-vendor-performance',

    // Release Controls OpenSearch
    RELEASE_CONTROLS_OPENSEARCH = 'release-controls-opensearch',

    // Release Overdue Task Notifications
    RELEASE_OVERDUE_TASKS = 'release-overdue-tasks',

    // Release frameworks
    RELEASE_FED_RAMP_20_X = 'release-fed-ramp-20-x',
    RELEASE_HITRUST = 'release-hitrust',
    RELEASE_MSSSPA_V11 = 'release-microsoft-sspa-v11',

    // Kill switch
    ENABLE_AP_TELEMETRY = 'enable-ap-tenant-telemetry',

    // Bulk Import used with Flatfile
    RELEASE_BULK_IMPORT = 'release-bulk-import',

    // Bulk Import for Risks with Flatfile
    RELEASE_BULK_IMPORT_RISKS = 'release-bulk-import-risks',

    // Bulk Import for Controls with Flatfile
    RELEASE_BULK_IMPORT_CONTROLS = 'release-bulk-import-controls',

    // WorkspaceONE API Enhanced Error Handling
    RELEASE_WORKSPACE_ONE_ENHANCED_ERROR_HANDLING = 'release-workspace-one-enhanced-error-handling',

    // Zoom app adding roles scope flag
    RELEASE_UAR_ZOOM_ROLES = 'release-uar-zoom-roles',

    // Agent Configuration
    CONFIGURE_MDM_SCREENLOCK = 'configure-mdm-screenlock',

    // Stale agent data notification
    RELEASE_NOTIFICATION_STALE_AGENT_DATA = 'release-notification-stale-agent-data',

    //Release Azure GCC High
    RELEASE_AZURE_GCC_HIGH_CONNECTION = 'release-azure-gcc-high-connection',

    // Monitoring Findings Enhancement Phase 1
    RELEASE_FINDINGS_ENHANCEMENT_PHASE_1 = 'release-findings-enhancements-phase-1',

    // Checkr Await Time
    RELEASE_TRABA_BG_CHECK_DELAY = 'release-traba-bg-check-delay',

    // Traba 'created_after' parameter in list candidates
    RELEASE_CHECKR_CREATED_AFTER_DATE = 'release-checkr-created-after-date',

    //JumpCloud API optimizations
    JUMPCLOUD_POLICY_STATUS_EXCLUDE_UNINSTALL_SUCCESS = 'jumpcloud-policy-status-exclude-uninstall-success',

    //GCP Optimizations: Individualize retries
    RELEASE_GCP_INDIVIDUALIZE_RETRIES = 'release-gcp-individualize-retries',

    //Control action panel:
    RELEASE_CONTROL_ACTION_PANEL = 'release-control-action-panel',

    // Constellation opt in
    RELEASE_CONSTELLATION_OPT_IN = 'release-constellation-opt-in',

    // Constellation opt in V2
    RELEASE_CONSTELLATION_OPT_IN_V2 = 'release-constellation-opt-in-v-2',

    // Upcoming tasks reminders
    RELEASE_UPCOMING_TASK_NOTIFICATIONS = 'release-upcoming-task-notifications',

    // Intune: Empty windows device report search param allowed
    RELEASE_EMPTY_WINDOWS_DEVICE_REPORT_SEARCH_PARAM_ALLOWED = 'release-empty-windows-device-report-search-param-allowed',

    // Vendor risk management agent mvp
    RELEASE_VRM_AGENT_MVP = 'release-vrm-agent-mvp',

    // Personnel exclusions count defaulting
    RELEASE_PERSONNEL_EXCLUSIONS_COUNT = 'release-personnel-exclusions-count',

    // Enhanced monitoring result counts
    RELEASE_ENHANCED_MONITORING_RESULT_COUNTS = 'release-enhanced-monitoring-result-counts',

    // Bulk Import for Controls with Flatfile
    RELEASE_BULK_IMPORT_TRAININGS = 'release-bulk-import-trainings',

    // Policy AI control suggestions mapping
    RELEASE_AI_CONTROL_SUGGESTIONS_MAPPING = 'release-ai-control-suggestions-mapping',

    // Autopilot tasks run in batches
    POC_AUTOPILOT_TASKS_RUN_IN_BATCHES = 'poc-autopilot-tasks-run-in-batches',
}

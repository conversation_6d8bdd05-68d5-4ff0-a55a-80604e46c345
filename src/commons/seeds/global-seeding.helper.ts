/* eslint-disable no-await-in-loop */
import {
    AuditorFrameworkType as AuditorFrameworkTypeEnum,
    FeatureGroup,
    FrameworkTag,
    RequirementIndexCategory,
    RequirementIndexSubcategory,
    RequirementIndexTag,
    SafeBaseMigrationStatus,
    TestSource,
    TrustServiceCriteria,
} from '@drata/enums';
import { RecipeDefinition } from '@drata/recipes';
import { faker } from '@faker-js/faker';
import { Company } from 'app/companies/entities/company.entity';
import { Framework } from 'app/frameworks/entities/framework.entity';
import { User } from 'app/users/entities/user.entity';
import { PolicyStatus } from 'app/users/policies/enums/policy-status.enum';
import { AccountSafeBaseSettings } from 'auth/entities/account-safebase-settings.entity';
import { Account } from 'auth/entities/account.entity';
import { Entry } from 'auth/entities/entry.entity';
import { TenantRouter } from 'auth/entities/tenant-router.entity';
import { Token } from 'auth/entities/token.entity';
import { AutopilotRecipeTemplate } from 'autopilot2/entities/autopilot-recipe-template.entity';
import { type DrataDataSource } from 'commons/classes/drata-data-source.class';
import { deprecatedControlTestIds } from 'commons/configs/deprecated-control-tests.config';
import { AccountEntitlementType } from 'commons/enums/account-entitlement-type.enum';
import { AuditorFrameworkDateType } from 'commons/enums/auditors/auditor-framework-date-type.enum';
import { ClientType } from 'commons/enums/auth/client-type.enum';
import { TenantRouterCategory } from 'commons/enums/auth/tenant-router-category.enum';
import { AutopilotTaskType } from 'commons/enums/autopilot/autopilot-task-type.enum';
import { Category } from 'commons/enums/category.enum';
import { CheckFrequency } from 'commons/enums/check-frequency.enum';
import { CheckType } from 'commons/enums/check-type.enum';
import { Domain } from 'commons/enums/domain.enum';
import { FeatureAccess } from 'commons/enums/feature-toggling/feature-access.enum';
import { FeatureType } from 'commons/enums/feature-toggling/feature.enum';
import { Priority } from 'commons/enums/priority.enum';
import { RequestDescriptionType } from 'commons/enums/request-description-type.enum';
import { RunMode } from 'commons/enums/run-mode.enum';
import { AdminRole } from 'commons/enums/site-admin/admin-role.enum';
import { PolicyMetadataType } from 'commons/enums/users/policies/policy-metadata-type.enums';
import { Role } from 'commons/enums/users/role.enum';
import { pathResolve } from 'commons/helpers/file.helper';
import { readJSONFile } from 'commons/helpers/json-reader.helper';
import { iEqualsWithTrimmer } from 'commons/helpers/string.helper';
import { hasRole } from 'commons/helpers/user.helper';
import {
    createActAsEntry,
    createCompany,
    switchToDefaultConnection,
    switchToTenantConnection,
} from 'commons/seeds/app-seeding.helper';
import { AccountRequest } from 'commons/types/account-request.type';
import { AdminOwner } from 'commons/types/admin-owner.type';
import { BasePolicy } from 'commons/types/base-policy.type';
import { ControlTestTemplateSeedOptions } from 'commons/types/contol-test-template-seed-option.seed';
import { DrataControlFixture } from 'commons/types/drata-control-fixture.type';
import { DrataControlFixtures } from 'commons/types/drata-control-fixtures.type';
import { DrataEvidenceFixture } from 'commons/types/drata-evidence-fixture.type';
import { DrataEvidenceFixtures } from 'commons/types/drata-evidence-fixtures.type';
import { FeatureTemplate as FeatureTemplateType } from 'commons/types/feature-template.type';
import { CatalogJSON } from 'commons/types/framework-oscal/catalog-json.type';
import { MonitorTemplateSeedOptions } from 'commons/types/monitor-template-seed-options.type';
import { SLATemplate } from 'commons/types/sla-template.type';
import config from 'config';
import FrameworkProvisioner from 'database/seeds/app/framework-provisioner.seed';
import CreateEvidenceTemplates from 'database/seeds/global/create-evidence-templates.seeds';
import CreatePolicyTemplates from 'database/seeds/global/create-policy-templates.seed';
import DrataSiteAdmins from 'database/seeds/global/drata-site-admins.seed';
import FrameworkTemplateProvisioner from 'database/seeds/global/framework-template-provisioner.seed';
import { getCustomRepository } from 'database/typeorm/typeorm.extensions.helper';
import { AccountEntitlement } from 'entitlements/entities/account-entitlement.entity';
import { TrustCenter } from 'entitlements/trust-center/entities/trust-center.entity';
import fs from 'fs';
import { cloneDeep, find, get, isEmpty, isNil, isUndefined, words } from 'lodash';
import { PlanEntitlement } from 'plan-and-usage/entities/plan-entitlement.entity';
import { Plan } from 'plan-and-usage/entities/plan.entity';
import { AccountPlan } from 'plan-and-usage/enums/account-plan.enum';
import { extractNumericCode } from 'scripts/grc/sync.helpers';
import { AuditorFrameworkTypeTemplate } from 'site-admin/entities/auditor-framework-type-template.entity';
import { ControlTemplate } from 'site-admin/entities/control-template.entity';
import { ControlTestTemplate } from 'site-admin/entities/control-test-template.entity';
import { EvidenceTemplate } from 'site-admin/entities/evidence-template.entity';
import { FeatureTemplate } from 'site-admin/entities/feature-template.entity';
import { FrameworkTemplate } from 'site-admin/entities/framework-template.entity';
import { MonitorTemplateCheckType } from 'site-admin/entities/monitor-template-check-type.entity';
import { MonitorTemplate } from 'site-admin/entities/monitor-template.entity';
import { TrustCenterMonitoringControlTemplate } from 'site-admin/entities/monitoring-controls-template.entity';
import { PolicyMetadataTemplate } from 'site-admin/entities/policy-metadata-template.entity';
import { PolicySLATemplate } from 'site-admin/entities/policy-sla-template.entity';
import { PolicyTemplateContent } from 'site-admin/entities/policy-template-content.entity';
import { PolicyTemplate } from 'site-admin/entities/policy-template.entity';
import { ProfileDetailsTemplate } from 'site-admin/entities/profile-details-template.entity';
import { RequirementIndexTagTemplate } from 'site-admin/entities/requirement-index-tag-template.entity';
import { RequirementIndexTemplate } from 'site-admin/entities/requirement-index-template.entity';
import { RequirementTemplate } from 'site-admin/entities/requirement-template.entity';
import { SiteAdminComment } from 'site-admin/entities/site-admin-comment.entity';
import { SiteAdminRole } from 'site-admin/entities/site-admin-role.entity';
import { SiteAdmin } from 'site-admin/entities/site-admin.entity';
import { TenantDatabaseHost } from 'site-admin/entities/tenant-database-host.entity';
import { ProfileDetailsTemplateRepository } from 'site-admin/repositories/profile-details-template.repository';
import { AgentRequestDescriptionType } from 'site-admin/types/agent-request-description.type';
import { ConnectionRequestDescriptionType } from 'site-admin/types/connection-request-description.type';
import { SingleRequestDescriptionType } from 'site-admin/types/single-request-description.type';
import { Repository } from 'typeorm';
import { Factory } from 'typeorm-seeding';

export async function createSiteAdmin(factory: Factory, owner: AdminOwner | null): Promise<any> {
    // use the factory to generate a site admin
    const siteAdmin: SiteAdmin = await factory(SiteAdmin)()
        .map(async (generatedSiteAdmin: SiteAdmin) => {
            // init
            const roleList: SiteAdminRole[] = [];
            //set default roles
            let adminRoles: AdminRole[] = [AdminRole.ADMIN, AdminRole.EMPLOYEE];
            // spin on the admin roles
            if (!isNil(owner)) {
                if (!isEmpty(owner.adminRoles)) {
                    // override
                    adminRoles = owner.adminRoles;
                }
                if (owner?.email && !isEmpty(owner.email)) {
                    // override the data
                    generatedSiteAdmin.email = owner.email;
                }
                if (owner?.firstName && !isEmpty(owner.firstName)) {
                    // override the data
                    generatedSiteAdmin.firstName = owner.firstName;
                }
                if (owner?.lastName && !isEmpty(owner.lastName)) {
                    // override the data
                    generatedSiteAdmin.lastName = owner.lastName;
                }
            }
            // iterate over the set
            for (const adminRole of adminRoles) {
                // generate it
                const newAdminRole = await factory(SiteAdminRole)()
                    .map(async (generatedRole: SiteAdminRole) => {
                        // set the requested role
                        generatedRole.role = adminRole;
                        // link them
                        generatedRole.siteAdmin = generatedSiteAdmin;
                        // ready to return
                        return generatedRole;
                    })
                    .make();
                // add it to thelist
                roleList.push(newAdminRole);
            }

            generatedSiteAdmin.roles = roleList;

            // override the data
            generatedSiteAdmin.avatar = null;

            // Override site admin id (GUID) if user matches the automation email in
            // the localdev_stub secrets in siteAdmin.
            // This is used supporting automation in lower environments and does not
            // affect production, as this only is seeded with nukeAndPave seeders
            const adminEmail = config.get('siteAdmin.email');
            const adminGuid = config.get('siteAdmin.guid');
            if (iEqualsWithTrimmer(generatedSiteAdmin.email, adminEmail)) {
                generatedSiteAdmin.id = adminGuid;
            }
            return generatedSiteAdmin;
        })
        .create();
    return siteAdmin;
}

export async function createManySiteAdmins(
    factory: Factory,
    owners: AdminOwner[],
): Promise<SiteAdmin[]> {
    // initialize
    const siteAdmins: SiteAdmin[] = [];
    // spin over the owners
    for (const owner of owners) {
        // create a single one
        const siteAdmin: SiteAdmin = await createSiteAdmin(factory, owner);

        siteAdmins.push(siteAdmin);
    }
    // ready to return
    return siteAdmins;
}

export async function createAutopilotRecipeTemplate(
    factory: Factory,
    baseAutopilotRecipeTemplate: RecipeDefinition,
): Promise<AutopilotRecipeTemplate> {
    return factory(AutopilotRecipeTemplate)()
        .map(async (createdAutopilotRecipe: AutopilotRecipeTemplate) => {
            // set html data
            const { assessmentId, recipe } = baseAutopilotRecipeTemplate;

            createdAutopilotRecipe.name = `Drata Test ${assessmentId}`;
            createdAutopilotRecipe.recipe = recipe;
            createdAutopilotRecipe.assessmentId = baseAutopilotRecipeTemplate.assessmentId;
            createdAutopilotRecipe.version = 1;
            return createdAutopilotRecipe;
        })
        .create();
}

export async function createManyAutopilotRecipesTemplates(
    factory: Factory,
    baseTemplateRecipes: RecipeDefinition[],
): Promise<Array<AutopilotRecipeTemplate>> {
    // initialize
    const autopilotRecipeTemplates: Array<AutopilotRecipeTemplate> = [];

    // spin over the base policies
    for (const baseTemplateRecipe of baseTemplateRecipes) {
        // create a single one
        const autopilotRecipeTemplate: AutopilotRecipeTemplate =
            await createAutopilotRecipeTemplate(factory, baseTemplateRecipe);

        autopilotRecipeTemplates.push(autopilotRecipeTemplate);
    }
    return autopilotRecipeTemplates;
}

export async function createPolicyTemplate(factory: Factory, basePolicy: BasePolicy): Promise<any> {
    // use the factory to generate a policy template
    const policyTemplate: PolicyTemplate = await factory(PolicyTemplate)()
        .map(async (generatedPolicyTemplate: PolicyTemplate) => {
            // set html data
            generatedPolicyTemplate.name = basePolicy.name;
            generatedPolicyTemplate.description = basePolicy.description;
            generatedPolicyTemplate.html = isNil(basePolicy.html)
                ? null
                : fs
                      .readFileSync(pathResolve(config.get('fixtures.path') + basePolicy.html))
                      .toString();
            generatedPolicyTemplate.policyStatus = PolicyStatus.ACTIVE;
            // ready to return
            return generatedPolicyTemplate;
        })
        .create();
    // ready to return
    return policyTemplate;
}

export async function createManyPolicyTemplates(
    factory: Factory,
    basePolicies: BasePolicy[],
    siteAdmin: SiteAdmin[],
): Promise<any> {
    // initialize
    const policyTemplates: PolicyTemplate[] = [];

    // spin over the base policies
    for (const basePolicy of basePolicies) {
        // create a single one
        const policyTemplate: PolicyTemplate = await createPolicyTemplate(factory, basePolicy);
        await createTopicsKeywords(basePolicies, policyTemplate, factory);
        // check if this policy template has comments set
        if (!isNil(basePolicy.comments) && !isEmpty(siteAdmin)) {
            const siteAdminComment = basePolicy.comments;

            // create comments/
            await createSiteAdminComments(factory, siteAdminComment, policyTemplate, siteAdmin);
        }

        if (!isNil(basePolicy.additionalContents)) {
            await createAdditionalsHtmlTemplates(factory, basePolicy, policyTemplate, siteAdmin);
        }

        // check if this policy template has an SLA set
        if (!isNil(basePolicy.slaTemplates)) {
            const slaTemplates = basePolicy.slaTemplates;
            // delegate to function
            const policySLATemplates = await createManyPolicySLATemplates(
                factory,
                policyTemplate,
                slaTemplates,
            );
            // set the property
            policyTemplate.policySLATemplates = policySLATemplates;
        }
        // add it to the set
        policyTemplates.push(policyTemplate);
    }
    return policyTemplates;
}

export async function createEvidenceTemplate(
    factory: Factory,
    evidenceTemplateFixture: DrataEvidenceFixture,
): Promise<EvidenceTemplate> {
    return factory(EvidenceTemplate)()
        .map(async (generatedEvidenceTemplate: EvidenceTemplate) => {
            generatedEvidenceTemplate.id = evidenceTemplateFixture.id;
            generatedEvidenceTemplate.evidenceTemplateCode =
                evidenceTemplateFixture.evidenceTemplateCode;
            generatedEvidenceTemplate.name = evidenceTemplateFixture.name;
            const trimmedDescription = evidenceTemplateFixture.description.trim();
            generatedEvidenceTemplate.requestDescription =
                trimmedDescription === '' ? null : trimmedDescription;
            generatedEvidenceTemplate.implementationGuidance =
                evidenceTemplateFixture.implementationGuidance;
            return generatedEvidenceTemplate;
        })
        .create();
}
export async function createEvidenceTemplates(
    factory: Factory,
    evidenceTemplateFixtures: DrataEvidenceFixtures,
): Promise<EvidenceTemplate[]> {
    const evidenceTemplates: EvidenceTemplate[] = [];
    for (const evidenceTemplateFixture of evidenceTemplateFixtures.evidenceTemplates) {
        const evidenceTemplate: EvidenceTemplate = await createEvidenceTemplate(
            factory,
            evidenceTemplateFixture,
        );
        evidenceTemplates.push(evidenceTemplate);
    }
    return evidenceTemplates;
}

async function createTopicsKeywords(policiesYml: any[], policy: PolicyTemplate, factory: Factory) {
    if (policiesYml && policiesYml.length > 0) {
        const policyYml = find(policiesYml, document => document.name === policy.name);

        if (!isNil(policyYml)) {
            await createPolicyMetadata(factory, policyYml.topics, policy, PolicyMetadataType.TOPIC);

            await createPolicyMetadata(
                factory,
                policyYml.keywords,
                policy,
                PolicyMetadataType.KEYWORD,
            );
        }
    }
}

export async function createPolicyMetadata(
    factory: Factory,
    metadata: any[],
    policyTemplate: PolicyTemplate,
    type: PolicyMetadataType,
): Promise<PolicyMetadataTemplate[]> {
    if (isNil(metadata)) {
        return [];
    }

    const policyMetadata: PolicyMetadataTemplate[] = [];

    for (const description of metadata) {
        const descriptionWords = words(description.name);

        for (const word of descriptionWords) {
            const policyMeta = await factory(PolicyMetadataTemplate)()
                .map(async (generatedMetadata: PolicyMetadataTemplate) => {
                    generatedMetadata.name = word;
                    generatedMetadata.description = description.name;
                    generatedMetadata.type = type;
                    generatedMetadata.policy = policyTemplate;

                    return generatedMetadata;
                })
                .create();

            policyMetadata.push(policyMeta);
        }
    }

    return policyMetadata;
}

export async function createSiteAdminComments(
    factory: Factory,
    comments: SiteAdminComment[],
    policy: PolicyTemplate,
    siteAdmin: SiteAdmin[],
): Promise<SiteAdminComment[]> {
    const siteAdminComments: SiteAdminComment[] = [];

    const admin = siteAdmin.find(user => user.email === config.get('email.adminEmail'));

    for (const comment of comments) {
        const siteAdminComment = await factory(SiteAdminComment)()
            .map(async (generatedComment: SiteAdminComment) => {
                generatedComment.channelId = String(policy.id);
                generatedComment.threadId = comment.threadId;
                generatedComment.commentId = comment.commentId;
                generatedComment.content = comment.content;
                generatedComment.attributes = comment.attributes;
                generatedComment.type = comment.type;
                if (admin !== undefined) {
                    generatedComment.siteAdmin = admin;
                }
                return generatedComment;
            })
            .create();
        siteAdminComments.push(siteAdminComment);
    }

    return siteAdminComments;
}

export async function createManyPolicySLATemplates(
    factory: Factory,
    policyTemplate: PolicyTemplate,
    slaTemplates: SLATemplate[],
): Promise<PolicySLATemplate[]> {
    // init
    const policySLATemplates: PolicySLATemplate[] = [];
    // iterate over the set
    for (const slaTemplate of slaTemplates) {
        const policySLATemplate = await factory(PolicySLATemplate)()
            .map(async (generatedPolicySLATemplate: PolicySLATemplate) => {
                // transfer the data
                generatedPolicySLATemplate.label = slaTemplate.label;
                // transfer the data
                generatedPolicySLATemplate.type = slaTemplate.type;
                // associate the entities
                generatedPolicySLATemplate.policyTemplate = policyTemplate;
                // ready to return
                return generatedPolicySLATemplate;
            })
            .create();
        // add to the list
        policySLATemplates.push(policySLATemplate);
    }
    // ready to return
    return policySLATemplates;
}

export async function createManyGenericSiteAdmins(
    factory: Factory,
    amount: number,
    adminOwners: AdminOwner[] = [],
): Promise<SiteAdmin[]> {
    // initialize
    const siteAdmins: SiteAdmin[] = [];
    // spin over the amount
    for (let i = 0; i < amount; i++) {
        let adminOwner: AdminOwner | null = null;
        // check for owner info
        if (adminOwners?.length && !isEmpty(adminOwners[i])) {
            adminOwner = adminOwners[i];
        }
        // create a single one
        const siteAdmin: SiteAdmin = await createSiteAdmin(factory, adminOwner);
        // add it to the set
        siteAdmins.push(siteAdmin);
    }
    // ready to return
    return siteAdmins;
}

export async function createEntriesFromAccountRequests(
    factory: Factory,
    generatedAccount: Account,
    accountRequests: AccountRequest[],
): Promise<Entry[]> {
    // init
    const entries: Entry[] = [];
    // spin
    for (const accountRequest of accountRequests) {
        // delegate to dedicated function
        const entry = await createEntryFromAccountRequest(
            factory,
            generatedAccount,
            accountRequest,
        );
        // add it to the set
        entries.push(entry);
    }
    // ready to return
    return entries;
}

export async function createEntryFromAccountRequest(
    factory: Factory,
    generatedAccount: Account,
    accountRequest: AccountRequest,
): Promise<Entry> {
    // generate an entry
    const entry = await factory(Entry)()
        .map(async (generatedEntry: Entry) => {
            const employeeId = get(accountRequest, 'employeeId', faker.datatype.number());
            // resolve the email
            generatedEntry.email = !isNil(accountRequest.email)
                ? accountRequest.email
                : `testuser${employeeId}@${generatedAccount.domain}`;
            // set the account
            generatedEntry.accounts = [generatedAccount];
            // ready to return
            return generatedEntry;
        })
        .create();

    // generate a magic link token for it
    await factory(Token)()
        .map(async (generatedToken: Token) => {
            // set the owning entry
            generatedToken.entry = entry;
            // ready to return
            return generatedToken;
        })
        .create();
    // ready to return
    return entry;
}

export async function createSafeBaseSettingsForAccount(
    factory: Factory,
    account: Account,
): Promise<AccountSafeBaseSettings> {
    // generate safeBaseSettings for the account
    return factory(AccountSafeBaseSettings)()
        .map(async (generatedSettings: AccountSafeBaseSettings) => {
            generatedSettings.accountId = account.id;
            generatedSettings.migrationStatus = SafeBaseMigrationStatus.NOT_STARTED;
            generatedSettings.showDrataTrustCenter = true;
            generatedSettings.showSsoLinks = false;
            // ready to return
            return generatedSettings;
        })
        .create();
}

export async function createControlTestTemplate(
    factory: Factory,
    controlTest: ControlTestTemplateSeedOptions,
): Promise<ControlTestTemplate> {
    const controlTestTemplate = await factory(ControlTestTemplate)()
        .map(async (generatedControlTestTemplate: ControlTestTemplate) => {
            generatedControlTestTemplate.name = controlTest.name;
            generatedControlTestTemplate.description = controlTest.description;
            generatedControlTestTemplate.testId = controlTest.testId;
            generatedControlTestTemplate.priority = Priority[controlTest.priority];

            generatedControlTestTemplate.source = TestSource[controlTest.source];

            generatedControlTestTemplate.ap2EnabledAt = controlTest.ap2Only ? new Date() : null;
            generatedControlTestTemplate.runMode = controlTest.ap2Only ? RunMode.AP2 : RunMode.AP1;

            if (deprecatedControlTestIds.includes(controlTest.testId)) {
                generatedControlTestTemplate.deletedAt = new Date();
            }
            return generatedControlTestTemplate;
        })
        .create();
    return controlTestTemplate;
}

export async function createMonitorTemplate(
    factory: Factory,
    controlTestTemplate: ControlTestTemplate,
    monitor: MonitorTemplateSeedOptions | null = null,
): Promise<MonitorTemplate> {
    const monitorTemplate = await factory(MonitorTemplate)()
        .map(async (generatedMonitorTemplate: MonitorTemplate) => {
            // check if we should override values
            if (!isNil(monitor)) {
                generatedMonitorTemplate.name = monitor.name;
                generatedMonitorTemplate.checkFrequency = CheckFrequency[monitor.checkFrequency];
                generatedMonitorTemplate.autopilotTaskType =
                    AutopilotTaskType[monitor.autopilotTaskType];
                generatedMonitorTemplate.failedTestDescription = monitor.failedTestDescription;
                generatedMonitorTemplate.evidenceCollectionDescription =
                    monitor.evidenceCollectionDescription;
                generatedMonitorTemplate.remedyDescription = monitor.remedyDescription;
                generatedMonitorTemplate.url = !isNil(monitor.url) ? monitor.url : null;
                const requestDescriptions = processRequestDescriptions(monitor.requestDescriptions);
                generatedMonitorTemplate.setRequestDescriptions(requestDescriptions);
            }
            generatedMonitorTemplate.controlTestTemplate = controlTestTemplate;
            return generatedMonitorTemplate;
        })
        .create();
    /**
     * Sanity check before referencing and make sure there are types
     */
    if (!isNil(monitor) && !isNil(monitor.types) && monitor.types.length > 0) {
        // create associated monitor template check types
        const monitorTemplateCheckTypes = await createMonitorTemplateCheckTypes(
            factory,
            monitorTemplate,
            monitor.types,
        );
        // assign
        monitorTemplate.monitorTemplateCheckTypes = monitorTemplateCheckTypes;
    }
    // ready to return
    return monitorTemplate;
}

export async function findOrCreateTenantDatabaseHost(
    factory: Factory,
    connection: DrataDataSource,
): Promise<TenantDatabaseHost> {
    const repository = connection.getRepository(TenantDatabaseHost);
    const query = repository.createQueryBuilder('DbHost');
    const hostCount = await query.getCount();

    if (hostCount == 0) {
        return factory(TenantDatabaseHost)()
            .map(async generatedHost => {
                generatedHost.dnsName = 'localhost';
                generatedHost.port = 3306;
                generatedHost.friendlyName = 'localhost';
                return generatedHost;
            })
            .create();
    } else {
        return query.getOne();
    }
}

export function processRequestDescriptions(
    requestDescriptions:
        | ConnectionRequestDescriptionType[]
        | SingleRequestDescriptionType[]
        | AgentRequestDescriptionType[],
): ConnectionRequestDescriptionType[] | SingleRequestDescriptionType[] {
    const rds: Array<
        | ConnectionRequestDescriptionType
        | AgentRequestDescriptionType
        | SingleRequestDescriptionType
    > = [];

    for (const requestDescription of requestDescriptions) {
        if (
            requestDescription.requestDescriptionType === RequestDescriptionType.CONNECTION ||
            requestDescription.requestDescriptionType === RequestDescriptionType.AGENT
        ) {
            // type guard
            if ('clientType' in requestDescription) {
                const rd = cloneDeep(requestDescription);
                const clientType = rd.clientType as string;
                rd.clientType = ClientType[clientType];
                rds.push(rd);
            } else {
                rds.push(requestDescription);
            }
        } else {
            rds.push(requestDescription);
        }
    }
    return rds;
}

export async function createMonitorTemplateCheckTypes(
    factory: Factory,
    monitorTemplate: MonitorTemplate,
    types: string[],
): Promise<MonitorTemplateCheckType[]> {
    // init
    const monitorTemplateCheckTypes: MonitorTemplateCheckType[] = [];
    // spin over the set of types
    for (const type of types) {
        // invoke single creator
        const monitorTemplateCheckType = await createMonitorTemplateCheckType(
            factory,
            monitorTemplate,
            type,
        );
        // add to the set
        monitorTemplateCheckTypes.push(monitorTemplateCheckType);
    }
    // ready to return
    return monitorTemplateCheckTypes;
}

export async function createTrustCenterMonitoringControlTemplate(
    factory: Factory,
    monitoringControlFixture: any,
): Promise<TrustCenterMonitoringControlTemplate> {
    return factory(TrustCenterMonitoringControlTemplate)()
        .map(async (generatedTrustCenterMonitor: TrustCenterMonitoringControlTemplate) => {
            generatedTrustCenterMonitor.name = monitoringControlFixture.name;
            generatedTrustCenterMonitor.controlCode = monitoringControlFixture.controlCode;
            generatedTrustCenterMonitor.controlsCategory = monitoringControlFixture.category;
            return generatedTrustCenterMonitor;
        })
        .create();
}

export async function createTrustCenterMonitoringControlTemplates(
    factory: Factory,
    monitoringControlFixture: any,
): Promise<TrustCenterMonitoringControlTemplate[]> {
    const trustCenterMonitoringControls: Array<TrustCenterMonitoringControlTemplate> = [];
    for (const trustPagesControlTemplateFixture of monitoringControlFixture.trustCenterControlTemplates) {
        const trustCenterMonitoringControl = await createTrustCenterMonitoringControlTemplate(
            factory,
            trustPagesControlTemplateFixture,
        );

        trustCenterMonitoringControls.push(trustCenterMonitoringControl);
    }

    return trustCenterMonitoringControls;
}

export async function createMonitorTemplateCheckType(
    factory: Factory,
    monitorTemplate: MonitorTemplate,
    type: string,
): Promise<MonitorTemplateCheckType> {
    const monitorTemplateCheckType = await factory(MonitorTemplateCheckType)()
        .map(async (generatedMonitorTemplateCheckType: MonitorTemplateCheckType) => {
            // assign the enum value derived from the string
            generatedMonitorTemplateCheckType.checkType = CheckType[type];
            // set the mapping monitor template
            generatedMonitorTemplateCheckType.monitorTemplate = monitorTemplate;
            return generatedMonitorTemplateCheckType;
        })
        .create();
    return monitorTemplateCheckType;
}

export async function createFrameworkTemplates(
    factory: Factory,
    frameworkTemplateFixtures: any,
): Promise<FrameworkTemplate[]> {
    const frameworkTemplates: FrameworkTemplate[] = [];
    for (const frameworkTemplateFixture of frameworkTemplateFixtures.frameworkTemplates) {
        const frameworkTemplate = await createFrameworkTemplate(factory, frameworkTemplateFixture);
        frameworkTemplates.push(frameworkTemplate);
    }
    return frameworkTemplates;
}

export async function createFrameworkTemplate(
    factory: Factory,
    frameworkTemplateFixture: any,
): Promise<FrameworkTemplate> {
    return factory(FrameworkTemplate)()
        .map(async (generatedFrameworkTemplate: FrameworkTemplate) => {
            generatedFrameworkTemplate.name = frameworkTemplateFixture.name;
            generatedFrameworkTemplate.description = frameworkTemplateFixture.description;
            generatedFrameworkTemplate.longDescription = frameworkTemplateFixture.longDescription;
            generatedFrameworkTemplate.slug = frameworkTemplateFixture.slug;
            generatedFrameworkTemplate.color = frameworkTemplateFixture.color;
            generatedFrameworkTemplate.bgColor = frameworkTemplateFixture.bgColor;
            generatedFrameworkTemplate.activeLogo = frameworkTemplateFixture.activeLogo;
            generatedFrameworkTemplate.inactiveLogo = frameworkTemplateFixture.inactiveLogo;
            generatedFrameworkTemplate.tag =
                FrameworkTag[FrameworkTag[frameworkTemplateFixture.tag]];
            generatedFrameworkTemplate.pill = frameworkTemplateFixture.pill;
            generatedFrameworkTemplate.levelLabel = frameworkTemplateFixture.levelLabel;
            generatedFrameworkTemplate.hasLevel = frameworkTemplateFixture.hasLevel;
            generatedFrameworkTemplate.privacy = frameworkTemplateFixture.privacy;

            if (!isNil(frameworkTemplateFixture.catalog)) {
                const catalogJson = readJSONFile<CatalogJSON>(
                    pathResolve(config.get(frameworkTemplateFixture.catalog)),
                );
                generatedFrameworkTemplate.externalId = catalogJson?.catalog.uuid ?? null;
            }

            if (!isNil(frameworkTemplateFixture.hasDynamicControlMapping)) {
                generatedFrameworkTemplate.hasDynamicControlMapping =
                    frameworkTemplateFixture.hasDynamicControlMapping;
            }

            return generatedFrameworkTemplate;
        })
        .create();
}

export async function createAuditorFrameworkTypeTemplates(
    factory: Factory,
    auditorFrameworkTemplateFixture: any,
    frameworkTemplates: FrameworkTemplate[],
): Promise<AuditorFrameworkTypeTemplate[]> {
    const auditorFrameworkTypeTemplates: Array<AuditorFrameworkTypeTemplate> = [];
    for (const auditorFrameworkTypeTemplateFixture of auditorFrameworkTemplateFixture.auditorFrameworkTypeTemplates) {
        const auditorFrameworkTypeTemplate = await createAuditorFrameworkTypeTemplate(
            factory,
            auditorFrameworkTypeTemplateFixture,
            frameworkTemplates,
        );
        auditorFrameworkTypeTemplates.push(auditorFrameworkTypeTemplate);
    }
    return auditorFrameworkTypeTemplates;
}

export async function createAuditorFrameworkTypeTemplate(
    factory: Factory,
    auditorFrameworkTemplateFixture: any,
    frameworkTemplates: FrameworkTemplate[],
): Promise<AuditorFrameworkTypeTemplate> {
    return factory(AuditorFrameworkTypeTemplate)()
        .map(async (generatedAuditorFrameworkTemplate: AuditorFrameworkTypeTemplate) => {
            generatedAuditorFrameworkTemplate.label = auditorFrameworkTemplateFixture.label;
            generatedAuditorFrameworkTemplate.type = AuditorFrameworkTypeEnum[
                auditorFrameworkTemplateFixture.type
            ] as unknown as AuditorFrameworkTypeEnum;
            generatedAuditorFrameworkTemplate.dateType = AuditorFrameworkDateType[
                auditorFrameworkTemplateFixture.dateType
            ] as unknown as AuditorFrameworkDateType;
            const relatedFramework = frameworkTemplates.find(
                framework =>
                    FrameworkTag[framework.tag] ===
                    auditorFrameworkTemplateFixture.relatedFrameworkTag,
            );

            if (relatedFramework) {
                generatedAuditorFrameworkTemplate.relatedFramework = relatedFramework;
            }
            return generatedAuditorFrameworkTemplate;
        })
        .create();
}

export async function createControlTemplates(
    factory: Factory,
    controlTemplateFixtures: DrataControlFixtures,
    controlTestTemplates: ControlTestTemplate[],
    policyToControlTemplateFixtures: any,
    policyTemplates: PolicyTemplate[],
    evidenceToControlTemplateFixtures: any,
    evidenceTemplates: EvidenceTemplate[],
): Promise<ControlTemplate[]> {
    const controlTemplates: ControlTemplate[] = [];
    for (const controlTemplateFixture of controlTemplateFixtures.controls) {
        const mappedControlTestTemplates: ControlTestTemplate[] = [];
        if (!isNil(controlTemplateFixture.tests)) {
            for (const testId of controlTemplateFixture.tests) {
                const controlTestTemplate = controlTestTemplates.find(
                    controlTest => controlTest.testId === testId.id,
                );
                if (isNil(controlTestTemplate)) {
                    continue;
                }
                mappedControlTestTemplates.push(controlTestTemplate);
            }
        }
        const mappedPolicyTemplates: PolicyTemplate[] = [];
        if (!isNil(policyToControlTemplateFixtures)) {
            for (const policy of policyToControlTemplateFixtures.policies) {
                for (const control of policy.controls) {
                    if (control.code === controlTemplateFixture.code) {
                        const policyTemplate = policyTemplates.find(
                            policyTemp => policyTemp.name === policy.name,
                        );
                        if (isNil(policyTemplate)) {
                            continue;
                        }
                        mappedPolicyTemplates.push(policyTemplate);
                    }
                }
            }
        }
        const mappedEvidenceTemplates: EvidenceTemplate[] = [];
        if (!isNil(evidenceToControlTemplateFixtures)) {
            for (const evidence of evidenceToControlTemplateFixtures.evidenceTemplates) {
                if (!isNil(evidence.controls)) {
                    for (const control of evidence.controls) {
                        if (control.code === controlTemplateFixture.code) {
                            const evidenceTemplate = evidenceTemplates.find(
                                evTemplate =>
                                    evTemplate.evidenceTemplateCode ===
                                    evidence.evidenceTemplateCode,
                            );
                            if (isNil(evidenceTemplate)) {
                                continue;
                            }
                            mappedEvidenceTemplates.push(evidenceTemplate);
                        }
                    }
                }
            }
        }
        const controlTemplate = await createControlTemplate(
            factory,
            controlTemplateFixture,
            mappedControlTestTemplates,
            mappedPolicyTemplates,
            mappedEvidenceTemplates,
        );
        controlTemplates.push(controlTemplate);
    }
    return controlTemplates;
}

export async function createControlTemplate(
    factory: Factory,
    controlTemplateFixture: DrataControlFixture,
    mappedControlTestTemplates: ControlTestTemplate[],
    mappedPolicyTemplates: PolicyTemplate[],
    mappedEvidenceTemplates: EvidenceTemplate[],
): Promise<ControlTemplate> {
    return factory(ControlTemplate)()
        .map(async (generatedControlTemplate: ControlTemplate) => {
            generatedControlTemplate.controlNumber = extractNumericCode(
                controlTemplateFixture.code,
            );
            generatedControlTemplate.code = controlTemplateFixture.code;
            generatedControlTemplate.name = controlTemplateFixture.name;
            generatedControlTemplate.domain = resolveControlTemplateDomain(
                controlTemplateFixture.domain,
            );
            generatedControlTemplate.category = resolveControlTemplateCategory(
                controlTemplateFixture.category,
            );
            generatedControlTemplate.description = controlTemplateFixture.description;
            const trimmedActivity = controlTemplateFixture.activity.trim();
            generatedControlTemplate.activity = trimmedActivity === '' ? null : trimmedActivity;
            const trimmedQuestion = controlTemplateFixture.question.trim();
            generatedControlTemplate.question = trimmedQuestion === '' ? null : trimmedQuestion;
            generatedControlTemplate.controlTestTemplates = mappedControlTestTemplates;
            generatedControlTemplate.policyTemplates = mappedPolicyTemplates;
            generatedControlTemplate.evidenceTemplates = mappedEvidenceTemplates;
            return generatedControlTemplate;
        })
        .create();
}

export async function createControlTestTemplates(
    factory: Factory,
    controlTestTemplateFixtures: any,
): Promise<ControlTestTemplate[]> {
    const controlTestTemplates: ControlTestTemplate[] = [];
    for (const controlTestTemplateFixture of controlTestTemplateFixtures.controlTestTemplates) {
        const item = {
            name: controlTestTemplateFixture.name,
            description: controlTestTemplateFixture.description,
            testId: controlTestTemplateFixture.testId,
            priority: controlTestTemplateFixture.priority,
            ap2Only: controlTestTemplateFixture.ap2Only,
            source: isNil(controlTestTemplateFixture.source)
                ? TestSource[TestSource.DRATA]
                : controlTestTemplateFixture.source,
        };
        const controlTestTemplate = await createControlTestTemplate(factory, item);
        if (!isNil(controlTestTemplateFixture.monitorTemplates)) {
            const monitorTemplates = await createMonitorTemplates(
                factory,
                controlTestTemplate,
                controlTestTemplateFixture.monitorTemplates,
            );
            controlTestTemplate.monitorTemplates = monitorTemplates;
        }
        controlTestTemplates.push(controlTestTemplate);
    }
    return controlTestTemplates;
}

export async function createMonitorTemplates(
    factory: Factory,
    controlTestTemplate: ControlTestTemplate,
    monitorTemplateFixtures: any,
): Promise<MonitorTemplate[]> {
    const monitorTemplates: MonitorTemplate[] = [];
    for (const monitorTemplateFixture of monitorTemplateFixtures) {
        const monitorTemplate = await createMonitorTemplate(
            factory,
            controlTestTemplate,
            monitorTemplateFixture,
        );
        monitorTemplates.push(monitorTemplate);
    }
    return monitorTemplates;
}

export async function createRequirementTemplates(
    factory: Factory,
    requirementFixtures: any[],
    controlTemplates: ControlTemplate[],
    connection: DrataDataSource,
): Promise<[Map<string, RequirementTemplate>, Map<string, RequirementIndexTagTemplate>]> {
    const requirementTemplates = new Map<string, RequirementTemplate>();
    const createdRequirementIndexTagTemplates = new Map<string, RequirementIndexTagTemplate>();
    const requirementIndexTagTemplateRepository = connection.getRepository(
        RequirementIndexTagTemplate,
    );
    let existingRequirementIndexTagTemplates: RequirementIndexTagTemplate[] = [];
    for (const fixture of requirementFixtures) {
        for (const requirementFixture of fixture.requirements) {
            const mappedControlTemplates: ControlTemplate[] = [];
            if (!isNil(requirementFixture.controls)) {
                for (const controlCode of requirementFixture.controls) {
                    const controlTemplate = controlTemplates.find(
                        ct => ct.code === controlCode.code,
                    );
                    if (!isNil(controlTemplate)) {
                        mappedControlTemplates.push(controlTemplate);
                    }
                }
            }
            if (!isEmpty(requirementFixture.mappedControlCodes)) {
                const requirementMappedControlTemplates = controlTemplates.filter(controlTemplate =>
                    requirementFixture.mappedControlCodes.some(
                        controlCode => controlCode === controlTemplate.code,
                    ),
                );
                if (!isEmpty(requirementMappedControlTemplates)) {
                    mappedControlTemplates.push(...requirementMappedControlTemplates);
                }
            }
            const [requirementTemplate, requirementIndexTagTemplates] =
                await createRequirementTemplate(
                    factory,
                    requirementFixture,
                    mappedControlTemplates,
                    requirementIndexTagTemplateRepository,
                    existingRequirementIndexTagTemplates,
                );

            // we want the frameworkSlug|name format to prevent duplicate requirement names
            const requirementNameKey = requirementFixture.name || requirementTemplate.externalId;
            requirementTemplates[requirementNameKey] = requirementTemplate;
            createdRequirementIndexTagTemplates[requirementNameKey] = requirementIndexTagTemplates;
            // concat without duplicates
            existingRequirementIndexTagTemplates = [
                ...new Set([
                    ...existingRequirementIndexTagTemplates,
                    ...requirementIndexTagTemplates,
                ]),
            ];
        }
    }

    return [requirementTemplates, createdRequirementIndexTagTemplates];
}

export async function createRequirementTemplate(
    factory: Factory,
    requirementFixture: any,
    mappedControlTemplates: ControlTemplate[],
    requirementIndexTagTemplateRepository: Repository<RequirementIndexTagTemplate>,
    existingTags: RequirementIndexTagTemplate[],
): Promise<[RequirementTemplate, RequirementIndexTagTemplate[]]> {
    const requirementTemplate = await factory(RequirementTemplate)()
        .map(async (generatedRequirementTemplate: RequirementTemplate) => {
            generatedRequirementTemplate.name = getRequirementNameFromFixture(requirementFixture);
            generatedRequirementTemplate.description = requirementFixture.description;
            generatedRequirementTemplate.longDescription = !isEmpty(
                requirementFixture.longDescription,
            )
                ? requirementFixture.longDescription
                : null;
            generatedRequirementTemplate.additionalInfo = !isEmpty(
                requirementFixture.additionalInfo,
            )
                ? requirementFixture.additionalInfo
                : null;
            generatedRequirementTemplate.additionalInfo2 = !isEmpty(
                requirementFixture.additionalInfo2,
            )
                ? requirementFixture.additionalInfo2
                : null;
            generatedRequirementTemplate.additionalInfo3 = !isEmpty(
                requirementFixture.additionalInfo3,
            )
                ? requirementFixture.additionalInfo3
                : null;
            generatedRequirementTemplate.controlTemplates = mappedControlTemplates;
            generatedRequirementTemplate.sortOrder = parseInt(requirementFixture.sortOrder);

            if (!isNil(requirementFixture.parts)) {
                generatedRequirementTemplate.parts = requirementFixture.parts;
            }

            if (!isNil(requirementFixture.params)) {
                generatedRequirementTemplate.params = requirementFixture.params;
            }

            if (!isNil(requirementFixture.externalId)) {
                generatedRequirementTemplate.externalId = requirementFixture.externalId;
            }
            return generatedRequirementTemplate;
        })
        .create();
    const tags: Array<RequirementIndexTagTemplate> = [];
    if (!isNil(requirementFixture.levels)) {
        for (const tag of requirementFixture.levels) {
            const foundTag: RequirementIndexTagTemplate | undefined = existingTags.find(
                t => t.tag === tag,
            );
            if (isNil(foundTag)) {
                const newTag: RequirementIndexTagTemplate = new RequirementIndexTagTemplate();
                newTag.id = getTagEnumValue(tag);
                newTag.tag = tag;
                newTag.requirementIndexTemplates = [];
                tags.push(newTag);
            } else {
                tags.push(foundTag);
            }
        }
        await requirementIndexTagTemplateRepository.save(tags);
    }
    return [requirementTemplate, tags];
}

function getTagEnumValue(input: string): number | undefined {
    const normalizedInput = input.toUpperCase().replace(/\s+/g, '_');

    for (const [key, value] of Object.entries(RequirementIndexTag)) {
        if (typeof value === 'number' && key === normalizedInput) {
            return value;
        }
    }
    return undefined;
}

export async function createRequirementIndexTemplates(
    factory: Factory,
    frameworkRequirementIndexTemplatesFixtures: any[],
    frameworkTemplate: FrameworkTemplate,
    requirementTemplates: Map<string, RequirementTemplate>,
    requirementIndexTagTemplates: Map<string, RequirementIndexTagTemplate[]>,
    connection: DrataDataSource,
): Promise<RequirementIndexTemplate[]> {
    const requirementIndexTemplates: RequirementIndexTemplate[] = [];
    const requirementIndexTagTemplateRepository = connection.getRepository(
        RequirementIndexTagTemplate,
    );

    for (const fixtures of frameworkRequirementIndexTemplatesFixtures) {
        for (const requirementIndexFixture of fixtures.requirementIndexFixtures[0]
            .requirementIndexes) {
            if (isNil(requirementIndexFixture.requirements)) {
                continue;
            }
            const topic =
                TrustServiceCriteria[
                    requirementIndexFixture.topic as keyof typeof TrustServiceCriteria
                ];
            const category =
                RequirementIndexCategory[
                    requirementIndexFixture.name as keyof typeof RequirementIndexCategory
                ];
            const subCategory =
                RequirementIndexSubcategory[
                    requirementIndexFixture.subCategory as keyof typeof RequirementIndexSubcategory
                ];
            for (const requirementFixture of requirementIndexFixture.requirements) {
                const requirementTemplate =
                    requirementTemplates[requirementFixture.name] ||
                    requirementTemplates[getRequirementNameFromFixture(requirementFixture)];
                if (isUndefined(requirementTemplate)) {
                    continue;
                }

                const requirementIndexTemplate = await createRequirementIndexTemplate(
                    factory,
                    frameworkTemplate,
                    topic,
                    category,
                    subCategory,
                    requirementTemplate,
                );
                const mappedTags = requirementIndexTagTemplates[requirementFixture.name] ?? [];
                for (const tag of mappedTags) {
                    tag.requirementIndexTemplates.push(requirementIndexTemplate);
                }
                await requirementIndexTagTemplateRepository.save(mappedTags);
                requirementIndexTemplate.requirementIndexTagTemplates = mappedTags;
                requirementIndexTemplates.push(requirementIndexTemplate);
            }
        }
    }

    return requirementIndexTemplates;
}

export async function createRequirementIndexTemplate(
    factory: Factory,
    frameworkTemplate: FrameworkTemplate,
    topic: TrustServiceCriteria,
    category: RequirementIndexCategory,
    subCategory: RequirementIndexSubcategory,
    requirementTemplate: RequirementTemplate,
): Promise<RequirementIndexTemplate> {
    return factory(RequirementIndexTemplate)()
        .map(async (generatedRequirementIndexTemplate: RequirementIndexTemplate) => {
            generatedRequirementIndexTemplate.frameworkTemplate = frameworkTemplate;
            generatedRequirementIndexTemplate.topic = topic;
            generatedRequirementIndexTemplate.category = category;
            generatedRequirementIndexTemplate.subCategory = subCategory;
            generatedRequirementIndexTemplate.requirementTemplate = requirementTemplate;
            return generatedRequirementIndexTemplate;
        })
        .create();
}

export function createAccountRequests(domain?: string): AccountRequest[] {
    // init the set
    const accountRequests: AccountRequest[] = [];
    // get the leader
    const leader = getPaveFirstUser();
    if (!isNil(domain)) {
        leader.email = `${leader?.email?.substring(0, leader?.email.indexOf('@'))}@${domain}`;
    }
    // add to set
    accountRequests.push(leader);
    // get the rest of the set
    const squad = getPaveUsers();
    // spin
    for (const member of squad) {
        // add to set
        if (!isNil(domain)) {
            member.email = `${member?.email?.substring(0, member.email.indexOf('@'))}@${domain}`;
        }
        accountRequests.push(member);
    }

    // ready to return
    return accountRequests;
}

export function createDummyAccountRequests(
    dummyAccountRequests: number,
    domain = 'drata.com',
): AccountRequest[] {
    const accountRequests: AccountRequest[] = [];
    for (let i = 0; i < dummyAccountRequests; i++) {
        const accountRequest = {
            firstName: faker.name.firstName(),
            lastName: faker.name.lastName(),
            email: `testuser${i}@${domain}`,
            roles: [Role.EMPLOYEE],
            avatar: null,
            companyName: 'Drata',
            jobTitle: faker.name.jobTitle(),
            employeeId: i,
        };
        accountRequests.push(accountRequest);
    }
    return accountRequests;
}

export function getPaveFirstUser(domain = 'drata.com'): AccountRequest {
    return {
        firstName: 'Adam',
        lastName: 'Markowitz',
        email: `adam@${domain}`,
        roles: [Role.EMPLOYEE, Role.ADMIN],
        avatar: null,
        companyName: 'Drata',
        jobTitle: 'CEO',
    };
}

export function getPaveUsers(domain = 'drata.com'): AccountRequest[] {
    const adminEmployeesEngs = [
        [
            'Drata',
            'Support',
            '<EMAIL>',
            'https://cdn.drata.com/icon/icon_fwhite_bblue_72-circle.png',
            'Drata',
            null,
        ],
        ['Daniel', 'Marashlian', `danielzev@${domain}`, null, 'Drata', 'CTO'],
        ['David', 'Knell', `dave@${domain}`, null, 'Drata', 'VP Engineering'],
        [
            'Michael',
            'Risoli',
            `michaelrisoli@${domain}`,
            null,
            'Drata',
            'Manager Solution Architecture',
        ],
        [
            'Serge',
            'Zhivotovsky',
            `sergezhivotovsky@${domain}`,
            null,
            'Drata',
            'Senior Sales Engineer',
        ],
        [
            'Manuel',
            'Lizarraga',
            `manuellizarrag@${domain}`,
            null,
            'Drata',
            'Senior Software Engineer',
        ],
        ['Hector', 'Zapata', `hectorzapata@${domain}`, null, 'Drata', 'Software Engineer'],
        ['Omar', 'Ziranhua', `omar@${domain}`, null, 'Drata', 'Senior Software Engineer'],
        ['James', 'Backert', `james@${domain}`, null, 'Drata', 'Senior Software Engineer'],
        ['Tyler', 'Henderson', `tyler@${domain}`, null, 'Drata', 'Senior Software Engineer'],
        ['John', 'Eisberg', `john@${domain}`, null, 'Drata', 'VP Architecture'],
        ['Luis', 'Sanchez', `luis@${domain}`, null, 'Drata', 'Director of QA'],
        [
            'Mark',
            'Davenport',
            `markdavenport@${domain}`,
            null,
            'Drata',
            'Director of Program Management',
        ],
        ['Vitus', 'Pelsey', `vituspelsey@${domain}`, null, 'Drata', 'Senior Content Designer'],
        ['John', 'Sant', `johnsant@${domain}`, null, 'Drata', 'Senior Product Designer'],
        ['Marcela', 'Espinosa', `marcelaespinosa@${domain}`, null, 'Drata', 'Sr UI/UX Designer'],
        ['Nico', 'Sanchez', `nico@${domain}`, null, 'Drata', 'International Consultant'],
        ['Lior', 'Solomon', `lior@${domain}`, null, 'Drata', 'VP, Data'],
        ['Rafael', 'Alvarado', `rafael@${domain}`, null, 'Drata', 'Senior Product Designer'],
        ['Harrison', 'Krat', `harrison@${domain}`, null, 'Drata', 'Solutions Architect'],
        ['Art', 'Ortega', `art@${domain}`, null, 'Drata', 'Associate Software Engineer'],
        ['Josef', 'Armenta', `josef@${domain}`, null, 'Drata', 'QA Engineer'],
        ['Manuel', 'Marquez', `manuel@${domain}`, null, 'Drata', 'Product Owner'],
        ['Jose Luis', 'Toledo', `joseluistoledo@${domain}`, null, 'Drata', 'Product Owner'],
        ['Javier', 'Garcia', `javiergarcia@${domain}`, null, 'Drata', 'Product Owner'],
        ['Fernando', 'De la cruz', `fernando@${domain}`, null, 'Drata', 'Senior Software Engineer'],
        ['Juan', 'Lopez', `juan@${domain}`, null, 'Drata', 'Senior Software Engineer'],
        ['Josue', 'Gonzalez', `josue@${domain}`, null, 'Drata', 'Senior Software Engineer'],
        ['Mauricio', 'Romo', `mauricio@${domain}`, null, 'Drata', 'Senior Software Engineer'],
        ['Dana', 'Mauger', `dana@${domain}`, null, 'Drata', 'Product Manager'],
        ['Houman', 'Haghighi', `houman@${domain}`, null, 'Drata', 'Board Member'],
        ['Ashley', 'Hyman', `ashley@${domain}`, null, 'Drata', 'Director of Customer Success'],
        ['Lincoln', 'Race', `link@${domain}`, null, 'Drata', 'Senior Software Engineer'],
        ['Daniel', 'Sosa', `danielsosa@${domain}`, null, 'Drata', 'Senior Software Engineer'],
        ['David', 'Guillen', `davidguillen@${domain}`, null, 'Drata', 'Senior Software Engineer'],
        ['Fernando', 'Rivera', `fernandorivera@${domain}`, null, 'Drata', 'Software Engineer'],
        ['Antonio', 'Hernandez', `antonio@${domain}`, null, 'Drata', 'Software Engineer'],
        ['Jason', 'Hatchett', `jason@${domain}`, null, 'Drata', 'Technical Product Manager'],
        ['Ari', 'Mojiri', `ari@${domain}`, null, 'Drata', 'Senior Program Manager'],
        ['Juan', 'Ibarra', `juanibarra@${domain}`, null, 'Drata', 'Software Engineer'],
        [
            'Emmanuel',
            'Rodriguez',
            `emmanuelrodriguez@${domain}`,
            null,
            'Drata',
            'Software Engineer',
        ],
        ['Esteban', 'Ovalle', `esteban@${domain}`, null, 'Drata', 'QA Engineer'],
        ['Tony', 'Gonzalez', `antoniogonzalez@${domain}`, null, 'Drata', 'Software Engineer'],
        ['Jose', 'Orosco', `jose@${domain}`, null, 'Drata', 'Software Engineer'],
        ['James', 'Perkins', `jamesperkins@${domain}`, null, 'Drata', 'Senior Solutions Engineer'],
        ['Denver', 'Peterson', `denver@${domain}`, null, 'Drata', 'QA Manager'],
        ['Brian', 'Elmi', `brianelmi@${domain}`, null, 'Drata', 'Head of Product'],
        ['Faraz', 'Yaghooti', `faraz@${domain}`, null, 'Drata', 'Director, Product Management'],
        [
            'Jhonnatan',
            'Guerrero',
            `jhonnatanguerrero@${domain}`,
            null,
            'Drata',
            'Senior Software Engineer',
        ],
        ['Aaron', 'Vega', `aaronvega@${domain}`, null, 'Drata', 'Senior Software Engineer'],
        ['Alec', 'Barba', `alecbarba@${domain}`, null, 'Drata', 'Software Engineer'],
        ['Ariel', 'Montoya', `arielmontoya@${domain}`, null, 'Drata', 'QA Engineer'],
        ['Guerrero', 'Campos', `guerrero@${domain}`, null, 'Drata', 'Associate QA Engineer'],
        ['Ivan', 'Cabrera', `ivancabrera@${domain}`, null, 'Drata', 'Software Engineer'],
        ['Eric', 'Thai', `eric@${domain}`, null, 'Drata', 'Senior Product Manager'],
        ['Jose', 'Zermeno', `josezermeno@${domain}`, null, 'Drata', 'Software Engineer'],
        ['Carlos', 'Gomez', `carlosgomez@${domain}`, null, 'Drata', 'Software Engineer in Test'],
        ['Aleida', 'Ramos', `aleidaramos@${domain}`, null, 'Drata', 'QA Engineer'],
        ['Chris', 'Milliano', `chrismilliano@${domain}`, null, 'Drata', 'Senior Software Engineer'],
        ['Osmar', 'Delgado', `osmar@${domain}`, null, 'Drata', 'Software Engineer'],
        ['Liam', 'Hession', `liam@${domain}`, null, 'Drata', 'Technical Support Representative'],
        ['Manny', 'Cocoba', `manny@${domain}`, null, 'Drata', 'Senior Software Engineer'],
        [
            'Lillian',
            'McGillivray',
            `lillianmcgillivray@${domain}`,
            null,
            'Drata',
            'Engineering Intern',
        ],
        ['Ashley', 'Lappies', `ashleylappies@${domain}`, null, 'Drata', 'Engineering Intern'],
        ['Kelly', 'Evans', `kellyevans@${domain}`, null, 'Drata', 'Engineering Intern'],
        ['Emily', 'Parr', `emily@${domain}`, null, 'Drata', 'Software Engineer'],
        ['Andrew', 'Morton', `andrewmorton@${domain}`, null, 'Drata', 'Staff Software Engineer'],
        ['Randy', 'Solton', `randysolton@${domain}`, null, 'Drata', 'API Systems Architect'],
        ['Kevin', 'Phung', `kevinphung@${domain}`, null, 'Drata', 'Senior QA Engineer'],
        ['Lindsey', 'Morando', `lindsey@${domain}`, null, 'Drata', 'Sr. Product Marketing Manager'],
        ['Federico', 'Lische', `federico@${domain}`, null, 'Drata', 'Software Engineer'],
        ['Ernie', 'Jimenez', `ernie@${domain}`, null, 'Drata', 'Senior Software Engineer in Test'],
        ['Stephen', 'Ward', `stephen@${domain}`, null, 'Drata', 'Solutions Architect'],
        ['Arlo', 'Guthrie', `arlo@${domain}`, null, 'Drata', 'Director of Design'],
        ['Alex', 'Varela', `alexvarela@${domain}`, null, 'Drata', 'Program Manager'],
        ['Edelman', 'Gutierrez', `edelman@${domain}`, null, 'Drata', 'Senior Software Engineer'],
        ['Douglas', 'Mason', `douglasmason@${domain}`, null, 'Drata', 'Staff Software Engineer'],
        ['Shane', 'Hook', `shane@${domain}`, null, 'Drata', 'QA Manager'],
        ['Aaron', 'Waldman', `aaron@${domain}`, null, 'Drata', 'Senior Software Engineer'],
        ['Roy', 'Luo', `royluo@${domain}`, null, 'Drata', 'Engineering Manager'],
        [
            'Slawomir',
            'Zabkiewicz',
            `slawomir@${domain}`,
            null,
            'Drata',
            'VP Engineering, Infrastructure',
        ],
        ['Luciano', 'Almenares', `luciano@${domain}`, null, 'Drata', 'QA Engineer'],
        ['Sangeetha', 'Munuswami', `sangeetha@${domain}`, null, 'Drata', 'Senior QA Engineer'],
        ['Inna', 'Litinsky', `innalitinsky@${domain}`, null, 'Drata', 'Senior QA Engineer'],
        ['Mario', 'Navarro', `marionavarro@${domain}`, null, 'Drata', 'QA Engineer'],
        ['Francisco', 'Bernabe', `franciscobernabe@${domain}`, null, 'Drata', 'Software Engineer'],
        ['Abraham', 'Talavera', `abrahamtalavera@${domain}`, null, 'Drata', 'Software Engineer'],
        ['Hugo', 'Salazar', `hugosalazar@${domain}`, null, 'Drata', 'Software Engineer'],
        ['Graciela', 'Martinez', `gracielamartinez@${domain}`, null, 'Drata', 'QA Engineer'],
        ['Cecilia', 'Cortez', `ceciliacortez@${domain}`, null, 'Drata', 'QA Engineer'],
        ['Pratik', 'Bhat', `pratik@${domain}`, null, 'Drata', 'Product Manager'],
        ['Jacob', 'Hammontree', `jacobhammontree@${domain}`, null, 'Drata', 'Cloud Engineer'],
        ['Irving', 'Calzada', `irvingcalzada@${domain}`, null, 'Drata', 'Jr QA'],
        ['Obed', 'Noriega', `obednoriega@${domain}`, null, 'Drata', 'QA'],
        ['Artur', 'Krzywanski', `artur@${domain}`, null, 'Drata', 'Senior Cloud Engineer'],
        ['Jakub', 'Gola', `jakub@${domain}`, null, 'Drata', 'Senior Cloud Engineer'],
        ['Rodel', 'Lominoque', `rodellominoque@${domain}`, null, 'Drata', 'QA Engineer'],
        ['SereyVathna', 'Saroun', `sereyvathnasaroun@${domain}`, null, 'Drata', 'QA Engineer'],
        [
            'Krzysztof',
            'Skarbinski',
            `krzysztof@${domain}`,
            null,
            'Drata',
            'Senior Software Engineer in Test',
        ],
        [
            'Dominik',
            'Zawrotny',
            `dominikzawrotny@${domain}`,
            null,
            'Drata',
            'Senior Site Reliability Engineer',
        ],
        [
            'Pawel',
            'Chmiel',
            `pawelchmiel@${domain}`,
            null,
            'Drata',
            'Senior Site Reliability Engineer',
        ],
        ['Austin', 'Ruby', `austin@${domain}`, null, 'Drata', 'Senior Software Engineer'],
        ['Alejandra', 'Rodriguez', `alejandra@${domain}`, null, 'Drata', 'Software Engineer'],
        ['Guillermo', 'Serrano', `guillermo@${domain}`, null, 'Drata', 'Software Engineer'],
        ['Pedro', 'Torres', `pedrotorres@${domain}`, null, 'Drata', 'Software Engineer'],
        ['Luis', 'Gudino', `luisgudino@${domain}`, null, 'Drata', 'Senior Software Engineer'],
        ['Gordy', 'Krueger', `gordon@${domain}`, null, 'Drata', 'QA Manager'],
        [
            'Christopher',
            'Santangelo',
            '<EMAIL>',
            null,
            'Drata',
            'Technical Support Representative',
        ],
        [
            'Gamaliel',
            'Medina',
            `gamalielmedina@${domain}`,
            null,
            'Drata',
            'Senior Software Engineer',
        ],
        ['Alan', 'Flores', `alanflores@${domain}`, null, 'Drata', 'Software Engineer'],
        ['Reid', 'von Gunten', `reid@${domain}`, null, 'Drata', 'Engineering Manager'],
        ['Aaron', 'Junot', `aaronjunot@${domain}`, null, 'Drata', 'Senior Software Engineer'],
        ['Taco', 'Taco', `taco@${domain}`, null, 'Drata', 'Break Glass Account'],
        ['Howard', 'Carter', `howard@${domain}`, null, 'Drata', 'Product Manager'],
        ['Dante', 'Gomez', `dante@${domain}`, null, 'Drata', 'Technical Support Representative'],
        [
            'Rachel',
            'Gillett',
            `rachelgillett@${domain}`,
            null,
            'Drata',
            'Technical Support Representative',
        ],
        ['J', 'Drill', `jdrill@${domain}`, null, 'Drata', 'Senior Software Engineer in Test'],
        ['Monica', 'Finc', `monicafinc@${domain}`, null, 'Drata', 'Senior Product Designer'],
        [
            'Christian',
            'Lopez',
            `christianlopez@${domain}`,
            null,
            'Drata',
            'Senior Backend Developer',
        ],
        ['Paola', 'Miramontes', `paolamiramontes@${domain}`, null, 'Drata', 'Jr Software Engineer'],
        ['Vania', 'Munoz', `vaniamunoz@${domain}`, null, 'Drata', 'Mid Software Engineer'],
        ['Mauricio', 'muniz', `mauriciomuniz@${domain}`, null, 'Drata', 'Product Owner'],
        ['Jake', 'Stewart', `jakestewart@${domain}`, null, 'Drata', 'Data Engineer'],
        ['Adam', 'Andrus', `adamandrus@${domain}`, null, 'Drata', 'Sr. QA Engineer'],
        ['Astrid', 'Lopez', `astridlopez@${domain}`, null, 'Drata', 'Product Owner'],
        ['Ian', 'Jaffe', `ianjaffe@${domain}`, null, 'Drata', 'Solutions Architect'],
        ['Evin', 'Whittington', `evinwhittington@${domain}`, null, 'Drata', 'Solutions Architect'],
        ['Nolan', 'Iriarte', `nolaniriarte@${domain}`, null, 'Drata', 'Solutions Architect'],
        ['Aldo', 'Torres', `aldotorres@${domain}`, null, 'Drata', 'Software Engineer'],
        ['Benjamin', 'Lezama', `benjaminlezama@${domain}`, null, 'Drata', 'Software Engineer'],
        ['Alan', 'Rodriguez', `alanrodriguez@${domain}`, null, 'Drata', 'Jr QA'],
        ['Sergio', 'Soto', `sergiosoto@${domain}`, null, 'Drata', 'Sr. Product Developer'],
        ['Daniel', 'Valles', `danielvalles@${domain}`, null, 'Drata', 'Software Engineer'],
        ['Hannah', 'Roddy', `hannahroddy@${domain}`, null, 'Drata', 'Sales Engineer'],
        ['Gilberto', 'Lopez', `gilbertolopez@${domain}`, null, 'Drata', 'Associate QA Engineer'],
        [
            'Bartosz',
            'Kwiecien',
            `bartoszkwiecien@${domain}`,
            null,
            'Drata',
            'Senior Software Engineer',
        ],
        ['Matthew', 'Reid', `matthewreid@${domain}`, null, 'Drata', 'Engineering Director'],
        ['Juan', 'Gonzales', `juangonzales@${domain}`, null, 'Drata', 'Senior QA Engineer'],
        [
            'Joshua',
            'Stuts',
            `joshuastuts@${domain}`,
            null,
            'Drata',
            'Manager of Information Security',
        ],
        ['Mamatha', 'Parekodi', `mamathaparekodi@${domain}`, null, 'Drata', 'QA Manager'],
        ['Javier', 'Ruvalcaba', `javierruvalcaba@${domain}`, null, 'Drata', 'Software Engineer'],
        [
            'Andres',
            'Pineda',
            `andrespineda@${domain}`,
            null,
            'Drata',
            'Technical Support Representative',
        ],
        ['Rodrigo', 'Mejia', `rodrigomejia@${domain}`, null, 'Drata', 'Senior QA Engineer'],
        ['Nidhi', 'Gandhi', `nidhigandhi@${domain}`, null, 'Drata', 'QA Manager'],
        ['Kian', 'Falcone', `kianfalcone@${domain}`, null, 'Drata', 'Site Reliability Engineer'],
        ['Eli', 'Grady', `eligrady@${domain}`, null, 'Drata', 'Senior Site Reliability Engineer'],
        ['Kyle', 'Nas', `kylenas@${domain}`, null, 'Drata', 'Senior Site Reliability Engineer'],
        ['Ilya', 'Pisman', `ilyapisman@${domain}`, null, 'Drata', 'Senior Cloud Engineer'],
        [
            'Anton',
            'Kachurin',
            `antonkachurin@${domain}`,
            null,
            'Drata',
            'Senior Site Reliability Engineer',
        ],
        [
            'Alexandru',
            'Macsim',
            `alexandrumacsim@${domain}`,
            null,
            'Drata',
            'Senior Software Engineer',
        ],
        [
            'Christopher',
            'Korokeyi',
            `christopherkorokeyi@${domain}`,
            null,
            'Drata',
            'Director of Product Management',
        ],
        [
            'Vijay',
            'Ilankamban',
            `vijayilankamban@${domain}`,
            null,
            'Drata',
            'Senior Product Manager',
        ],
        ['Ernesto', 'Celis', `ernestocelis@${domain}`, null, 'Drata', 'Sr. Fullstack Developer'],
        [
            'Alejandro',
            'Crispin',
            `alejandrocrispin@${domain}`,
            null,
            'Drata',
            'Senior Software Engineer',
        ],
        [
            'Brandom',
            'Rodriguez',
            `brandomrodriguez@${domain}`,
            null,
            'Drata',
            'Junior Software Engineer',
        ],
        ['Samuel', 'Coronado', `samuelcoronado@${domain}`, null, 'Drata', 'Software Engineer'],
        ['Christian', 'Lau', `christianlau@${domain}`, null, 'Drata', 'Senior Software Engineer'],
        ['Sergio', 'Cienfuegos', `sergiocienfuegos@${domain}`, null, 'Drata', 'QA Engineer'],
        [
            'Galdino',
            'Manzanero',
            `galdinomanzanero@${domain}`,
            null,
            'Drata',
            'Senior Software Engineer',
        ],
        ['Mackenzie', 'Bateman', `mackenziebateman@${domain}`, null, 'Drata', 'Product Manager'],
        ['Santiago', 'Patiño', `santiagopatino@${domain}`, null, 'Drata', 'Jr Software Engineer'],
        ['Jesus', 'Beltran', `jesusbeltran@${domain}`, null, 'Drata', 'Software Engineer'],
        ['Angel', 'Davila', `angeldavila@${domain}`, null, 'Drata', 'Junior Software Engineer'],
        [
            'Emmanuel',
            'Juarez',
            `emmanueljuarez@${domain}`,
            null,
            'Drata',
            'Senior Frontend Developer',
        ],
        ['Isai', 'Madueno', `isaimadueno@${domain}`, null, 'Drata', 'Software Engineer'],
        ['Joyce', 'Liang', `joyceliang@${domain}`, null, 'Drata', 'Product Manager'],
        ['Jonathan', 'Hernandez', `jonathanhernandez@${domain}`, null, 'Drata', 'Product Owner'],
        [
            'Miguel',
            'Plascencia',
            `miguelplascencia@${domain}`,
            null,
            'Drata',
            'Senior Software Engineer',
        ],
        ['Raul', 'Torres', `raultorres@${domain}`, null, 'Drata', 'Software Engineer'],
        ['Yael', 'Lira', `yaellira@${domain}`, null, 'Drata', 'Product Manager'],
        ['Savid', 'Salazar', `savidsalazar@${domain}`, null, 'Drata', 'Program Manager'],
        ['Elliot', 'Schaff', `elliotschaff@${domain}`, null, 'Drata', 'Product Manager'],
        ['Julio', 'Carozo', `juliocarozo@${domain}`, null, 'Drata', 'Senior Software Engineer'],
        ['Sergio', 'Zermeno', `sergiozermeno@${domain}`, null, 'Drata', 'Software Engineer'],
        ['Leonardo', 'Diaz', `leonardosteven@${domain}`, null, 'Drata', 'Senior Software Engineer'],
        ['Arturo', 'Balsimelli', `arturobalsimelli@${domain}`, null, 'Drata', 'Software Engineer'],
        ['Pedro', 'Cruz', `pedrocruz@${domain}`, null, 'Drata', 'Software Engineer'],
        ['Hazel', 'Loya', `hazelloya@${domain}`, null, 'Drata', 'QA Automation'],
        ['Gabriel', 'Perez', `gabrielperez@${domain}`, null, 'Drata', 'Software Engineer in Test'],
        [
            'Dhakshanya',
            'Gangatharan',
            `dhakshanyagangatharan@${domain}`,
            null,
            'Drata',
            'Tech Lead, Quality Assurance',
        ],
        ['Francisco', 'Camacho', `franciscocamacho@${domain}`, null, 'Drata', 'Product Owner'],
        [
            'Hannah',
            'Starcevich',
            `hannahstarcevich@${domain}`,
            null,
            'Drata',
            'Solutions Architect',
        ],
        ['Alnair', 'Gonzalez', `alnairgonzalez@${domain}`, null, 'Drata', 'Software Engineer'],
        ['Carlos', 'Lopez', `carloslopez@${domain}`, null, 'Drata', 'Software Engineer'],
        ['Alex', 'Koh', `alexkoh@${domain}`, null, 'Drata', 'Program Manager'],
        ['Bob', 'Ciotti', `bobciotti@${domain}`, null, 'Drata', 'Sales Engineer'],
        [
            'Victor',
            'Rebolloso',
            `victorrebolloso@${domain}`,
            null,
            'Drata',
            'Senior Software Engineer',
        ],
        ['Ivan', 'Osorio', `ivanosorio@${domain}`, null, 'Drata', 'Senior Tech Lead'],
        ['Eduardo', 'Ferreira', `eduardoferreira@${domain}`, null, 'Drata', 'Software Engineer'],
        [
            'Cameron',
            'Loughman',
            `cameronloughman@${domain}`,
            null,
            'Drata',
            'Senior Engineering Manager',
        ],
        [
            'Alvin',
            'Accad',
            `alvinaccad@${domain}`,
            null,
            'Drata',
            'Associate Software Engineer in Test',
        ],
        [
            'Octavio',
            'Palacios',
            `octaviopalacios@${domain}`,
            null,
            'Drata',
            'Senior Software Engineer',
        ],
        ['Sai', 'Mohan', `saimohan@${domain}`, null, 'Drata', 'Senior Product Designer'],
        ['Araceli', 'Juarez', `aracelijuarez@${domain}`, null, 'Drata', 'Senior Product Owner'],
        ['Luisa', 'Peralta', `luisaperalta@${domain}`, null, 'Drata', 'Senior QA Engineer'],
        ['Raúl', 'Rivadeneyra', `raulrivadeneyra@${domain}`, null, 'Drata', 'Software Engineer'],
        ['Miguel', 'Bonachea', `miguelbonachea@${domain}`, null, 'Drata', 'Software Engineer'],
        ['Jesus', 'Vadillo', `jesusvadillo@${domain}`, null, 'Drata', 'Senior Software Engineer'],
        ['Edgar', 'Alcantara', `edgaralcantara@${domain}`, null, 'Drata', 'Software Engineer'],
        ['Dwigth', 'Astacio', `dwigthastacio@${domain}`, null, 'Drata', 'Software Engineer'],
        ['Gabriel', 'Soto', `gabrielsoto@${domain}`, null, 'Drata', 'Software Engineer'],
        ['Antonio', 'Medina', `josemedina@${domain}`, null, 'Drata', 'Senior QA Engineer'],
        ['Isaias', 'Hinojosa', `isaiashinojosa@${domain}`, null, 'Drata', 'Product Owner'],
        ['Fernando', 'Gonzalez', `fernandogonzalez@${domain}`, null, 'Drata', 'Software Engineer'],
        ['Daniel', 'Caballero', `danielcaballero@${domain}`, null, 'Drata', 'Software Engineer'],
        ['Norman', 'Lau', `normanlau@${domain}`, null, 'Drata', 'Associate Software Engineer'],
        ['Juliana', 'Munoz', `julianamunoz@${domain}`, null, 'Drata', 'Software Engineer'],
        ['César', 'Lomelí', `cesarlomeli@${domain}`, null, 'Drata', 'Software Engineer'],
        ['Amulya', 'Kandikonda', `amulyakandikonda@${domain}`, null, 'Drata', 'Product Manager'],
        ['Alejandro', 'Estrada', `alejandroestrada@${domain}`, null, 'Drata', 'Software Engineer'],
        ['Juan', 'Vanegas', `juanvanegas@${domain}`, null, 'Drata', 'Tech Lead'],
        ['Sergio', 'Gomez', `sergiogomez@${domain}`, null, 'Drata', 'Software Engineer'],
        [
            'Akanksha',
            'Nguyen',
            `akankshanguyen@${domain}`,
            null,
            'Drata',
            'Principal Product Manager',
        ],
        ['Christian', 'Diaz', `christiandiaz@${domain}`, null, 'Drata', 'Software Engineer'],
        ['Mark', 'Uy', `markuy@${domain}`, null, 'Drata', 'Software Engineer'],
        ['Gerardo', 'Chavez', `gerardochavez@${domain}`, null, 'Drata', 'Software Engineer'],
        ['Sahid', 'Ayala', `sahidayala@${domain}`, null, 'Drata', 'Software Engineer'],
        [
            'Isaac',
            'Gonzalez',
            `isaacgonzalez@${domain}`,
            null,
            'Drata',
            'Associate Software Engineer',
        ],
        ['Laila', 'Porte Petit', `lailapetit@${domain}`, null, 'Drata', 'Senior Product Owner'],
        [
            'Juan Miguel',
            'de la Torre Loza',
            `juandelatorre@${domain}`,
            null,
            'Drata',
            'Software Engineer',
        ],
        ['Angel', 'Roca', `angelroca@${domain}`, null, 'Drata', 'Tech Lead'],
        [
            'Vincent',
            'Grosso',
            `vincentgrosso@${domain}`,
            null,
            'Drata',
            'Senior Product Marketing Manager',
        ],
        ['Marcos', 'Alvarez', `marcosalvarez@${domain}`, null, 'Drata', 'Senior QA Engineer'],
        ['Ivan', 'Lopez', `ivanlopez@${domain}`, null, 'Drata', 'Senior Product Owner'],
        ['Daniel', 'Garcia', `danielgarcia@${domain}`, null, 'Drata', 'Senior Engineering Manager'],
        ['Israel', 'Mata', `israelmata@${domain}`, null, 'Drata', 'Senior Product Owner'],
        [
            'Keri',
            'Aldahondo',
            `kerialdahondo@${domain}`,
            null,
            'Drata',
            'Software Quality Assurance Manager',
        ],
        [
            'Michael',
            'Patterson',
            `michaelpatterson@${domain}`,
            null,
            'Drata',
            'Partner Solutions Engineer',
        ],
        [
            'Brian',
            'Stanforth',
            `brianstanforth@${domain}`,
            null,
            'Drata',
            'Senior Software Engineer',
        ],
        ['Ashish', 'Jha', `ashishjha@${domain}`, null, 'Drata', 'Senior Manager Engineering'],
        [
            'Ed',
            'Rubio',
            `edrubio@${domain}`,
            null,
            'Drata',
            'Manager, Quality Assurance - Platform',
        ],
        ['Lilia', 'Flores', `liliaflores@${domain}`, null, 'Drata', 'Senior Software Engineer'],
        ['Sam', 'Ringleman', `samringleman@${domain}`, null, 'Drata', 'Staff Software Engineer'],
        [
            'Mackenzie',
            'Chyatte',
            `mackenziechyatte@${domain}`,
            null,
            'Drata',
            'Solutions Architect',
        ],
        ['Isaac', 'Palacios', `isaacpalacios@${domain}`, null, 'Drata', 'Senior Software Engineer'],
        ['Cristobal', 'Rodriguez', `cristobalrodriguez@${domain}`, null, 'Drata', 'Product Owner'],
        ['Jesus', 'Gonzalez', `jesusgonzalez@${domain}`, null, 'Drata', 'Software Engineer'],
        ['Jessica', 'Pardo', `jessicapardo@${domain}`, null, 'Drata', 'Senior Software Engineer'],
        ['Leonardo', 'Serrano', `leonardoserrano@${domain}`, null, 'Drata', 'QA Engineer'],
        ['Arturo', 'Renteria', `arturor@${domain}`, null, 'Drata', 'Senior Software Engineer'],
        [
            'Octavio',
            'Santiago',
            `octaviosantiago@${domain}`,
            null,
            'Drata',
            'Senior Software Engineer',
        ],
        ['Abel', 'Fuentes', `abelfuentes@${domain}`, null, 'Drata', 'Senior Software Engineer'],
        ['Ruben', 'Fajardo', `rubenfajardo@${domain}`, null, 'Drata', 'Senior Software Engineer'],
        ['Andrea', 'Delgado', `andreadelgado@${domain}`, null, 'Drata', 'Product Owner'],
        [
            'Mateusz',
            'Stachurzewski',
            `mateuszstachurzewski@${domain}`,
            null,
            'Drata',
            'Senior Software Engineer in Test',
        ],
        ['Jorge', 'Delgado', `jorgedelgado@${domain}`, null, 'Drata', 'Software Engineer'],
        [
            'Brayan',
            'Prieto',
            `brayanprieto@${domain}`,
            null,
            'Drata',
            'Associate Software Engineer',
        ],
        ['Sushil', 'Khadka', `sushilkhadka@${domain}`, null, 'Drata', 'Solutions Architect'],
        ['Taylor', 'Kloustin', `taylorkloustin@${domain}`, null, 'Drata', 'Solutions Architect'],
        [
            'Yerlinson',
            'Maturana',
            `yerlinsonmaturana@${domain}`,
            null,
            'Drata',
            'Senior Software Engineer',
        ],
        ['Aniket', 'Kulkarni', `aniketkulkarni@${domain}`, null, 'Drata', 'QA Manager'],
        [
            'Matthew',
            'Traughber',
            `matthewtraughber@${domain}`,
            null,
            'Drata',
            'Manager, Quality Assurance',
        ],
        ['Abhi', 'Anand', `abhianand@${domain}`, null, 'Drata', 'Group Product Manager'],
        [
            'Horacio',
            'Espinosa',
            `espinosahoracio@${domain}`,
            null,
            'Drata',
            'Software Engineer in Test',
        ],
        ['Jose', 'Salazar', `josesalazar@${domain}`, null, 'Drata', 'Senior Software Engineer'],
        [
            'Arnoldo',
            'Cortez y Quevedo',
            `arnoldocortezyquevedo@${domain}`,
            null,
            'Drata',
            'Software Engineer',
        ],
        ['Gonzalo', 'Lorieto', `gonzalolorieto@${domain}`, null, 'Drata', 'Software Engineer'],
        ['Allie', 'Holcombe', `allieholcombe@${domain}`, null, 'Drata', 'Senior Software Engineer'],
        ['Huilun', 'Zhang', `huilunzhang@${domain}`, null, 'Drata', 'Senior Product Manager'],
        [
            'Santiago',
            'Herrera',
            `santiagoherrera@${domain}`,
            null,
            'Drata',
            'Senior Software Engineer',
        ],
        ['Alejandro', 'Díaz', `alejandrodiaz@${domain}`, null, 'Drata', 'Software Engineer'],
        ['Jose', 'Santacoloma', `josesantacoloma@${domain}`, null, 'Drata', 'Senior Product Owner'],
        ['Luis', 'Sosa', `luissosa@${domain}`, null, 'Drata', 'Senior QA Engineer'],
        ['Josh', 'Cove', `joshcove@${domain}`, null, 'Drata', 'Senior Software Engineer in Test'],
        ['Jon', 'Wolfe', `jonwolfe@${domain}`, null, 'Drata', 'Staff Software Engineer'],
        [
            'James',
            'Carpino',
            `jamescarpino@${domain}`,
            null,
            'Drata',
            'Technical Support Representative',
        ],
        ['Devon', 'Henry', `devonhenry@${domain}`, null, 'Drata', 'Senior QA Engineer'],
        ['Josue', 'Marquez', `josuemarquez@${domain}`, null, 'Drata', 'Senior Software Engineer'],
        ['Axel', 'Vargas', `axelvargas@${domain}`, null, 'Drata', 'Senior Software Engineer'],
        ['Gabriela', 'Zavalia', `gabrielazavalia@${domain}`, null, 'Drata', 'Senior QA Engineer'],
        ['Arif', 'Islam', `arifislam@${domain}`, null, 'Drata', 'Senior Product Manager'],
        ['Nicolas', 'Straub', `nicolasstraub@${domain}`, null, 'Drata', 'Senior Tech Lead'],
        [
            'Christian',
            'Camacho',
            `christiancamacho@${domain}`,
            null,
            'Drata',
            'Senior Software Engineer',
        ],
        ['Harys', 'Vizcaino', `harysvizcaino@${domain}`, null, 'Drata', 'Senior Software Engineer'],
        ['Tom', 'Belote', `tombelote@${domain}`, null, 'Drata', 'Software Engineer'],
        ['Shane', 'Tierney', `shanetierney@${domain}`, null, 'Drata', 'Senior Program Manager'],
        ['Ana', 'Rojas', `anarojas@${domain}`, null, 'Drata', 'Senior QA Engineer'],
        ['Josie', 'Beaudoin', `josiebeaudoin@${domain}`, null, 'Drata', 'Solutions Architect'],
        ['Erick', 'Sosa', `ericksosa@${domain}`, null, 'Drata', 'Senior Software Engineer'],
        ['Jaji', 'Olajide', `olajidejaji@${domain}`, null, 'Drata', 'Senior Product Manager'],
        ['Casey', 'Arendt', `caseyarendt@${domain}`, null, 'Drata', 'Senior Product Designer'],
        ['Danny', 'Philayvanh', `dannyphilayvanh@${domain}`, null, 'Drata', 'Engineering Manager'],
        ['Kirk', 'Rehal', `kirkrehal@${domain}`, null, 'Drata', 'Senior Software Engineer'],
        ['Aakash', 'Shah', `aakashshah@${domain}`, null, 'Drata', 'Senior Director'],
        ['Kevin', 'Janssen', `kevinjanssen@${domain}`, null, 'Drata', 'Senior Software Engineer'],
        ['Jane', 'Baik', `janebaik@${domain}`, null, 'Drata', 'Technical Writer'],
        [
            'Jess',
            'Delgado Perez',
            `jessdelgadoperez@${domain}`,
            null,
            'Drata',
            'Senior Software Engineer',
        ],
        ['Brandon', 'Nicoll', `brandonnicoll@${domain}`, null, 'Drata', 'Staff Software Engineer'],
        ['Paolo', 'Posso', `paolovictor@${domain}`, null, 'Drata', 'Senior Software Engineer'],
        ['Jonn', 'Novaretti', `jonnnovaretti@${domain}`, null, 'Drata', 'Senior Software Engineer'],
        [
            'Hor-kan',
            'Chan',
            `horkan@${domain}`,
            null /* no avatar */,
            'Drata',
            'Engineering Director',
        ],
        ['Om', 'Vyas', `omvyas@${domain}`, null, 'Drata', 'Senior Product Director'],
        ['Tony', 'Bentley', `tonybentley@${domain}`, null, 'Drata', 'Senior Software Engineer'],
        [
            'Stasi',
            'Vladimirov',
            `stasivladimirov@${domain}`,
            null,
            'Drata',
            'Senior Software Engineer',
        ],
        ['Saloni', 'Porwal', `saloniporwal@${domain}`, null, 'Drata', 'Senior Product Manager'],
        ['Satwik', 'Kommi', `satwikkommi@${domain}`, null, 'Drata', 'Senior Security Engineer'],
        ['Alev', 'Viggio', `alev@${domain}`, null, 'Drata', 'Director, Complience'],
        ['Jonathan', 'Wong', `jonathanwong@${domain}`, null, 'Drata', 'Senior Compliance Analyst'],
        [
            'Daniel',
            'Flores',
            `danielfloresherrera@${domain}`,
            null,
            'Drata',
            'Program Manager, GCR',
        ],
        ['Morgan', 'Such', `morgansuch@${domain}`, null, 'Drata', 'Security Engineer'],
        ['Razvan', 'Tolbaru', `razvantolbaru@${domain}`, null, 'Drata', 'Security Engineer'],
        ['Jason', 'Hills', `jasonhills@${domain}`, null, 'Drata', 'Security Engineer'],
        ['Michael', 'Krec', `michaelkrec@${domain}`, null, 'Drata', 'Security Engineer'],
        ['Ryan', 'Goodman', `ryangoodman@${domain}`, null, 'Drata', 'Security Engineer'],
        ['Ray', 'Lambert', `raylambert@${domain}`, null, 'Drata', 'Security Engineer'],
        ['Matt', 'Hillary', `matt@${domain}`, null, 'Drata', 'CISO'],
        [
            'Joshua',
            'Beck',
            `joshuabeck@${domain}`,
            null,
            'Drata',
            'Senior Software Engineer in Test',
        ],
        ['Cedrick', 'Guzman', `cedrickguzman@${domain}`, null, 'Drata', 'Associate QA Engineer'],
        ['Vince', 'Uy', `vinceuy@${domain}`, null, 'Drata', 'Associate QA Engineer'],
        ['Ramesh', 'Patel', `rameshpatel@${domain}`, null, 'Drata', 'Director, Infrastructure'],
        ['Tim', 'Hansen', `timhansen@${domain}`, null, 'Drata', 'Solutions Architect'],
        ['Diego', 'Burlando', `diegoburlando@${domain}`, null, 'Drata', 'Tech Lead'],
        ['Dean', 'Clark', `dean@${domain}`, null, 'Drata', 'Group Product Manager'],
        ['Johnny', 'Kinder', `johnny@${domain}`, null, 'Drata', 'Principal Product Manager'],
        [
            'Stephen',
            'Schwahn',
            `stephenschwahn@${domain}`,
            null,
            'Drata',
            'Staff Software Engineer',
        ],
        ['Kevin', 'Kho', `kevinkho@${domain}`, null, 'Drata', 'Senior AI Engineer'],
        [
            'Tomasz',
            'Gintowt',
            `tomaszgintowt@${domain}`,
            null,
            'Drata',
            'Senior Database Reliability Engineer',
        ],
        ['Matt', 'Long', `mattlong@${domain}`, null, 'Drata', 'Manager, Software Engineering'],
        ['Agustin', 'Giacchello', `agugiacchello@${domain}`, null, 'Drata', 'Software Engineer'],
        ['Jake', 'Ayala', `jakeayala@${domain}`, null, 'Drata', 'Staff Software Engineer'],
        [
            'Aashima',
            'Dhankhar',
            `aashimadhankhar@${domain}`,
            null,
            'Drata',
            'Senior Product Manager',
        ],
        [
            'Nisha',
            'Pattan',
            `nishapattan@${domain}`,
            null,
            'Drata',
            'Senior Product Marketing Manager',
        ],
        ['Manuel', 'Hernandez', `manuelhernandez@${domain}`, null, 'Drata', 'Software Engineer'],
        [
            'Curtis',
            'Fraser',
            `curtisfraser@${domain}`,
            null,
            'Drata',
            'Manager, Software Engineering',
        ],
        ['Mauricio', 'Raini', `mauricioraini@${domain}`, null, 'Drata', 'Software Engineer'],
        ['Roy', 'Jimenez', `royjimenez@${domain}`, null, 'Drata', 'Senior Software Engineer'],
        ['John', 'Delshadi', `johndelshadi@${domain}`, null, 'Drata', 'Senior Product Manager'],
        ['Skeept', 'Espinoza', `skeeptespinoza@${domain}`, null, 'Drata', 'Staff Engineer'],
        ['Jose', 'Villa', `josevilla@${domain}`, null, 'Drata', 'Software Engineer'],
        ['Rafael', 'Ramos', `rafaelramos@${domain}`, null, 'Drata', 'Senior Software Engineer'],
        ['Hancel', 'Avila', `hancelavila@${domain}`, null, 'Drata', 'Software Engineer'],
        [
            'Salvatore Maria',
            'Italiano',
            `salvatoremaria@${domain}`,
            null,
            'Drata',
            'Software Engineer',
        ],
        [
            'Sebastian',
            'Galvan',
            `sebastiangalvan@${domain}`,
            null,
            'Drata',
            'Associate Software Engineer',
        ],
        [
            'Eduardo',
            'Rendon',
            `eduardorendon@${domain}`,
            null,
            'Drata',
            'Associate Software Engineer',
        ],
        ['Jorge', 'Torres', `jorgetorres@${domain}`, null, 'Drata', 'Associate Software Engineer'],
        [
            'Antonio',
            'Torres',
            `antoniotorres@${domain}`,
            null,
            'Drata',
            'Associate Software Engineer',
        ],
        ['Hendra', 'Wong', `hendrawong@${domain}`, null, 'Drata', 'Engineering Manager'],
        [
            'Juan',
            'Viloria',
            `juanviloria@${domain}`,
            'https://avatars.githubusercontent.com/u/218815615',
            'Drata',
            'Associate Support Service Engineer',
        ],
        [
            'Elina',
            'Garcia',
            `elinagarcia@${domain}`,
            'https://avatars.githubusercontent.com/u/36629418',
            'Drata',
            'Senior Software Engineer',
        ],
        [
            'Zachary',
            'Hancock',
            `zacharyhancock@${domain}`,
            null,
            'Drata',
            'Senior Software Engineer',
        ],
        [
            'Eli',
            'Perez-Figueroa',
            `eliperezfigueroa@${domain}`,
            null,
            'Drata',
            'Senior Software Engineer',
        ],
        [
            'Onyekachukwu',
            'Onyerikwu',
            `onyekachukwuonyerikwu@${domain}`,
            null,
            'Drata',
            'Senior Software Engineer',
        ],
        [
            'Angel',
            'Quinonez',
            `angelquinonez@${domain}`,
            null,
            'Drata',
            'Associate Software Engineer',
        ],
        [
            'Guillermo',
            'Tlapa',
            `guillermotlapa@${domain}`,
            null,
            'Drata',
            'Associate Software Engineer',
        ],
    ];

    const employeeTest = [
        [
            'Silvester',
            'Cat',
            `sc@${domain}`,
            'https://icons.iconarchive.com/icons/sykonist/looney-tunes/128/Silvester-icon.png',
            'Drata',
            'Chief Eater',
        ],
        [
            'Tasmanian',
            'Devil',
            `td@${domain}`,
            'https://icons.iconarchive.com/icons/sykonist/looney-tunes/128/Taz-icon.png',
            'Drata',
            'The Wildcard',
        ],
        [
            'Marvin',
            'Martian',
            `mm@${domain}`,
            'https://icons.iconarchive.com/icons/sykonist/looney-tunes/128/Marvin-Martian-Angry-icon.png',
            'Drata',
            'Overruler',
        ],
        [
            'Speedy',
            'Gonzales',
            `sg@${domain}`,
            'https://icons.iconarchive.com/icons/sykonist/looney-tunes/128/Speedy-Gonzales-icon.png',
            'Drata',
            'Chief Speedster',
        ],
        ['Gabriel', 'Tello', `gabrieltello@${domain}`, null, 'Drata', 'Senior Software Engineer'],
        [
            'Ryan',
            'Pedersen',
            `ryanpedersen@${domain}`,
            null,
            'Drata',
            'Senior Partner Solutions Engineer',
        ],
        [
            'Kat',
            'Kenny',
            `kathleenkenny@${domain}`,
            null,
            'Drata',
            'Director, Customer Success Digital',
        ],
    ];

    const accountRequests: AccountRequest[] = [];

    for (const item of adminEmployeesEngs) {
        accountRequests.push({
            firstName: item[0],
            lastName: item[1],
            email: item[2],
            roles: [Role.ADMIN, Role.EMPLOYEE],
            avatar: item[3],
            companyName: item[4],
            jobTitle: item[5] ?? '',
        });
    }

    for (const item of employeeTest) {
        accountRequests.push({
            firstName: item[0],
            lastName: item[1],
            email: item[2],
            roles: [Role.EMPLOYEE],
            avatar: item[3],
            companyName: item[4],
            jobTitle: item[5],
        });
    }

    return accountRequests;
}

export async function createFeatureTemplates(
    factory: Factory,
    featureTemplates: FeatureTemplateType[],
): Promise<{ templates: FeatureTemplate[] }> {
    const templates: FeatureTemplate[] = [];

    for (const featureTemplate of featureTemplates) {
        const template: FeatureTemplate = await factory(FeatureTemplate)()
            .map(async (generatedFeatureTemplate: FeatureTemplate) => {
                generatedFeatureTemplate.defaultValue = featureTemplate.defaultValue;
                generatedFeatureTemplate.defaultIsEnabled = featureTemplate.defaultIsEnabled;
                generatedFeatureTemplate.description = featureTemplate.description;
                generatedFeatureTemplate.featureType = FeatureType[featureTemplate.featureType];
                generatedFeatureTemplate.group = FeatureGroup[featureTemplate.group];
                generatedFeatureTemplate.access = FeatureAccess[featureTemplate.access];
                generatedFeatureTemplate.valueType = featureTemplate.valueType;

                return generatedFeatureTemplate;
            })
            .create();

        templates.push(template);
    }

    return { templates };
}

export function resolveControlTemplateDomain(sectionFixture: string): Domain {
    switch (sectionFixture) {
        case 'Data and Privacy':
            return Domain.DATA_AND_PRIVACY;
        case 'Data & Privacy':
            return Domain.DATA_AND_PRIVACY;
        case 'Internal Security Procedures':
            return Domain.INTERNAL_SECURITY_PROCEDURES;
        case 'Organizational Security':
            return Domain.ORGANIZATIONAL_SECURITY;
        case 'Product Security':
            return Domain.PRODUCT_SECURITY;
        case 'Infrastructure Security':
            return Domain.INFRASTRUCTURE_SECURITY;
        case 'Physical Security':
            return Domain.PHYSICAL_SECURITY;
        case 'Availability':
            return Domain.AVAILABILITY;
        case 'Confidentiality':
            return Domain.CONFIDENTIALITY;
        case 'Processing Integrity':
            return Domain.PROCESSING_INTEGRITY;
        case 'Privacy':
            return Domain.PRIVACY;
        case 'General':
            return Domain.GENERAL;
        default:
            return null;
    }
}

export function resolveControlTemplateCategory(categoryFixture: string): Category {
    switch (categoryFixture) {
        case 'Customer Data Policies':
            return Category.CUSTOMER_DATA_POLICIES;
        case 'Internal Admin Tool':
            return Category.INTERNAL_ADMIN_TOOL;
        case 'Software Development Life Cycle':
            return Category.SOFTWARE_DEVELOPMENT_LIFE_CYCLE;
        case 'Responsible Disclosure Policy':
            return Category.RESPONSIBLE_DISCLOSURE_POLICY;
        case 'Access Control':
            return Category.ACCESS_CONTROL;
        case 'Vulnerability Management':
            return Category.VULNERABILITY_MANAGEMENT;
        case 'Security Issues':
            return Category.SECURITY_ISSUES;
        case 'Business Continuity':
            return Category.BUSINESS_CONTINUITY;
        case 'Incident Response Plan':
            return Category.INCIDENT_RESPONSE_PLAN;
        case 'Security Policies':
            return Category.SECURITY_POLICIES;
        case 'Security Program':
            return Category.SECURITY_PROGRAM;
        case 'Personnel Security':
            return Category.PERSONNEL_SECURITY;
        case 'Endpoints (Laptops)':
            return Category.ENDPOINTS_LAPTOPS;
        case 'Data Encryption':
            return Category.DATA_ENCRYPTION;
        case 'Vendor Management':
            return Category.VENDOR_MANAGEMENT;
        case 'Software Application Security':
            return Category.SOFTWARE_APPLICATION_SECURITY;
        case 'Customer Communication':
            return Category.CUSTOMER_COMMUNICATION;
        case 'Authentication and Authorization':
            return Category.AUTHENTICATION_AND_AUTHORIZATION;
        case 'Availability':
            return Category.AVAILABILITY;
        case 'Storage':
            return Category.STORAGE;
        case 'Backup':
            return Category.BACKUP;
        case 'Logging':
            return Category.LOGGING;
        case 'Monitoring':
            return Category.MONITORING;
        case 'Network':
            return Category.NETWORK;
        case 'Protecting Secrets':
            return Category.PROTECTING_SECRETS;
        case 'Data Center Security':
            return Category.DATA_CENTER_SECURITY;
        case 'Scaling':
            return Category.SCALING;
        case 'Backups':
            return Category.BACKUPS;
        case 'Data':
            return Category.DATA;
        case 'Employee Responsibilities':
            return Category.EMPLOYEE_RESPONSIBILITIES;
        case 'Regression Testing':
            return Category.REGRESSION_TESTING;
        case 'Application and System Edits':
            return Category.APPLICATION_AND_SYSTEM_EDITS;
        case 'Communication of Requirements Related to Privacy Practices':
            return Category.COMMUNICATION_OF_OBJECTIVES_RELATED_TO_PRIVACY_PRACTICES;
        case 'Privacy Related to Choice and Consent':
            return Category.PRIVACY_RELATED_TO_CHOICE_AND_CONSENT;
        case 'Privacy Related to Collection':
            return Category.PRIVACY_RELATED_TO_COLLECTION;
        case 'Privacy Related to Use, Retention, and Disposal':
            return Category.PRIVACY_RELATED_TO_USE_RETENTION_AND_DISPOSAL;
        case 'Privacy Criteria Related to Access':
            return Category.PRIVACY_CRITERIA_RELATED_TO_ACCESS;
        case 'Privacy Criteria Related to Disclosure and Notification':
            return Category.PRIVACY_CRITERIA_RELATED_TO_DISCLOSURE_AND_NOTIFICATION;
        case 'Privacy Related to Quality':
            return Category.PRIVACY_RELATED_TO_QUALITY;
        case 'Privacy Related to Monitoring and Enforcement':
            return Category.PRIVACY_RELATED_TO_MONITORING_AND_ENFORCEMENT;
        case 'Definitions':
            return Category.DEFINITIONS;
        case 'Breach Notification':
            return Category.BREACH_NOTIFICATIONS;
        default:
            return null;
    }
}

export async function createEntitlements(
    factory: Factory,
    entitlementList: { name: string; description: string; type: number }[],
): Promise<AccountEntitlement[]> {
    const entitlements: AccountEntitlement[] = [];

    for (const entitlement of entitlementList) {
        const newEntitlement: AccountEntitlement = await factory(AccountEntitlement)()
            .map(async (generatedEntitlement: AccountEntitlement) => {
                generatedEntitlement.name = entitlement.name;
                generatedEntitlement.description = entitlement.description;
                generatedEntitlement.type = entitlement.type;
                return generatedEntitlement;
            })
            .create();
        entitlements.push(newEntitlement);
    }

    return entitlements;
}

export async function enableTrustCenterGlobal(
    factory: Factory,
    account: Account,
    entitlements: AccountEntitlement[],
): Promise<TrustCenter> {
    const entitlement = entitlements.find(f => f.type === AccountEntitlementType.TRUST_CENTER);

    return factory(TrustCenter)()
        .map(async (generatedTrustCenter: TrustCenter) => {
            generatedTrustCenter.account = account;
            generatedTrustCenter.isPublished = false;
            if (entitlement) {
                generatedTrustCenter.entitlement = entitlement;
            }
            return generatedTrustCenter;
        })
        .create();
}

export async function createTenantRouters(
    factory: Factory,
    account: Account,
    tenantRouterList: {
        key: string;
        clientType: ClientType | null;
        category: TenantRouterCategory;
    }[],
): Promise<TenantRouter[]> {
    const tenantRouters: TenantRouter[] = [];

    for (const tenantRouter of tenantRouterList) {
        const newTenantRouter: TenantRouter = await factory(TenantRouter)()
            .map(async (generatedTenantRouter: TenantRouter) => {
                generatedTenantRouter.account = account;
                generatedTenantRouter.key = tenantRouter.key;
                generatedTenantRouter.clientType = tenantRouter.clientType;
                generatedTenantRouter.category = tenantRouter.category;
                return generatedTenantRouter;
            })
            .create();
        tenantRouters.push(newTenantRouter);
    }

    return tenantRouters;
}

export async function createControlTestInstances(
    factory: Factory,
    users: User[],
    account: Account,
    connection: DrataDataSource,
    tenantConnection: DrataDataSource,
): Promise<any> {
    // switch to tenant
    switchToTenantConnection(tenantConnection);
    const companyRepository = tenantConnection.getRepository(Company);
    let company: Company | null = await companyRepository.findOneBy({});
    if (isNil(company)) {
        // create act as entry
        const adminUsers = users.filter(function (u) {
            return hasRole(u, [Role.ADMIN]);
        });
        const actAsEntry = await createActAsEntry(factory, account, adminUsers[0]);
        company = await createCompany(factory, account, actAsEntry);
    }
    const siteAdminRepository = connection.getRepository(SiteAdmin);
    let siteAdmins: SiteAdmin[] = await siteAdminRepository.find();
    if (isEmpty(siteAdmins)) {
        const drataSiteAdminsSeeder = new DrataSiteAdmins();

        ({ siteAdmins } = await drataSiteAdminsSeeder.run(factory, connection));
    }
    const evidenceTemplateRespository = connection.getRepository(EvidenceTemplate);
    let evidenceTemplates: EvidenceTemplate[] = await evidenceTemplateRespository.find({});
    if (isEmpty(evidenceTemplates)) {
        const evidenceTemplatesSeeder = new CreateEvidenceTemplates();
        evidenceTemplates = await evidenceTemplatesSeeder.run(factory, connection);
    }
    const policyTemplateRepository = connection.getRepository(PolicyTemplate);
    let policyTemplates: PolicyTemplate[] = await policyTemplateRepository.find({});

    if (isEmpty(policyTemplates)) {
        const policyTemplatesSeeder = new CreatePolicyTemplates(siteAdmins);
        policyTemplates = await policyTemplatesSeeder.run(factory, connection);
    }
    switchToDefaultConnection(connection);
    const frameworkTemplateProvisioner = new FrameworkTemplateProvisioner(
        policyTemplates,
        evidenceTemplates,
    );
    const { frameworkTemplates } = await frameworkTemplateProvisioner.run(factory, connection);

    const frameworkRepository = tenantConnection.getRepository(Framework);
    let frameworkEntities = await frameworkRepository.find();
    let monitorStat = null;
    if (isEmpty(frameworkEntities)) {
        const frameworkProvisioner = new FrameworkProvisioner(
            company,
            frameworkTemplates,
            users,
            policyTemplates,
        );
        ({ frameworkEntities, monitorStat } = await frameworkProvisioner.run(
            factory,
            tenantConnection,
            account,
        ));
    }
    return {
        siteAdmins,
        policyTemplates,
        evidenceTemplates,
        frameworkTemplates,
        frameworkEntities,
        monitorStat,
        company,
    };
}

async function createAdditionalsHtmlTemplates(
    factory: Factory,
    basePolicy: BasePolicy,
    policyTemplate: PolicyTemplate,
    siteAdmin: SiteAdmin[],
) {
    const { additionalContents } = basePolicy;
    if (!isEmpty(additionalContents)) {
        for (const { html, type, comments = [] } of additionalContents) {
            await factory(PolicyTemplateContent)()
                .map(async (htmlTemplate: PolicyTemplateContent) => {
                    htmlTemplate.html = fs
                        .readFileSync(pathResolve(config.get('fixtures.path') + html))
                        .toString();
                    htmlTemplate.type = type;
                    htmlTemplate.policyTemplate = policyTemplate;
                    return htmlTemplate;
                })
                .create();
            if (!isEmpty(comments)) {
                await createSiteAdminComments(factory, comments, policyTemplate, siteAdmin);
            }
        }
    }
}

export async function createProfileDetailsTemplates(
    factory: Factory,
    profileJsonTemplateFixtures: any,
    frameworkTemplates: FrameworkTemplate[],
    connection: DrataDataSource,
): Promise<ProfileDetailsTemplate[]> {
    const profileDetailsTemplates: ProfileDetailsTemplate[] = [];
    const profileDetailsTemplateRepository = getCustomRepository(
        ProfileDetailsTemplateRepository,
        connection,
    );
    for (const profileFixture of profileJsonTemplateFixtures.profileDetailsTemplates) {
        const { frameworkTag, profiles } = profileFixture;

        const frameworkTemplate = frameworkTemplates.find(fwTemp => fwTemp.tag === frameworkTag);

        if (isNil(frameworkTemplate)) {
            continue;
        }

        const existingProfiles =
            await profileDetailsTemplateRepository.findDetailsTemplateByFrameworkTemplateId(
                frameworkTemplate.id,
            );
        const newProfiles = profiles.filter(
            prof => !existingProfiles.some(existingProf => existingProf.externalId === prof.uuid),
        );

        for (const profile of newProfiles) {
            const { uuid, path } = profile;
            const existingNotMappedProfile =
                await profileDetailsTemplateRepository.findProfileDetailsTemplateByExternalId(uuid);
            if (!isNil(existingNotMappedProfile)) {
                existingNotMappedProfile.frameworkTemplate = frameworkTemplate;
                await profileDetailsTemplateRepository.save(existingNotMappedProfile);
                profileDetailsTemplates.push(existingNotMappedProfile);
                frameworkTemplate.profileDetailsTemplates = [
                    ...(frameworkTemplate.profileDetailsTemplates || []),
                    existingNotMappedProfile,
                ];
            } else {
                const profileJsonDocument = readJSONFile<CatalogJSON>(
                    pathResolve(config.get(path)),
                );
                const newProfileDetailsTemplate = await createProfileDetailsTemplate(
                    factory,
                    profile,
                    profileJsonDocument,
                    frameworkTemplate,
                );
                frameworkTemplate.profileDetailsTemplates = [
                    ...(frameworkTemplate.profileDetailsTemplates || []),
                    newProfileDetailsTemplate,
                ];
                profileDetailsTemplates.push(newProfileDetailsTemplate);
            }
        }
    }
    return profileDetailsTemplates;
}

export async function createProfileDetailsTemplate(
    factory: Factory,
    profileDetailsTemplateFixture: any,
    jsonDocument: CatalogJSON,
    frameworkTemplate: FrameworkTemplate,
): Promise<ProfileDetailsTemplate> {
    const { uuid, baselineName, order, sortOrder } = profileDetailsTemplateFixture;
    return factory(ProfileDetailsTemplate)()
        .map(async (generatedProfileJsonTemplate: ProfileDetailsTemplate) => {
            generatedProfileJsonTemplate.externalId = uuid;
            generatedProfileJsonTemplate.name = baselineName;
            generatedProfileJsonTemplate.jsonDocument = jsonDocument;
            generatedProfileJsonTemplate.order = order;
            generatedProfileJsonTemplate.sortOrder = sortOrder;
            generatedProfileJsonTemplate.frameworkTemplate = frameworkTemplate;

            return generatedProfileJsonTemplate;
        })
        .create();
}

export async function mapProfileDetailsTemplatesToFrameworkTemplates(
    frameworkTemplates: FrameworkTemplate[],
    globalConnection: DrataDataSource,
): Promise<FrameworkTemplate[]> {
    const profileDetailsTemplateRepository = getCustomRepository(
        ProfileDetailsTemplateRepository,
        globalConnection,
    );
    for (const fwTemplate of frameworkTemplates) {
        if (!isNil(fwTemplate.externalId)) {
            const profileDetailsTemplates =
                await profileDetailsTemplateRepository.findDetailsTemplateByFrameworkTemplateId(
                    fwTemplate.id,
                );
            if (!isEmpty(profileDetailsTemplates)) {
                fwTemplate.profileDetailsTemplates = profileDetailsTemplates;
            }
        }
    }

    return frameworkTemplates;
}

/**
 * Update Access Application users/reviewers
 * @param requirementFixture: any, set as any since we don't have a specific type for the fixtures.
 * for FedRAMP we are taking the name from the catalog json file which is found inside the props
 * from the props we exclude the label with a class property because it's not the desired format
 * @returns string, Check for empty string as it is not guaranteed to work.
 */
export function getRequirementNameFromFixture(requirementFixture: any): string {
    if (!isEmpty(requirementFixture.props)) {
        const foundJsonRequirementName = requirementFixture.props?.find(
            prop => prop.name === 'label' && isNil(prop.class),
        );
        if (!isNil(foundJsonRequirementName?.value)) {
            return foundJsonRequirementName.value;
        }
    }
    const name = requirementFixture.name.split('|')[1];

    return name ?? '';
}

type PlanEntitlementsMap = {
    type: AccountEntitlementType;
    entitlementDefaultLimit?: number;
};

type PlanEntitlementsMaps = {
    [key in keyof typeof AccountPlan]: PlanEntitlementsMap[];
};

const plans = [
    {
        name: AccountPlan.ESSENTIAL, // Deprecated
        version: 1,
    },
    {
        name: AccountPlan.DRATA_ESSENTIALS,
        version: 1,
    },
    {
        name: AccountPlan.FOUNDATION,
        version: 1,
    },
    {
        name: AccountPlan.ADVANCED,
        version: 1,
    },
    {
        name: AccountPlan.ENTERPRISE,
        version: 1,
    },
] as Plan[];

const defaultEntitlements = [
    {
        type: AccountEntitlementType.DOWNLOAD_CONTROL,
    },
    {
        type: AccountEntitlementType.CUSTOM_CONNECTIONS_AND_TESTS,
    },
    {
        type: AccountEntitlementType.WELCOME_EXPERIENCE,
    },
    {
        type: AccountEntitlementType.AI_DATA_SHARE,
    },
    {
        type: AccountEntitlementType.SECURITY_QUESTIONNAIRE_AUTOMATION,
        entitlementDefaultLimit: 500,
    },
    {
        type: AccountEntitlementType.MULTI_IDP,
    },
    {
        type: AccountEntitlementType.PUBLIC_API,
    },
] as PlanEntitlementsMap[];

const planEntitlementsMapsData = {
    [AccountPlan.ESSENTIAL]: [
        ...defaultEntitlements,
        {
            type: AccountEntitlementType.MAP_CONTROLS_TESTS,
        },
        {
            type: AccountEntitlementType.CUSTOM_CONTROLS,
        },
    ],
    [AccountPlan.FOUNDATION]: [
        ...defaultEntitlements,
        {
            type: AccountEntitlementType.MAP_CONTROLS_TESTS,
        },
        {
            type: AccountEntitlementType.CUSTOM_CONTROLS,
        },
    ],
    // Same as Foundations except it doesn't have custom controls
    [AccountPlan.DRATA_ESSENTIALS]: [
        ...defaultEntitlements,
        {
            type: AccountEntitlementType.MAP_CONTROLS_TESTS,
        },
    ],
    [AccountPlan.ADVANCED]: [
        ...defaultEntitlements,
        {
            type: AccountEntitlementType.MAP_CONTROLS_TESTS,
        },
        {
            type: AccountEntitlementType.CUSTOM_FRAMEWORKS,
            entitlementDefaultLimit: 1,
        },
        {
            type: AccountEntitlementType.ACCESS_REVIEW,
        },
        {
            type: AccountEntitlementType.CUSTOM_FIELDS_AND_FORMULAS,
        },
        {
            type: AccountEntitlementType.CUSTOM_CONTROLS,
        },
    ],
    [AccountPlan.ENTERPRISE]: [
        ...defaultEntitlements,
        {
            type: AccountEntitlementType.TRUST_CENTER,
        },
        {
            type: AccountEntitlementType.MAP_CONTROLS_TESTS,
        },
        {
            type: AccountEntitlementType.RISK_MANAGEMENT,
        },
        {
            type: AccountEntitlementType.CUSTOM_FRAMEWORKS,
            entitlementDefaultLimit: 3,
        },
        {
            type: AccountEntitlementType.ACCESS_REVIEW,
        },
        {
            type: AccountEntitlementType.TPRM_PRO,
        },
        {
            type: AccountEntitlementType.CODE_REVIEW_PRO,
        },
        {
            type: AccountEntitlementType.CUSTOM_FIELDS_AND_FORMULAS,
        },
        {
            type: AccountEntitlementType.CUSTOM_CONTROLS,
        },
    ],
} as unknown as PlanEntitlementsMaps;

export async function populateAccountManagementTables(
    factory: Factory,
    connection: DrataDataSource,
): Promise<void> {
    const planRepository = connection.getRepository(Plan);
    const planEntitlementRepository = connection.getRepository(PlanEntitlement);
    const entitlementRepository = connection.getRepository(AccountEntitlement);

    // Save plans
    const storedPlans = await planRepository.save(plans);
    console.log('Plans information saved in DB');

    // Get all entitlements
    const entitlements = await entitlementRepository.find();

    // Create plan entitlements
    const savePromises = Object.values(AccountPlan).map(async planName => {
        if (planName === AccountPlan.LEGACY) {
            return;
        }

        console.log(`Creating plan entitlements map for ${planName}`);
        const planEntitlements = planEntitlementsMapsData[planName];
        const plan = storedPlans.find((storedPlan: Plan) => storedPlan.name === planName);

        if (!plan) {
            console.error(`Plan ${planName} not found in stored plans`);
            return;
        }

        const newPlanEntitlements = planEntitlements.map(entitlementMap => ({
            entitlementDefaultLimit: entitlementMap.entitlementDefaultLimit ?? null,
            entitlement: entitlements.find(
                (entitlement: AccountEntitlement) => entitlement.type === entitlementMap.type,
            ),
            plan,
        })) as PlanEntitlement[];

        await planEntitlementRepository.save(newPlanEntitlements);
        console.log(`Plan entitlements map created for ${planName}`);
    });

    await Promise.all(savePromises);

    console.log('Account management tables populated successfully');
}

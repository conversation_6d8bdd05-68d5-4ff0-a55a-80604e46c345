import { ComplianceCheckStatus, ComplianceCheckType } from '@drata/enums';
import { DeviceComplianceCheck } from 'app/devices/entities/device-compliance-check.entity';
import { DEVICE_VALID_COMPLIANCE_CHECKS } from 'app/devices/helpers/device.helper';
import { isAutoResetEnabled } from 'app/settings/helper/settings.helper';
import { AutoResetConfig } from 'app/settings/types/auto-reset-config.type';
import { UserDocument } from 'app/users/entities/user-document.entity';
import { ComplianceCheck } from 'app/users/personnel/entities/compliance-check.entity';
import { CheckFrequency } from 'commons/enums/check-frequency.enum';
import { AutoResetType } from 'commons/enums/security-training/auto-reset-type.enum';
import { hasExpired } from 'commons/helpers/date.helper';
import moment from 'moment';

/**
 *
 * @param {ComplianceCheckType} complianceCheckType
 * @returns {CheckFrequency}
 */
export function complianceCheckFrequency(complianceCheckType: ComplianceCheckType): CheckFrequency {
    let frequency: CheckFrequency;
    switch (complianceCheckType) {
        case ComplianceCheckType.ACCEPTED_POLICIES:
        case ComplianceCheckType.BG_CHECK:
        case ComplianceCheckType.HDD_ENCRYPTION:
            frequency = CheckFrequency.ONCE;
            break;
        case ComplianceCheckType.IDENTITY_MFA:
        case ComplianceCheckType.SECURITY_TRAINING:
            frequency = CheckFrequency.YEARLY;
            break;
        case ComplianceCheckType.PASSWORD_MANAGER:
            frequency = CheckFrequency.MONTHLY;
            break;
        case ComplianceCheckType.ANTIVIRUS:
            frequency = CheckFrequency.WEEKLY;
            break;
        // case ComplianceCheckType.LOCATION_SERVICES:
        case ComplianceCheckType.AGENT_INSTALLED:
        case ComplianceCheckType.FULL_COMPLIANCE:
        case ComplianceCheckType.LOCK_SCREEN:
        case ComplianceCheckType.OFFBOARDING:
        default:
            frequency = CheckFrequency.DAILY;
            break;
    }
    return frequency;
}

/**
 * Does the target compliance check need to be re-evaluated
 * for compliance based on its current state
 * @param {ComplianceCheck | DeviceComplianceCheck} complianceCheck
 * @returns {boolean}
 */
export function doesComplianceCheckNeedEvaluation(
    complianceCheck: ComplianceCheck | DeviceComplianceCheck,
): boolean {
    // init
    let needsCheck = false;
    // when not compliant, it should need a check asap
    if (complianceCheck.status === ComplianceCheckStatus.FAIL) {
        // set the flag
        needsCheck = true;
    } else {
        // we know the compliance check is currently compliant (== true)
        if (complianceCheck.checkFrequency !== CheckFrequency.ONCE) {
            // create a date with exp date
            const expirationDate = new Date(complianceCheck.expiresAt);
            // get a timestamp
            const today = new Date();
            // compliance check needed when we have hit the expiration date
            needsCheck = today > expirationDate;
        } // else there is no need to check again
    }
    // ready to return
    return needsCheck;
}

/**
 *
 * @param {DeviceComplianceCheck[]} complianceChecks
 * @param {ComplianceCheckType} type
 * @returns {boolean}
 */
export function isDeviceCompliant(
    complianceChecks: DeviceComplianceCheck[],
    type: ComplianceCheckType,
): boolean {
    let isCompliant: boolean;
    const isCompliantCheckFunction = ({ status }) => status === ComplianceCheckStatus.PASS;
    if (type === ComplianceCheckType.AGENT_INSTALLED) {
        isCompliant = complianceChecks.some(isCompliantCheckFunction);
    } else {
        isCompliant = complianceChecks.every(isCompliantCheckFunction);
    }
    return isCompliant;
}

/**
 *
 * @param {ComplianceCheck[]} complianceChecks
 * @returns {boolean}
 */
export function isPersonnelDeviceCompliant(complianceChecks: ComplianceCheck[]): boolean {
    const deviceComplianceChecks = complianceChecks.filter(complianceCheck =>
        DEVICE_VALID_COMPLIANCE_CHECKS.includes(complianceCheck.type),
    );
    return deviceComplianceChecks.every(
        deviceComplianceCheck =>
            deviceComplianceCheck.status === ComplianceCheckStatus.PASS ||
            deviceComplianceCheck.status === ComplianceCheckStatus.EXCLUDED,
    );
}

/**
 * Evaluates whether personnel should fail compliance due to training document status.
 * Applies customer-configured validation strictness.
 * @param userDocuments - Array of UserDocument objects representing training evidence
 * @param shouldResetTrainingForPersonnel - Auto-reset configuration flag
 * @returns {boolean} Compliance failure decision
 */
export function areUserDocumentsCompliant(
    userDocuments: UserDocument[],
    autoResetConfig: AutoResetConfig | null,
): boolean {
    const hasDocuments = hasAnyDocuments(userDocuments);
    if (!hasDocuments) {
        return false;
    }

    const autoResetEnabled = autoResetConfig && isAutoResetEnabled(autoResetConfig);
    if (!autoResetEnabled) {
        // Default lenient validation - only require documents exist
        return hasDocuments;
    }

    // Customer wants strict validation - check document expiration
    if (autoResetConfig.type === AutoResetType.GLOBAL_RESET) {
        return hasValidDocuments(userDocuments);
    }

    // For rolling reset, check documents created within the last year
    const oneYearAgoDate = new Date();
    oneYearAgoDate.setFullYear(oneYearAgoDate.getFullYear() - 1);
    return hasValidDocumentsAfterDate(userDocuments, oneYearAgoDate);
}

export function isDocumentValidAfterDate(
    renewalDate: Date,
    createdAt: Date,
    lastResetDate: Date,
): boolean {
    return !hasExpired(renewalDate) || moment.utc(createdAt).isAfter(moment.utc(lastResetDate));
}

function hasValidDocuments(userDocuments: UserDocument[]): boolean {
    return Boolean(
        userDocuments &&
            userDocuments.some(({ renewalDate }) => !hasExpired(new Date(renewalDate))),
    );
}

function hasValidDocumentsAfterDate(userDocuments: UserDocument[], lastResetDate: Date): boolean {
    return Boolean(
        userDocuments &&
            userDocuments.some(({ renewalDate, createdAt }) =>
                isDocumentValidAfterDate(new Date(renewalDate), createdAt, lastResetDate),
            ),
    );
}

function hasAnyDocuments(userDocuments: UserDocument[]): boolean {
    return Boolean(userDocuments && userDocuments.length > 0);
}

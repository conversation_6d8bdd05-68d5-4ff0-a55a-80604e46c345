import { AutoResetConfig } from 'app/settings/types/auto-reset-config.type';
import { UserDocument } from 'app/users/entities/user-document.entity';
import { AutoResetType } from 'commons/enums/security-training/auto-reset-type.enum';
import { UserDocumentType } from 'commons/enums/users/user-document-type.enum';
import { areUserDocumentsCompliant } from 'commons/helpers/compliance-check.helper';

describe('ComplianceCheckHelper', () => {
    describe('areUserDocumentsCompliant', () => {
        const createMockUserDocument = (
            renewalDate: string,
            createdAt?: Date,
        ): Partial<UserDocument> => ({
            id: 1,
            type: UserDocumentType.SEC_TRAINING,
            renewalDate,
            name: 'test-document.pdf',
            createdAt: createdAt || new Date(),
        });

        const createAutoResetConfig = (type: AutoResetType): AutoResetConfig => ({
            type,
            month: 1,
            day: 1,
        });

        describe('when autoResetConfig is null', () => {
            it('should return true when documents exist (lenient validation)', () => {
                const userDocuments = [
                    createMockUserDocument('2020-01-01'), // Expired document
                ] as UserDocument[];

                const result = areUserDocumentsCompliant(userDocuments, null);
                expect(result).toBe(true);
            });

            it('should return false when no documents exist', () => {
                const userDocuments: UserDocument[] = [];

                const result = areUserDocumentsCompliant(userDocuments, null);
                expect(result).toBe(false);
            });
        });

        describe('when autoResetConfig has NO_AUTOMATION type', () => {
            const noAutomationConfig = createAutoResetConfig(AutoResetType.NO_AUTOMATION);

            it('should return true when documents exist (lenient validation)', () => {
                const userDocuments = [
                    createMockUserDocument('2020-01-01'), // Expired document
                ] as UserDocument[];

                const result = areUserDocumentsCompliant(userDocuments, noAutomationConfig);
                expect(result).toBe(true);
            });

            it('should return false when no documents exist', () => {
                const userDocuments: UserDocument[] = [];

                const result = areUserDocumentsCompliant(userDocuments, noAutomationConfig);
                expect(result).toBe(false);
            });
        });

        describe('when autoResetConfig has GLOBAL_RESET type', () => {
            const globalResetConfig = createAutoResetConfig(AutoResetType.GLOBAL_RESET);

            it('should return true when valid documents exist', () => {
                const futureDate = new Date();
                futureDate.setFullYear(futureDate.getFullYear() + 1);

                const userDocuments = [
                    createMockUserDocument(futureDate.toISOString().split('T')[0]),
                ] as UserDocument[];

                const result = areUserDocumentsCompliant(userDocuments, globalResetConfig);
                expect(result).toBe(true);
            });

            it('should return false when only expired documents exist', () => {
                const userDocuments = [
                    createMockUserDocument('2020-01-01'), // Expired document
                ] as UserDocument[];

                const result = areUserDocumentsCompliant(userDocuments, globalResetConfig);
                expect(result).toBe(false);
            });

            it('should return false when no documents exist', () => {
                const userDocuments: UserDocument[] = [];

                const result = areUserDocumentsCompliant(userDocuments, globalResetConfig);
                expect(result).toBe(false);
            });

            it('should return true when has mix of valid and expired documents', () => {
                const futureDate = new Date();
                futureDate.setFullYear(futureDate.getFullYear() + 1);

                const userDocuments = [
                    createMockUserDocument('2020-01-01'), // Expired document
                    createMockUserDocument(futureDate.toISOString().split('T')[0]), // Valid document
                ] as UserDocument[];

                const result = areUserDocumentsCompliant(userDocuments, globalResetConfig);
                expect(result).toBe(true);
            });
        });

        describe('when autoResetConfig has ROLLING_RESET type', () => {
            const rollingResetConfig = createAutoResetConfig(AutoResetType.ROLLING_RESET);

            it('should return true when valid documents exist after one year ago', () => {
                const futureDate = new Date();
                futureDate.setFullYear(futureDate.getFullYear() + 1);
                const recentCreatedAt = new Date();
                recentCreatedAt.setMonth(recentCreatedAt.getMonth() - 6); // 6 months ago

                const userDocuments = [
                    createMockUserDocument(futureDate.toISOString().split('T')[0], recentCreatedAt),
                ] as UserDocument[];

                const result = areUserDocumentsCompliant(userDocuments, rollingResetConfig);
                expect(result).toBe(true);
            });

            it('should return false when no documents exist', () => {
                const userDocuments: UserDocument[] = [];

                const result = areUserDocumentsCompliant(userDocuments, rollingResetConfig);
                expect(result).toBe(false);
            });

            it('should return true when has mix of documents but at least one is valid and recent', () => {
                const futureDate = new Date();
                futureDate.setFullYear(futureDate.getFullYear() + 1);
                const recentCreatedAt = new Date();
                recentCreatedAt.setMonth(recentCreatedAt.getMonth() - 6); // 6 months ago
                const oldCreatedAt = new Date();
                oldCreatedAt.setFullYear(oldCreatedAt.getFullYear() - 2); // 2 years ago

                const userDocuments = [
                    createMockUserDocument('2020-01-01', oldCreatedAt), // Expired and old
                    createMockUserDocument(futureDate.toISOString().split('T')[0], recentCreatedAt), // Valid and recent
                ] as UserDocument[];

                const result = areUserDocumentsCompliant(userDocuments, rollingResetConfig);
                expect(result).toBe(true);
            });
        });
    });
});

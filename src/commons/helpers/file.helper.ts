import { ErrorCode } from '@drata/enums';
import { BadRequestException } from '@nestjs/common';
import { addLeadingSlash } from '@nestjs/common/utils/shared.utils';
import { Account } from 'auth/entities/account.entity';
import { insertTextBeforeFileExtension } from 'commons/helpers/string.helper';
import { FileTypes } from 'commons/maps/file-extensions-and-mime-types-validation.map';
import { FileBufferType } from 'commons/types/file-buffer.type';
import { SymlinkType } from 'commons/types/symlink.type';
import config from 'config';
import { format } from 'date-fns';
import fs from 'fs';
import hbs from 'hbs';
import { isEmpty, replace } from 'lodash';
import moment from 'moment';
import path from 'path';
import { PolloMessage } from 'pollo-logger/classes/pollo.message';
import { PolloLogger } from 'pollo-logger/pollo.logger';

type FileNameInfo = {
    fileName: string;
    fileExtension: string;
};

type ManifestEntry = {
    originalPath: string;
    abbreviatedPath: string;
    abbreviatedFilename: string;
};

function validateOriginalFileNameOrFail(originalFileName: string): void {
    if (originalFileName.length >= config.get('db.varcharLength')) {
        throw new BadRequestException(ErrorCode[ErrorCode.INVALID_FILE_NAME]);
    }
}

function getFileNameFromPath(file: string): FileNameInfo {
    const fileName: string = path.basename(file);
    const fileExtension = path.extname(file);

    return {
        fileName,
        fileExtension,
    };
}

function sanitizeFileName(name: string): string {
    return replace(name, /(?:\.(?![^.]+$)|[^\w.]+)/g, '-');
}

/**
 * Based on local dev or on a server
 * the system needs to know to load the file
 * on the "src" or "dist" path.
 *
 * @param filePath The file path after "src" or "dist"
 */
function pathResolve(filePath: string): string {
    const prefix = config.get('api.pathPrefix');

    if (isEmpty(filePath) || isEmpty(prefix)) {
        return '';
    }

    return path.resolve(`${prefix}${addLeadingSlash(filePath)}`);
}

function makeFileNamesUniqueByEnumeration<T extends FileBufferType>(files: T[]): T[] {
    const fileNameMapAndCount = new Map<string, number>();

    return files.map(item => {
        const originalFileName = item.filename;
        const itemNameAlreadyInList = fileNameMapAndCount.get(originalFileName);
        if (itemNameAlreadyInList) {
            item.filename = insertTextBeforeFileExtension(
                originalFileName,
                `_${itemNameAlreadyInList}`,
            );
        }
        const updatedNameCount = itemNameAlreadyInList ? itemNameAlreadyInList + 1 : 1;
        fileNameMapAndCount.set(originalFileName, updatedNameCount);

        return item;
    });
}

function getMimeTypeFromFileTypes(fileName: string): string | null {
    const extension = fileName.split('.').pop()?.toLowerCase();

    if (!extension) return null;

    for (const [, fileTypes] of FileTypes.entries()) {
        for (const fileType of fileTypes) {
            if (fileType.extension.toLowerCase() === `.${extension}`) {
                return fileType.mimeTypes[0] || null;
            }
        }
    }

    return null;
}

function sortByDCFNumber(a: string, b: string) {
    const extractDCFNumber = (filename: string): number | null => {
        const match = filename.match(/DCF(\d+)-/);
        return match ? parseInt(match[1], 10) : null;
    };

    const dcfA = extractDCFNumber(a);
    const dcfB = extractDCFNumber(b);

    // If both have DCF numbers, sort by DCF number
    if (dcfA !== null && dcfB !== null) {
        return dcfA - dcfB;
    }

    // If only A has DCF number, A comes first
    if (dcfA !== null && dcfB === null) {
        return -1;
    }

    // If only B has DCF number, B comes first
    if (dcfA === null && dcfB !== null) {
        return 1;
    }

    // If neither has DCF number, sort alphabetically
    return a.localeCompare(b);
}

/**
 * Generates an HTML manifest file that maps abbreviated filenames to their original paths.
 * The HTML includes clickable links that show the original routes.
 */
function generateHtmlManifest(
    manifestEntries: ManifestEntry[],
    options?: { title?: string },
): string {
    const viewsControlViewPath = path.join(__dirname, '../../../views/control-evidence-package');
    const title = options?.title || 'Drata Evidence Manifest';

    if (manifestEntries.length === 0) {
        const emptyTemplatePath = path.join(
            __dirname,
            `${viewsControlViewPath}/evidence-manifest-empty.hbs`,
        );
        const emptySource = fs.readFileSync(emptyTemplatePath, 'utf8');
        const emptyTemplate = hbs.handlebars.compile(emptySource);
        return emptyTemplate({ title });
    }

    // Sort files with symlinks by DCF number if applies
    const hasDcfNumber = manifestEntries.some(paths => /DCF(\d+)-/.test(paths.originalPath));

    if (hasDcfNumber) {
        manifestEntries.sort((a, b) => sortByDCFNumber(a.originalPath, b.originalPath));
    } else {
        // sort files alphabetically
        manifestEntries.sort((a, b) => {
            return a.originalPath.localeCompare(b.originalPath);
        });
    }

    // Prepare data for template
    const templateData = manifestEntries.map(
        ({ originalPath, abbreviatedPath, abbreviatedFilename }) => {
            const controlName = originalPath.split('/').shift();
            const displayPath = originalPath.replace(controlName + '/', '');

            return {
                controlName,
                displayPath,
                abbreviatedPath,
                abbreviatedFilename,
            };
        },
    );

    // Load and compile template
    const templatePath = path.join(
        __dirname,
        `${viewsControlViewPath}/evidence-manifest-empty.hbs`,
    );
    const source = fs.readFileSync(templatePath, 'utf8');
    const template = hbs.handlebars.compile(source);

    return template({
        title,
        manifestEntries: templateData,
    });
}

function abbreviateFilenames(
    account: Account,
    evidenceFilesJson: {
        stream: string;
        filename: string;
    }[],
    symlinks: SymlinkType[],
) {
    const logger = PolloLogger.logger();
    const manifest: ManifestEntry[] = [];
    const controlEvidencePath = 'ControlEvidence/Evidence/';

    // Sort files by DCF number if applies
    const hasDcfNumber = evidenceFilesJson.some(file => /DCF(\d+)-/.test(file.filename));

    if (hasDcfNumber) {
        evidenceFilesJson.sort((a, b) => sortByDCFNumber(a.filename, b.filename));
    } else {
        // sort files alphabetically (for Custom Frameworks)
        evidenceFilesJson.sort((a, b) => {
            return a.filename.localeCompare(b.filename);
        });
    }

    // Abbreviate filenames
    evidenceFilesJson
        .filter(({ filename }) => filename.includes(controlEvidencePath))
        .forEach((file, index) => {
            const dirs = file.filename.split('/');
            const fileName = dirs.pop() as unknown as string;
            const extension = path.extname(fileName);
            const abbreviatedFileName = index + 1;
            file.filename = `${controlEvidencePath}${abbreviatedFileName}${extension}`; // updates by reference

            manifest.push({
                originalPath: `${dirs.join('/').replace(controlEvidencePath, '')}/${fileName}`,
                abbreviatedPath: file.filename,
                abbreviatedFilename: `${abbreviatedFileName}${extension}`,
            });
        });

    // Add symlinks that live in the Evidence folder to the manifest
    symlinks
        .filter(symlink => symlink.link.includes(controlEvidencePath))
        .forEach(symlink => {
            const originalPathManifestEntry = manifest.find(paths => {
                const firstDirOfOriginalPath = paths.originalPath.split('/')[0]; // Personnel Evidence, Vendor info, etc
                const linkParts = symlink.link.split('/');
                // Validate expected structure: ControlEvidence/Evidence/{controlFolder}/{evidenceSymlink}
                if (linkParts.length < 4) {
                    logger.warn(
                        PolloMessage.msg(
                            `Unexpected symlink structure: ${symlink.link}`,
                        ).setIdentifier({ accountId: account.id, context: 'abbreviateFilenames' }),
                    );
                    return false;
                }
                const symlinkDirectoryFromControl = linkParts[3];
                return symlinkDirectoryFromControl.includes(firstDirOfOriginalPath);
            });

            if (originalPathManifestEntry) {
                const controlFolder = symlink.link.replace(controlEvidencePath, '').split('/')[0];
                const originalFilePath = originalPathManifestEntry.originalPath;

                manifest.push({
                    originalPath: controlFolder + '/' + originalFilePath,
                    abbreviatedPath: originalPathManifestEntry.abbreviatedPath,
                    abbreviatedFilename: originalPathManifestEntry.abbreviatedFilename,
                });
            }
        });

    // Generate HTML manifest content
    const htmlControlEvidenceContent = generateHtmlManifest(manifest);

    // Add manifest.html to evidenceFilesJson
    evidenceFilesJson.push({
        filename: 'evidence_manifest.html',
        stream: Buffer.from(htmlControlEvidenceContent).toString('base64'),
    });

    // Create manifest files for each requirement
    abbreviateSymbolicLinksInRequirementFolders(evidenceFilesJson, symlinks, manifest);
}

function abbreviateSymbolicLinksInRequirementFolders(
    evidenceFilesJson: {
        stream: string;
        filename: string;
    }[],
    symlinks: SymlinkType[],
    manifest: ManifestEntry[],
) {
    const REQUIREMENT_FOLDER_TO_EVIDENCE_RELATIVE_PATH = '../../../';
    const CONTROL_EVIDENCE_PATH = 'ControlEvidence/Evidence/';
    const REQUIREMENT_FOLDER_INDEX = 3;

    const requirementFolders = symlinks
        .filter(
            symlink =>
                !symlink.link.includes(CONTROL_EVIDENCE_PATH) &&
                symlink.link.includes('ControlEvidence') &&
                symlink.link.split('/').length >= REQUIREMENT_FOLDER_INDEX,
        )
        .reduce(
            (requirementControlsMap, symlink) => {
                const dirs = symlink.link.split('/');
                const requirementFolderPath = dirs.slice(0, REQUIREMENT_FOLDER_INDEX).join('/'); // Pattern: ControlEvidence/<Framework>/<Requirement>
                const symlinkName = symlink.link.replace(requirementFolderPath + '/', '');

                if (!requirementControlsMap[requirementFolderPath]) {
                    requirementControlsMap[requirementFolderPath] = [];
                }
                requirementControlsMap[requirementFolderPath].push(symlinkName);

                return requirementControlsMap;
            },
            {} as Record<string, string[]>,
        );

    Object.entries(requirementFolders).forEach(([requirementFolderPath, symbolicLinks]) => {
        const requirementManifest = symbolicLinks.reduce((acc, symlink) => {
            const originalPathManifestEntries = manifest.filter(paths => {
                return paths.originalPath.includes(symlink);
            });

            if (originalPathManifestEntries.length) {
                const newEntries = originalPathManifestEntries.map(entry => ({
                    ...entry,
                    abbreviatedPath:
                        REQUIREMENT_FOLDER_TO_EVIDENCE_RELATIVE_PATH + entry.abbreviatedPath, // Pattern: ControlEvidence/<Framework>/<Requirement>
                }));

                acc.push(...newEntries);
            }

            return acc;
        }, [] as ManifestEntry[]);

        const requirementFolder = requirementFolderPath.split('/').pop();

        const htmlRequirementManifestContent = generateHtmlManifest(requirementManifest, {
            title: `${requirementFolder} - Evidence Manifest`,
        });

        evidenceFilesJson.push({
            filename: requirementFolderPath + `/${requirementFolder}-evidence_manifest.html`,
            stream: Buffer.from(htmlRequirementManifestContent).toString('base64'),
        });
    });
}

function removeDuplicatedFiles(
    evidenceFilesJson: {
        stream: string;
        filename: string;
    }[],
) {
    const filenames = [...new Set(evidenceFilesJson.map(file => file.filename))];
    return filenames.map(
        filename =>
            evidenceFilesJson.find(file => file.filename === filename) as {
                stream: string;
                filename: string;
            },
    );
}

/*
 * Generate a common file name for monitor test reports
 * @param type - Report type ('included' for failing resources, 'excluded' for excluded results)
 * @param testId - The test ID
 * @param testName - The test name (will be sanitized)
 * @param date - Optional date to use (defaults to current date)
 * @returns Common file name without extension
 */
function generateMonitorTestReportFileName(
    type: 'included' | 'excluded',
    testId: number,
    testName: string,
    date?: Date,
): string {
    const reportType = type === 'included' ? 'Failing-Resources' : 'Excluded-Results';
    const sanitizedTestName = sanitizeFileName(testName);
    const formattedDate = format(date || moment().toDate(), 'MMddyyyy');

    return sanitizeFileName(
        `${reportType}-For-Test-${testId}-${sanitizedTestName}-${formattedDate}`,
    );
}

export {
    abbreviateFilenames,
    generateMonitorTestReportFileName,
    getFileNameFromPath,
    getMimeTypeFromFileTypes,
    makeFileNamesUniqueByEnumeration,
    pathResolve,
    removeDuplicatedFiles,
    sanitizeFileName,
    validateOriginalFileNameOrFail,
};

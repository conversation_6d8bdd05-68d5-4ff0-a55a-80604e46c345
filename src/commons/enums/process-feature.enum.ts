/**
 * Private ENUM: This enum was found to be used only in this repository during https://drata.atlassian.net/browse/ENG-62605.
 * Move to shared package in https://github.com/drata/platform/tree/release/packages/enums, if sharing is required.
 **/
export enum ProcessFeature {
    QUESTIONNAIRE_RESPONSE = 1,
    MONITOR_TEST_LOGIC = 2,
    EVENT_TEST_FAILURE = 3,
    EXTRACT_SECURITY_QUESTIONS = 4,
    ANSWERING_QUESTION = 5,
    ANSWER_SECURITY_QUESTION = 6,
    MONITOR_TEST_INSTRUCTIONS = 7,
    MON<PERSON>OR_TEST_TEMPLATE_INSTRUCTIONS = 8,
    VENDOR_CRITERIA_CREATION = 9,
    VENDOR_SECURITY_REVIEW_ASSESSMENT = 10,
    VENDOR_CRITERIA_QUESTIONS_CREATION = 11,
    // Add other process feature as needed
}

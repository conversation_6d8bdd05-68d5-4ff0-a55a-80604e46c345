import { type DrataDataSource } from 'commons/classes/drata-data-source.class';
import { AdminRole } from 'commons/enums/site-admin/admin-role.enum';
import { createManySiteAdmins } from 'commons/seeds/global-seeding.helper';
import { Seeding } from 'commons/seeds/seeding';
import { SiteAdmin } from 'site-admin/entities/site-admin.entity';
import { Factory, Seeder } from 'typeorm-seeding';

/**
 * Create the set of site admins for Drata
 */
export default class DrataSiteAdmins extends Seeding implements Seeder {
    // implement the function
    public async run(
        factory: Factory,
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        connection: DrataDataSource,
    ): Promise<any> {
        const adminRoles = [
            AdminRole.EMPLOYEE,
            AdminRole.ADMIN,
            AdminRole.ACT_AS,
            AdminRole.ACT_AS_READ_ONLY,
        ];
        // define the list
        const owners = [
            { firstName: 'Adam', lastName: '<PERSON><PERSON><PERSON>', email: '<EMAIL>', adminRoles },
            {
                firstName: '<PERSON>',
                lastName: '<PERSON><PERSON><PERSON>',
                email: 'hectorza<PERSON><EMAIL>',
                adminRoles,
            },
            {
                firstName: 'Harys',
                lastName: 'Vizcaino',
                email: '<EMAIL>',
                adminRoles,
            },
            {
                firstName: 'Daniel',
                lastName: 'Marashlian',
                email: '<EMAIL>',
                adminRoles,
            },
            {
                firstName: 'Manuel',
                lastName: 'Lizarraga',
                email: '<EMAIL>',
                adminRoles,
            },
            { firstName: 'John', lastName: 'Eisberg', email: '<EMAIL>', adminRoles },
            {
                firstName: 'Emmanuel',
                lastName: 'Rodriguez',
                email: '<EMAIL>',
                adminRoles,
            },
            { firstName: 'Dave', lastName: 'Knell', email: '<EMAIL>', adminRoles },
            { firstName: 'Tyler', lastName: 'Henderson', email: '<EMAIL>', adminRoles },
            { firstName: 'Omar', lastName: 'Ziranhua', email: '<EMAIL>', adminRoles },
            {
                firstName: 'Douglas',
                lastName: 'Mason',
                email: '<EMAIL>',
                adminRoles,
            },
            { firstName: 'Randy', lastName: 'Solton', email: '<EMAIL>', adminRoles },
            { firstName: 'Luis', lastName: 'Sanchez', email: '<EMAIL>', adminRoles },
            {
                firstName: 'Mark',
                lastName: 'Davenport',
                email: '<EMAIL>',
                adminRoles,
            },
            { firstName: 'Vitus', lastName: 'Pelsey', email: '<EMAIL>', adminRoles },
            { firstName: 'John', lastName: 'Sant', email: '<EMAIL>', adminRoles },
            {
                firstName: 'Marcela',
                lastName: 'Espinosa',
                email: '<EMAIL>',
                adminRoles,
            },
            { firstName: 'Nico', lastName: 'Sanchez', email: '<EMAIL>', adminRoles },
            { firstName: 'Harrison', lastName: 'Krat', email: '<EMAIL>', adminRoles },
            { firstName: 'Lior', lastName: 'Solomon', email: '<EMAIL>', adminRoles },
            { firstName: 'Rafael', lastName: 'Alvarado', email: '<EMAIL>', adminRoles },
            { firstName: 'Art', lastName: 'Ortega', email: '<EMAIL>', adminRoles },
            { firstName: 'James', lastName: 'Backert', email: '<EMAIL>', adminRoles },
            { firstName: 'Josef', lastName: 'Armenta', email: '<EMAIL>', adminRoles },
            { firstName: 'Manuel', lastName: 'Marquez', email: '<EMAIL>', adminRoles },
            {
                firstName: 'Jose Luis',
                lastName: 'Toledo',
                email: '<EMAIL>',
                adminRoles,
            },
            {
                firstName: 'Javier',
                lastName: 'Garcia',
                email: '<EMAIL>',
                adminRoles,
            },
            {
                firstName: 'David',
                lastName: 'Guillen',
                email: '<EMAIL>',
                adminRoles,
            },
            { firstName: 'Juan', lastName: 'Lopez', email: '<EMAIL>', adminRoles },
            { firstName: 'Josue', lastName: 'Gonzalez', email: '<EMAIL>', adminRoles },
            { firstName: 'Mauricio', lastName: 'Romo', email: '<EMAIL>', adminRoles },
            { firstName: 'Dana', lastName: 'Mauger', email: '<EMAIL>', adminRoles },
            { firstName: 'Houman', lastName: 'Haghighi', email: '<EMAIL>', adminRoles },
            { firstName: 'Ashley', lastName: 'Hyman', email: '<EMAIL>', adminRoles },
            { firstName: 'Lincoln', lastName: 'Race', email: '<EMAIL>', adminRoles },
            {
                firstName: 'Fernando',
                lastName: 'Rivera',
                email: '<EMAIL>',
                adminRoles,
            },
            {
                firstName: 'Fernando',
                lastName: 'De La Cruz',
                email: '<EMAIL>',
                adminRoles,
            },
            { firstName: 'Daniel', lastName: 'Sosa', email: '<EMAIL>', adminRoles },
            { firstName: 'Chris', lastName: 'Lundy', email: '<EMAIL>', adminRoles },
            { firstName: 'Antonio', lastName: 'Hernandez', email: '<EMAIL>', adminRoles },
            { firstName: 'Jason', lastName: 'Hatchett', email: '<EMAIL>', adminRoles },
            { firstName: 'Ari', lastName: 'Mojiri', email: '<EMAIL>', adminRoles },
            { firstName: 'Juan', lastName: 'Ibarra', email: '<EMAIL>', adminRoles },
            { firstName: 'Esteban', lastName: 'Ovalle', email: '<EMAIL>', adminRoles },
            {
                firstName: 'Tony',
                lastName: 'Gonzalez',
                email: '<EMAIL>',
                adminRoles,
            },
            { firstName: 'Jose', lastName: 'Orosco', email: '<EMAIL>', adminRoles },
            {
                firstName: 'James',
                lastName: 'Perkins',
                email: '<EMAIL>',
                adminRoles,
            },
            { firstName: 'Denver', lastName: 'Peterson', email: '<EMAIL>', adminRoles },
            { firstName: 'Brian', lastName: 'Elmi', email: '<EMAIL>', adminRoles },
            { firstName: 'Faraz', lastName: 'Yaghooti', email: '<EMAIL>', adminRoles },
            {
                firstName: 'Jhonnatan',
                lastName: 'Guerrero',
                email: '<EMAIL>',
                adminRoles,
            },
            {
                firstName: 'Aaron',
                lastName: 'Vega',
                email: '<EMAIL>',
                adminRoles,
            },
            { firstName: 'Alec', lastName: 'Barba', email: '<EMAIL>', adminRoles },
            {
                firstName: 'Ariel',
                lastName: 'Montoya',
                email: '<EMAIL>',
                adminRoles,
            },
            { firstName: 'Guerrero', lastName: 'Campos', email: '<EMAIL>', adminRoles },
            {
                firstName: 'Inna',
                lastName: 'Litinsky',
                email: '<EMAIL>',
                adminRoles,
            },
            { firstName: 'Eric', lastName: 'Thai', email: '<EMAIL>', adminRoles },
            { firstName: 'Ivan', lastName: 'Cabrera', email: '<EMAIL>', adminRoles },
            {
                firstName: 'Tyler',
                lastName: 'Pickett',
                email: '<EMAIL>',
                adminRoles,
            },
            { firstName: 'Jose', lastName: 'Zermeno', email: '<EMAIL>', adminRoles },
            { firstName: 'Aleida', lastName: 'Ramos', email: '<EMAIL>', adminRoles },
            {
                firstName: 'Chris',
                lastName: 'Milliano',
                email: '<EMAIL>',
                adminRoles,
            },
            { firstName: 'Osmar', lastName: 'Delgado', email: '<EMAIL>', adminRoles },
            { firstName: 'Axel', lastName: 'Vargas', email: '<EMAIL>', adminRoles },
            { firstName: 'Liam', lastName: 'Hession', email: '<EMAIL>', adminRoles },
            { firstName: 'Manny', lastName: 'Cocoba', email: '<EMAIL>', adminRoles },
            {
                firstName: 'Lillian',
                lastName: 'McGillivray',
                email: '<EMAIL>',
                adminRoles,
            },
            {
                firstName: 'Ashley',
                lastName: 'Lappies',
                email: '<EMAIL>',
                adminRoles,
            },
            { firstName: 'Kelly', lastName: 'Evans', email: '<EMAIL>', adminRoles },
            { firstName: 'Emily', lastName: 'Parr', email: '<EMAIL>', adminRoles },
            {
                firstName: 'Andrew',
                lastName: 'Morton',
                email: '<EMAIL>',
                adminRoles,
            },
            { firstName: 'Kevin', lastName: 'Phung', email: '<EMAIL>', adminRoles },
            { firstName: 'Lindsey', lastName: 'Morando', email: '<EMAIL>', adminRoles },
            { firstName: 'Federico', lastName: 'Lische', email: '<EMAIL>', adminRoles },
            { firstName: 'Ernie', lastName: 'Jimenez', email: '<EMAIL>', adminRoles },
            { firstName: 'Stephen', lastName: 'Ward', email: '<EMAIL>', adminRoles },
            { firstName: 'Arlo', lastName: 'Guthrie', email: '<EMAIL>', adminRoles },
            { firstName: 'Alex', lastName: 'Varela', email: '<EMAIL>', adminRoles },
            { firstName: 'Carmil', lastName: 'Thelemarque', email: '<EMAIL>', adminRoles },
            { firstName: 'Edelman', lastName: 'Gutierrez', email: '<EMAIL>', adminRoles },
            { firstName: 'Shane', lastName: 'Hook', email: '<EMAIL>', adminRoles },
            { firstName: 'Aaron', lastName: 'Waldman', email: '<EMAIL>', adminRoles },
            {
                firstName: 'Slawomir',
                lastName: 'Zabkiewicz',
                email: '<EMAIL>',
                adminRoles,
            },
            { firstName: 'Luciano', lastName: 'Almenares', email: '<EMAIL>', adminRoles },
            {
                firstName: 'Sangeetha',
                lastName: 'Munuswami',
                email: '<EMAIL>',
                adminRoles,
            },
            {
                firstName: 'Mario',
                lastName: 'Navarro',
                email: '<EMAIL>',
                adminRoles,
            },
            {
                firstName: 'Francisco',
                lastName: 'Bernabe',
                email: '<EMAIL>',
                adminRoles,
            },
            {
                firstName: 'Abraham',
                lastName: 'Talavera',
                email: '<EMAIL>',
                adminRoles,
            },
            { firstName: 'Hugo', lastName: 'Salazar', email: '<EMAIL>', adminRoles },
            {
                firstName: 'Graciela',
                lastName: 'Martinez',
                email: '<EMAIL>',
                adminRoles,
            },
            {
                firstName: 'Cecilia',
                lastName: 'Cortez',
                email: '<EMAIL>',
                adminRoles,
            },
            { firstName: 'Pratik', lastName: 'Bhat', email: '<EMAIL>', adminRoles },
            {
                firstName: 'Jacob',
                lastName: 'Hammontree',
                email: '<EMAIL>',
                adminRoles,
            },
            {
                firstName: 'Irving',
                lastName: 'Calzada',
                email: '<EMAIL>',
                adminRoles,
            },
            { firstName: 'Obed', lastName: 'Noriega', email: '<EMAIL>', adminRoles },
            { firstName: 'Artur', lastName: 'Krzywanski', email: '<EMAIL>', adminRoles },
            { firstName: 'Jakub', lastName: 'Gola', email: '<EMAIL>', adminRoles },
            {
                firstName: 'Rodel',
                lastName: 'Lominoque',
                email: '<EMAIL>',
                adminRoles,
            },
            {
                firstName: 'SereyVathna',
                lastName: 'Saroun',
                email: '<EMAIL>',
                adminRoles,
            },
            {
                firstName: 'Krzysztof',
                lastName: 'Skarbinski',
                email: '<EMAIL>',
                adminRoles,
            },
            { firstName: 'Austin', lastName: 'Ruby', email: '<EMAIL>', adminRoles },
            {
                firstName: 'Alejandra',
                lastName: 'Rodriguez',
                email: '<EMAIL>',
                adminRoles,
            },
            { firstName: 'Gordon', lastName: 'Krueger', email: '<EMAIL>', adminRoles },
            {
                firstName: 'Guillermo',
                lastName: 'Serrano',
                email: '<EMAIL>',
                adminRoles,
            },
            { firstName: 'Pedro', lastName: 'Torres', email: '<EMAIL>', adminRoles },
            { firstName: 'Luis', lastName: 'Gudino', email: '<EMAIL>', adminRoles },
            {
                firstName: 'Christopher',
                lastName: 'Santangelo',
                email: '<EMAIL>',
                adminRoles,
            },
            {
                firstName: 'Gamaliel',
                lastName: 'Medina',
                email: '<EMAIL>',
                adminRoles,
            },
            { firstName: 'Alan', lastName: 'Flores', email: '<EMAIL>', adminRoles },
            { firstName: 'Reid', lastName: 'von Gunten', email: '<EMAIL>', adminRoles },
            { firstName: 'Aaron', lastName: 'Junot', email: '<EMAIL>', adminRoles },
            { firstName: 'Taco', lastName: 'Taco', email: '<EMAIL>', adminRoles },
            { firstName: 'Howard', lastName: 'Carter', email: '<EMAIL>', adminRoles },
            { firstName: 'Dante', lastName: 'Gomez', email: '<EMAIL>', adminRoles },
            {
                firstName: 'Rachel',
                lastName: 'Gillett',
                email: '<EMAIL>',
                adminRoles,
            },
            { firstName: 'J', lastName: 'Drill', email: '<EMAIL>', adminRoles },
            { firstName: 'Monica', lastName: 'Finc', email: '<EMAIL>', adminRoles },
            {
                firstName: 'Christian',
                lastName: 'Lopez',
                email: '<EMAIL>',
                adminRoles,
            },
            {
                firstName: 'Paola',
                lastName: 'Miramontes',
                email: '<EMAIL>',
                adminRoles,
            },
            { firstName: 'Vania', lastName: 'Munoz', email: '<EMAIL>', adminRoles },
            {
                firstName: 'Mauricio',
                lastName: 'Muniz',
                email: '<EMAIL>',
                adminRoles,
            },
            { firstName: 'Jake', lastName: 'Stewart', email: '<EMAIL>', adminRoles },
            { firstName: 'Adam', lastName: 'Andrus', email: '<EMAIL>', adminRoles },
            { firstName: 'Astrid', lastName: 'Lopez', email: '<EMAIL>', adminRoles },
            { firstName: 'Ian', lastName: 'Jaffe', email: '<EMAIL>', adminRoles },
            {
                firstName: 'Evin',
                lastName: 'Whittington',
                email: '<EMAIL>',
                adminRoles,
            },
            { firstName: 'Aldo', lastName: 'Torres', email: '<EMAIL>', adminRoles },
            {
                firstName: 'Benjamin',
                lastName: 'Lezama',
                email: '<EMAIL>',
                adminRoles,
            },
            {
                firstName: 'Alan',
                lastName: 'Rodriguez',
                email: '<EMAIL>',
                adminRoles,
            },
            { firstName: 'Sergio', lastName: 'Soto', email: '<EMAIL>', adminRoles },
            {
                firstName: 'Daniel',
                lastName: 'Valles',
                email: '<EMAIL>',
                adminRoles,
            },
            { firstName: 'Hannah', lastName: 'Roddy', email: '<EMAIL>', adminRoles },
            {
                firstName: 'Gilberto',
                lastName: 'Lopez',
                email: '<EMAIL>',
                adminRoles,
            },
            {
                firstName: 'Bartosz',
                lastName: 'Kwiecien',
                email: '<EMAIL>',
                adminRoles,
            },
            { firstName: 'Matthew', lastName: 'Reid', email: '<EMAIL>', adminRoles },
            {
                firstName: 'Juan',
                lastName: 'Gonzales',
                email: '<EMAIL>',
                adminRoles,
            },
            { firstName: 'Joshua', lastName: 'Stuts', email: '<EMAIL>', adminRoles },
            {
                firstName: 'Mamatha',
                lastName: 'Parekodi',
                email: '<EMAIL>',
                adminRoles,
            },
            {
                firstName: 'Javier',
                lastName: 'Ruvalcaba',
                email: '<EMAIL>',
                adminRoles,
            },
            {
                firstName: 'Andres',
                lastName: 'Pineda',
                email: '<EMAIL>',
                adminRoles,
            },
            {
                firstName: 'Rodrigo',
                lastName: 'Mejia',
                email: '<EMAIL>',
                adminRoles,
            },
            { firstName: 'Nidhi', lastName: 'Gandhi', email: '<EMAIL>', adminRoles },
            { firstName: 'Kian', lastName: 'Falcone', email: '<EMAIL>', adminRoles },
            { firstName: 'Eli', lastName: 'Grady', email: '<EMAIL>', adminRoles },
            { firstName: 'Kyle', lastName: 'Nas', email: '<EMAIL>', adminRoles },
            { firstName: 'Ilya', lastName: 'Pisman', email: '<EMAIL>', adminRoles },
            {
                firstName: 'Anton',
                lastName: 'Kachurin',
                email: '<EMAIL>',
                adminRoles,
            },
            {
                firstName: 'Dominik',
                lastName: 'Zawrotny',
                email: '<EMAIL>',
                adminRoles,
            },
            { firstName: 'Pawel', lastName: 'Chmiel', email: '<EMAIL>', adminRoles },
            {
                firstName: 'Alexandru',
                lastName: 'Macsim',
                email: '<EMAIL>',
                adminRoles,
            },
            {
                firstName: 'Christopher',
                lastName: 'Korokeyi',
                email: '<EMAIL>',
                adminRoles,
            },
            {
                firstName: 'Vijay',
                lastName: 'Ilankamban',
                email: '<EMAIL>',
                adminRoles,
            },
            {
                firstName: 'Ernesto',
                lastName: 'Celis',
                email: '<EMAIL>',
                adminRoles,
            },
            {
                firstName: 'Alejandro',
                lastName: 'Crispin',
                email: '<EMAIL>',
                adminRoles,
            },
            {
                firstName: 'Brandom',
                lastName: 'Rodriguez',
                email: '<EMAIL>',
                adminRoles,
            },
            {
                firstName: 'Samuel',
                lastName: 'Coronado',
                email: '<EMAIL>',
                adminRoles,
            },
            {
                firstName: 'Christian',
                lastName: 'Lau',
                email: '<EMAIL>',
                adminRoles,
            },
            {
                firstName: 'Sergio',
                lastName: 'Cienfuegos',
                email: '<EMAIL>',
                adminRoles,
            },
            {
                firstName: 'Galdino',
                lastName: 'Manzanero',
                email: '<EMAIL>',
                adminRoles,
            },
            {
                firstName: 'Mackenzie',
                lastName: 'Bateman',
                email: '<EMAIL>',
                adminRoles,
            },
            {
                firstName: 'Santiago',
                lastName: 'Patiño',
                email: '<EMAIL>',
                adminRoles,
            },
            {
                firstName: 'Jesus',
                lastName: 'Beltran',
                email: '<EMAIL>',
                adminRoles,
            },
            { firstName: 'Angel', lastName: 'Davila', email: '<EMAIL>', adminRoles },
            {
                firstName: 'Emmanuel',
                lastName: 'Juarez',
                email: '<EMAIL>',
                adminRoles,
            },
            { firstName: 'Isai', lastName: 'Madueno', email: '<EMAIL>', adminRoles },
            { firstName: 'Joyce', lastName: 'Liang', email: '<EMAIL>', adminRoles },
            {
                firstName: 'Jonathan',
                lastName: 'Hernandez',
                email: '<EMAIL>',
                adminRoles,
            },
            {
                firstName: 'Miguel',
                lastName: 'Plascencia',
                email: '<EMAIL>',
                adminRoles,
            },
            { firstName: 'Raul', lastName: 'Torres', email: '<EMAIL>', adminRoles },
            { firstName: 'Yael', lastName: 'Lira', email: '<EMAIL>', adminRoles },
            {
                firstName: 'Savid',
                lastName: 'Salazar',
                email: '<EMAIL>',
                adminRoles,
            },
            {
                firstName: 'Elliot',
                lastName: 'Schaff',
                email: '<EMAIL>',
                adminRoles,
            },
            { firstName: 'Carlos', lastName: 'Gomez', email: '<EMAIL>', adminRoles },
            { firstName: 'Julio', lastName: 'Carozo', email: '<EMAIL>', adminRoles },
            {
                firstName: 'Sergio',
                lastName: 'Zermeno',
                email: '<EMAIL>',
                adminRoles,
            },
            {
                firstName: 'Leonardo',
                lastName: 'Diaz',
                email: '<EMAIL>',
                adminRoles,
            },
            {
                firstName: 'Arturo',
                lastName: 'Balsimelli',
                email: '<EMAIL>',
                adminRoles,
            },
            { firstName: 'Pedro', lastName: 'Cruz', email: '<EMAIL>', adminRoles },
            { firstName: 'Hazel', lastName: 'Loya', email: '<EMAIL>', adminRoles },
            {
                firstName: 'Gabriel',
                lastName: 'Perez',
                email: '<EMAIL>',
                adminRoles,
            },
            {
                firstName: 'Dhakshanya',
                lastName: 'Gangatharan',
                email: '<EMAIL>',
                adminRoles,
            },
            {
                firstName: 'Francisco',
                lastName: 'Camacho',
                email: '<EMAIL>',
                adminRoles,
            },
            {
                firstName: 'Hannah',
                lastName: 'Starcevich',
                email: '<EMAIL>',
                adminRoles,
            },
            {
                firstName: 'Alnair',
                lastName: 'Gonzalez',
                email: '<EMAIL>',
                adminRoles,
            },
            { firstName: 'Carlos', lastName: 'Lopez', email: '<EMAIL>', adminRoles },
            { firstName: 'Alex', lastName: 'Koh', email: '<EMAIL>', adminRoles },
            { firstName: 'Bob', lastName: 'Ciotti', email: '<EMAIL>', adminRoles },
            {
                firstName: 'Victor',
                lastName: 'Rebolloso',
                email: '<EMAIL>',
                adminRoles,
            },
            { firstName: 'Ivan', lastName: 'Osorio', email: '<EMAIL>', adminRoles },
            {
                firstName: 'Eduardo',
                lastName: 'Ferreira',
                email: '<EMAIL>',
                adminRoles,
            },
            {
                firstName: 'Cameron',
                lastName: 'Loughman',
                email: '<EMAIL>',
                adminRoles,
            },
            { firstName: 'Alvin', lastName: 'Accad', email: '<EMAIL>', adminRoles },
            {
                firstName: 'Octavio',
                lastName: 'Palacios',
                email: '<EMAIL>',
                adminRoles,
            },
            { firstName: 'Sai', lastName: 'Mohan', email: '<EMAIL>', adminRoles },
            {
                firstName: 'Araceli',
                lastName: 'Juarez',
                email: '<EMAIL>',
                adminRoles,
            },
            {
                firstName: 'Luisa',
                lastName: 'Peralta',
                email: '<EMAIL>',
                adminRoles,
            },
            {
                firstName: 'Raúl',
                lastName: 'Rivadeneyra',
                email: '<EMAIL>',
                adminRoles,
            },
            {
                firstName: 'Miguel',
                lastName: 'Bonachea',
                email: '<EMAIL>',
                adminRoles,
            },
            {
                firstName: 'Jesus',
                lastName: 'Vadillo',
                email: '<EMAIL>',
                adminRoles,
            },
            {
                firstName: 'Edgar',
                lastName: 'Alcantara',
                email: '<EMAIL>',
                adminRoles,
            },
            {
                firstName: 'Dwigth',
                lastName: 'Astacio',
                email: '<EMAIL>',
                adminRoles,
            },
            { firstName: 'Gabriel', lastName: 'Soto', email: '<EMAIL>', adminRoles },
            { firstName: 'Antonio', lastName: 'Medina', email: '<EMAIL>', adminRoles },
            {
                firstName: 'Isaias',
                lastName: 'Hinojosa',
                email: '<EMAIL>',
                adminRoles,
            },
            {
                firstName: 'Fernando',
                lastName: 'Gonzalez',
                email: '<EMAIL>',
                adminRoles,
            },
            {
                firstName: 'Daniel',
                lastName: 'Caballero',
                email: '<EMAIL>',
                adminRoles,
            },
            { firstName: 'Norman', lastName: 'Lau', email: '<EMAIL>', adminRoles },
            {
                firstName: 'Juliana',
                lastName: 'Munoz',
                email: '<EMAIL>',
                adminRoles,
            },
            { firstName: 'César', lastName: 'Lomelí', email: '<EMAIL>', adminRoles },
            {
                firstName: 'Amulya',
                lastName: 'Kandikonda',
                email: '<EMAIL>',
                adminRoles,
            },
            {
                firstName: 'Alejandro',
                lastName: 'Estrada',
                email: '<EMAIL>',
                adminRoles,
            },
            { firstName: 'Juan', lastName: 'Vanegas', email: '<EMAIL>', adminRoles },
            { firstName: 'Sergio', lastName: 'Gomez', email: '<EMAIL>', adminRoles },
            {
                firstName: 'Akanksha',
                lastName: 'Nguyen',
                email: '<EMAIL>',
                adminRoles,
            },
            {
                firstName: 'Christian',
                lastName: 'Diaz',
                email: '<EMAIL>',
                adminRoles,
            },
            { firstName: 'Mark', lastName: 'Uy', email: '<EMAIL>', adminRoles },
            { firstName: 'Sahid', lastName: 'Ayala', email: '<EMAIL>', adminRoles },
            {
                firstName: 'Isaac',
                lastName: 'Gonzalez',
                email: '<EMAIL>',
                adminRoles,
            },
            { firstName: 'Angel', lastName: 'Roca', email: '<EMAIL>', adminRoles },
            {
                firstName: 'Nolan',
                lastName: 'Iriarte',
                email: '<EMAIL>',
                adminRoles,
            },
            {
                firstName: 'Laila',
                lastName: 'Porte Petit',
                email: '<EMAIL>',
                adminRoles,
            },
            {
                firstName: 'Juan Miguel',
                lastName: 'de la Torre Loza',
                email: '<EMAIL>',
                adminRoles,
            },
            {
                firstName: 'Gerardo',
                lastName: 'Chavez',
                email: '<EMAIL>',
                adminRoles,
            },
            {
                firstName: 'Vincent',
                lastName: 'Grosso',
                email: '<EMAIL>',
                adminRoles,
            },
            {
                firstName: 'Marcos',
                lastName: 'Alvarez',
                email: '<EMAIL>',
                adminRoles,
            },
            { firstName: 'Ivan', lastName: 'Lopez', email: '<EMAIL>', adminRoles },
            {
                firstName: 'Daniel',
                lastName: 'Garcia',
                email: '<EMAIL>',
                adminRoles,
            },
            { firstName: 'Israel', lastName: 'Mata', email: '<EMAIL>', adminRoles },
            {
                firstName: 'Keri',
                lastName: 'Aldahondo',
                email: '<EMAIL>',
                adminRoles,
            },
            {
                firstName: 'Brian',
                lastName: 'Stanforth',
                email: '<EMAIL>',
                adminRoles,
            },
            {
                firstName: 'Michael',
                lastName: 'Patterson',
                email: '<EMAIL>',
                adminRoles,
            },
            {
                firstName: 'Serge',
                lastName: 'Zhivotovsky',
                email: '<EMAIL>',
                adminRoles,
            },
            { firstName: 'Roy', lastName: 'Luo', email: '<EMAIL>', adminRoles },
            { firstName: 'Ashish', lastName: 'Jha', email: '<EMAIL>', adminRoles },
            { firstName: 'Ed', lastName: 'Rubio', email: '<EMAIL>', adminRoles },
            { firstName: 'Lilia', lastName: 'Flores', email: '<EMAIL>', adminRoles },
            {
                firstName: 'Sam',
                lastName: 'Ringleman',
                email: '<EMAIL>',
                adminRoles,
            },
            {
                firstName: 'Mackenzie',
                lastName: 'Chyatte',
                email: '<EMAIL>',
                adminRoles,
            },
            {
                firstName: 'Isaac',
                lastName: 'Palacios',
                email: '<EMAIL>',
                adminRoles,
            },
            {
                firstName: 'Cristobal',
                lastName: 'Rodriguez',
                email: '<EMAIL>',
                adminRoles,
            },
            {
                firstName: 'Jesus',
                lastName: 'Gonzalez',
                email: '<EMAIL>',
                adminRoles,
            },
            {
                firstName: 'Jessica',
                lastName: 'Pardo',
                email: '<EMAIL>',
                adminRoles,
            },
            {
                firstName: 'Leonardo',
                lastName: 'Serrano',
                email: '<EMAIL>',
                adminRoles,
            },
            { firstName: 'Arturo', lastName: 'Renteria', email: '<EMAIL>', adminRoles },
            {
                firstName: 'Octavio',
                lastName: 'Santiago',
                email: '<EMAIL>',
                adminRoles,
            },
            { firstName: 'Abel', lastName: 'Fuentes', email: '<EMAIL>', adminRoles },
            {
                firstName: 'Ruben',
                lastName: 'Fajardo',
                email: '<EMAIL>',
                adminRoles,
            },
            {
                firstName: 'Andrea',
                lastName: 'Delgado',
                email: '<EMAIL>',
                adminRoles,
            },
            {
                firstName: 'Mateusz',
                lastName: 'Stachurzewski',
                email: '<EMAIL>',
                adminRoles,
            },
            {
                firstName: 'Jorge',
                lastName: 'Delgado',
                email: '<EMAIL>',
                adminRoles,
            },
            {
                firstName: 'Brayan',
                lastName: 'Prieto',
                email: '<EMAIL>',
                adminRoles,
            },
            {
                firstName: 'Sushil',
                lastName: 'Khadka',
                email: '<EMAIL>',
                adminRoles,
            },
            {
                firstName: 'Taylor',
                lastName: 'Kloustin',
                email: '<EMAIL>',
                adminRoles,
            },
            {
                firstName: 'Yerlinson',
                lastName: 'Maturana',
                email: '<EMAIL>',
                adminRoles,
            },
            {
                firstName: 'Aniket',
                lastName: 'Kulkarni',
                email: '<EMAIL>',
                adminRoles,
            },
            {
                firstName: 'Matthew',
                lastName: 'Traughber',
                email: '<EMAIL>',
                adminRoles,
            },
            { firstName: 'Abhi', lastName: 'Anand', email: '<EMAIL>', adminRoles },
            {
                firstName: 'Horacio',
                lastName: 'Espinosa',
                email: '<EMAIL>',
                adminRoles,
            },
            { firstName: 'Jose', lastName: 'Salazar', email: '<EMAIL>', adminRoles },
            {
                firstName: 'Arnoldo',
                lastName: 'Cortez y Quevedo',
                email: '<EMAIL>',
                adminRoles,
            },
            {
                firstName: 'Gonzalo',
                lastName: 'Lorieto',
                email: '<EMAIL>',
                adminRoles,
            },
            {
                firstName: 'Allie',
                lastName: 'Holcombe',
                email: '<EMAIL>',
                adminRoles,
            },
            { firstName: 'Huilun', lastName: 'Zhang', email: '<EMAIL>', adminRoles },
            {
                firstName: 'Alejandro',
                lastName: 'Díaz',
                email: '<EMAIL>',
                adminRoles,
            },
            {
                firstName: 'Santiago',
                lastName: 'Herrera',
                email: '<EMAIL>',
                adminRoles,
            },
            {
                firstName: 'Jose',
                lastName: 'Santacoloma',
                email: '<EMAIL>',
                adminRoles,
            },
            { firstName: 'Luis', lastName: 'Sosa', email: '<EMAIL>', adminRoles },
            { firstName: 'Josh', lastName: 'Cove', email: '<EMAIL>', adminRoles },
            { firstName: 'Jon', lastName: 'Wolfe', email: '<EMAIL>', adminRoles },
            {
                firstName: 'James',
                lastName: 'Carpino',
                email: '<EMAIL>',
                adminRoles,
            },
            { firstName: 'Devon', lastName: 'Henry', email: '<EMAIL>', adminRoles },
            {
                firstName: 'Josue',
                lastName: 'Marquez',
                email: '<EMAIL>',
                adminRoles,
            },
            {
                firstName: 'Michael',
                lastName: 'Risoli',
                email: '<EMAIL>',
                adminRoles,
            },
            {
                firstName: 'Gabriela',
                lastName: 'Zavalia',
                email: '<EMAIL>',
                adminRoles,
            },
            { firstName: 'Arif', lastName: 'Islam', email: '<EMAIL>', adminRoles },
            {
                firstName: 'Nicolas',
                lastName: 'Straub',
                email: '<EMAIL>',
                adminRoles,
            },
            {
                firstName: 'Christian',
                lastName: 'Camacho',
                email: '<EMAIL>',
                adminRoles,
            },
            { firstName: 'Tom', lastName: 'Belote', email: '<EMAIL>', adminRoles },
            {
                firstName: 'Shane',
                lastName: 'Tierney',
                email: '<EMAIL>',
                adminRoles,
            },
            { firstName: 'Ana', lastName: 'Rojas', email: '<EMAIL>', adminRoles },
            {
                firstName: 'Josie',
                lastName: 'Beaudoin',
                email: '<EMAIL>',
                adminRoles,
            },
            { firstName: 'Erick', lastName: 'Sosa', email: '<EMAIL>', adminRoles },
            { firstName: 'Jaji', lastName: 'Olajide', email: '<EMAIL>', adminRoles },
            { firstName: 'Casey', lastName: 'Arendt', email: '<EMAIL>', adminRoles },
            {
                firstName: 'Danny',
                lastName: 'Philayvanh',
                email: '<EMAIL>',
                adminRoles,
            },
            { firstName: 'Kirk', lastName: 'Rehal', email: '<EMAIL>', adminRoles },
            { firstName: 'Aakash', lastName: 'Shah', email: '<EMAIL>', adminRoles },
            {
                firstName: 'Kevin',
                lastName: 'Janssen',
                email: '<EMAIL>',
                adminRoles,
            },
            { firstName: 'Jane', lastName: 'Baik', email: '<EMAIL>', adminRoles },
            {
                firstName: 'Jess',
                lastName: 'Delgado Perez',
                email: '<EMAIL>',
                adminRoles,
            },
            {
                firstName: 'Brandon',
                lastName: 'Nicoll',
                email: '<EMAIL>',
                adminRoles,
            },
            {
                firstName: 'Saloni',
                lastName: 'Porwal',
                email: '<EMAIL>',
                adminRoles,
            },
            { firstName: 'Paolo', lastName: 'Posso', email: '<EMAIL>', adminRoles },
            {
                firstName: 'Jonn',
                lastName: 'Novaretti',
                email: '<EMAIL>',
                adminRoles,
            },
            { firstName: 'Hor-kan', lastName: 'Chan', email: '<EMAIL>', adminRoles },
            { firstName: 'Om', lastName: 'Vyas', email: '<EMAIL>', adminRoles },
            { firstName: 'Tony', lastName: 'Bentley', email: '<EMAIL>', adminRoles },
            { firstName: 'Satwik', lastName: 'Kommi', email: '<EMAIL>', adminRoles },
            { firstName: 'Alev', lastName: 'Viggio', email: '<EMAIL>', adminRoles },
            {
                firstName: 'Jonathan',
                lastName: 'Wong',
                email: '<EMAIL>',
                adminRoles,
            },
            {
                firstName: 'Daniel',
                lastName: 'Flores',
                email: '<EMAIL>',
                adminRoles,
            },
            {
                firstName: 'Stasi',
                lastName: 'Vladimirov',
                email: '<EMAIL>',
                adminRoles,
            },
            { firstName: 'Morgan', lastName: 'Such', email: '<EMAIL>', adminRoles },
            {
                firstName: 'Razvan',
                lastName: 'Tolbaru',
                email: '<EMAIL>',
                adminRoles,
            },
            { firstName: 'Jason', lastName: 'Hills', email: '<EMAIL>', adminRoles },
            { firstName: 'Michael', lastName: 'Krec', email: '<EMAIL>', adminRoles },
            { firstName: 'Ryan', lastName: 'Goodman', email: '<EMAIL>', adminRoles },
            { firstName: 'Ray', lastName: 'Lambert', email: '<EMAIL>', adminRoles },
            { firstName: 'Matt', lastName: 'Hillary', email: '<EMAIL>', adminRoles },
            { firstName: 'Joshua', lastName: 'Beck', email: '<EMAIL>', adminRoles },
            {
                firstName: 'Cedrick',
                lastName: 'Guzman',
                email: '<EMAIL>',
                adminRoles,
            },
            { firstName: 'Vince', lastName: 'Uy', email: '<EMAIL>', adminRoles },
            { firstName: 'Ramesh', lastName: 'Patel', email: '<EMAIL>', adminRoles },
            { firstName: 'Tim', lastName: 'Hansen', email: '<EMAIL>', adminRoles },
            {
                firstName: 'Diego',
                lastName: 'Burlando',
                email: '<EMAIL>',
                adminRoles,
            },
            { firstName: 'Dean', lastName: 'Clark', email: '<EMAIL>', adminRoles },
            { firstName: 'Johnny', lastName: 'Kinder', email: '<EMAIL>', adminRoles },
            {
                firstName: 'Stephen',
                lastName: 'Schwahn',
                email: '<EMAIL>',
                adminRoles,
            },
            {
                firstName: 'Tomasz',
                lastName: 'Gintowt',
                email: '<EMAIL>',
                adminRoles,
            },
            { firstName: 'Matt', lastName: 'Long', email: '<EMAIL>', adminRoles },
            {
                firstName: 'Gabriel',
                lastName: 'Tello',
                email: '<EMAIL>',
                adminRoles,
            },
            {
                firstName: 'Agustin',
                lastName: 'Giacchello',
                email: '<EMAIL>',
                adminRoles,
            },
            { firstName: 'Jake', lastName: 'Ayala', email: '<EMAIL>', adminRoles },
            { firstName: 'Kim', lastName: 'Seale', email: '<EMAIL>', adminRoles },
            {
                firstName: 'Aashima',
                lastName: 'Dhankhar',
                email: '<EMAIL>',
                adminRoles,
            },
            { firstName: 'Nisha', lastName: 'Pattan', email: '<EMAIL>', adminRoles },
            {
                firstName: 'Manuel',
                lastName: 'Hernandez',
                email: '<EMAIL>',
                adminRoles,
            },
            {
                firstName: 'Curtis',
                lastName: 'Fraser',
                email: '<EMAIL>',
                adminRoles,
            },
            {
                firstName: 'Automation',
                lastName: 'Admin',
                email: '<EMAIL>',
                adminRoles,
            },
            {
                firstName: 'Ryan',
                lastName: 'Pedersen',
                email: '<EMAIL>',
                adminRoles,
            },
            {
                firstName: 'Veeral',
                lastName: 'Shah',
                email: '<EMAIL>',
                adminRoles,
            },
            {
                firstName: 'Sean',
                lastName: 'McGovern',
                email: '<EMAIL>',
                adminRoles,
            },
            {
                firstName: 'Reed',
                lastName: 'Percival',
                email: '<EMAIL>',
                adminRoles,
            },
            {
                firstName: 'George',
                lastName: 'Perkins',
                email: '<EMAIL>',
                adminRoles,
            },
            {
                firstName: 'Akshay',
                lastName: 'Sharma',
                email: '<EMAIL>',
                adminRoles,
            },
            { firstName: 'Kat', lastName: 'Kenny', email: '<EMAIL>', adminRoles },
            {
                firstName: 'Mauricio',
                lastName: 'Raini',
                email: '<EMAIL>',
                adminRoles,
            },
            {
                firstName: 'Roy',
                lastName: 'Jimenez',
                email: '<EMAIL>',
                adminRoles,
            },
            {
                firstName: 'John',
                lastName: 'Delshadi',
                email: '<EMAIL>',
                adminRoles,
            },
            {
                firstName: 'Skeept',
                lastName: 'Espinoza',
                email: '<EMAIL>',
                adminRoles,
            },
            {
                firstName: 'Jose',
                lastName: 'Villa',
                email: '<EMAIL>',
                adminRoles,
            },
            {
                firstName: 'Rafael',
                lastName: 'Ramos',
                email: '<EMAIL>',
                adminRoles,
            },
            {
                firstName: 'Hancel',
                lastName: 'Avila',
                email: '<EMAIL>',
                adminRoles,
            },
            {
                firstName: 'Salvatore',
                lastName: 'Italiano',
                email: '<EMAIL>',
                adminRoles,
            },
            {
                firstName: 'Eduardo',
                lastName: 'Rendon',
                email: '<EMAIL>',
                adminRoles,
            },
            {
                firstName: 'Sebastian',
                lastName: 'Galvan',
                email: '<EMAIL>',
                adminRoles,
            },
            {
                firstName: 'Jorge',
                lastName: 'Torres',
                email: '<EMAIL>',
                adminRoles,
            },
            {
                firstName: 'Antonio',
                lastName: 'Torres',
                email: '<EMAIL>',
                adminRoles,
            },
            {
                firstName: 'Hendra',
                lastName: 'Wong',
                email: '<EMAIL>',
                adminRoles,
            },
            {
                firstName: 'Zachary',
                lastName: 'Hancock',
                email: '<EMAIL>',
                adminRoles,
            },
            {
                firstName: 'Juan',
                lastName: 'Viloria',
                email: '<EMAIL>',
                adminRoles,
            },
            {
                firstName: 'Elina',
                lastName: 'Garcia',
                email: '<EMAIL>',
                adminRoles,
            },
            {
                firstName: 'Eli',
                lastName: 'Perez-Figueroa',
                email: '<EMAIL>',
                adminRoles,
            },
            { firstName: 'Onyekachukwu', lastName: 'Onyerikwu', email: '<EMAIL>', adminRoles },
            {
                firstName: 'Angel',
                lastName: 'Quinonez',
                email: '<EMAIL>',
                adminRoles,
            },
            {
                firstName: 'Guillermo',
                lastName: 'Tlapa',
                email: '<EMAIL>',
                adminRoles,
            },
        ];
        const siteAdmins: SiteAdmin[] = await createManySiteAdmins(factory, owners);
        return { siteAdmins };
    }
}

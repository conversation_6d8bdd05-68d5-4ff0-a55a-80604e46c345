controls:
    - code: 'DCF-1'
      name: 'Customer Data Policies'
      description: '%s Management has approved all policies that detail how customer data may be made accessible and should be handled. These policies are accessible to all employees and contractors.'
      activity: "Maintain policies detailing how customer data is handled.\nEnsure policies are accessible to all employees."
      question: 'Does the organization maintain policies that detail how customer data may be made accessible and should be handled?'
      tests: []
      domain: 'Data & Privacy'
      category: 'Customer Data Policies'
    - code: 'DCF-2'
      name: 'Least-Privileged Policy for Sensitive Data Access'
      description: '%s authorizes access to information resources, including data and the systems that store or process sensitive data, based on the principle of least privilege.'
      activity: 'Authorize access to information resources based on least-privilege policy.'
      question: 'Does the organization utilize the concept of least privilege, allowing only authorized access to processes necessary to accomplish assigned tasks in accordance with organizational business functions?'
      tests: []
      domain: 'Data & Privacy'
      category: 'Customer Data Policies'
    - code: 'DCF-3'
      name: 'Encryption of Web-Based Management Interfaces'
      description: 'Administrator access to web-based management interfaces is encrypted with strong cryptographic algorithms.'
      activity: 'Use encryption to protect user authentication and web-based admin sessions.'
      question: 'Does the organization use encryption to protect user authentication on any internal administrator tools?'
      tests:
          - id: 4
      domain: 'Data & Privacy'
      category: 'Internal Admin Tool'
    - code: 'DCF-4'
      name: 'Version Control System'
      description: '%s uses a version control system to manage source code, change documentation and tracking, release labeling, and other change management tasks. Access to the version control system is restricted to authorized personnel.'
      activity: ''
      question: ''
      tests:
          - id: 5
          - id: 7
      domain: 'Internal Security Procedures'
      category: 'Software Development Life Cycle'
    - code: 'DCF-5'
      name: 'Change Review Process'
      description: 'Changes are peer-reviewed and approved prior to deployment by an individual different from the developer to maintain segregation of duties. Review requirements are enforced through automated mechanisms such as branch protection settings in the production code repository.'
      activity: ''
      question: ''
      tests:
          - id: 8
      domain: 'Internal Security Procedures'
      category: 'Software Development Life Cycle'
    - code: 'DCF-6'
      name: 'Production Changes Restricted'
      description: "Access to make changes in production environments is restricted to authorized personnel in accordance with segregation of duties principles and the company's documented policies and procedures."
      activity: ''
      question: ''
      tests:
          - id: 9
      domain: 'Internal Security Procedures'
      category: 'Software Development Life Cycle'
    - code: 'DCF-7'
      name: 'Separate Environments'
      description: 'Pre-production environments (e.g., development, testing, etc.) are separated from production environments and the separation is enforced with access controls.'
      activity: ''
      question: ''
      tests: []
      domain: 'Internal Security Procedures'
      category: 'Software Development Life Cycle'
    - code: 'DCF-8'
      name: 'External Communication Channels'
      description: '%s provides external communication mechanisms for customers and third parties (e.g., communication features, support portal, external ticketing system, etc.) to report complaints, failures, bugs, incidents, vulnerabilities, requests for information, etc. Customer communications are responded to within defined SLAs.'
      activity: ''
      question: ''
      tests:
          - id: 11
      domain: 'Internal Security Procedures'
      category: 'Responsible Disclosure Policy'
    - code: 'DCF-9'
      name: 'Internal Communication Channels'
      description: 'Internal communication channels are in place for employees to report failures, events, incidents, policy violations, concerns, and other issues to company management, including anonymous reporting channels if applicable.'
      activity: ''
      question: ''
      tests: []
      domain: 'Internal Security Procedures'
      category: 'Responsible Disclosure Policy'
    - code: 'DCF-10'
      name: 'Access Control Policy'
      description: '%s has developed and documented a policy that outlines requirements for access control.'
      activity: ''
      question: ''
      tests:
          - id: 13
      domain: 'Internal Security Procedures'
      category: 'Access Control'
    - code: 'DCF-11'
      name: 'Periodic Access Reviews'
      description: 'Management performs user access reviews periodically (as defined by policy and compliance requirements) to validate user accounts, including third party or vendor accounts, and their associated privileges remain appropriate. The review includes validation of logical and physical access as necessary. Changes resulting from the review, if any, are documented and implemented.'
      activity: ''
      question: ''
      tests: []
      domain: 'Internal Security Procedures'
      category: 'Access Control'
    - code: 'DCF-12'
      name: 'Baseline Configuration and Hardening Standards'
      description: '%s has identified and documented baseline security configuration standards for all system components in accordance with industry-accepted hardening standards or vendor recommendations. These standards are reviewed periodically and updated as needed (e.g., when vulnerabilities are identified) and verified to be in place before or immediately after a production system component is installed or modified (e.g., through infrastructure as code, configuration checklists, etc.).'
      activity: ''
      question: ''
      tests:
          - id: 292
          - id: 8018
      domain: 'Internal Security Procedures'
      category: 'Access Control'
    - code: 'DCF-13'
      name: 'Information Security Policies'
      description: '%s has defined and documented an information security policy and other topic-specific policies as needed to support the functioning of internal control.'
      activity: ''
      question: ''
      tests:
          - id: 16
      domain: 'Internal Security Procedures'
      category: 'Vulnerability Management'
    - code: 'DCF-14'
      name: 'Organizational Chart'
      description: "An organizational chart is in place to describe the organizational structure and reporting lines. The chart is available to all employees (e.g., through the company's HRMS, intranets, etc.) and is updated upon changes to the organizational structure."
      activity: ''
      question: ''
      tests:
          - id: 17
      domain: 'Internal Security Procedures'
      category: 'Vulnerability Management'
    - code: 'DCF-15'
      name: 'Risk Assessment Policy'
      description: "%s has defined and documented a process for risk assessment and risk management that outlines the organization's approach for identifying risks and assigning risk owners, the risk acceptance criteria, and the approach for evaluating and treating risks based on the defined criteria."
      activity: ''
      question: ''
      tests:
          - id: 18
      domain: 'Internal Security Procedures'
      category: 'Vulnerability Management'
    - code: 'DCF-16'
      name: 'Periodic Risk Assessment'
      description: '%s conducts risks assessments periodically as required by company policy and compliance requirements. The risk assessment includes consideration of threats and vulnerabilities and an evaluation of the likelihood and impact for each risk. A risk owner is assigned to each risk, and every risk is assigned a risk treatment option. Results of the risk assessment are documented (e.g., in a risk register).'
      activity: ''
      question: ''
      tests: []
      domain: 'Internal Security Procedures'
      category: 'Vulnerability Management'
    - code: 'DCF-17'
      name: 'Risk Treatment Plan'
      description: "%s's management has documented a risk treatment plan to formally manage risks identified in risk assessment activities."
      activity: ''
      question: ''
      tests: []
      domain: 'Internal Security Procedures'
      category: 'Vulnerability Management'
    - code: 'DCF-18'
      name: 'Vulnerability Scans'
      description: '%s conducts vulnerability scans of the production environment as dictated by company policy and compliance requirements. Results are reviewed by company personnel and vulnerabilities are tracked to resolution in accordance with company policies.'
      activity: ''
      question: ''
      tests:
          - id: 21
          - id: 212
          - id: 213
          - id: 235
          - id: 236
          - id: 237
          - id: 238
          - id: 239
          - id: 240
          - id: 241
          - id: 242
          - id: 282
          - id: 283
          - id: 284
          - id: 285
          - id: 286
          - id: 287
          - id: 288
          - id: 289
          - id: 313
          - id: 314
          - id: 315
          - id: 316
          - id: 317
          - id: 318
          - id: 319
          - id: 320
      domain: 'Internal Security Procedures'
      category: 'Vulnerability Management'
    - code: 'DCF-19'
      name: 'Penetration Tests'
      description: 'An external penetration test of production environments is performed by an independent third party periodically or after any significant infrastructure or application changes. Results are reviewed by management and vulnerabilities are tracked to resolution in accordance with company policies.'
      activity: ''
      question: ''
      tests: []
      domain: 'Internal Security Procedures'
      category: 'Vulnerability Management'
    - code: 'DCF-20'
      name: 'Asset Inventory'
      description: 'A centralized asset register is maintained for physical, cloud, and other assets that includes descriptive attributes for asset accountability such as owner, description, location, classification, and/or other information based on the type of asset. Processes are in place to maintain an updated inventory through manual reviews (e.g., as a result of new purchases, installations, removals, system changes, etc.) or automated mechanisms.'
      activity: ''
      question: ''
      tests: []
      domain: 'Internal Security Procedures'
      category: 'Vulnerability Management'
    - code: 'DCF-21'
      name: 'Architectural Diagram'
      description: 'A documented architectural diagram is in place to document system boundaries and support the functioning of internal control. The diagram is reviewed and approved by management at least annually and updated as necessary when there are changes to the environment.'
      activity: ''
      question: ''
      tests: []
      domain: 'Internal Security Procedures'
      category: 'Vulnerability Management'
    - code: 'DCF-22'
      name: 'Network Diagram'
      description: 'A documented network diagram is in place to document system boundaries and connections to external networks. The diagram is reviewed and approved by management at least annually and updated as necessary when there are changes to the environment.'
      activity: ''
      question: ''
      tests: []
      domain: 'Internal Security Procedures'
      category: 'Vulnerability Management'
    - code: 'DCF-23'
      name: 'Security Issues are Prioritized'
      description: '%s tracks and prioritizes security deficiencies through internal tools according to their severity by an independent technical resource.'
      activity: ''
      question: 'Does the organization ensure that vulnerabilities are properly identified, tracked and remediated?'
      tests: []
      domain: 'Internal Security Procedures'
      category: 'Security Issues'
    - code: 'DCF-24'
      name: 'SLA for Security Bugs'
      description: '%s tracks security deficiencies through internal tools and closes them within an SLA that management has pre-specified.'
      activity: ''
      question: 'Does the organization identify and assign a risk ranking to newly discovered security vulnerabilities and prioritize remediation based on the ranking?'
      tests: []
      domain: 'Internal Security Procedures'
      category: 'Security Issues'
    - code: 'DCF-25'
      name: 'Disaster Recovery Plan'
      description: '%s has a documented disaster recovery plan that outlines roles, responsibilities and detailed procedures for recovery of systems in the event of a disaster scenario.'
      activity: ''
      question: ''
      tests:
          - id: 28
      domain: 'Internal Security Procedures'
      category: 'Business Continuity'
    - code: 'DCF-26'
      name: 'BCP/DR Tests'
      description: '%s conducts tests of the business continuity/disaster recovery plans at least annually. Results and lessons learned are documented, and updates to the plans are made as necessary.'
      activity: ''
      question: ''
      tests: []
      domain: 'Internal Security Procedures'
      category: 'Business Continuity'
    - code: 'DCF-27'
      name: 'Cloud Resources Availability'
      description: 'Business-critical cloud resources are deployed in accordance with high availability architecture principles (e.g., replicated across multiple availability zones or regions, configured for high-availability, etc.).'
      activity: ''
      question: ''
      tests:
          - id: 30
          - id: 8004
          - id: 8036
      domain: 'Internal Security Procedures'
      category: 'Business Continuity'
    - code: 'DCF-28'
      name: 'Security Events Tracked and Evaluated'
      description: '%s evaluates security events to determine if they constitute an incident. Incidents are assigned a priority, categorized, documented, tracked, escalated, contained, eradicated, communicated, and resolved in accordance with company policies and procedures.'
      activity: ''
      question: ''
      tests:
          - id: 26
      domain: 'Internal Security Procedures'
      category: 'Incident Response Plan'
    - code: 'DCF-29'
      name: 'Incident Response Team'
      description: '%s has identified and documented roles and responsibilities for incident management (e.g., roles and responsibilities for invoking the incident management process, incident leads, incident handlers, communication coordinators, technical advisors, legal advisors, etc.).'
      activity: ''
      question: ''
      tests: []
      domain: 'Internal Security Procedures'
      category: 'Incident Response Plan'
    - code: 'DCF-30'
      name: 'Incident Response Lessons Learned Documented'
      description: '%s documents a post-mortem review for identified incidents that includes incident metadata, root-cause analysis, documentation of evidence, summary of containment, eradication, and recovery actions, timelines, incident metrics, evidence of internal and external communications, estimation of impact and scope, and lessons learned, as applicable, in accordance with company policies and procedures.'
      activity: ''
      question: ''
      tests: []
      domain: 'Internal Security Procedures'
      category: 'Incident Response Plan'
    - code: 'DCF-31'
      name: 'Software Development Policies'
      description: '%s has developed policies and procedures governing the system development life cycle, including requirements, design, implementation, testing, and deployment.'
      activity: ''
      question: ''
      tests:
          - id: 36
      domain: 'Organizational Security'
      category: 'Security Policies'
    - code: 'DCF-32'
      name: 'Security Policies'
      description: 'Company policies are accessible to all employees and, as applicable, third parties such as contractors. Personnel are required to acknowledge the information security policy and other topic-specific policies based on their job duties during onboarding and annually thereafter.'
      activity: ''
      question: ''
      tests:
          - id: 38
          - id: 49
          - id: 190
      domain: 'Organizational Security'
      category: 'Security Policies'
    - code: 'DCF-33'
      name: 'Periodic Policy Reviews'
      description: 'Management reviews and approves company policies at least annually. Updates to the policies are made as deemed necessary (e.g., based on changes to business objectives,  legal or regulatory requirements, organizational risks, etc.).'
      activity: ''
      question: ''
      tests:
          - id: 39
      domain: 'Organizational Security'
      category: 'Security Policies'
    - code: 'DCF-34'
      name: 'Security Team/Steering Committee'
      description: "%s has an assigned security team that is responsible for the design, implementation, management, and review of the organization's security policies, standards, baselines, procedures, and guidelines."
      activity: '1. Monthly meeting minutes of steering committee where they discuss security goals, initiatives and projects, including remediation of vulnerabilities'
      question: 'Does the organization provide a security controls oversight function?'
      tests: []
      domain: 'Organizational Security'
      category: 'Security Program'
    - code: 'DCF-35'
      name: 'Security Team Communicates in a Timely Manner'
      description: 'The security team communicates important information security events to company management in a timely manner.'
      activity: 'Tools such as Slack, PagerDuty, Drata to streamline and in many cases automate communication of security events'
      question: 'Does the organization ensure effective communication of security events to management in an efficient manner?'
      tests: []
      domain: 'Organizational Security'
      category: 'Security Program'
    - code: 'DCF-36'
      name: 'Periodic Security Training'
      description: '%s has established training programs to help personnel gain awareness of information security best practices. Personnel (including employees and contractors as applicable) are required to complete the training during onboarding. Periodic refresher training is provided to personnel at least annually and as deemed necessary (e.g., upon changes in security requirements, policies, regulations, etc.).'
      activity: ''
      question: ''
      tests:
          - id: 43
      domain: 'Organizational Security'
      category: 'Security Program'
    - code: 'DCF-37'
      name: 'Acceptable Use Policy'
      description: "%s has a documented acceptable use policy that outlines requirements for personnel's usage of company assets."
      activity: ''
      question: ''
      tests:
          - id: 44
          - id: 45
      domain: 'Organizational Security'
      category: 'Personnel Security'
    - code: 'DCF-38'
      name: 'Performance Evaluations'
      description: 'Management conducts periodic evaluations of performance against established goals and objectives for eligible personnel in accordance with company policies and procedures.'
      activity: ''
      question: ''
      tests:
          - id: 46
      domain: 'Organizational Security'
      category: 'Personnel Security'
    - code: 'DCF-39'
      name: 'Background Checks'
      description: 'Background checks are conducted on eligible personnel (employees and third parties as deemed necessary by the organization) prior to hire as permitted by local laws.'
      activity: ''
      question: ''
      tests:
          - id: 47
          - id: 50
      domain: 'Organizational Security'
      category: 'Personnel Security'
    - code: 'DCF-40'
      name: 'Contractor Requirements'
      description: '%s requires its contractors to read and acknowledge the Code of Conduct, read and acknowledge the Acceptable Use Policy, and pass a background check.'
      activity: ''
      question: 'Does the organization define acceptable and unacceptable rules of behavior for the use of technologies, including consequences for unacceptable behavior?'
      tests: []
      domain: 'Organizational Security'
      category: 'Personnel Security'
    - code: 'DCF-41'
      name: 'Independent Board of Directors'
      description: 'The board of directors includes members independent from management who are not involved in control operations.'
      activity: ''
      question: ''
      tests:
          - id: 51
      domain: 'Organizational Security'
      category: 'Personnel Security'
    - code: 'DCF-42'
      name: 'Defined Roles and Responsibilities'
      description: 'Management has defined and documented roles and responsibilities for implementation and oversight of the risk management and compliance programs (e.g., security, privacy, AI, etc.).'
      activity: ''
      question: ''
      tests:
          - id: 40
      domain: 'Organizational Security'
      category: 'Personnel Security'
    - code: 'DCF-43'
      name: 'Termination/Offboarding Checklist'
      description: "%s uses a termination checklist to ensure that an employee's system access, including physical access, is removed within a specified timeframe and all organization assets (physical or electronic) are properly returned."
      activity: "1. Termination checklist includes removal of system access, destruction and wiping of sensitive information, hardware, etc.\n2. Termination checklist includes identification of assets to be returned, and documents the retrieval"
      question: 'Does the organization govern the termination of individual employment?'
      tests: []
      domain: 'Organizational Security'
      category: 'Personnel Security'
    - code: 'DCF-44'
      name: 'Code of Conduct'
      description: "%s maintains a documented code of conduct. Eligible personnel are required to acknowledge %s's code of conduct during onboarding and annually thereafter."
      activity: ''
      question: ''
      tests:
          - id: 48
          - id: 54
          - id: 55
      domain: 'Organizational Security'
      category: 'Personnel Security'
    - code: 'DCF-45'
      name: 'Data Protection Policy'
      description: '%s has a documented a policy that outlines the procedures and technical measures to be implemented at the organization to protect the confidentiality, integrity, and availability of data.'
      activity: ''
      question: ''
      tests:
          - id: 56
      domain: 'Organizational Security'
      category: 'Personnel Security'
    - code: 'DCF-46'
      name: 'Formal Screening Process'
      description: 'Management evaluates candidates for employment through a formal screening process. The process may include verification of academic and professional qualifications, identity verifications, validation of personal or professional references, technical interviews, or other steps as deemed applicable by the organization.'
      activity: ''
      question: ''
      tests: []
      domain: 'Organizational Security'
      category: 'Personnel Security'
    - code: 'DCF-47'
      name: 'Job Descriptions'
      description: '%s has documented job descriptions for each position at the company, which include roles and responsibilities as well as required qualifications, skills, and experience for the role.'
      activity: ''
      question: ''
      tests:
          - id: 59
          - id: 60
      domain: 'Organizational Security'
      category: 'Personnel Security'
    - code: 'DCF-48'
      name: 'Screen Lockout'
      description: 'Company-managed devices are configured to enforce a screensaver lock with after a defined period of inactivity in accordance with company policies and compliance requirements.'
      activity: ''
      question: ''
      tests:
          - id: 61
      domain: 'Organizational Security'
      category: 'Endpoints (Laptops)'
    - code: 'DCF-49'
      name: 'Password Manager'
      description: 'A password manager is installed on all company-managed devices.'
      activity: ''
      question: ''
      tests:
          - id: 63
      domain: 'Organizational Security'
      category: 'Endpoints (Laptops)'
    - code: 'DCF-50'
      name: 'Antimalware Software on Devices'
      description: 'Anti-malware software is installed on all company-managed devices.'
      activity: ''
      question: ''
      tests:
          - id: 64
      domain: 'Organizational Security'
      category: 'Endpoints (Laptops)'
    - code: 'DCF-51'
      name: 'Automated Updates on Devices'
      description: 'Automated operating system (OS) updates are enabled on company-managed devices to install security patches.'
      activity: ''
      question: ''
      tests:
          - id: 65
      domain: 'Organizational Security'
      category: 'Endpoints (Laptops)'
    - code: 'DCF-52'
      name: 'Hard-Disk Encryption'
      description: 'Hard-disk encryption is enabled on all company-managed devices.'
      activity: ''
      question: ''
      tests:
          - id: 66
      domain: 'Organizational Security'
      category: 'Endpoints (Laptops)'
    - code: 'DCF-53'
      name: 'Cryptography Policies'
      description: '%s has an established policy and procedures that governs the use of cryptographic controls.'
      activity: ''
      question: 'Does the organization facilitate the implementation of cryptographic protections controls using known public standards and trusted cryptographic technologies?'
      tests: []
      domain: 'Product Security'
      category: 'Data Encryption'
    - code: 'DCF-54'
      name: 'Encryption at Rest'
      description: 'Data at rest is encrypted using strong cryptographic algorithms.'
      activity: ''
      question: ''
      tests:
          - id: 68
          - id: 69
          - id: 218
          - id: 222
          - id: 231
          - id: 270
          - id: 8002
      domain: 'Product Security'
      category: 'Data Encryption'
    - code: 'DCF-55'
      name: 'Encryption in Transit'
      description: 'Data in transit is encrypted using strong cryptographic algorithms.'
      activity: ''
      question: ''
      tests:
          - id: 70
          - id: 71
          - id: 72
          - id: 210
          - id: 234
          - id: 253
          - id: 263
          - id: 269
          - id: 299
          - id: 8008
          - id: 8015
      domain: 'Product Security'
      category: 'Data Encryption'
    - code: 'DCF-56'
      name: 'Vendor Register and Agreements'
      description: '%s maintains a vendor/third party register that includes a complete and accurate list of vendors/third parties, relationship owners, description for each of the services provided, risk ratings, results of vendor/third party risk management activities, etc. %s executes agreements with vendors and service providers involved in accessing, processing, storing or managing information assets that outline the responsibilities of each vendor or service provider.'
      activity: ''
      question: ''
      tests: []
      domain: 'Product Security'
      category: 'Vendor Management'
    - code: 'DCF-57'
      name: 'Vendor Compliance Monitoring'
      description: "%s obtains and reviews compliance reports or other evidence for critical vendors and service providers at least annually to monitor the third parties' compliance with industry frameworks, regulations, standards (e.g., SOC 2, ISO, PCI DSS, etc.) and %s's requirements. Results of the review and action items, if any, are documented."
      activity: ''
      question: ''
      tests: []
      domain: 'Product Security'
      category: 'Vendor Management'
    - code: 'DCF-58'
      name: 'Centralized Authentication and Account Management'
      description: '%s has implemented systems or mechanisms to centralize authentication and account management across the organization (e.g., directory service, identity provider, etc.).'
      activity: ''
      question: ''
      tests: []
      domain: 'Product Security'
      category: 'Software Application Security'
    - code: 'DCF-59'
      name: 'Privileged Access Restricted'
      description: 'Administrative or privileged access to systems, resources, and functions is restricted to authorized personnel.'
      activity: ''
      question: ''
      tests:
          - id: 208
      domain: 'Product Security'
      category: 'Software Application Security'
    - code: 'DCF-60'
      name: 'Secure Password Storage'
      description: '%s has implemented technical measures to protect stored user passwords for the system (e.g., encryption, hashing, salting, etc.).'
      activity: ''
      question: ''
      tests: []
      domain: 'Product Security'
      category: 'Software Application Security'
    - code: 'DCF-61'
      name: 'Customer Data Segregation'
      description: '%s has implemented segregation mechanisms so that customers cannot impact or access data or resources of other customers.'
      activity: ''
      question: ''
      tests: []
      domain: 'Product Security'
      category: 'Software Application Security'
    - code: 'DCF-62'
      name: 'Session Termination'
      description: "%s's systems automatically terminate a user's logical session based on predefined conditions (e.g., predefined periods of inactivity, closure of the system or internet browser, etc.)."
      activity: ''
      question: ''
      tests: []
      domain: 'Product Security'
      category: 'Software Application Security'
    - code: 'DCF-63'
      name: 'Terms of Service'
      description: '%s maintains a publicly available terms of service for use of the system. All users must agree to the terms of service prior to using the system.'
      activity: ''
      question: ''
      tests:
          - id: 85
      domain: 'Product Security'
      category: 'Software Application Security'
    - code: 'DCF-64'
      name: 'Commitments Communicated to Customers'
      description: '%s communicates service commitments and system requirements to customers and other external parties, as appropriate, through contracts, agreements, company website, etc. %s provides notification to relevant parties of any changes to service commitments and system requirements.'
      activity: ''
      question: ''
      tests: []
      domain: 'Product Security'
      category: 'Customer Communication'
    - code: 'DCF-65'
      name: 'Public Privacy Policy'
      description: '%s maintains a publicly available privacy policy/notice.'
      activity: ''
      question: ''
      tests:
          - id: 84
      domain: 'Product Security'
      category: 'Customer Communication'
    - code: 'DCF-66'
      name: 'Master Service Agreements'
      description: 'Master service agreements outlining specific requirements are executed with enterprise customers or when the standard terms of service may not apply.'
      activity: ''
      question: ''
      tests:
          - id: 83
      domain: 'Product Security'
      category: 'Customer Communication'
    - code: 'DCF-67'
      name: 'Multi-Factor Authentication'
      description: 'Authentication to systems requires the use of multi-factor authentication.'
      activity: ''
      question: ''
      tests:
          - id: 86
          - id: 87
          - id: 88
      domain: 'Infrastructure Security'
      category: 'Authentication and Authorization'
    - code: 'DCF-68'
      name: 'Password Policy and Configuration'
      description: '%s has a documented policy outlining the minimum requirements for passwords used for authentication to organizational systems. Password requirements are enforced for all systems in accordance with company policy.'
      activity: ''
      question: ''
      tests:
          - id: 89
          - id: 215
      domain: 'Infrastructure Security'
      category: 'Authentication and Authorization'
    - code: 'DCF-69'
      name: 'Access Provisioning'
      description: 'Access requests to information resources, including physical access and access to systems and data, are documented and approved by management based on least privilege, need to know, and segregation of duties principles.'
      activity: ''
      question: ''
      tests: []
      domain: 'Infrastructure Security'
      category: 'Authentication and Authorization'
    - code: 'DCF-70'
      name: 'Access Deprovisioning'
      description: 'System and physical access is revoked within one business day of effective termination date for terminated users (including employees, third parties and vendors, and other personnel).'
      activity: ''
      question: ''
      tests:
          - id: 94
          - id: 95
          - id: 199
      domain: 'Infrastructure Security'
      category: 'Authentication and Authorization'
    - code: 'DCF-71'
      name: 'Unique User IDs'
      description: 'Authentication to systems requires the use of unique identities.'
      activity: ''
      question: ''
      tests:
          - id: 96
          - id: 97
          - id: 98
      domain: 'Infrastructure Security'
      category: 'Authentication and Authorization'
    - code: 'DCF-72'
      name: 'Root Access Control'
      description: 'Root password authentication to production resources (e.g., virtual machines, containers, etc.) is disabled and only allowed for under exceptional circumstances for a limited time duration based on documented business justification and approval from management.'
      activity: ''
      question: ''
      tests: []
      domain: 'Infrastructure Security'
      category: 'Authentication and Authorization'
    - code: 'DCF-73'
      name: 'Access to Remote Server Administration Ports Restricted'
      description: 'Network security controls are in place to restrict public access to remote server administration ports (e.g., SSH, RDP) to authorized IP addresses or address ranges only.'
      activity: ''
      question: ''
      tests:
          - id: 102
          - id: 227
          - id: 228
          - id: 268
      domain: 'Infrastructure Security'
      category: 'Authentication and Authorization'
    - code: 'DCF-74'
      name: 'Communication of System Changes'
      description: "%s communicates system changes via release notes or change log in the company's website or via periodic communications."
      activity: ''
      question: ''
      tests: []
      domain: 'Infrastructure Security'
      category: 'Availability'
    - code: 'DCF-75'
      name: 'Restricted Public Access'
      description: 'Cloud resources are configured to deny public access.'
      activity: ''
      question: ''
      tests:
          - id: 104
          - id: 209
          - id: 220
          - id: 311
          - id: 312
          - id: 8011
      domain: 'Infrastructure Security'
      category: 'Storage'
    - code: 'DCF-76'
      name: 'Critical Change Management'
      description: 'Emergency changes or hot fixes implemented outside of the standard change management process are reviewed and approved by an authorized individual after implementation.'
      activity: ''
      question: ''
      tests: []
      domain: 'Internal Security Procedures'
      category: 'Software Development Life Cycle'
    - code: 'DCF-77'
      name: 'Data Backups'
      description: 'Backups of production data are performed at least daily and are configured to be retained in accordance with the retention periods established in company policies and procedures.'
      activity: ''
      question: ''
      tests:
          - id: 107
          - id: 8001
          - id: 8017
      domain: 'Infrastructure Security'
      category: 'Backup'
    - code: 'DCF-78'
      name: 'Storage Bucket Versioning'
      description: 'Storage buckets that contain sensitive data have versioning enabled to preserve, retrieve, and restore versions of objects.'
      activity: ''
      question: ''
      tests:
          - id: 108
          - id: 8003
      domain: 'Infrastructure Security'
      category: 'Backup'
    - code: 'DCF-79'
      name: 'Logging System'
      description: '%s uses a centralized system that collects and stores logs of system activity and sends alerts to personnel based on pre-configured rules. Access to logs is restricted to authorized personnel.'
      activity: ''
      question: ''
      tests:
          - id: 109
          - id: 110
      domain: 'Infrastructure Security'
      category: 'Logging'
    - code: 'DCF-80'
      name: 'Log Management System'
      description: '%s uses logging software that sends alerts to appropriate personnel. Corrective actions are performed, as necessary, in a timely manner.'
      activity: "1. Enable AWS CloudTrail and CloudWatch, Azure Monitor (Log Analytics), Google StackDriver, or analogous software.\n2. Enable rulesets to alert appropriate personnel of anomalous or suspicious activity.\n3. Perform corrective actions when alerted."
      question: 'Does the organization use logging software to alert appropriate personnel of anomalous or suspicious activity?'
      tests: []
      domain: 'Infrastructure Security'
      category: 'Logging'
    - code: 'DCF-81'
      name: 'Databases Monitored and Alarmed'
      description: "%s has implemented tools to monitor %s's databases and notify appropriate personnel of any events or incidents based on predetermined criteria. Incidents are escalated per policy."
      activity: "1. Ensure tools are implemented to monitor databases\n2. Ensure notifications based on specific criteria are sent to the appropriate personnel\n3. Escalate incidents appropriately"
      question: 'Does the organization implement tools to monitor its databases and notify appropriate personnel of incidents based on predetermined criteria?'
      tests: []
      domain: 'Infrastructure Security'
      category: 'Monitoring'
    - code: 'DCF-82'
      name: 'Messaging Queues Monitored and Alarmed'
      description: "%s has implemented tools to monitor %s's messaging queues and notify appropriate personnel of any events or incidents based on predetermined criteria. Incidents are escalated per policy."
      activity: ''
      question: 'Does the organization implement tools to monitor its messaging queues and notify appropriate personnel of incidents based on predetermined criteria?'
      tests: []
      domain: 'Infrastructure Security'
      category: 'Monitoring'
    - code: 'DCF-83'
      name: 'NoSQL Database Monitored and Alarmed'
      description: "%s has implemented tools to monitor %s's NoSQL databases and notify appropriate personnel of any events or incidents based on predetermined criteria. Incidents are escalated per policy."
      activity: ''
      question: 'Does the organization implement tools to monitor its NoSQL databases and notify appropriate personnel of incidents based on predetermined criteria?'
      tests:
          - id: 116
      domain: 'Infrastructure Security'
      category: 'Monitoring'
    - code: 'DCF-84'
      name: 'Servers Monitored and Alarmed'
      description: "%s has implemented tools to monitor %s's servers and notify appropriate personnel of any events or incidents based on predetermined criteria. Incidents are escalated per policy."
      activity: ''
      question: 'Does the organization implement tools to monitor its servers and notify appropriate personnel of incidents based on predetermined criteria?'
      tests: []
      domain: 'Infrastructure Security'
      category: 'Monitoring'
    - code: 'DCF-85'
      name: 'Network Security Controls'
      description: 'Network security controls are in place to limit inbound and outbound traffic to the environment to only what is necessary based on business justification. All other traffic is specifically denied.'
      activity: ''
      question: ''
      tests:
          - id: 119
          - id: 209
          - id: 233
          - id: 291
          - id: 8007
          - id: 8009
          - id: 8010
          - id: 8012
          - id: 8016
      domain: 'Infrastructure Security'
      category: 'Network'
    - code: 'DCF-86'
      name: 'System Monitoring'
      description: 'Production systems and resources are monitored and automated alerts are sent out personnel based on pre-configured rules. Events are triaged to determine if they constitute an incident and escalated per policy if necessary.'
      activity: ''
      question: ''
      tests:
          - id: 112
          - id: 113
          - id: 114
          - id: 115
          - id: 117
          - id: 118
          - id: 206
          - id: 243
          - id: 244
          - id: 245
          - id: 246
          - id: 247
          - id: 248
          - id: 249
          - id: 251
          - id: 252
          - id: 290
          - id: 293
          - id: 294
          - id: 295
          - id: 296
          - id: 297
          - id: 298
          - id: 300
          - id: 301
          - id: 250
      domain: 'Infrastructure Security'
      category: 'Network'
    - code: 'DCF-87'
      name: 'Threat Detection System'
      description: 'A threat detection system is in place to monitor web traffic and suspicious activity. When anomalous traffic activity is identified, alerts are automatically sent to personnel, investigated, and escalated through the incident management process, if necessary.'
      activity: ''
      question: ''
      tests:
          - id: 105
      domain: 'Infrastructure Security'
      category: 'Network'
    - code: 'DCF-88'
      name: 'Web Application Firewall'
      description: 'A web application firewall is in place to protect public-facing web applications from outside threats.'
      activity: ''
      question: ''
      tests:
          - id: 122
          - id: 311
          - id: 312
      domain: 'Infrastructure Security'
      category: 'Network'
    - code: 'DCF-89'
      name: 'Cloud Infrastructure Linked to Drata'
      description: '%s is using Drata to monitor the security and compliance of its cloud infrastructure configuration.'
      activity: ''
      question: 'Does the organization utilize software to monitor its infrastructure security controls?'
      tests: []
      domain: 'Infrastructure Security'
      category: 'Network'
    - code: 'DCF-90'
      name: 'Root Infrastructure Account Monitored'
      description: 'Access to the root account in the cloud infrastructure provider is monitored. Login activity for the root account is investigated and validated for appropriateness.'
      activity: ''
      question: ''
      tests:
          - id: 124
          - id: 214
          - id: 225
      domain: 'Infrastructure Security'
      category: 'Network'
    - code: 'DCF-91'
      name: 'Intrusion Detection/Prevention System'
      description: 'An intrusion detection system (IDS)/intrusion prevention system (IPS) or equivalent is in place to detect real-time suspicious or anomalous network traffic that may be indicative of threat actor activity and is configured to alert personnel when a potential intrusion is detected.'
      activity: ''
      question: ''
      tests: []
      domain: 'Infrastructure Security'
      category: 'Network'
    - code: 'DCF-92'
      name: 'Encrypted Remote Production Access'
      description: 'Remote access to production systems is only available through an encrypted connection (e.g., encrypted virtual private network, SSH, etc.)'
      activity: ''
      question: ''
      tests: []
      domain: 'Infrastructure Security'
      category: 'Network'
    - code: 'DCF-93'
      name: 'Credential Keys Managed'
      description: "%s has an established key management process in place to support the organization's use of cryptographic techniques."
      activity: ''
      question: 'Does the organization have an established key management process?'
      tests: []
      domain: 'Infrastructure Security'
      category: 'Protecting Secrets'
    - code: 'DCF-94'
      name: 'Physical Security Policy'
      description: '%s has a documented policy that outlines requirements for physical security.'
      activity: ''
      question: ''
      tests:
          - id: 128
      domain: 'Physical Security'
      category: 'Data Center Security'
    - code: 'DCF-95'
      name: 'Monitoring Processing Capacity and Usage'
      description: '%s monitors processing capacity and use of resources continuously to manage demand and to enable the implementation of additional resources as necessary.'
      activity: ''
      question: ''
      tests:
          - id: 129
      domain: 'Availability'
      category: 'Scaling'
    - code: 'DCF-96'
      name: 'Load Balancer'
      description: '%s uses a load balancer to automatically distribute incoming traffic across multiple targets.'
      activity: ''
      question: ''
      tests:
          - id: 130
          - id: 8035
      domain: 'Availability'
      category: 'Scaling'
    - code: 'DCF-97'
      name: 'Autoscaling'
      description: '%s has enabled auto-scaling configurations to provision new cloud resources when predefined capacity thresholds are met.'
      activity: ''
      question: ''
      tests:
          - id: 131
          - id: 8000
      domain: 'Availability'
      category: 'Scaling'
    - code: 'DCF-98'
      name: 'Backup Storage'
      description: 'Backups are encrypted and segmented from production systems (e.g., air-gapped, replicated to a different region, stored offsite, etc.) to ensure protection from a disaster or incident.'
      activity: ''
      question: ''
      tests: []
      domain: 'Availability'
      category: 'Backups'
    - code: 'DCF-99'
      name: 'Backup Monitoring'
      description: 'Automated notifications are sent to personnel in the event of a backup failure. Backup failures are investigated and resolved by engineering personnel following company policies and procedures.'
      activity: ''
      question: ''
      tests:
          - id: 132
          - id: 133
          - id: 134
      domain: 'Availability'
      category: 'Backups'
    - code: 'DCF-100'
      name: 'Backup Restore Testing'
      description: '%s tests the integrity and recoverability of backed-up data at least annually.'
      activity: ''
      question: ''
      tests: []
      domain: 'Availability'
      category: 'Backups'
    - code: 'DCF-101'
      name: 'Data Retention Policy'
      description: '%s has a documented and implemented a policy for data retention defining the types of data (including company and customer data) and the period of time for which they should be retained.'
      activity: ''
      question: ''
      tests:
          - id: 111
          - id: 136
          - id: 8019
      domain: 'Confidentiality'
      category: 'Data'
    - code: 'DCF-102'
      name: 'Data Classification Policy'
      description: '%s has established a data classification policy in order to identify the types of information stored or processed by the organization and the protection measures that are required for each.'
      activity: ''
      question: ''
      tests:
          - id: 137
      domain: 'Confidentiality'
      category: 'Data'
    - code: 'DCF-103'
      name: 'Customer Data Deletion Upon Termination'
      description: '%s disposes of customer data upon termination of services in accordance with contractual agreements.'
      activity: ''
      question: ''
      tests: []
      domain: 'Confidentiality'
      category: 'Data'
    - code: 'DCF-104'
      name: 'Test Data'
      description: 'Test data is used in testing and development environments to prevent sensitive information from being copied to non-production environments.'
      activity: ''
      question: ''
      tests: []
      domain: 'Confidentiality'
      category: 'Data'
    - code: 'DCF-105'
      name: 'Personnel Non-Disclosure Agreements (NDA)'
      description: 'Personnel, including employees and contractors, are required to sign an agreement that outlines confidentiality requirements (e.g., non-disclosure agreements) prior to hire.'
      activity: ''
      question: ''
      tests: []
      domain: 'Confidentiality'
      category: 'Employee Responsibility'
    - code: 'DCF-106'
      name: 'Clean Desk and Clear Screen Policies and Procedures'
      description: '%s has defined clear desk and clear screen policies and procedures to protect confidential data (physical and electronic) which are communicated to personnel and enforced across the organization.'
      activity: ''
      question: ''
      tests: []
      domain: 'Confidentiality'
      category: 'Employee Responsibility'
    - code: 'DCF-107'
      name: 'Disposal of Sensitive Data on Paper'
      description: 'When %s disposes of hard copy materials, it does so through secure means such as cross-cut shredding, incinerating, or pulping, so that sensitive data cannot be reconstructed.'
      activity: ''
      question: ''
      tests: []
      domain: 'Confidentiality'
      category: 'Employee Responsibility'
    - code: 'DCF-108'
      name: 'Secure Storage Mechanisms'
      description: '%s uses secure storage mechanisms for digital media and hardcopy materials that contain sensitive data (e.g., locked codes, combination locks to offices, rooms and facilities such as key cabinets, etc.) as well as critical equipment and other assets. Access to the secured storage mechanisms (including access to physical keys and knowledge of authentication information) is restricted to authorized personnel.'
      activity: ''
      question: ''
      tests: []
      domain: 'Confidentiality'
      category: 'Employee Responsibility'
    - code: 'DCF-109'
      name: 'Disposal of Sensitive Data on Hardware'
      description: '%s disposes of data on hardware through secure means, such as wiping and hard drive destruction, in accordance with documented policies and procedures.'
      activity: ''
      question: ''
      tests: []
      domain: 'Confidentiality'
      category: 'Employee Responsibility'
    - code: 'DCF-110'
      name: 'Acceptable Input Ranges'
      description: "%s's has implemented automated edit checks in the system to limit input to defined value ranges and formats."
      activity: ''
      question: ''
      tests: []
      domain: 'Processing Integrity'
      category: 'Regression Testing'
    - code: 'DCF-111'
      name: 'Mandatory Fields'
      description: 'Automated application checks are in place that require mandatory fields to be complete before data entry is accepted.'
      activity: ''
      question: ''
      tests: []
      domain: 'Processing Integrity'
      category: 'Application & System Edits'
    - code: 'DCF-112'
      name: 'Notice and Acknowledgement of Privacy Practices'
      description: '%s provides notice of its privacy practices to users prior to accessing the system. Users are required to explicitly acknowledge the privacy policy prior to entering information into the system.'
      activity: ''
      question: ''
      tests: []
      domain: 'Processing Integrity'
      category: null
    - code: 'DCF-113'
      name: 'Review Privacy Notice Annually'
      description: "%s's management reviews the privacy notice to ensure that the privacy notice is accurate."
      activity: ''
      question: 'Does the organization review its privacy notice annually to ensure accuracy?'
      tests: []
      domain: 'Privacy'
      category: 'Communication of Objectives Related to Privacy Practices'
    - code: 'DCF-114'
      name: 'Privacy Policy Publicly Available'
      description: '%s communicates its Privacy Policy on its public-facing website.'
      activity: ''
      question: 'Does the organization communicate its privacy policy on its public-facing website?'
      tests: []
      domain: 'Privacy'
      category: 'Communication of Objectives Related to Privacy Practices'
    - code: 'DCF-115'
      name: 'Privacy Policy Content'
      description: "%s's documented Privacy Policy includes information on:\n- Purpose for collecting/processing personal information\n- Lawful basis for collecting/processing personal information \n- Types of personal information collected or processed\n- Choice and consent\n- Methods of collection (for example, use of cookies or other tracking techniques)\n- Use, retention, and disposal\n- Data subject rights\n- Use of subprocessors \n- Technical and organizational measures\n- Quality, including data subjects' responsibilities for quality\n- Monitoring and enforcement"
      activity: ''
      question: ''
      tests: []
      domain: 'Privacy'
      category: 'Communication of Objectives Related to Privacy Practices'
    - code: 'DCF-116'
      name: 'Acknowledge The Privacy Policy'
      description: "%s's users are required to explicitly acknowledge the notice of privacy practices prior to entering information into the application."
      activity: ''
      question: 'Does the organization require users to acknowledge its privacy practices prior to entering information into the application?'
      tests: []
      domain: 'Privacy'
      category: 'Communication of Objectives Related to Privacy Practices'
    - code: 'DCF-117'
      name: 'Minimal Information Required'
      description: "%s's collection of personal information is limited to that necessary to meet the entity's objectives."
      activity: ''
      question: "Does the organization limit the collection of personal information to only that which is necessary to meet the entity's objectives?"
      tests: []
      domain: 'Privacy'
      category: 'Privacy Related to Choice and Consent'
    - code: 'DCF-118'
      name: 'Third Party Reliability'
      description: "%s's management confirms that third parties from whom personal information is collected (that is, sources other than the individual) are reliable sources that collect information fairly and lawfully."
      activity: ''
      question: "Does the organization's management confirm that third parties from whom personal information is collected are reliable sources of information collecting information fairly and lawfully?"
      tests: []
      domain: 'Privacy'
      category: 'Privacy Related to Collection'
    - code: 'DCF-119'
      name: 'Allowable Use and Disclosure'
      description: '%s maintains policies and procedures that define allowable use and disclosure scenarios.'
      activity: ''
      question: 'Does the organization define controls related to allowable use and disclosure scenarios?'
      tests: []
      domain: 'Privacy'
      category: 'Privacy Related to Collection'
    - code: 'DCF-120'
      name: 'Periodic Review of Privacy Policy'
      description: "%s's management reviews the online privacy policy and/or notice at least annually to validate its continued suitability and accuracy. The online privacy policy/notice includes the date it was last updated. %s notifies customers of changes to the privacy notice and the nature of the changes, including when personal information will be used for new purposes not previously identified."
      activity: ''
      question: ''
      tests: []
      domain: 'Privacy'
      category: '"Privacy Related to Use, Retention, and Disposal"'
    - code: 'DCF-121'
      name: 'Purposeful Use Only'
      description: "%s only uses personal information for the purposes identified in the entity's privacy policy."
      activity: ''
      question: 'Does the organization use personal information for only the purposes identified in the privacy policy?'
      tests: []
      domain: 'Privacy'
      category: 'Privacy Related to Use, Retention, and Disposal'
    - code: 'DCF-122'
      name: 'Requests for Deletion of PII'
      description: '%s complies with legitimate requests to delete PII from data subjects by permanently and completely erasing the personal information from its existing systems or de-identifying the personal information within the timelines established by regulatory requirements. %s provides notification to subprocessors and contractors of the need to delete and informs the data subject whether it has complied with the consumer’s request. Supporting documentation is retained.'
      activity: ''
      question: ''
      tests: []
      domain: 'Privacy'
      category: 'Privacy Related to Use, Retention, and Disposal'
    - code: 'DCF-123'
      name: 'Procedures for Information Disposal '
      description: '%s has documented policies and procedures for erasure or destruction of information that has been identified for disposal.'
      activity: ''
      question: ''
      tests: []
      domain: 'Privacy'
      category: 'Privacy Related to Use, Retention, and Disposal'
    - code: 'DCF-124'
      name: 'Require Authentication for Access'
      description: "Users accessing their personal information through %s's application must be authenticated with a username and password."
      activity: ''
      question: 'Does the organization require that users accessing personal information through the application must be authenticated with a username and password?'
      tests: []
      domain: 'Privacy'
      category: 'Privacy Related to Use, Retention, and Disposal'
    - code: 'DCF-125'
      name: 'Users Can Access All Their Information'
      description: 'Users can access all of their personal information through the application by navigating to their settings and profile.'
      activity: ''
      question: 'Does the organization ensure that users can access all of their information through the application?'
      tests: []
      domain: 'Privacy'
      category: 'Privacy Criteria Related to Access'
    - code: 'DCF-126'
      name: 'Personal Information Accessible Through System Authentication'
      description: '%s provides a mechanism for users to view, correct, and/or delete their personal information by authenticating into the system with a username and password and navigating to their profile settings.'
      activity: ''
      question: ''
      tests: []
      domain: 'Privacy'
      category: 'Privacy Criteria Related to Access'
    - code: 'DCF-127'
      name: 'Privacy Requirements Communicated to Third parties'
      description: "%s's privacy policies or other specific instructions for handling personal information, including requirements and procedures to notify %s of breaches or unauthorized disclosures, are communicated to third parties to whom personal information is disclosed."
      activity: ''
      question: ''
      tests: []
      domain: 'Privacy'
      category: 'Privacy Criteria Related to Access'
    - code: 'DCF-128'
      name: 'Disclosure with 3rd Parties'
      description: "%s discloses personal information only to third parties who have agreements with %s to protect personal information in a manner consistent with the relevant aspects of %s's privacy notice or other specific instructions or requirements."
      activity: ''
      question: "Does the organization only disclose personal information to third parties who have agreements in place to protect personal information in a manner consistent with the relevant aspects of the company's privacy notice?"
      tests: []
      domain: 'Privacy'
      category: 'Privacy Criteria Related to Disclosure and Notification'
    - code: 'DCF-129'
      name: 'PII with 3rd Parties and Vendors'
      description: '%s maintains a documented list of third parties and vendors that are authorized to receive or access PII'
      activity: ''
      question: 'Does the organization maintain a list of third parties authorized to receive or access PII?'
      tests: []
      domain: 'Privacy'
      category: 'Privacy Criteria Related to Disclosure and Notification'
    - code: 'DCF-130'
      name: 'Documentation of Breaches or Unauthorized Disclosures of PII'
      description: '%s maintains documentation of any personal data breaches or unauthorized disclosures of PII including the facts relating to the personal data breach or disclosure, its effects and impact, and the remedial action taken.'
      activity: ''
      question: ''
      tests: []
      domain: 'Privacy'
      category: 'Privacy Criteria Related to Disclosure and Notification'
    - code: 'DCF-131'
      name: 'Incident Report Template and Process'
      description: '%s has incident management procedures that include detailed instructions on how to escalate a suspected incident to the Information Security Team and, when necessary, to the Privacy or Legal department. %s has a standard incident report template that must be completed for each incident.'
      activity: ''
      question: 'Does the organization have procedures to escalate a suspected incident to the InfoSec team?'
      tests: []
      domain: 'Privacy'
      category: 'Privacy Criteria Related to Disclosure and Notification'
    - code: 'DCF-132'
      name: 'Privacy and Security Requirements in Third-Party Agreements'
      description: '%s shares information with vendors and third parties only when an executed agreement (e.g., service agreements, business associate agreements, data processing agreements, etc.) is in place that includes security, confidentiality, and privacy requirements for the transfer and processing of information.'
      activity: ''
      question: ''
      tests: []
      domain: 'Privacy'
      category: 'Privacy Criteria Related to Disclosure and Notification'
    - code: 'DCF-133'
      name: 'Unauthorized Disclosures by 3rd Parties'
      description: '%s requires vendors and third parties with access to personal information to sign a formal contract that requires them to notify %s in the event of actual or suspected unauthorized disclosures of personal information'
      activity: ''
      question: 'Does the organization require third parties to sign a formal contract that requires them to notify the company in the event of any suspected disclosures of personal information?'
      tests: []
      domain: 'Privacy'
      category: 'Privacy Criteria Related to Disclosure and Notification'
    - code: 'DCF-134'
      name: '3rd Parties and Vendors Given Instructions on Breach Reporting'
      description: '%s provides vendors and third parties with information on how to report breaches to %s.'
      activity: ''
      question: 'Does the organization provide vendors with information on how to report breaches?'
      tests: []
      domain: 'Privacy'
      category: 'Privacy Criteria Related to Disclosure and Notification'
    - code: 'DCF-135'
      name: 'Notification of Incidents or Breaches'
      description: '%s provides communications about breaches and incidents to affected parties, organizational officials, authorities, and other internal and external stakeholders, in accordance with company policies and procedures and contractual and legal obligations.'
      activity: ''
      question: ''
      tests: []
      domain: 'Privacy'
      category: 'Privacy Criteria Related to Disclosure and Notification'
    - code: 'DCF-136'
      name: 'Use of Subprocessors Communicated'
      description: '%s communicates to customers any use of subprocessors to process PII (e.g., through a list of subprocessors in the company website or data processing agreement, etc.). %s obtains authorization from customers for the use of subprocessors (e.g., through executed data processing agreements, accepting the terms in the website, etc.).'
      activity: ''
      question: ''
      tests: []
      domain: 'Privacy'
      category: 'Privacy Criteria Related to Disclosure and Notification'
    - code: 'DCF-137'
      name: 'Data Entry Field Completion Automated'
      description: 'As personal information is collected, automated edit checks are in place to ensure that data entry fields are completed properly.'
      activity: ''
      question: 'Does the organization ensure data entry fields are completed properly when personal information is being collected?'
      tests: []
      domain: 'Privacy'
      category: 'Privacy Criteria Related to Disclosure and Notification'
    - code: 'DCF-138'
      name: 'Confirmation Before Submission'
      description: 'As personal information is collected, users are asked to confirm that their information is correct prior to submitting the information to %s.'
      activity: ''
      question: 'Does the organization ask to confirm if personal information being input is correct prior to submitting forms?'
      tests: []
      domain: 'Privacy'
      category: 'Privacy Related to Quality'
    - code: 'DCF-139'
      name: 'Contact Information for Privacy Concerns'
      description: "%s informs users about how to contact %s with inquiries, complaints, and disputes via the privacy practices that are posted on the %s's public-facing website."
      activity: ''
      question: 'Does the organization inform its users about to contact how the company with inquiries, complaints, and disputes via the privacy practices?'
      tests: []
      domain: 'Privacy'
      category: 'Privacy Related to Quality'
    - code: 'DCF-140'
      name: 'Point of Contact for Privacy Inquiries'
      description: '%s provides a contact mechanism for data subjects to submit privacy-related requests or report privacy incidents (e.g., email address, customer portal, etc.).'
      activity: ''
      question: ''
      tests: []
      domain: 'Privacy'
      category: 'Privacy Related to Monitoring and Enforcement'
    - code: 'DCF-141'
      name: 'Privacy Inquiries Tracked'
      description: '%s maintains records of privacy rights requests in ticket or log format that includes the date of request, nature of request, manner in which the request was made, the date of the business’s response, the nature of the response, and the basis for the denial of the request if the request is denied in whole or in part. Records are retained for a defined period in accordance with legal requirements.'
      activity: ''
      question: ''
      tests: []
      domain: 'Privacy'
      category: 'Privacy Related to Monitoring and Enforcement'
    - code: 'DCF-142'
      name: 'Quarterly Review of Privacy Compliance'
      description: 'Executive management meets on a quarterly basis to review compliance with privacy practices and privacy regulations.'
      activity: ''
      question: "Does the organization's management meet quarterly to review compliance with privacy practices and regulations?"
      tests: []
      domain: 'Privacy'
      category: 'Privacy Related to Monitoring and Enforcement'
    - code: 'DCF-143'
      name: 'Board Oversight Briefings Conducted'
      description: "The company's board of directors or a relevant subcommittee is briefed by senior management at least annually on the state of the company's cybersecurity and privacy risk. The board provides feedback and direction to management as needed."
      activity: "1. Review of internal risk assessment\n2. Review of findings from vulnerability scans\n3. Review of external 3rd party penetration tests"
      question: "Does the organization conduct board oversight briefings annually to provide an update of the company's cybersecurity and privacy risk?"
      tests: []
      domain: null
      category: null
    - code: 'DCF-144'
      name: 'Board Charter Documented'
      description: "The company's board of directors has a documented charter that outlines its oversight responsibilities for internal control."
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-145'
      name: 'Board Expertise Developed'
      description: "The company's board members have sufficient expertise to oversee management's ability to design, implement and operate information security controls. The board engages third-party information security experts and consultants as needed."
      activity: "1. Maintain a record of each board member's expertise sufficient for overseeing management's information security control abilities \n2. Maintain a record of the board's engagement with third-party information security experts and consultants"
      question: "Does the organization have sufficient board expertise to oversee management's ability to design, implement, and operate information security controls?"
      tests: []
      domain: null
      category: null
    - code: 'DCF-146'
      name: 'Board Meetings'
      description: "The company's board of directors, owners, senior leadership, or equivalent body, meets at least annually with management to review company performance, strategic objectives, compliance initiatives, and security and privacy risk and mitigation strategies. Meeting minutes, including decisions made and action items, are documented."
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-147'
      name: 'Physical Access to Facilities is Protected'
      description: "%s has security policies that have been approved by management and detail how physical access to the company's headquarters is maintained. These policies are accessible to all employees and contractors."
      activity: "1. Company maintains a physical security policy\n2. A card-based physical access control system is in place within the perimeter of facilities and at the entry and exit points of sensitive areas within these facilities, including areas containing backup media\n3. Visitors must be signed in by an authorized workforce member before a visitor badge that identifies them as an authorized visitor can be issued."
      question: 'Does the organization identify, authorize and monitor visitors before allowing access to the facility (other than areas designated as publicly accessible)?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-148'
      name: 'Regression Testing in Place'
      description: '%s does application regression testing to validate key processing for the application during the change management process.'
      activity: ''
      question: 'Does the organization perform regression testing to validate key processing during the change management process?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-149'
      name: 'Removable Media Device Encryption'
      description: '%s encrypts removable media devices, such as USB drives, digital video disks, compact disks, external or removable hard disks, etc., that contain sensitive data, to protect the confidentiality of the information during transport.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-150'
      name: 'Data Loss Prevention (DLP) Mechanisms'
      description: '%s has implemented data leakage prevention mechanisms to systems that could process, store or transmit sensitive information (e.g., sending personal information via email). These mechanisms are configured to prevent data leakage and generate audit logs and alerts.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-151'
      name: 'FIM (File Integrity Monitoring) Software in Place'
      description: '%s ensures that file integrity monitoring (FIM) software is in place to detect whether operating system and application software files have been tampered with.'
      activity: ''
      question: 'Does the organization utilize a File Integrity Monitor (FIM) or similar change-detection technology on critical assets to generate alerts for unauthorized modifications?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-152'
      name: 'Automated Security Updates'
      description: '%s has implemented automated mechanisms (e.g., unattended upgrades, automated patching tools, etc.) to install security fixes to systems.'
      activity: ''
      question: ''
      tests:
          - id: 219
          - id: 8013
      domain: null
      category: null
    - code: 'DCF-153'
      name: 'Conduct Control Self-Assessments'
      description: '%s performs control self-assessments at least annually to gain assurance that controls are in place and operating effectively. Corrective actions are taken based on relevant findings.'
      activity: "1. Conduct annual self-assessment of controls\n2. Take corrective action based on findings"
      question: 'Does the organization provide a security controls oversight function?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-154'
      name: 'Incident Response Test'
      description: '%s performs a test of all components of the incident response plan and procedures at least annually through different mechanisms (e.g., walk-through or tabletop exercises, simulations, etc.). The documented plan and procedures are updated if necessary based on the results of the test.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-155'
      name: 'Testing of Changes'
      description: 'Changes are tested in an environment separate from production prior to deployment in accordance with the nature of the change. Documented evidence of testing criteria and testing results is retained.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-156'
      name: 'Change Releases Approved'
      description: 'Change releases are approved by authorized personnel prior to deployment to production.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-157'
      name: 'Cybersecurity Insurance'
      description: '%s maintains cybersecurity insurance to mitigate the financial impact of security incidents and business disruptions.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-158'
      name: 'MFA Available for External Users'
      description: '%s allows for external users to implement multi-factor authentication on their accounts in order to require two forms of authentication prior to authentication'
      activity: '1. Allow for MFA on user accounts'
      question: 'Does the organization provide the ability for external users to enable 2 factor authentication or MFA?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-159'
      name: 'Incident Response Plan'
      description: '%s has a documented an incident response plan that outlines roles, responsibilities, and procedures to document, analyze, categorize, and respond to incidents. The incident response plan reviewed periodically and updated as needed according to lessons learned from previous incidents and industry developments.'
      activity: ''
      question: ''
      tests:
          - id: 33
      domain: 'Internal Security Procedures'
      category: 'Incident Response Plan'
    - code: 'DCF-160'
      name: 'Continuous Control Monitoring'
      description: '%s uses compliance automation software to identify, select, and continuously monitor internal controls.'
      activity: ''
      question: ''
      tests:
          - id: 123
      domain: 'Internal Security Procedures'
      category: 'Security Issues'
    - code: 'DCF-161'
      name: 'Management System Scope'
      description: '%s has documented the scope of its management system(s) that outlines the boundaries and applicability of the system(s) and considers internal and external issues, requirements of interested parties, and interfaces and dependencies with other organizations.'
      activity: ''
      question: ''
      tests: []
      domain: 'Information Security Management System'
      category: 'Organization Context'
    - code: 'DCF-161.AI'
      name: 'Management System Scope (AI)'
      description: '%s has documented the scope of its AI management system that outlines the boundaries and applicability of the system(s) and considers internal and external issues, requirements of interested parties, and interfaces and dependencies with other organizations.'
      domain: null
      category: null
      activity: ''
      question: ''
      tests: []
    - code: 'DCF-162'
      name: 'Statement of Applicability'
      description: '%s has a documented statement of applicability, which defines the controls deemed necessary by the organization as a result of the risk assessment to implement the risk treatment plan.'
      activity: ''
      question: ''
      tests: []
      domain: 'Information Security Management System'
      category: 'Planning'
    - code: 'DCF-162.AI'
      name: 'Statement of Applicability (AI)'
      description: '%s has a documented statement of applicability for its AIMS, which defines the controls deemed necessary by the organization as a result of the risk assessment to implement the risk treatment plan.'
      domain: null
      category: null
      activity: ''
      question: ''
      tests: []
    - code: 'DCF-163'
      name: 'Legal Requirements'
      description: '%s has identified and documented the legal, statutory, regulatory and contractual requirements relevant to the organization. %s has assigned responsibility and identified and implemented processes to satisfy these requirements and monitor and review changes.'
      activity: ''
      question: ''
      tests: []
      domain: 'Information Security Management System'
      category: 'Organization Context'
    - code: 'DCF-164'
      name: 'Management System Management Review'
      description: "%s's top management conducts reviews of its management system(s) at planned intervals to evaluate suitability, adequacy and effectiveness. %s retains documentation of the results of management reviews."
      activity: ''
      question: ''
      tests: []
      domain: 'Information Security Management System'
      category: 'Performance Evaluation'
    - code: 'DCF-164.AI'
      name: 'Management System Management Review (AI)'
      description: "%s's top management conducts reviews of its AI management system at planned intervals to evaluate suitability, adequacy and effectiveness. %s retains documentation of the results of management reviews."
      domain: null
      category: null
      activity: ''
      question: ''
      tests: []
    - code: 'DCF-165'
      name: 'Periodic Independent Assessments'
      description: "%s conducts evaluations and assessments at planned intervals to ensure that internal controls are effectively implemented and maintained in conformance with the organization's requirements (e.g., internal audits). %s retains documented information of the assessment program(s) and results."
      activity: ''
      question: ''
      tests: []
      domain: 'Information Security Management System'
      category: 'Performance Evaluation'
    - code: 'DCF-166'
      name: 'Business Continuity Plan'
      description: '%s has a defined business continuity plan that outlines strategies for maintaining operations during a disruption.'
      activity: ''
      question: ''
      tests: []
      domain: 'Internal Security Procedures'
      category: 'Business Continuity'
    - code: 'DCF-167'
      name: 'Business Impact Analysis'
      description: '%s performs a business impact analysis (BIA) periodically to identify criticality, business recovery order, and minimum service levels for key business processes and assets. Results of the business impact analysis are documented and incorporated into business continuity and disaster recovery plans.'
      activity: ''
      question: ''
      tests: []
      domain: 'Internal Security Procedures'
      category: 'Business Continuity'
    - code: 'DCF-168'
      name: 'Vendor Management Policy'
      description: '%s has a documented policy that outlines requirements for managing vendor and third-party relationships through their entire life cycle.'
      activity: ''
      question: ''
      tests: []
      domain: 'Product Security'
      category: 'Vendor Management'
    - code: 'DCF-169'
      name: 'Backup Policy'
      description: '%s has defined and documented a backup policy that establishes the requirements for backup information, software and systems.'
      activity: ''
      question: ''
      tests:
          - id: 106
      domain: 'Infrastructure Security'
      category: 'Backup'
    - code: 'DCF-170'
      name: 'Management System Objectives'
      description: '%s has documented objectives for its management system(s) (e.g., security objectives, privacy objectives, AI objectives, etc.) and plans to achieve them.'
      activity: ''
      question: ''
      tests: []
      domain: 'Organizational Security'
      category: 'Security Policies'
    - code: 'DCF-170.AI'
      name: 'Management System Objectives (AI)'
      description: '%s has documented objectives for its AI management system.'
      domain: null
      category: null
      activity: ''
      question: ''
      tests: []
    - code: 'DCF-171'
      name: 'Documented Operating Procedures'
      description: "%s maintains documented procedures that describe how to perform activities including controls, methods, and processes to be followed to achieve the company's policies objectives and compliance activities. The procedures are reviewed and updated as needed to address changes in processes, technologies, and business objectives, or at least annually, and are available to all relevant parties."
      activity: ''
      question: ''
      tests: []
      domain: 'Organizational Security'
      category: 'Security Program'
    - code: 'DCF-172'
      name: 'Organizational Change Management'
      description: '%s has a defined change management process for the organization, business processes, and information processing facilities and systems that affect information security.'
      activity: "1. Establish and maintain a change management system\n2. Properly plan for and test changes\n3. Analyze impact of changes\n4. Communicate changes to appropriate members\n5. Account and provision for emergency changes"
      question: 'Does the organization control changes to processes, facilities and systems that affect information security?'
      tests: []
      domain: 'Organizational Security'
      category: 'Security Policies'
    - code: 'DCF-173'
      name: 'Employment Terms & Conditions'
      description: 'Personnel responsibilities for information security (including confidentiality, legal, and data handling requirements), including responsibilities that remain after employment, are communicated to and acknowledged by personnel (e.g., through employment contracts, etc.)'
      activity: ''
      question: ''
      tests:
          - id: 58
      domain: 'Confidentiality'
      category: 'Employee Responsibility'
    - code: 'DCF-174'
      name: 'Telework and Endpoint Devices'
      description: '%s has a defined policy that establishes requirements and responsibilities for remote work and the use of company and personal IT devices.'
      activity: "1. Create and maintain security measures for mobile device use\n2. Establish security measures for the protection of information access, processing, and storage from remote sites"
      question: 'Does the organization have established requirements for personal device use and teleworking?'
      tests: []
      domain: 'Internal Security Procedures'
      category: 'Vulnerability Management'
    - code: 'DCF-175'
      name: 'Communications Plan'
      description: '%s has documented communication plans that establishes procedures for internal and external communications relevant to its information security, privacy, or other programs.'
      activity: ''
      question: ''
      tests: []
      domain: 'Information Security Management System'
      category: 'Support'
    - code: 'DCF-176'
      name: 'Measurement and Monitoring Plan'
      description: '%s has defined performance and/or effectiveness measurements for its management system(s) and implemented procedures to monitor these measurements periodically as determined by the organization.'
      activity: ''
      question: ''
      tests: []
      domain: 'Information Security Management System'
      category: 'Performance Evaluation'
    - code: 'DCF-176.AI'
      name: 'Measurement and Monitoring Plan (AI)'
      description: '%s has defined performance and/or effectiveness measurements for its AI management system and implemented procedures to monitor these measurements periodically as determined by the organization.'
      domain: null
      category: null
      activity: ''
      question: ''
      tests: []
    - code: 'DCF-177'
      name: 'Event Logging'
      description: '%s has a defined plan for event logging that establishes the required criteria for logs, protection of logged information, clock synchronization.'
      activity: "1. Develop and maintain event logs with defined required criteria, such as user IDs, system activities, and use of privileges\n2. Implement protection measures against unauthorized changes to log information\n3. Review logs for accountability for the privileged users\n4. Define the internal and external requirements for time representation, synchronization, and accuracy"
      question: 'Does the organization have an event logging plan in place?'
      tests: []
      domain: 'Infrastructure Security'
      category: 'Logging'
    - code: 'DCF-178'
      name: 'Record Management and Control'
      description: '%s implemented procedures for the control of documented information relevant for its management system(s).'
      activity: ''
      question: ''
      tests: []
      domain: 'Information Security Management System'
      category: 'Support'
    - code: 'DCF-178.AI'
      name: 'Record Management and Control (AI)'
      description: '%s implemented procedures for the control of documented information relevant for its AI management system.'
      domain: null
      category: null
      activity: ''
      question: ''
      tests: []
    - code: 'DCF-179'
      name: 'Competence Records'
      description: '%s has identified and documented skill and competence requirements for personnel that contribute to the development, implementation and oversight of its management system(s) and retains documented evidence of competence.'
      activity: ''
      question: ''
      tests: []
      domain: 'Information Security Management System'
      category: 'Support'
    - code: 'DCF-180'
      name: 'Secure Information Transfer'
      description: '%s has defined and documented policies and procedures for the secure transfer of information within the organization and with any external parties.'
      activity: ''
      question: ''
      tests: []
      domain: 'Internal Security Procedures'
      category: 'Vulnerability Management'
    - code: 'DCF-181'
      name: 'Encryption Policy'
      description: '%s has a documented policy that establishes requirements for the use of cryptographic controls.'
      activity: ''
      question: ''
      tests:
          - id: 127
      domain: 'Infrastructure Security'
      category: 'Protecting Secrets'
    - code: 'DCF-182'
      name: 'Asset Management Policy'
      description: '%s has established and documented a policy that outlines requirements for the management and tracking of company assets.'
      activity: ''
      question: ''
      tests: []
      domain: 'Internal Security Procedures'
      category: 'Vulnerability Management'
    - code: 'DCF-183'
      name: 'Vulnerability Management Policy'
      description: '%s has a defined policy that establishes requirements for vulnerability management across the organization, including monitoring, cataloging, and assigning risk ratings to vulnerabilities to prioritize remediation efforts.'
      activity: ''
      question: ''
      tests:
          - id: 27
      domain: 'Internal Security Procedures'
      category: 'Vulnerability Management'
    - code: 'DCF-184'
      name: 'Management System Plan'
      description: '%s has a defined and documented a plan for the establishment, implementation, maintenance, and continuous improvement of its management systems(s).'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-184.AI'
      name: 'Management System Plan (AI)'
      description: '%s has a defined and documented a plan for the establishment, implementation, maintenance, and continuous improvement of its AI management system.'
      domain: null
      category: null
      activity: ''
      question: ''
      tests: []
    - code: 'DCF-185'
      name: 'Threat Intelligence'
      description: '%s has implemented mechanisms to collect threat information and produce threat intelligence (e.g., commercial cyber threat intelligence tools, security product/vendor intelligence feeds, open source feeds, etc.) in accordance with defined threat intelligence objectives.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-186'
      name: 'Data De-identification'
      description: '%s has implemented mechanisms for the de-identification of data that has been classified as sensitive (e.g., data masking, anonymization, pseudonymization, etc.).'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-187'
      name: 'Configuration Management Plan'
      description: '%s has a defined Configuration Management Plan that outlines the proper procedures to manage and protect new and existing configurations.'
      activity: "1. Establish, document, and implement procedures to ensure new and existing configurations are properly managed for hardware, software, services, and networks\n2. Ensure proper roles and responsibilities are in place to support configuration management\n3. Review and update plan periodically\n4. Maintain change logs"
      question: 'Does the organization have a configuration management plan?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-188'
      name: 'Communication with Advisories and Special Interest Groups'
      description: '%s exchanges information with relevant security and privacy organizations, professional associations, and other specialist forums, including information on newly identified threats and vulnerabilities, new technologies, etc. (e.g., through bulletin subscriptions, email alerts from security advisories, participation in conferences, etc.).'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-189'
      name: 'Activity Review'
      description: '%s performs a review of information system activities on regular intervals'
      activity: "1. Regularly review audit logs\n2. Regularly review security incident tracking reports\n3. Regularly review access reports"
      question: 'Does the organization perform a review of information system activities on regular intervals?'
      tests: []
      domain: 'Organizational Security'
      category: 'Security Program'
    - code: 'DCF-190'
      name: 'Designated Security Officials'
      description: '%s has formally assigned responsibility for information security in the organization to a Chief Information Security Officer or other security-knowledgeable member of management.'
      activity: ''
      question: ''
      tests: []
      domain: 'Organizational Security'
      category: 'Security Program'
    - code: 'DCF-191'
      name: 'Security Updates'
      description: '%s has documented procedures for periodic communication of security updates and reminders to all personnel, and other interested parties when appropriate'
      activity: "1. Establish a well-defined process for communicating security updates and reminders\n2. Define the methods for communication (e.g., email, newsletter, posters, etc.)\n3. Define the frequency of the communication"
      question: 'Does the organization provide periodic security reminders and updates to all members of its workforce and contractors periodically?'
      tests: []
      domain: 'Internal Security Procedures'
      category: 'Security Issues'
    - code: 'DCF-192'
      name: 'Privacy, Use, and Disclosure'
      description: '%s has a defined policy that establishes the requirements of the HIPAA Privacy Rule'
      activity: "1. Identify and define all applicable uses and disclosures that are applicable and authorized\n2. Identify and outline the procedures for safeguarding privacy and private health information"
      question: 'Does the organization have policies and procedures that address the HIPAA Privacy Rule requirements?'
      tests: []
      domain: 'Privacy'
      category: 'Communication of Objectives Related to Privacy Practices'
    - code: 'DCF-193'
      name: 'Breach Notification'
      description: '%s information security policies should be augmented by a statement concerning support for and commitment to achieving compliance with applicable PII protection legislation and the contractual terms agreed between the public cloud PII processor and its clients (cloud service customers).'
      activity: "1. Establish a process for breach notification based on applicable regulations (e.g., HIPAA, GDPR, CCPA, etc.)\n2. Ensure that the process includes appropriate reporting timelines (e.g., 60 days for HIPAA, 72 hours for GDPR, immediately for CCPA), content, and methods outlined by those regulations"
      question: 'Does the organization have policies and procedures for notifying appropriate parties of a breach of sensitive information?'
      tests: []
      domain: 'Internal Security Procedures'
      category: 'Breach Notification'
    - code: 'DCF-194'
      name: 'Group Health Plans'
      description: '%s has a defined policy that establishes the requirements related to Group Health Plans'
      activity: 'Identify all applicable requirements pertaining to Group Health Plans and how to implement those requirements'
      question: 'Does the group health plan have specific criteria required for group health plans and plan sponsors?'
      tests: []
      domain: 'Organizational Security'
      category: 'Security Policies'
    - code: 'DCF-195'
      name: 'Business Associate Agreements'
      description: '%s has a defined policy that establishes the requirements related to Business Associate Agreements'
      activity: "1. Identify all applicable requirements pertaining to Business Associate Agreements and how to implement those requirements\n2. Develop a Business Associate Agreement per HIPAA standards\n3. Note applicable situations where a written agreement can be replaced by other methods"
      question: 'Does the organization have specific criteria required for written contracts or other arrangements between a covered entity and its business associates?'
      tests: []
      domain: 'Organizational Security'
      category: 'Security Policies'
    - code: 'DCF-196'
      name: 'HIPAA Awareness Training'
      description: '%s has established a training program for the use and disclosure of protected health information (PHI) to help personnel understand their obligations and responsibilities related to HIPAA. All eligible members of the workforce are required to complete this training during onboarding and annually thereafter.'
      activity: "Mechanisms exist to provide HIPAA privacy awareness training: \n1. Upon hire\n2. Before authorizing access to the system or performing assigned duties that involve PHI; \n3. When required by system changes; and \n4. Annually thereafter, at the least"
      question: "Does the organization provide HIPAA privacy awareness training that is specific to the proper use and disclosure of PHI?\n\nDoes the organization document, retain and monitor individual training activities, including basic security awareness training, ongoing awareness training and specific-system training?"
      tests: []
      domain: 'Privacy'
      category: 'Communication of Objectives Related to Privacy Practices'
    - code: 'DCF-197'
      name: 'Document Retention Period'
      description: "%s retains HIPAA-related policies and procedures for at least 6 years from the date of the document's creation or when it was last in effect (whichever is later)."
      activity: 'All documents that are required by HIPAA, must be retained and available for at least 6 years in physical or electronic format. The time period begins from the later of the following two: Date of document creation OR date the document was last in effect'
      question: 'Does the organization maintain required documentation for a period of six years?'
      tests: []
      domain: 'Organizational Security'
      category: 'Personnel Security'
    - code: 'DCF-201'
      name: 'Network Security Controls Configuration Standards'
      description: '%s has defined, documented and implemented configuration standards for network security controls, including configurations for firewalls, routers configured with access control lists, and cloud virtual networks. All services, protocols, and ports allowed are identified, documented, approved, and have a defined business need.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-202'
      name: 'CDE Network Diagram'
      description: '%s has a current network diagram that identifies all connections between the cardholder data environment and other networks, including any wireless networks.'
      activity: ''
      question: 'Is there a current network diagram that documents all connections between the cardholder data environment and other networks, including any wireless networks?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-203'
      name: 'CDE Network Diagram - Review'
      description: '%s has a process to ensure the network diagram is kept current.'
      activity: ''
      question: 'Is there a process to ensure the diagram is kept current?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-204'
      name: 'Dataflow Diagram'
      description: 'A dataflow diagram is maintained to show all account data flows across systems and networks. The diagram is reviewed and approved by management at least annually and updated as necessary when there are changes to the environment.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-205'
      name: 'Dataflow Diagram Review'
      description: '%s has a process to ensure the Dataflow Diagram is kept current.'
      activity: ''
      question: 'Is there a process to ensure the diagram is kept current?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-206'
      name: 'Network Security Controls Between Trusted and Untrusted Networks'
      description: '%s has implemented network security controls between trusted and untrusted networks to prevent unauthorized traffic from traversing network boundaries.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-207'
      name: 'Network Diagram is consistent with Firewall Configuration'
      description: '%s has a current network diagram consistent with the firewall configuration standards.'
      activity: ''
      question: 'Is the current network diagram consistent with the firewall configuration standards?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-208'
      name: 'Network Management Roles and Responsibilities'
      description: '%s defines groups, roles, and responsibilities for management of network components.'
      activity: ''
      question: 'Are groups, roles, and responsibilities for logical management of network components assigned and documented in the firewall and router configuration standards?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-209'
      name: 'Services, Protocols, and Ports Approval List'
      description: '%s has a documentation of business justification and approval for use of all services, protocols, and ports allowed, including documentation of security features implemented for those protocols considered to be insecure.'
      activity: ''
      question: 'Do firewall and router configuration standards include a documented list of services, protocols, and ports, including business justification and approval for each?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-210'
      name: 'Insecure Services, Protocols, and Ports Documentation and Control'
      description: '%s identifies all services, protocols, and ports in use considered to be in use. %s identifies, documents and implements security features for each insecure service, protocol, or port in use, such that the risk is mitigated.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-211'
      name: 'Firewall and Router Rule Review Standard'
      description: '%s has a requirement to review firewall and router rule sets at least every six months.'
      activity: ''
      question: 'Do firewall and router configuration standards require review of firewall and router rule sets at least every six months?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-212'
      name: 'Network Security Controls Review'
      description: '%s performs a review of network security controls at least once every six months. Results of the review are documented and configurations identified as no longer being supported by a business justification are removed or updated.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-213'
      name: 'Network Traffic Restrictions'
      description: '%s restricts inbound and outbound traffic to that which is necessary for the cardholder data environment, and specifically denies all other traffic.'
      activity: ''
      question: 'Is inbound and outbound traffic restricted to that which is necessary for the cardholder data environment?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-214'
      name: 'Network Traffic Denial'
      description: '%s ensures that all other inbound and outbound traffic is specifically denied (for example by using an explicit “deny all” or an implicit deny after allow statement)'
      activity: ''
      question: 'Is all other inbound and outbound traffic specifically denied (for example by using an explicit “deny all” or an implicit deny after allow statement)?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-215'
      name: 'Secured Configuration Files'
      description: 'Configuration files for network security controls (including files, automated and system-based controls, scripts, settings, infrastructure as code, or other parameters used to configure and synchronize network security controls) are secured from unauthorized access and kept consistent with active network configurations.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-216'
      name: 'Network Security Controls Restricting Wireless Network Traffic'
      description: 'Network security controls are implemented to deny all traffic from wireless networks into the environment by default and only allow wireless traffic with authorized business purpose.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-217'
      name: 'Prohibited Direct Public Access to Data Environment'
      description: '%s prohibits direct public access between the Internet and any system component in the cardholder data environment.'
      activity: ''
      question: 'Is direct public access prohibited between the Internet and any system component in the cardholder data environment, as follows:'
      tests: []
      domain: null
      category: null
    - code: 'DCF-218'
      name: 'Inbound Traffic Restricted Between Untrusted and Trusted Networks '
      description: 'Inbound traffic from untrusted networks is restricted to communications with system components that are authorized to provide publicly accessible services, protocols, and ports, and to stateful responses to communications initiated by system components in a trusted network. All other traffic is denied.'
      activity: ''
      question: ''
      tests:
          - id: 209
      domain: null
      category: null
    - code: 'DCF-219'
      name: 'DMZ IP Addresses'
      description: '%s limits inbound Internet traffic to IP addresses within the DMZ.'
      activity: ''
      question: 'Is inbound Internet traffic limited to IP addresses within the DMZ?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-220'
      name: 'Anti-Spoofing Measures'
      description: '%s has implemented anti-spoofing measures to detect and block forged source IP addresses from entering the trusted network.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-221'
      name: 'Explicit Authorization for CDE Outbound Traffic'
      description: '%s restricts unauthorized outbound traffic from the cardholder data environment to the Internet.'
      activity: ''
      question: 'Is outbound traffic from the cardholder data environment to the Internet explicitly authorized?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-222'
      name: 'Permit Established Connections Only'
      description: '%s only permits “established” connections into the network.'
      activity: ''
      question: 'Are only established connections permitted into the network?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-223'
      name: 'Sensitive Data Not Directly Accessible From Untrusted Networks '
      description: 'Network security controls are in place such that system components storing sensitive data are not directly accessible from untrusted networks.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-224'
      name: 'Prevention of Private IP Information Disclosure'
      description: '%s private IP addresses and routing information are not disclosed to unauthorized parties.'
      activity: ''
      question: "Are methods in place to prevent the disclosure of private IP addresses and routing information to the Internet?\nNote: Methods to obscure IP addressing may include, but are not limited to:\nNetwork Address Translation (NAT)\nPlacing servers containing cardholder data behind proxy servers/firewalls, \nRemoval or filtering of route advertisements for private networks that employ registered addressing,\nInternal use of RFC1918 address space instead of registered addresses."
      tests: []
      domain: null
      category: null
    - code: 'DCF-225'
      name: 'External Private IP Information Disclosure Restricted'
      description: '%s has implemented mechanisms to restrict disclosure of internal IP addresses and routing information to only authorized parties (for example, network address translation (NAT), proxy servers, etc.)'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-226'
      name: 'Personal Firewall Installed on Portable Devices'
      description: "%s has installed personal firewall software or equivalent functionality on any portable computing devices (including company and/or employee-owned) that connect to the Internet when outside the network (for example, laptops used by employees), and which are also used to access the CDE. Firewall (or equivalent) configurations include:\n \n* Specific configuration settings are defined. \n* Personal firewall (or equivalent functionality) is actively running. \n* Personal firewall (or equivalent functionality) is not alterable by users of the portable computing devices."
      activity: ''
      question: 'Is personal firewall software (or equivalent functionality) installed and active on any portable computing devices (including company and/or employee-owned) that connect to the Internet when outside the network (for example, laptops used by employees), and which are also used to access the CDE?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-227'
      name: 'Personal Firewall on Portable Devices Configured Properly'
      description: '%s has configured personal firewall software (or equivalent functionality) to specific configuration settings, actively running, and not alterable by users of mobile and/or employee-owned devices.'
      activity: ''
      question: 'Is the personal firewall software (or equivalent functionality) configured to specific configuration settings, actively running, and not alterable by users of mobile and/or employee-owned devices?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-228'
      name: 'Firewall Security Policy'
      description: '%s ensures that security policies and operational procedures for managing firewalls are documented, in use, and known to all affected parties.'
      activity: ''
      question: "Are security policies and operational procedures for managing firewalls: \n  Documented\n In use\n Known to all affected parties?"
      tests: []
      domain: null
      category: null
    - code: 'DCF-229'
      name: 'Vendor Default Accounts Disabled, Removed or Changed'
      description: "All vendor-supplied default accounts are either disabled or removed, or their default password is changed in accordance with the company's policy and compliance requirements."
      activity: ''
      question: ''
      tests:
          - id: 8020
      domain: null
      category: null
    - code: 'DCF-230'
      name: 'Unnecessary Default Accounts Removed/Disabled'
      description: '%s ensures that unnecessary default accounts are removed or disabled before installing a system on the network.'
      activity: ''
      question: 'Are unnecessary default accounts removed or disabled before installing a system on the network?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-231'
      name: 'Changes in Encryption Keys for Wireless Environments'
      description: 'For wireless environments connected to the environment or transmitting account data, encryption keys are changed whenever personnel with knowledge of the key leave the company or change roles and whenever a key is suspected of or known to be compromised.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-232'
      name: 'SNMP Community Strings Changed '
      description: '%s changes default SNMP community strings on wireless devices at installation.'
      activity: ''
      question: 'Are default SNMP community strings on wireless devices changed at installation?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-233'
      name: 'Wireless Network Vendor Defaults Changed'
      description: 'For wireless environments connected to the environment or transmitting sensitive data, all wireless vendor defaults are changed at installation or are confirmed to be secure, including but not limited to default wireless encryption keys, passwords on wireless access points, simple network management protocol (SNMP) defaults, and any other security-related wireless vendor defaults.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-234'
      name: 'Updated Firmware on Wireless Devices'
      description: '%s ensures that firmware on wireless devices is updated to support strong encryption for authentication and transmission over wireless networks.'
      activity: ''
      question: 'Is firmware on wireless devices updated to support strong encryption for authentication and transmission over wireless networks?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-235'
      name: 'Wireless Vendor Defaults Changed'
      description: '%s ensures that other security-related wireless vendor defaults are changed, if applicable.'
      activity: ''
      question: 'Are other security-related wireless vendor defaults changed, if applicable?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-236'
      name: 'Update Configuration Standards after New Vulnerabilities'
      description: '%s ensures that system configuration standards are updated as new vulnerability issues are identified, as defined in Requirement 6.1.'
      activity: ''
      question: 'Are system configuration standards updated as new vulnerability issues are identified, as defined in Requirement 6.1?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-237'
      name: 'System Configuration Standards'
      description: "%s's system configuration standards include all of the following: \nChanging of all vendor-supplied defaults and elimination of unnecessary default accounts; implementing only one primary function per server to prevent functions that require different security levels from coexisting on the same server; enabling only necessary services, protocols, daemons, etc., as required for the function of the system; implementing additional security features for any required services, protocols or daemons that are considered to be insecure; configuring system security parameters to prevent misuse; removing all unnecessary functionality, such as scripts, drivers, features, subsystems, file systems, and unnecessary web servers."
      activity: ''
      question: "Do system configuration standards include all of the following: \nChanging of all vendor-supplied defaults and elimination of unnecessary default accounts?\nImplementing only one primary function per server to prevent functions that require different security levels from co-existing on the same server?\nEnabling only necessary services, protocols, daemons, etc., as required for the function of the system?\nImplementing additional security features for any required services, protocols or daemons that are considered to be insecure?\nConfiguring system security parameters to prevent misuse?\nRemoving all unnecessary functionality, such as scripts, drivers, features, subsystems, file systems, and unnecessary web servers?"
      tests: []
      domain: null
      category: null
    - code: 'DCF-238'
      name: 'One Primary Function per Server'
      description: '%s has Implemented only one primary function per server to prevent functions that require different security levels from coexisting on the same server.'
      activity: ''
      question: "Is only one primary function implemented per server, to prevent functions that require different security levels from co-existing on the same server?\nFor example, web servers, database servers, and DNS should be implemented on separate servers."
      tests: []
      domain: null
      category: null
    - code: 'DCF-239'
      name: 'One Primary Function per System Component '
      description: '%s has implemented technical measures so that primary functions with lower security needs cannot affect the security of primary functions with higher security needs on the same system component (for example, assigning one primary function per system component, isolating functions that exist within the same system component, or securing all functions within the same system component to the level required by the function with the highest security need).'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-240'
      name: 'Only Necessary System Function Services Used'
      description: '%s uses only necessary services, software programs, protocols, daemons, and functions in system components, and all unnecessary functionality (e.g., scripts, drivers, features, subsystems, file systems, interfaces, unused web servers, programs, etc.) is removed or disabled in accordance with documented configuration standards.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-241'
      name: 'Documentation and Risk Mitigation for Insecure Services'
      description: '%s has documented business justification and implemented additional security features for any required services, protocols, or daemons in use that are considered insecure so that the risk is mitigated.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-242'
      name: 'Additional Security Features for Enabled Insecure Services'
      description: '%s has implemented additional security features for any required services, protocols, or daemons that are considered to be insecure.'
      activity: ''
      question: 'Are additional security features documented and implemented for any required services, protocols or daemons that are considered to be insecure?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-243'
      name: 'Proficiency in Common Security Parameter System Settings'
      description: '%s has configured system security parameters to prevent misuse.'
      activity: ''
      question: 'Are system administrators and/or personnel that configure system components knowledgeable about common security parameter settings for those system components?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-244'
      name: 'System Security Parameters in Configuration Standards'
      description: 'Security parameters in organizational systems are configured in accordance with documented secure configuration standards.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-245'
      name: 'Security Parameter Settings Set Appropriately'
      description: "%s's security parameter settings are set appropriately on system components."
      activity: ''
      question: 'Are security parameter settings set appropriately on system components?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-246'
      name: 'Unnecessary Functions Removed'
      description: '%s has removed all unnecessary functionality, such as scripts, drivers, features, subsystems, file systems, and unnecessary web servers.'
      activity: ''
      question: 'Has all unnecessary functionality—such as scripts, drivers, features, subsystems, file systems, and unnecessary web servers—been removed?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-247'
      name: 'Enabled Functions Documented'
      description: "%s's enabled functions are documented and support secure configuration."
      activity: ''
      question: 'Are enabled functions documented and do they support secure configuration?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-248'
      name: 'Documented Functionality on System Components'
      description: '%s ensures that only documented functionality is present on system components.'
      activity: ''
      question: 'Is only documented functionality present on system components?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-249'
      name: 'Encrypted Non-Console Administrative Access'
      description: 'All non-console administrative access is encrypted using strong cryptography, including includes administrative access via browser-based interfaces and application programming interfaces (APIs).'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-250'
      name: 'Insecure Remote Login Commands Prevented'
      description: '%s ensures system services and parameter files are configured to prevent the use of Telnet and other insecure remote login commands.'
      activity: ''
      question: 'Are system services and parameter files configured to prevent the use of Telnet and other insecure remote login commands?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-251'
      name: 'Vendor Management Security Policies and Operational Procedures Documented and Accessible'
      description: '%s ensures that security policies and operational procedures for managing vendor defaults and other security parameters are documented, in use, and known to all affected parties.'
      activity: ''
      question: "Are security policies and operational procedures for managing vendor defaults and other security parameters: \nDocumented\nIn use\nKnown to all affected parties?"
      tests: []
      domain: null
      category: null
    - code: 'DCF-252'
      name: 'Shared Hosting Provider Secure Configurations'
      description: "%s ensures that shared hosting providers protect each entity’s hosted environment and cardholder data. These providers must meet specific requirements as detailed in Appendix A1:\n Additional PCI DSS Requirements for Shared Hosting Providers."
      activity: ''
      question: "If you are a shared hosting provider, are your systems configured to protect each entity’s (your customers’) hosted environment and cardholder data? \nSee Appendix A1: Additional PCI DSS Requirements for Shared Hosting Providers for specific requirements that must be met."
      tests: []
      domain: null
      category: null
    - code: 'DCF-253'
      name: 'Data Secure Disposal'
      description: '%s disposes of data securely upon expiration of the established retention periods, when requested by customers, or when no longer needed for legal, regulatory, and/or business reasons.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-254'
      name: 'Cardholder Data Retention Requirements'
      description: '%s has specific retention requirements for cardholder data.'
      activity: ''
      question: "Are there specific retention requirements for cardholder data? \nFor example, cardholder data needs to be held for X period for Y business reasons."
      tests: []
      domain: null
      category: null
    - code: 'DCF-255'
      name: 'Quarterly Cardholder Data Disposal Review'
      description: '%s verifies at least once every three months that stored account data exceeding the defined retention period has been securely deleted or rendered unrecoverable. Evidence of the verification is documented and retained.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-256'
      name: 'Cardholder Data meets Data Retention Policy Requirements'
      description: '%s ensures that all stored cardholder data meet the requirements defined in the data-retention policy.'
      activity: ''
      question: 'Does all stored cardholder data meet the requirements defined in the data-retention policy?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-257'
      name: 'Sensitive Authentication Data Storage'
      description: '%s maintains documentation of the legitimate business justification to store sensitive authentication data (if sensitive authentication data is stored after the authorization process).'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-258'
      name: 'Sensitive Authentication Data Secured'
      description: '%s encrypts sensitive authentication data (SAD) that is stored electronically prior to completion of authorization using strong cryptography.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-259'
      name: 'Sensitive Authentication Data Deleted after Authorization Process'
      description: 'If sensitive authentication data (SAD) is received, %s deletes and renders the data unrecoverable upon completion of the authorization process.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-260'
      name: 'Full Track Contents Not Retained'
      description: 'The full contents of any track are not stored upon completion of the authorization process.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-261'
      name: 'Card Verification Code Not Retained'
      description: 'The card verification code is not stored upon completion of the authorization process.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-262'
      name: 'PIN Not Stored'
      description: 'The personal identification number (PIN) and the PIN block are not stored upon completion of the authorization process.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-263'
      name: 'PAN is Masked when Displayed'
      description: '%s has implemented technical measures to mask primary account numbers (PANs) when displayed (on screen, paper receipts, etc.) such that only personnel with a legitimate business need can see more than the bank identification number (BIN) and last four digits of the PAN.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-264'
      name: 'PAN Unreadable Where Stored'
      description: 'Primary account numbers (PANs) are rendered unreadable anywhere they are stored, including primary storage (databases, or flat files such as text files spreadsheets) as well as non-primary storage (backup, audit logs, exception, or troubleshooting logs).'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-265'
      name: 'Separate Encrypted File System Access Management'
      description: 'Disk encryption implementations are configured to require independent authentication and logical access controls for decryption to protect data in the event of physical loss of a disk.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-266'
      name: 'Cryptographic Keys Stored Securely'
      description: '%s stores cryptographic keys securely.'
      activity: ''
      question: 'Are cryptographic keys stored securely (for example, stored on removable media that is adequately protected with strong access controls)?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-267'
      name: 'Sensitive Data on Removable Media Encrypted'
      description: 'Sensitive data on removable storage media is encrypted wherever stored.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-268'
      name: 'Cryptographic Architecture Description (Service Providers Only)'
      description: '%s maintains a documented description of the cryptographic architecture in place, including details of all algorithms, protocols, and keys used for the protection of stored account data, including key strength and expiry date, preventing the use of the same cryptographic keys in production and test environments, description of the key usage for each key, and inventory of any hardware security modules (HSMs), key management systems (KMS), and other secure cryptographic devices (SCDs) used for key management, including type and location of devices.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-269'
      name: 'Restricted Cleartext Key Access'
      description: '%s restricts access to cleartext cryptographic key components to the fewest number of custodians necessary to reduce the risk of stored data being retrieved or rendered visible by unauthorized parties.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-270'
      name: 'Key-Encrypting Keys Secured'
      description: 'Key-encrypting keys used are at least as strong as the data-encrypting keys they protect and are stored separately from data-encrypting keys.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-271'
      name: 'Key Storage Locations Limited'
      description: '%s stores cryptographic keys in the fewest possible locations to minimize the potential for keys to be exposed to unauthorized parties.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-272'
      name: 'Guidance for Shared Key Management'
      description: 'Where %s shares cryptographic keys with its customers for transmission or storage of account data, the company documents and distributes guidance on secure transmission, storage and updating of such keys to those customers.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-273'
      name: 'Strong Key Generation Policies and Procedures'
      description: 'Key-management policies and procedures are documented and implemented including: generation of strong cryptographic keys, secure distribution, and secure storage of cryptographic keys used to protect sensitive data.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-274'
      name: 'Secure Key Distribution Procedure'
      description: "%s's cryptographic key procedures include secure cryptographic key distribution"
      activity: ''
      question: 'Do cryptographic key procedures include secure cryptographic key distribution?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-275'
      name: 'Secure Key Storage Procedure'
      description: "%s's cryptographic key procedures include secure cryptographic key storage"
      activity: ''
      question: 'Do cryptographic key procedures include secure cryptographic key storage?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-276'
      name: 'Key Changes for Retired Keys'
      description: 'A defined cryptoperiod for each key type in use as defined by the associated application vendor or key owner is documented. %s changes encryption keys when they reach the end of their cryptoperiod in accordance with documented policies and procedures.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-277'
      name: 'Key Retirement Procedures'
      description: "%s's cryptographic key procedures include suspension, retirement or replacement of cryptographic keys when the integrity of the key has been weakened."
      activity: ''
      question: 'Do cryptographic key procedures include suspension, retirement or replacement (for example, archiving, destruction, and/or revocation) of cryptographic keys when the integrity of the key has been weakened (for example, departure of an employee with knowledge of a clear-text key)?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-278'
      name: 'Key Retirement Policies and Procedures'
      description: '%s retires, replaces or destructs cryptographic keys that are no longer used or needed or when the key expires, the integrity of the key has been weakened, or the key is known or suspected to be compromised, in accordance with documented company policies and procedures. Retired or replaced keys are not used for encryption operations.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-279'
      name: 'Retained Keys Used for Decryption/Verification'
      description: '%s ensures that if retired or replaced cryptographic keys need to be retained, they are securely archived, and only used for decryption/verification purposes.'
      activity: ''
      question: 'If retired or replaced cryptographic keys are retained, are these keys only used for decryption/verification purposes, and not used for encryption operations?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-280'
      name: 'Split Knowledge and Dual Control of Keys'
      description: 'Split knowledge and dual control are used to manage operations where manual cleartext cryptographic key management is performed in accordance with documented company policies and procedures.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-281'
      name: 'Prevention of Unauthorized Key Substitution'
      description: 'Key management policies and procedures are documented and implemented to include the prevention of unauthorized substitution of cryptographic keys.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-282'
      name: 'Formal Acknowledgment of Key Custodial Responsibilities'
      description: '%s requires cryptographic key custodians to formally acknowledge that they understand and accept their key-custodian responsibilities in accordance with documented company policies and procedures.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-283'
      name: 'Secure and Encrypted Data Transmission'
      description: '%s uses strong cryptography and security protocols to safeguard sensitive data during transmission over open, public networks.'
      activity: 'Ensure strong encryption during data transmission over public networks.'
      question: "Are strong cryptography and security protocols used to safeguard sensitive data during transmission over open, public networks? \nNote: Examples of open, public networks include but are not limited to the Internet; wireless technologies, including 802.11 and Bluetooth; cellular technologies, for example, Global System for Mobile communications (GSM), Code division multiple access (CDMA); and General Packet Radio Service (GPRS)."
      tests: []
      domain: null
      category: null
    - code: 'DCF-284'
      name: ' Key and Certificate Validation'
      description: '%s has implemented security mechanisms so that only trusted keys and/or certificates are accepted during transmission of sensitive data that are confirmed valid and not expired or revoked.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-285'
      name: 'Insecure Versions or Configurations Not Supported'
      description: 'Security protocols in use for transmission of sensitive data support only secure versions or configurations and do not support fallback to, or use of insecure versions, algorithms, key sizes, or implementations.'
      activity: ''
      question: ''
      tests:
          - id: 263
          - id: 8006
          - id: 8015
      domain: null
      category: null
    - code: 'DCF-286'
      name: 'Proper Encryption Strength'
      description: '%s has implemented a proper encryption strength for the encryption methodology in use, during transmission of sensitive information (e.g., PII, PHI, Cardholder Data, etc.).'
      activity: ''
      question: 'Is the proper encryption strength implemented for the encryption methodology in use (check vendor recommendations/best practices)?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-287'
      name: 'TLS Enabled during Data Transmission'
      description: '%s enables TLS whenever sensitive information (e.g., PII, PHI, Cardholder Data, etc.) is transmitted or received.'
      activity: ''
      question: "For TLS implementations, is TLS enabled whenever sensitive information (e.g., PII, PHI, Cardholder Data, etc.) is transmitted or received? \nFor example, for browser-based implementations:\n“HTTPS” appears as the browser Universal Record Locator (URL) protocol, and\nSensitive information (e.g., PII, PHI, Cardholder Data, etc.) is only requested if “HTTPS” appears as part of the URL."
      tests: []
      domain: null
      category: null
    - code: 'DCF-288'
      name: 'Strong Encryption for Wireless Network Transmission'
      description: 'Wireless networks transmitting sensitive data or connected to the environment use strong protocols for authentication and encryption of data transmissions.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-289'
      name: 'Sensitive Data Secured in Transmission via End-User Messaging Technologies'
      description: '%s encrypts sensitive data with strong cryptography when transmitted via e-mail, instant messaging, SMS, chat or other end-user messaging technologies.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-290'
      name: 'Unprotected PANs Not Sent via End-User Messaging Technologies'
      description: '%s has policies in place that state that unprotected PANs are not to be sent via end-user messaging technologies.'
      activity: ''
      question: 'Are policies in place that state that unprotected PANs are not to be sent via end-user messaging technologies?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-291'
      name: 'Anti-Malware on All System Components'
      description: 'An anti-malware solution is deployed on all system components, except for those system components identified through periodic risk assessments that concludes the system components are not at risk from malware.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-292'
      name: 'Periodic Evaluation of Malware Threats'
      description: '%s maintains a documented list of all system components evaluated as not at risk for malware that are not subjected to anti-malware controls. %s performs periodic evaluations to identify and assess evolving malware threats for those system components and to confirm whether such system components continue to not require anti-malware protection. Results of the periodic evaluation are documented.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-293'
      name: 'Anti-Malware Capabilities and Automatic Updates'
      description: 'The deployed anti-malware solution is kept current via automatic updates and configured to detect all known types of malware and to remove, block, or contain all known types of malware.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-294'
      name: 'Anti-Malware Tools Behavior'
      description: 'The implemented anti-malware solutions are configured to perform periodic scans and active/real-time scans (e.g., scanning files from external sources as they are downloaded, opened, or executed) or to perform continuous behavioral analysis of systems or processes.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-295'
      name: 'Anti-Virus Audit Logs'
      description: '%s ensures that all anti-virus mechanisms generate audit logs, and logs are retained.'
      activity: ''
      question: 'Are all anti-virus mechanisms generating audit logs, and are logs retained?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-296'
      name: 'Access to Anti-Virus Configuration'
      description: '%s restricts access to disable or alter anti-malware mechanisms to authorized personnel based on documented approval by management on a case-by-case basis for a limited time period.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-297'
      name: 'Patch Management'
      description: "%s has implemented a formal patch management process where critical patches/updates (as identified per the entity's vulnerability risk analysis) are installed within one month of release. All other applicable security patches/updates are installed within the timeframe established by the entity per the risk analysis and company policies and procedures."
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-298'
      name: 'Information Security in SDLC'
      description: '%s ensures that information security is included throughout the software-development life cycle.'
      activity: ''
      question: 'Is information security included throughout the software-development life cycle?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-299'
      name: 'Software Development in line with PCI'
      description: '%s ensures that software applications are developed in accordance with PCI DSS.'
      activity: ''
      question: 'Are software applications developed in accordance with PCI DSS (for example, secure authentication and logging)?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-300'
      name: 'Removal of Account Information before Application Release'
      description: '%s removes development, test and/or custom application accounts, user IDs, and passwords before applications become active or are released to customers.'
      activity: ''
      question: 'Are development, test, and/or custom application accounts, user IDs, and passwords removed before applications become active or are released to customers?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-301'
      name: 'Code Review prior to Release'
      description: "%s reviews custom code prior to release to production or customers in order to identify any potential coding vulnerability (using either manual or automated processes) to include at least the following:\n* Code changes are reviewed by individuals other than the originating code author, and by individuals knowledgeable about code-review techniques and secure coding practices. \n* Code reviews ensure code is developed according to secure coding guidelines \n* Appropriate corrections are implemented prior to release. \n* Code-review results are reviewed and approved by management prior to release."
      activity: ''
      question: "Is all custom code reviewed prior to release to production or customers to identify any potential coding vulnerability (using either manual or automated processes as follows:\nAre code changes reviewed by individuals other than the originating code author, and by individuals who are knowledgeable about code review techniques and secure coding practices?\nDo code reviews ensure code is developed according to secure coding guidelines?\nAre appropriate corrections implemented prior to release? \nAre code review results reviewed and approved by management prior to release?\nNote: This requirement for code reviews applies to all custom code (both internal and public-facing), as part of the system development life cycle. Code reviews can be conducted by knowledgeable internal personnel or third parties. Public-facing web applications are also subject to additional controls, to address ongoing threats and vulnerabilities after implementation, as defined at PCI DSS Requirement 6.6."
      tests: []
      domain: null
      category: null
    - code: 'DCF-302'
      name: 'Separate Test and Production Environments Access Control'
      description: '%s has access control in place to enforce the separation between the development/test environments and the production environment.'
      activity: ''
      question: 'Is access control in place to enforce the separation between the development/test environments and the production environment?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-303'
      name: 'Separation of Duties in Test and Production Environments'
      description: '%s has separation of duties between development/test and production environments.'
      activity: ''
      question: 'Is there separation of duties between personnel assigned to the development/test environments and those assigned to the production environment?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-304'
      name: 'Test Data Removed before System Activation'
      description: 'Test data and test accounts are removed from system components before the system goes into production.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-305'
      name: 'Production Components Change Control Procedures '
      description: 'Changes to all system components in the production environment (including software, code, infrastructure, network, configuration changes, etc.) are made according to established procedures that include documentation (change description, justification, evaluation of security requirements and impact, approval by authorized parties, rollback procedures, etc.) and testing (including acceptance and security impact testing).'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-306'
      name: 'Documentation of Authorized Party Approval'
      description: '%s has documented change approval by authorized parties.'
      activity: ''
      question: 'Are change-control procedures documented and require the documented approval by authorized parties?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-307'
      name: 'Functionality Testing'
      description: "%s's change-control procedures include functionality testing to verify that the change does not adversely impact the security of the system."
      activity: ''
      question: 'Are change-control procedures documented and require the functionality testing to verify that the change does not adversely impact the security of the system?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-308'
      name: 'Updates for PCI Compliance Testing'
      description: "%s's change-control procedures include, for custom code changes, the testing of updates for compliance with PCI DSS Requirement 6.5 before being deployed into production."
      activity: ''
      question: 'Are change-control procedures documented and require, for custom code changes, the testing of updates for compliance with PCI DSS Requirement 6.5 before being deployed into production?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-309'
      name: 'Back-out Procedures'
      description: "%s's change-control procedures include back-out procedures."
      activity: ''
      question: 'Are change-control procedures documented and require the back-out procedures?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-310'
      name: 'PCI Requirements Validation Upon Changes'
      description: '%s verifies all system components after a change to validate they are compliant with the applicable PCI DSS requirements. Record of the validation is documented and retained, and updates to PCI DSS requirements documentation are made as applicable based on the nature of the change.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-311'
      name: 'Common Coding Vulnerabilities'
      description: '%s addresses common coding vulnerabilities in software-development processes.'
      activity: ''
      question: 'Do software-development processes address common coding vulnerabilities?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-312'
      name: 'Secure Code Development Training'
      description: 'Developers are required to complete secure code development training at least once every 12 months, including training on software security relevant to their job function and development languages, secure software design and secure coding techniques, and how to use tools for detecting vulnerabilities in software if these are used in the organization.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-313'
      name: 'Application Development based on Secure Coding Guidelines'
      description: '%s develops applications based on secure coding guidelines.'
      activity: ''
      question: "Are applications developed based on secure coding guidelines to protect applications from, at a minimum, the following vulnerabilities:\nNote: The vulnerabilities listed at 6.5.1 through 6.5.10 were current with industry best practices when this version of PCI DSS was published. However, as industry best practices for vulnerability management are updated (for example, the Open Web Application Security Project (OWASP) Guide, SANS CWE Top 25, CERT Secure Coding, etc.), the current best practices must be used for these requirements."
      tests: []
      domain: null
      category: null
    - code: 'DCF-314'
      name: 'Injection Flaws'
      description: "%s's coding techniques address Injection flaws, particularly SQL injection."
      activity: ''
      question: "Do coding techniques address injection flaws, particularly SQL injection?\nNote: Also consider OS Command Injection, LDAP and XPath injection flaws as well as other injection flaws."
      tests: []
      domain: null
      category: null
    - code: 'DCF-315'
      name: 'Buffer Overflow'
      description: "%s's coding techniques address buffer overflows."
      activity: ''
      question: 'Do coding techniques address buffer overflow vulnerabilities?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-316'
      name: 'Insecure Cryptographic Storage'
      description: "%s's coding techniques address insecure cryptographic storage."
      activity: ''
      question: 'Do coding techniques address insecure cryptographic storage?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-317'
      name: 'Insecure Communications'
      description: "%s's coding techniques address insecure communications."
      activity: ''
      question: 'Do coding techniques address insecure communications?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-318'
      name: 'Improper Error Handling'
      description: "%s's coding techniques address improper error handling."
      activity: ''
      question: 'Do coding techniques address improper error handling?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-319'
      name: 'High Risk Vulnerabilities'
      description: "%s's coding techniques address all “high risk” vulnerabilities identified in the vulnerability identification process."
      activity: ''
      question: 'Do coding techniques address all “high risk” vulnerabilities identified in the vulnerability identification process (as defined in PCI DSS Requirement 6.1)?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-320'
      name: 'Cross-Site Scripting (XSS)'
      description: "%s's coding techniques address cross-site scripting (XSS)."
      activity: ''
      question: 'Do coding techniques address cross-site scripting (XSS) vulnerabilities?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-321'
      name: 'Improper Access Control'
      description: "%s's coding techniques address improper access control (such as insecure direct object references, failure to restrict URL access, directory traversal, and failure to restrict user access to functions)."
      activity: ''
      question: 'Do coding techniques address improper access control such as insecure direct object references, failure to restrict URL access, directory traversal, and failure to restrict user access to functions?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-322'
      name: 'Cross-Site Request Forgery (CSRF)'
      description: "%s's coding techniques address cross-site request forgery (CSRF)."
      activity: ''
      question: 'Do coding techniques address cross-site request forgery (CSRF)?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-323'
      name: 'Broken Authentication and Session Management'
      description: "%s's coding techniques address broken authentication and session management."
      activity: ''
      question: 'Do coding techniques address broken authentication and session management?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-324'
      name: 'Public-Facing Web Application Vulnerability Assessment'
      description: '%s evaluates public-facing web applications via manual or automated application vulnerability security assessment tools at least once every 12 months and after significant changes. Vulnerabilities identified, if any, are risk-ranked and corrected, and the application is re-evaluated after the corrections.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-325'
      name: 'Policy for Secure Systems and Applications Documented and Accessible'
      description: '%s has security policies and operational procedures for developing and maintaining secure systems and applications are documented, in use, and known to all affected parties.'
      activity: ''
      question: "Are security policies and operational procedures for developing and maintaining secure systems and applications:\nDocumented\nIn use\nKnown to all affected parties?"
      tests: []
      domain: null
      category: null
    - code: 'DCF-326'
      name: 'Need-to-Know Principle'
      description: '%s restricts access to system components and data to only those individuals whose job requires such access.'
      activity: ''
      question: ''
      tests:
          - id: 208
      domain: null
      category: null
    - code: 'DCF-327'
      name: 'System Access Roles Defined'
      description: '%s defines access needs for each role, including: System components and data resources that each role needs to access for their job function; Level of privilege required for accessing resources.'
      activity: ''
      question: "Are access needs for each role defined, including:\nSystem components and data resources that each role needs to access for their job function?\nLevel of privilege required (for example, user, administrator, etc.) for accessing resources?"
      tests: []
      domain: null
      category: null
    - code: 'DCF-328'
      name: 'Documented Approval by Authorized Parties'
      description: '%s requires documented approval by authorized parties specifying required privileges.'
      activity: ''
      question: 'Is documented approval by authorized parties required, specifying required privileges?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-329'
      name: 'Access Control System'
      description: 'For all system components, access is managed via an access control system. The access control system(s) is configured to enforce permissions assigned to individuals, applications, and systems based on job classification and function and is set to “deny all” by default.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-330'
      name: 'Access Control Model'
      description: '%s has defined and implemented an access control model for all system components (e.g., role-based access control (RBAC), attribute-based access control (ABAC), policy-based access control (PBAC), etc.).'
      activity: ''
      question: ''
      tests:
          - id: 8037
      domain: null
      category: null
    - code: 'DCF-331'
      name: 'Default "Deny All" on Access Control System'
      description: "%s's access control system(s) has a default “deny-all” setting."
      activity: ''
      question: 'Does the access control system(s) have a default “deny-all” setting?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-332'
      name: 'Policy for Restricting Access is Documented and Available'
      description: '%s has security policies and operational procedures for restricting access to cardholder data are documented, in use, and known to all affected parties.'
      activity: ''
      question: "Are security policies and operational procedures for restricting access to cardholder data:\nDocumented\nIn use\nKnown to all affected parties?"
      tests: []
      domain: null
      category: null
    - code: 'DCF-333'
      name: 'Unique User ID'
      description: '%s assigns all users a unique ID before allowing them to access system components or cardholder data.'
      activity: ''
      question: 'Are all users assigned a unique ID before allowing them to access system components or cardholder data?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-334'
      name: 'Privileged and General User ID Authorization'
      description: '%s controls addition, deletion, and modification of user IDs, credentials, and other identifier objects.'
      activity: ''
      question: 'Are additions, deletions, and modifications of user IDs, credentials, and other identifier objects controlled such that user IDs are implemented only as authorized (including with specified privileges)?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-335'
      name: 'Inactive User Accounts Removed'
      description: '%s removes or disables inactive accounts within a specified period of inactivity.'
      activity: ''
      question: ''
      tests:
          - id: 229
      domain: null
      category: null
    - code: 'DCF-336'
      name: 'Third Party Remote Access Monitored'
      description: 'Accounts used by third parties to access, support, or maintain system components via remote access are enabled during the time period needed based on documented authorization by management and disabled when not in use. Third-party remote access is monitored by company personnel for unexpected activity.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-337'
      name: 'Access to Accounts Used by Remote 3rd Parties Monitored'
      description: '%s ensures that accounts used by third parties to access, support, or maintain system components via remote access are monitored when in use.'
      activity: ''
      question: 'Are vendor remote access accounts monitored when in use?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-338'
      name: 'User ID Lockout After Repeated Access Attempts'
      description: '%s limits repeated access attempts by locking out the user ID after not more than six attempts.'
      activity: ''
      question: 'Are repeated access attempts limited by locking out the user ID after no more than six attempts?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-339'
      name: 'Account Lockout after Failed Logins'
      description: 'Invalid authentication attempts are limited by locking out the user ID after not more than 10 failed attempts.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-340'
      name: 'Lockout Duration'
      description: '%s has configured account lockout duration following a set number of invalid authentication attempts to a minimum of 30 minutes or until the identity of the user is confirmed (for example, by a system administrator).'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-341'
      name: 'Reauthentication of Idle Sessions'
      description: '%s ensures that if a session has been idle for more than 15 minutes, require the user to re-authenticate to re-activate the terminal or session.'
      activity: ''
      question: 'If a session has been idle for more than 15 minutes, are users required to re-authenticate (for example, re-enter the password) to re-activate the terminal or session?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-342'
      name: 'User Authentication Methods'
      description: "%s ensures that in addition to assigning a unique ID, ensure proper user-authentication management for non-consumer users and administrators on all system components by employing at least one of the following methods to authenticate all users: \n* Something you know, such as a password or passphrase \n* Something you have, such as a token device or smart card \n* Something you are, such as a biometric."
      activity: ''
      question: "In addition to assigning a unique ID, is one or more of the following methods employed to authenticate all users?\nSomething you know, such as a password or passphrase\nSomething you have, such as a token device or smart card\nSomething you are, such as a biometric"
      tests: []
      domain: null
      category: null
    - code: 'DCF-343'
      name: 'Strong Encryption of Authentication Credentials During Transmission and Storage'
      description: 'Strong cryptographic protocols are used to render all authentication credentials (e.g.,passwords, passphrases, etc.) unreadable during transmission and storage on all system components.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-344'
      name: 'Strong Encryption of Non-consumer Customer Authentication Credentials During Transmission and Storage (Service Provider Only)'
      description: '%s uses strong cryptography to render all non-consumer customers’ authentication credentials unreadable during transmission and storage on all system components.'
      activity: ''
      question: 'For service providers only: Is strong cryptography used to render all non-consumer customers’ authentication credentials (such as passwords/passphrases) unreadable during transmission and storage on all system components?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-345'
      name: 'User Identity Verification Before Modifying Authentication'
      description: 'User identity is verified before allowing changes to any authentication factor (for example, performing password resets, provisioning new tokens, or generating new keys). Verification is done through secret question/answers, knowledge-based information, or other mechanisms.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-346'
      name: 'Minimum Strong Password Requirements'
      description: 'Minimum password requirements are enforced on system components including a minimum length of 12 characters (or if the system does not support 12 characters, a minimum length of eight characters) and complexity requirements to include both numbers and letters.'
      activity: ''
      question: ''
      tests:
          - id: 215
      domain: null
      category: null
    - code: 'DCF-347'
      name: 'Minimum Password Requirements for Non-consumer Customer (Service Providers Only)'
      description: '%s requires non-consumer customer passwords to meet the following minimum length and complexity requirements: A minimum password length of at least seven characters; contain both numeric and alphabetic characters'
      activity: ''
      question: "For service providers only: Are non-consumer customer passwords required to meet the following minimum length and complexity requirements?\nA minimum password length of at least seven characters\nContain both numeric and alphabetic characters"
      tests: []
      domain: null
      category: null
    - code: 'DCF-348'
      name: 'Periodic Password Change for In-Scope Components Not In the CDE'
      description: 'For in-scope components that are not in the cardholder data environment (CDE) where passwords/passphrases are used as the only authentication factor, credentials are required to be changed at least once every 90 days. Alternatively, technical measures are implemented to dynamically analyze the security posture of accounts, and real-time access to resources is automatically determined accordingly.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-349'
      name: 'Guidance Provided to Customers for Password Changes'
      description: 'Where customer user access to cardholder data is achieved only through passwords/passphrases (i.e., single factor authentication), %s provides guidance to customer users as to how frequently, and under what circumstances, they should change their passwords.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-350'
      name: 'Password History Enforcement'
      description: 'System configuration settings are in place to prevent password reuse in accordance with company policy and compliance requirements.'
      activity: ''
      question: ''
      tests:
          - id: 216
      domain: null
      category: null
    - code: 'DCF-351'
      name: 'Non-consumer Customer Passwords Different from Last Four (Service Providers Only)'
      description: '%s requires new, non-consumer customer passwords to be different from any of the last four passwords used.'
      activity: ''
      question: 'For service providers only:  Are new, non-consumer customer passwords required to be different from any of the last four passwords used?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-352'
      name: 'Unique First-time Passwords With One-Time Use'
      description: 'Passwords are set to a unique value for first-time use and upon reset. Temporary initial passwords are forced to be changed immediately after the first use.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-353'
      name: 'MFA for Non-Admin and Remote CDE Access'
      description: '%s secures all individual non-console administrative access and all remote access to the CDE using multi-factor authentication.'
      activity: ''
      question: "Is all individual non-console administrative access and all remote access to the CDE secured using multi-factor authentication, as follows:\nNote: Multi-factor authentication requires that a minimum of two of the three authentication methods (see PCI DSS Requirement 8.2 for descriptions of authentication methods) be used for authentication. Using one factor twice (for example, using two separate passwords) is not considered multi-factor authentication."
      tests: []
      domain: null
      category: null
    - code: 'DCF-354'
      name: 'MFA for Non-Console Admin Access'
      description: 'Multi-factor authentication (MFA) is required for all non-console access into the environment for personnel with administrative access.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-355'
      name: 'MFA for Remote Access'
      description: 'All remote access to the entity’s network and systems (including that of users, administrators, and external access from third parties or vendors including access for maintenance sessions) requires multi-factor authentication.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-356'
      name: 'Communication of Authentication Best Practices'
      description: '%s has documented policies and procedures for authentication that are communicated to all personnel. These documents include guidance on selecting strong authentication factors, guidance on protecting authentication credentials, instructions not to reuse previously used credentials, instructions to change authentication credentials in the event of known or suspected compromise along with guidance on how to report the incident, etc.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-357'
      name: 'Shared Authentication Methods are Prohibited'
      description: '%s does not use group, shared, or generic IDs, passwords, or other authentication methods as follows: Generic user IDs are disabled or removed; shared user IDs do not exist for system administration and other critical functions; shared and generic user IDs are not used to administer any system components.'
      activity: ''
      question: "Are group, shared, or generic accounts, passwords, or other authentication methods prohibited as follows: \nGeneric user IDs and accounts are disabled or removed;\nShared user IDs for system administration activities and other critical functions do not exist; and\nShared and generic user IDs are not used to administer any system components?"
      tests: []
      domain: null
      category: null
    - code: 'DCF-358'
      name: 'Unique Authentication Credential for Service Providers with Remote Access'
      description: "%s's authentication factors and credentials used to access customer environments remotely are unique for each customer."
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-359'
      name: 'Authentication Mechanism Use'
      description: '%s ensures that where other authentication mechanisms are used, use of these mechanisms are assigned as follows: Authentication mechanisms must be assigned to an individual account and not shared among multiple accounts; physical and/or logical controls must be in place to ensure only the intended account can use that mechanism to gain access.'
      activity: ''
      question: "Where other authentication mechanisms are used (for example, physical or logical security tokens, smart cards, and certificates, etc.), is the use of these mechanisms assigned as follows?\nAuthentication mechanisms must be assigned to an individual account and not shared among multiple accounts\nPhysical and/or logical controls must be in place to ensure only the intended account can use that mechanism to gain access"
      tests: []
      domain: null
      category: null
    - code: 'DCF-360'
      name: 'Direct Query Access Restricted'
      description: 'Direct query access to cardholder data repositories is restricted via applications or other programmatic methods (e.g., stored procedures) with access and allowed actions based on user roles and least privileges, unless performed by an authorized administrator.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-361'
      name: 'Direct Access to Database Restrictions'
      description: '%s restricts user direct access to or queries of databases to database administrators.'
      activity: ''
      question: 'Is user direct access to or queries of databases restricted to database administrators?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-362'
      name: 'Application IDs Only Used by the Application'
      description: '%s ensures that application IDs are only able to be used by the applications (and not by individual users or other processes).'
      activity: ''
      question: 'Are application IDs only able to be used by the applications (and not by individual users or other processes)?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-363'
      name: 'Entry Controls in Place'
      description: 'Entry controls (e.g., badge access systems, biometrics readers, monitored reception areas or front desks, etc.) are in place to restrict physical access to corporate facilities, including systems or areas that may process or store sensitive data, to authorized personnel, and to log and monitor such access.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-364'
      name: 'Physical Access Controlled'
      description: '%s has developed and approved a list of individuals with authorized physical access to the facilities, equipment, and operating environments, which is maintained up-to-date and reviewed periodically to validate the ongoing appropriateness of access. Authorized individuals are issued physical authentication credentials (e.g., identification badges, identification cards, smart cards, etc.).'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-365'
      name: 'Secure Physical Access Control Mechanisms'
      description: '%s physical surveillance mechanisms (e.g., video monitoring systems, sensors and detectors) are in place to deter and detect unauthorized physical access and are protected from tampering or disabling.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-366'
      name: 'Physical Access Control Mechanism Periodic Data Review'
      description: 'Data collected from video cameras and/or access control mechanisms are reviewed and correlated with other entries (e.g., access logs) on a periodic basis and as needed (e.g., upon suspicious physical security activity).'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-367'
      name: 'Physical Access Control Mechanism Data Retention'
      description: 'Data collected from video cameras and/or access control mechanisms is stored for at least three months unless otherwise restricted by law.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-368'
      name: 'Restricted Physical Access to Publicly Accessible Network Jacks'
      description: '%s has implemented physical and/or logical controls to restrict access to publicly accessible network jacks.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-369'
      name: 'Restricted Physical Access to Network Components'
      description: '%s restricts physical access to wireless access points, gateways, networking/communications hardware, and telecommunication lines within the company facilities.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-370'
      name: 'Onsite Identification Management'
      description: '%s has procedures to easily distinguish between onsite personnel and visitors, to include: Identifying onsite personnel and visitors (for example, assigning badges); changes to access requirements; revoking or terminating onsite personnel and expired visitor identification (such as ID badges).'
      activity: ''
      question: "Are procedures developed to easily distinguish between onsite personnel and visitors, which include:\nIdentifying onsite personnel and visitors (for example, assigning badges), \nChanging access requirements, and \nRevoking terminated onsite personnel and expired visitor identification (such as ID badges)\nFor the purposes of Requirement 9, “onsite personnel” refers to full-time and part-time employees, temporary employees, contractors and consultants who are physically present on the entity’s premises. A “visitor” refers to a vendor, guest of any onsite personnel, service workers, or anyone who needs to enter the facility for a short duration, usually not more than one day."
      tests: []
      domain: null
      category: null
    - code: 'DCF-371'
      name: 'Onsite Identification Methods'
      description: "%s's identification methods clearly identify visitors and easily distinguish between onsite personnel and visitors."
      activity: ''
      question: 'Do identification methods (such as ID badges) clearly identify visitors and easily distinguish between onsite personnel and visitors?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-372'
      name: 'Restricted Access to Badge System'
      description: '%s restricts access to the identification or badge system to authorized personnel based on need-to-know principles.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-373'
      name: 'Role-Based Physical Access'
      description: '%s controls physical access for onsite personnel to sensitive areas as follows: Access must be authorized and based on individual job function; access is revoked immediately upon termination, and all physical access mechanisms are returned or disabled.'
      activity: ''
      question: "Is physical access to sensitive areas controlled for onsite personnel, as follows:\nIs access authorized and based on individual job function?\nIs access revoked immediately upon termination\nUpon termination, are all physical access mechanisms, such as keys, access cards, etc., returned or disabled?"
      tests: []
      domain: null
      category: null
    - code: 'DCF-374'
      name: 'Visitors Authorized and Escorted'
      description: 'Visitors are authorized before entering, and escorted at all times within company facilities including areas where sensitive data may be processed or stored.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-375'
      name: 'Personnel and Visitor Badges'
      description: '%s personnel are required to wear a badge or other form of identification within company facilities. %s provides visitors with a badge or other form of identification that visibly distinguishes visitors from onsite personnel.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-376'
      name: 'Visitor Badge Expiration'
      description: "%s's visitor badges or other identification have an expiration date."
      activity: ''
      question: 'Do visitor badges or other identification expire?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-377'
      name: 'Visitor Badge Control'
      description: 'Visitor badges or identification are surrendered or deactivated before visitors leave the facility or at the date of expiration.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-378'
      name: 'Visitor Log'
      description: '%s maintains visitor logs to keep an audit trail of visitor activity to the company facilities, computer rooms, or data centers where sensitive data may be stored or transmitted.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-379'
      name: 'Visitor Log Inclusions'
      description: "%s' visitor logs include, at a minimum, the visitor’s name and the organization represented, the date and time of the visit, and the name of the personnel authorizing physical access."
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-380'
      name: 'Visitor Log Retention'
      description: '%s retains visitor logs for a minimum of three months, unless otherwise restricted by law.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-381'
      name: 'Media Physically Secured'
      description: 'Media with sensitive data is physically secured to prevent unauthorized persons from gaining access to it.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-382'
      name: 'Security of Offline Media Backup Storage'
      description: '%s stores offline media backups in a secure location (e.g., off-site facility, commercial storage facility, etc.) with security measures to protect the confidentiality of the information. The security of the location is reviewed at least once every 12 month through inspection of the facilities. Results of the review are documented.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-383'
      name: 'Media Transfer Procedures '
      description: '%s maintains strict control over the internal or external distribution of any kind of media.'
      activity: ''
      question: 'Is strict control maintained over the internal or external distribution of any kind of media?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-384'
      name: 'Media Classification'
      description: "All media with sensitive data is classified in accordance with the nature of the data and the company's data classification policy."
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-385'
      name: 'Media Transported Securely'
      description: "Media with sensitive data transported within or outside the company's facilities is logged, securely transported (e.g., via secure courier or other trackable method), and captured within tracking logs to include details about media location, responsible party, etc."
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-386'
      name: 'Management Approval for Media Transport'
      description: "Management approves all assets (including media with sensitive data) that are moved within or outside the facility, including when the assets are distributed to individuals. Documentation of management's approval for the movement of assets is retained."
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-387'
      name: 'Media Storage and Accessibility'
      description: '%s maintains strict control over the storage and accessibility of media.'
      activity: ''
      question: 'Is strict control maintained over the storage and accessibility of media?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-388'
      name: 'Media Inventory Logs'
      description: '%s maintains documented inventory all electronic media with sensitive data. A verification of the inventory is conducted at least once every 12 months in accordance with company procedures.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-389'
      name: 'Periodic Inventory of Media Logs'
      description: '%s conducts media inventories at least annually.'
      activity: ''
      question: 'Are periodic media inventories conducted at least annually?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-390'
      name: 'Media Destruction  '
      description: 'Electronic media is destroyed or sensitive data is rendered unrecoverable so that it cannot be reconstructed when no longer needed for business or legal reasons.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-391'
      name: 'Media Destruction Policies and Procedures'
      description: '%s has policies and procedures for the destruction of electronic media when no longer needed for business or legal reasons.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-392'
      name: 'Destruction of Hardcopy Material'
      description: "%s's media destruction policy includes cross-cut shredding, incinerating, or pulping hardcopy materials, so that cardholder data cannot be reconstructed."
      activity: ''
      question: 'Are hardcopy materials cross-cut shredded, incinerated, or pulped so that cardholder data cannot be reconstructed?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-393'
      name: 'Storage Containers for Destroyable Material'
      description: "%s's media destruction policy includes use of storage containers for materials that contain information to be destroyed secured to prevent access to the contents."
      activity: ''
      question: 'Are storage containers used for materials that contain information to be destroyed secured to prevent access to the contents?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-394'
      name: 'Cardholder Data Unrecoverable on Electronic Media upon Deletion'
      description: '%s renders cardholder data on electronic media unrecoverable so that cardholder data cannot be reconstructed.'
      activity: ''
      question: 'Is cardholder data on electronic media rendered unrecoverable (e.g. via a secure wipe program in accordance with industry-accepted standards for secure deletion, or otherwise by physically destroying the media), so that cardholder data cannot be reconstructed?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-395'
      name: 'Payment Card Device Management Policy'
      description: '%s protects devices that capture payment card data via direct physical interaction with the card from tampering and substitution.'
      activity: ''
      question: "Are devices that capture payment card data via direct physical interaction with the card protected against tampering and substitution as follows?\nNote: This requirement applies to card-reading devices used in card-present transactions (that is, card swipe or dip) at the point of sale. This requirement is not intended to apply to manual key-entry components such as computer keyboards and POS keypads."
      tests: []
      domain: null
      category: null
    - code: 'DCF-396'
      name: 'List of Payment Card Capture Devices Maintained'
      description: '%s maintains an up-to-date list of devices to include: Make, model of device; location of device; device serial number or other method of unique identification.'
      activity: ''
      question: 'Do policies and procedures require that a list of such devices be maintained?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-397'
      name: 'Payment Card Capture Devices Periodic Inspection'
      description: '%s performs periodic inspections of point of interaction (POI) device surfaces to detect tampering and unauthorized substitution in accordance with documented company policies and procedures.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-398'
      name: 'Payment Card Capture Device Training and Reporting'
      description: '%s ensures that policies and procedures require that personnel are trained to be aware of suspicious behavior and to report tampering or substitution of devices.'
      activity: ''
      question: 'Do policies and procedures require that personnel are trained to be aware of suspicious behavior and to report tampering or substitution of devices?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-399'
      name: 'Payment Card Capture Device List Inclusions'
      description: "%s's list of devices includes: Make and model of device; location of device; and device serial number or other method of unique identification."
      activity: ''
      question: "Does the list of devices include the following?\nMake, model of device \nLocation of device (for example, the address of the site or facility where the device is located) \nDevice serial number or other method of unique identification"
      tests: []
      domain: null
      category: null
    - code: 'DCF-400'
      name: 'Accurate and Updated Payment Card Capture Device List'
      description: "%s's list of devices is accurate and up to date."
      activity: ''
      question: 'Is the list accurate and up to date?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-401'
      name: 'Payment Card Capture Device List Updates'
      description: "%s's list of devices is updated when devices are added, relocated, decommissioned, etc."
      activity: ''
      question: 'Is the list of devices updated when devices are added, relocated, decommissioned, etc.?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-402'
      name: 'Payment Card Capture Device Surface Inspection'
      description: '%s periodically inspects device surfaces to detect tampering, or substitution.'
      activity: ''
      question: "Are device surfaces periodically inspected to detect tampering (for example, addition of card skimmers to devices), or substitution (for example, by checking the serial number or other device characteristics to verify it has not been swapped with a fraudulent device)?\nNote: Examples of signs that a device might have been tampered with or substituted include unexpected attachments or cables plugged into the device, missing or changed security labels, broken or differently colored casing, or changes to the serial number or other external markings."
      tests: []
      domain: null
      category: null
    - code: 'DCF-403'
      name: 'Payment Card Capture Device Inspection Procedures'
      description: '%s personnel are aware of procedures for inspecting devices.'
      activity: ''
      question: 'Are personnel aware of procedures for inspecting devices?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-404'
      name: 'Training Material for Payment Card Capture Device Tampering Awareness'
      description: '%s provides periodic training for personnel in point-of-interaction (POI) environments to be aware of attempted tampering or replacement of POI devices including: verifying the identity of any third-party persons claiming to be repair or maintenance personnel prior to granting them access to modify or troubleshoot devices; procedures to ensure devices are not installed, replaced, or returned without verification; being aware of suspicious behavior around devices, and reporting suspicious behavior and indications of device tampering or substitution to appropriate personnel.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-405'
      name: 'Training for Payment Card Capture Device Tampering Awareness and Reporting Received'
      description: "%s's personnel at point-of-sale locations receive training, and are aware of procedures to detect and report attempted tampering or replacement of devices."
      activity: ''
      question: 'Have personnel at point-of-sale locations received training, and are they aware of procedures to detect and report attempted tampering or replacement of devices?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-406'
      name: 'Audit Logging'
      description: 'Audit logs are enabled and active for all system components and sensitive data in accordance with company policies.'
      activity: ''
      question: ''
      tests:
          - id: 221
          - id: 224
          - id: 226
          - id: 243
          - id: 244
          - id: 245
          - id: 246
          - id: 247
          - id: 248
          - id: 249
          - id: 251
          - id: 252
          - id: 256
          - id: 257
          - id: 310
          - id: 8005
          - id: 250
      domain: null
      category: null
    - code: 'DCF-407'
      name: 'Audit Logs Data Points'
      description: '%s has configured audit logs to contain user or identity, type of event, date and time, success and failure indication, origination of event, affected data, and system component, resource, or service.'
      activity: ''
      question: ''
      tests:
          - id: 243
          - id: 244
          - id: 245
          - id: 246
          - id: 247
          - id: 248
          - id: 249
          - id: 251
          - id: 252
          - id: 256
          - id: 250
      domain: null
      category: null
    - code: 'DCF-408'
      name: 'Audit Trail for Individual User Access to Cardholder Data'
      description: 'Automated audit trails or logs are implemented for all system components to capture all access to cardholder data.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-409'
      name: 'Audit Trail for Privileged Access'
      description: 'Automated audit trails or logs are implemented for all system components to capture all actions taken by any identities with administrative access, including execution of privileged functions and any interactive use of application or system accounts.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-410'
      name: 'Audit Trail Access Logging'
      description: 'Automated audit trails or logs are implemented for all system components to capture all access to audit logs.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-411'
      name: 'Audit Trail for Invalid Access Attempts'
      description: 'Automated audit trails or logs are implemented for all system components to capture all invalid access attempts.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-412'
      name: 'Audit Trail for Identification and Authentication Mechanism Changes'
      description: 'Automated audit trails or logs are implemented to capture all changes to identification and authentication credentials (e.g., creation of new accounts, elevation of privileges, changes, additions, or deletions to accounts with administrative access, etc.).'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-413'
      name: 'Audit Trail of Changes to Audit Logs'
      description: 'Automated audit trails or logs are implemented for all system components to capture initialization of new audit logs and all starting, stopping, or pausing of the existing audit logs.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-414'
      name: 'Audit Trail of System-Level Object Changes'
      description: 'Automated audit trails or logs are implemented for all system components to capture all creation and deletion of system-level objects.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-415'
      name: 'Audit Trail Entries: User Identification'
      description: '%s ensures that audit trail entries are recorded for all system components for user identification.'
      activity: ''
      question: 'Are the following audit trail entries recorded for all system components for user identification?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-416'
      name: 'Audit Trail Entries: Event Type'
      description: '%s ensures that audit trail entries are recorded for all system components for type of event.'
      activity: ''
      question: 'Are the audit trail entries recorded for all system components for type of event?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-417'
      name: 'Audit Trail Entries: Date and Time'
      description: '%s ensures that audit trail entries are recorded for all system components for date and time.'
      activity: ''
      question: 'Are the audit trail entries recorded for all system components for date and time?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-418'
      name: 'Audit Trail Entries: Pass/Fail Indication'
      description: '%s ensures that audit trail entries are recorded for all system components for success or failure indication.'
      activity: ''
      question: 'Are the audit trail entries recorded for all system components for success or failure indication?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-419'
      name: 'Audit Trail Entries: Origination'
      description: '%s ensures that audit trail entries are recorded for all system components for origination of an event.'
      activity: ''
      question: 'Are the audit trail entries recorded for all system components for origination of event?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-420'
      name: 'Audit Trail Entries: Affected Item Name'
      description: '%s ensures that audit trail entries are recorded for all system components for identity or name of affected data, system component, or resource.'
      activity: ''
      question: 'Are the audit trail entries recorded for all system components for identity or name of affected data, system component, or resource?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-421'
      name: 'Clock Synchronization'
      description: '%s synchronizes all critical system clocks and times using time-synchronization technology such as Network Time Protocol (NTP).'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-422'
      name: 'Time-related System Parameters'
      description: 'Systems are configured so that one or more designated central time servers are in use and receiving time from industry-accepted external sources based on International Atomic Time or Coordinated Universal Time (UTC).'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-423'
      name: 'Time Server Peering'
      description: 'Where there is more than one designated time server, the time servers peer with one another to keep accurate time.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-424'
      name: 'System Time Source'
      description: 'Internal systems receive time information only from designated central time server or servers.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-425'
      name: 'Need-to-Know Access to Time Data'
      description: 'Access to modify time synchronization configurations or system time is restricted to authorized system administrators or personnel with a business need.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-426'
      name: 'Time Settings Changes Logged, Monitored, Reviewed'
      description: 'Any changes to time synchronization configurations or system time on critical systems are logged, monitored, and reviewed in accordance with company policies and procedures.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-427'
      name: 'Time Settings Source'
      description: '%s receives time settings from specific, industry-accepted time sources.'
      activity: ''
      question: "Are time settings received from specific, industry-accepted time sources? (This is to prevent a malicious individual from changing the clock). \nOptionally, those updates can be encrypted with a symmetric key, and access control lists can be created that specify the IP addresses of client machines that will be provided with the time updates (to prevent unauthorized use of internal time servers)."
      tests: []
      domain: null
      category: null
    - code: 'DCF-428'
      name: 'Secured Audit Trails'
      description: '%s secures audit trails so they cannot be altered.'
      activity: ''
      question: 'Are audit trails secured so they cannot be altered?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-429'
      name: 'Limited Access to Audit Trails'
      description: 'Access to audit log files and associated configurations is limited to those with a job-related need as authorized by management.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-430'
      name: 'Audit Trail Files Protected'
      description: 'Audit log files are protected to prevent modifications by individuals (e.g., via access control mechanisms, physical segregation, network segregation, etc.)'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-431'
      name: 'Audit Trail Files Backed Up'
      description: 'Audit log files, including those for external facing technologies, are promptly backed up to a secure, central, internal log server(s) or other media that is difficult to modify.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-432'
      name: 'Logs for External-Facing Technologies'
      description: '%s writes logs for external-facing technologies onto a secure, centralized, internal log server or media device.'
      activity: ''
      question: 'Are logs for external-facing technologies (for example, wireless, firewalls, DNS, mail) written onto a secure, centralized, internal log server or media?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-433'
      name: 'FIM on Logs'
      description: '%s uses file-integrity monitoring or change-detection software on logs to ensure that existing log data cannot be changed without generating alerts (although new data being added should not cause an alert).'
      activity: ''
      question: 'Is file-integrity monitoring or change-detection software used on logs to ensure that existing log data cannot be changed without generating alerts (although new data being added should not cause an alert)?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-434'
      name: 'Policies and Procedures for Logging'
      description: '%s has documented policies and procedures for logging and monitoring that describe the events the organization must log and monitor, the general systems and system components that should be monitored, the specific information that must be captured in logs, the configuration of specific elements of the logging infrastructure, etc. The identified logged/monitored events are reviewed and updated periodically in accordance with company requirements.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-435'
      name: 'Critical System Logs Reviewed Daily'
      description: '%s performs reviews of the following critical audit logs at least daily (e.g., through the use of alerting mechanisms): all security events, logs of all system components that store, process, or transmit CHD and/or SAD, logs of all critical system components, and logs of all servers and system components that perform security functions.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-436'
      name: 'Policy for Non-critical Systems Periodic Log Review'
      description: '%s has policies and procedures for reviewing logs of all other system components periodically—either manually or via log tools—based on the organization’s policies and risk management strategy.'
      activity: ''
      question: 'Are written policies and procedures defined for reviewing logs of all other system components periodically—either manually or via log tools—based on the organization’s policies and risk management strategy?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-437'
      name: 'Periodic Review of Non-Critical Logs'
      description: '%s performs reviews of non-critical system logs periodically based on the organization’s policies and risk management strategy, as determined by a risk assessment.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-438'
      name: 'Follow-up Procedures on Log Review Anomalies and Exceptions'
      description: "Exceptions and anomalies identified during the periodic log-review process are investigated, escalated, and resolved in accordance with the company's documented policies and procedures."
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-439'
      name: 'Follow-up on Discovered Anomalies and Exceptions Performed'
      description: '%s ensures that follow up to exceptions and anomalies are performed.'
      activity: ''
      question: 'Is follow up to exceptions and anomalies performed?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-440'
      name: 'Policy for Audit Log Retention'
      description: '%s has audit log retention policies and procedures in place.'
      activity: ''
      question: 'Are audit log retention policies and procedures in place and do they require that logs are retained for at least one year, with a minimum of three months immediately available for analysis (for example, online, archived, or restorable from backup)?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-441'
      name: 'Audit Log Retention Period'
      description: '%s retains audit log history and historical records of activity for a specified frequency (e.g., minimum of 90 days for CIS 8; or at least 12 months, with at least the most recent three months immediately available for analysis, for PCI 4).'
      activity: ''
      question: ''
      tests:
          - id: 8019
      domain: null
      category: null
    - code: 'DCF-442'
      name: 'Audit Logs Available for Analysis'
      description: "%s has the three most current months' logs, at the least, immediately available for analysis."
      activity: ''
      question: 'Are at least the last three months’ logs immediately available for analysis?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-443'
      name: 'Critical Security Control System Failure Detection and Reporting'
      description: '%s has implemented processes for the timely detection and reporting of failures of critical security control systems, including but not limited to failure of: Firewalls, IDS/IPS, FIM, anti-virus, physical access controls, logical access controls, audit logging mechanisms, segmentation controls (if used).'
      activity: ''
      question: "Are processes implemented for the timely detection and reporting of failures of critical security control systems, including but not limited to failure of:\nFirewalls \nIDS/IPS \nFIM \nAnti-virus \nPhysical access controls \nLogical access controls \nAudit logging mechanisms \nSegmentation controls (if used)"
      tests: []
      domain: null
      category: null
    - code: 'DCF-444'
      name: 'Critical Security Control System Failure Alert'
      description: '%s has implemented alerting mechanisms to notify personnel of failures of critical security control systems (including network security controls, IDS/IPS, change-detection mechanisms, anti-malware solutions, physical access controls, logical access controls, audit logging mechanisms, segmentation controls, audit log review mechanisms, automated security testing tools, etc.). Failures of critical security control systems are evaluated as a security event and investigated in accordance with company policies and procedures.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-445'
      name: 'Critical Security Control System Failure Response'
      description: 'Failures of any critical security controls systems are addressed promptly based on the nature of the failure and monitoring of security controls is resumed. Documentation is maintained to include identification of the issue, start time and end time, root cause and required remediation, identification of any security issues that arose during the failure along with associated response, identification of follow-up actions are required as a result of the security failure, and implemented controls to prevent the cause of failure from reoccurring.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-446'
      name: 'Critical Security Control System Failure Documentation'
      description: '%s documents failures in critical security controls, and includes: Identification of cause(s) of the failure, including root cause; duration (date and time start and end) of the security failure; details of the remediation required to address the root cause.'
      activity: ''
      question: "Are failures in critical security controls documented, including:\nIdentification of cause(s) of the failure, including root cause\nDuration (date and time start and end) of the security failure\nDetails of the remediation required to address the root cause?"
      tests: []
      domain: null
      category: null
    - code: 'DCF-447'
      name: 'Policy for Network Access Monitoring Documented and Accessible'
      description: '%s has security policies and operational procedures for monitoring all access to network resources and sensitive information (e.g., PII, PHI, Cardholder Data, etc.) that are documented, in use, and known to all affected parties.'
      activity: ''
      question: "Are security policies and operational procedures for monitoring all access to network resources and sensitive information (e.g., PII, PHI, Cardholder Data, etc.):\nDocumented\nIn use\nKnown to all affected parties?"
      tests: []
      domain: null
      category: null
    - code: 'DCF-448'
      name: 'Wireless Access Point Detection and Identification'
      description: '%s conducts tests (either through manual verification or automated mechanisms) to identify the presence of authorized and unauthorized wireless (Wi-Fi) access points periodically per policy and compliance requirements. If automated monitoring is used, personnel are notified via generated alerts. Results are documented.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-449'
      name: 'Unauthorized Wireless Access Points Detected and Identified'
      description: "%s's methodology to detect and identify any unauthorized wireless access points, includes: WLAN cards inserted into system components; portable or mobile devices attached to system components to create a wireless access point; and, wireless devices attached to a network port or network device."
      activity: ''
      question: "Does the methodology detect and identify any unauthorized wireless access points, including at least the following?\nWLAN cards inserted into system components;\nPortable or mobile devices attached to system components to create a wireless access point (for example, by USB, etc.); and\nWireless devices attached to a network port or network device."
      tests: []
      domain: null
      category: null
    - code: 'DCF-450'
      name: 'Quarterly Wireless Scan of Wireless Access Points'
      description: '%s ensures that if wireless scanning is utilized to identify authorized and unauthorized wireless access points, the scan is performed at least quarterly for all system components and facilities.'
      activity: ''
      question: 'If wireless scanning is utilized to identify authorized and unauthorized wireless access points, is the scan performed at least quarterly for all system components and facilities?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-451'
      name: 'Wireless Access Point Automated Monitoring Alerts'
      description: '%s ensures that if automated monitoring is utilized, monitoring is configured to generate alerts to notify personnel.'
      activity: ''
      question: 'If automated monitoring is utilized (for example, wireless IDS/IPS, NAC, etc.), is monitoring configured to generate alerts to notify personnel?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-452'
      name: 'Inventory of Authorized Wireless Access Points'
      description: '%s maintains a documented inventory of authorized wireless access points including business justification.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-453'
      name: 'Incident Response Plan for Unauthorized Wireless Access Points'
      description: '%s has implemented incident response procedures in the event unauthorized wireless access points are detected.'
      activity: ''
      question: 'Does the incident response plan define and require a response in the event that an unauthorized wireless access point is detected?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-454'
      name: 'Actions Against Unauthorized Wireless Access Points'
      description: "%s executes an incident response process in the event unauthorized wireless access points are detected in accordance with the company's documented policies and procedures."
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-455'
      name: 'Internal Vulnerability Scans'
      description: '%s conducts internal vulnerability scans at least once every three months or upon significant changes to network or systems. The scan tool is kept up to date with latest vulnerability information.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-456'
      name: 'Vulnerabilities Identified and Resolved'
      description: "All critical or high vulnerabilities identified are addressed immediately and a subsequent scan is performed to validate resolution. All other applicable vulnerabilities are addressed based on the company's evaluation of risk per documented policies and procedures and rescans are conducted as needed to confirm resolution."
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-457'
      name: 'Independent Internal Vulnerability'
      description: '%s ensures that vulnerability scans are performed by a competent and independent party.'
      activity: ''
      question: 'Are quarterly internal scans performed by a qualified internal resource(s) or qualified external third party, and if applicable, does organizational independence of the tester exist (not required to be a QSA or ASV)?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-458'
      name: 'Quarterly External Vulnerability Scans (PCI) '
      description: 'External vulnerability scans are performed at least once every three months by a PCI SSC Approved Scanning Vendor (ASV). Vulnerabilities are resolved and ASV Program Guide requirements for a passing scan are met. Rescans are performed as needed to confirm that vulnerabilities are resolved.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-459'
      name: 'External Vulnerability Rescans Until Pass '
      description: '%s performs external vulnerability rescans as needed until passing scans are achieved.'
      activity: ''
      question: 'Do external quarterly scan and rescan results satisfy the ASV Program Guide requirements for a passing scan (for example, no vulnerabilities rated 4.0 or higher by the CVSS, and no automatic failures)?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-460'
      name: 'External Vulnerability Scans by PCI-Approved Vendor'
      description: '%s ensures that quarterly external vulnerability scans are performed by an Approved Scanning Vendor (ASV), approved by the Payment Card Industry Security Standards Council (PCI SSC).'
      activity: ''
      question: 'Are quarterly external vulnerability scans performed by a PCI SSC Approved Scanning Vendor (ASV?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-461'
      name: 'External Vulnerability Scans After Significant Change'
      description: 'External vulnerability scans are performed after any significant change in the environment by qualified personnel on all system components affected by the change. Organizational independence of the tester is maintained. Vulnerabilities that are scored 4.0 or higher by the CVSS are resolved and rescans are conducted as needed to validate corrections.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-462'
      name: 'Vulnerability Rescans'
      description: "%s's vulnerability scan process includes rescans until: No vulnerabilities exist that are scored 4.0 or higher by the CVSS for external users; and, a passing result is obtained or all “high-risk” vulnerabilities as defined in PCI DSS Requirement 6.1 are resolved for internal users."
      activity: ''
      question: "Does the scan process include rescans until: \nFor external scans, no vulnerabilities exist that are scored 4.0 or higher by the CVSS,\nFor internal scans, a passing result is obtained or all “high-risk” vulnerabilities as defined in PCI DSS Requirement 6.1 are resolved?"
      tests: []
      domain: null
      category: null
    - code: 'DCF-463'
      name: 'Internal Vulnerability Scans by Competent and Independent Party'
      description: '%s assigns a qualified internal resource(s) or qualified external third party to perform internal vulnerability scans. Organizational independence of the tester exists to maintain segregation of duties in the process.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-464'
      name: 'Penetration Testing Methodology'
      description: '%s has defined and documented a penetration testing methodology for the organization. The methodology includes industry-accepted penetration testing approaches, coverage for the entire CDE perimeter and critical systems, testing from both inside and outside the network, testing to validate any segmentation and scope reduction controls, application-layer penetration testing, network layer penetration tests, review and consideration of threats and vulnerabilities experienced in the last 12 months, documented approach to assessing and addressing the risk posed by exploitable vulnerabilities and security weaknesses found during penetration testing, and retention of penetration testing results and remediation activities results for at least 12 months.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-465'
      name: 'External Penetration Testing Requirements'
      description: "%s conducts external penetration tests at least once every 12 months or after any significant infrastructure or application upgrade or change per the company's defined methodology. Testing is performed by a qualified internal resource or qualified external third party and organizational independence of the tester is maintained."
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-466'
      name: 'External Penetration Tests by Competent and Independent Party'
      description: '%s perform external penetration testing at least annually and after any significant infrastructure or application upgrade or modification.'
      activity: ''
      question: 'Are tests performed by a qualified internal resource or qualified external third party, and if applicable, does organizational independence of the tester exist (not required to be a QSA or ASV)?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-467'
      name: 'Internal Penetration Testing Requirements'
      description: "%s conducts internal penetration tests at least once every 12 months and after any significant infrastructure or application upgrade or change per the company's defined methodology. Testing is performed by a qualified internal resource or qualified external third-party and organizational independence of the tester is maintained."
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-468'
      name: 'Internal Penetration Test by Competent and Independent Party'
      description: '%s ensures that penetration tests performed by a qualified internal resource(s) or qualified external third party, and if applicable, organizational independence of the tester exists.'
      activity: ''
      question: 'Are tests performed by a qualified internal resource or qualified external third party, and if applicable, does organizational independence of the tester exist (not required to be a QSA or ASV)?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-469'
      name: 'Resolving Vulnerabilities from Penetration Testing'
      description: '%s corrects exploitable vulnerabilities and security weaknesses found during penetration testing based on the assessment of the risk posed by the security issue and in accordance with company policies and procedures. Penetration testing is repeated to verify the corrections.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-470'
      name: 'Periodic Penetration Testing on Segmentation Controls'
      description: "Where segmentation is used to isolate the CDE from other networks, %s conducts penetration periodically and after any changes to segmentation controls/methods to validate segmentation and isolation mechanisms are operational and effective. Testing covers all methods in use per the company's defined methodology and is performed by a qualified internal resource or qualified external third party with organizational independence of the tester."
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-471'
      name: 'Penetration Testing Requirements'
      description: '%s performs penetration testing to verify segmentation controls: Performed at least annually and after any changes to segmentation controls/methods; covers all segmentation controls/methods in use; verifies that segmentation methods are operational and effective, and isolate all out-of-scope systems from systems in the CDE.'
      activity: ''
      question: "Does penetration testing to verify segmentation controls meet the following?\nPerformed at least annually and after any changes to segmentation controls/methods.\nCovers all segmentation controls/methods in use.\nVerifies that segmentation methods are operational and effective, and isolate all out-of-scope systems from systems in the CDE."
      tests: []
      domain: null
      category: null
    - code: 'DCF-472'
      name: 'Penetration Tests for Control Segmentation Verification by Competent and Independent Party'
      description: '%s ensures that penetration testing to verify segmentation controls is performed by a qualified internal resource(s) or qualified external third party, and if applicable, organizational independence of the tester exists.'
      activity: ''
      question: 'Are tests performed by a qualified internal resource or qualified external third party, and if applicable, does organizational independence of the tester exist (not required to be a QSA or ASV)?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-473'
      name: 'Periodic Segmentation Control Penetration Testing'
      description: 'For mutitenant service providers, a penetration test is performed at least every six months to validate the effectiveness of logical separation controls used to separate customer environments.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-474'
      name: 'Penetration Testing Scope (Service Providers Only)'
      description: '%s ensures that penetration testing covers all segmentation controls/methods in use.'
      activity: ''
      question: 'Does penetration testing cover all segmentation controls/methods in use?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-475'
      name: 'Penetration Testing Verification of Segmentation Control Effectiveness (Service Providers Only)'
      description: '%s ensures that penetration testing verifies that segmentation controls/methods are operational and effective, and isolates all out-of-scope systems from systems in the CDE.'
      activity: ''
      question: 'Does penetration testing verify that segmentation controls/methods are operational and effective, and isolate all out-of-scope systems from systems in the CDE'
      tests: []
      domain: null
      category: null
    - code: 'DCF-476'
      name: 'Segmentation Control Penetration Tests by Competent and Independent Party (Service Providers Only)'
      description: '%s ensures that penetration testing to verify segmentation controls is performed by a qualified internal resource or qualified external third party, and if applicable, does organizational independence of the tester exist (not required to be a QSA or ASV).'
      activity: ''
      question: 'Are tests performed by a qualified internal resource or qualified external third party, and if applicable, does organizational independence of the tester exist (not required to be a QSA or ASV)?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-477'
      name: 'IDS/IPS Up to Date'
      description: 'Intrusion-detection and/or intrusion-prevention techniques are configured to keep all engines, baselines, and signatures up to date.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-478'
      name: 'Change Detection Mechanism'
      description: '%s has enabled file integrity monitoring or a change-detection mechanism to detect unauthorized modification (including changes, additions, and deletions) of critical system files, configuration files, audit files, or content files to ensure critical data cannot be changed without generating alerts.'
      activity: ''
      question: ''
      tests:
          - id: 205
          - id: 8014
      domain: null
      category: null
    - code: 'DCF-479'
      name: 'Periodic Critical File Comparisons'
      description: '%s file integrity monitoring or change-detection mechanism perform critical file comparisons at least once weekly.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-480'
      name: 'Change Detection Mechanism Alert Response'
      description: '%s has implemented a process to respond to any alerts generated by the change-detection solution.'
      activity: ''
      question: 'Is a process in place to respond to any alerts generated by the change-detection solution?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-481'
      name: 'Policy for Security Monitoring and Testing Documented and Accessible'
      description: '%s has security policies and operational procedures for security monitoring and testing that are documented, in use, and known to all affected parties.'
      activity: ''
      question: "Are security policies and operational procedures for security monitoring and testing:\nDocumented\nIn use\nKnown to all affected parties?"
      tests: []
      domain: null
      category: null
    - code: 'DCF-482'
      name: 'Acceptable Use Policy for End-User Technologies'
      description: '%s has documented and implemented acceptable use policies for end-user technologies (e.g., remote access and wireless technologies, laptops, tablets, mobile phones, removable electronic media, email usage, internet, etc.), which include explicit approval by authorized parties, acceptable uses of the technology, and list of products approved by the company for employee use, including hardware and software.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-483'
      name: 'Technology Use Authentication'
      description: '%s has included authentication for use of the technology in critical technologies usage policy.'
      activity: ''
      question: 'Authentication for use of the technology included in critical technologies usage policy?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-484'
      name: 'Access List of Devices and Personnel'
      description: '%s has included a list of all such devices and personnel with access in critical technologies usage policy.'
      activity: ''
      question: 'A list of all such devices and personnel with access included in critical technologies usage policy?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-485'
      name: 'Technology User Tags'
      description: '%s has included a method to accurately and readily determine owner, contact information, and purpose in critical technologies usage policy.'
      activity: ''
      question: 'A method to accurately and readily determine owner, contact information, and purpose (for example, labeling, coding, and/or inventorying of devices) included in critical technologies usage policy?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-486'
      name: 'Acceptable Network Locations'
      description: '%s has included acceptable network locations for the technologies in critical technologies usage policy.'
      activity: ''
      question: 'Acceptable network locations for the technologies included in critical technologies usage policy?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-487'
      name: 'Company-Approved Product List'
      description: '%s has included a list of company-approved products in critical technologies usage policy.'
      activity: ''
      question: 'List of company-approved products included in critical technologies usage policy?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-488'
      name: 'Network Connection Termination'
      description: '%s has implemented technical controls to terminate network connections (including remote connections) associated with communications sessions at the end of the session and after a defined period of inactivity in accordance with company policies and procedures (e.g., de-allocating associated TCP/IP address or port pairs at the operating system level).'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-489'
      name: '3rd Party Remote-Access Usage'
      description: '%s has included activation of remote-access technologies for vendors and business partners only when needed by vendors and business partners, with immediate deactivation after use in critical technologies usage policy.'
      activity: ''
      question: 'Activation of remote-access technologies for vendors and business partners only when needed by vendors and business partners, with immediate deactivation after use included in critical technologies usage policy?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-490'
      name: 'Employee Remote-Access Usage'
      description: '%s prohibits, for personnel accessing sensitive information (e.g., PII, PHI, Cardholder Data, etc.) via remote-access technologies, the copying, moving, and storage of sensitive information (e.g., PII, PHI, Cardholder Data, etc.) onto local hard drives and removable electronic media, unless explicitly authorized for a defined business need.'
      activity: ''
      question: 'For personnel accessing sensitive information (e.g., PII, PHI, Cardholder Data, etc.) via remote-access technologies, does the policy specify the prohibition of copying, moving, and storage of sensitive information (e.g., PII, PHI, Cardholder Data, etc.) onto local hard drives and removable electronic media, unless explicitly authorized for a defined business need?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-491'
      name: 'Cardholder Data Protection per PCI Requirements'
      description: "%s's critical technologies usage policy requires, for personnel with proper authorization, the protection of cardholder data in accordance with PCI DSS Requirements."
      activity: ''
      question: 'For personnel with proper authorization, does the policy require the protection of cardholder data in accordance with PCI DSS Requirements?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-492'
      name: 'Information Security Responsibilities'
      description: "%s's security policy and procedures clearly define information security responsibilities for all personnel."
      activity: ''
      question: 'Do security policy and procedures clearly define information security responsibilities for all personnel?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-493'
      name: 'PCI DSS Compliance Program Charter'
      description: "%s's executive management has defined a charter for the PCI DSS compliance program. The charter assigns responsibility within executive management for the protection of cardholder data and overall accountability for maintaining PCI DSS compliance, and establishes communication requirements to provide executive management and board of directors updates regarding PCI DSS compliance activities at least once every 12 months."
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-494'
      name: 'PCI DSS Compliance Accountability (Service Providers Only)'
      description: "%s's executive management has assigned overall accountability for maintaining the entity’s PCI DSS compliance."
      activity: ''
      question: 'Has executive management assigned overall accountability for maintaining the entity’s PCI DSS compliance?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-495'
      name: 'PCI DSS Compliance Program Charter (Service Providers Only)'
      description: "%s's executive management has defined a charter for the PCI DSS compliance program and communication to executive management."
      activity: ''
      question: 'Has executive management defined a charter for the PCI DSS compliance program and communication to executive management?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-496'
      name: 'Designated Information Security Official'
      description: '%s has formally assigned and documented the responsibility for information security to a Chief Security Officer or other security-knowledgeable member of management'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-497'
      name: 'Designated Entity to Maintain Security Policies and Procedures'
      description: '%s has established, documented, and distributed security policies and procedures.'
      activity: ''
      question: 'Establishing, documenting, and distributing security policies and procedures?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-498'
      name: 'Designated Entity to Monitor and Analyze Security Alerts'
      description: '%s monitors and analyzes security alerts and information, and distributes to appropriate personnel.'
      activity: ''
      question: 'Monitoring and analyzing security alerts and information, and distributing to appropriate personnel?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-499'
      name: 'Incident Response Plan (PCI) '
      description: "%s's incident response plan includes roles, responsibilities, and communication and contact strategies in the event of a suspected or confirmed security incident (including notification of payment brands and acquirers), incident response procedures with specific containment and mitigation activities for different types of incidents, business recovery and continuity procedures, data backup processes, analysis of legal requirements for reporting compromises, coverage and responses of all critical system components, and reference or inclusion of incident response procedures from the payment brands."
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-500'
      name: 'Designated Entity to Administer User Accounts'
      description: '%s administers user accounts, including additions, deletions, and modifications.'
      activity: ''
      question: 'Administering user accounts, including additions, deletions, and modifications?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-501'
      name: 'Designated Entity to Monitor and Control Data Access'
      description: '%s monitors and controls all access to data.'
      activity: ''
      question: 'Monitoring and controlling all access to data?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-502'
      name: 'Cardholder Data Security Awareness Program'
      description: '%s has implemented a formal security awareness program to make all personnel aware of the cardholder data security policy and procedures.'
      activity: ''
      question: 'Is a formal security awareness program in place to make all personnel aware of the cardholder data security policy and procedures?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-503'
      name: 'Multiple Methods for Security Awareness'
      description: "%s's security awareness program includes multiple methods of communicating awareness and educating personnel, such as newsletters, web-based training, in-person training, team meetings, phishing simulations, etc. Periodic security updates are provided to personnel through these multiple methods of communication."
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-504'
      name: 'Cardholder Data Security Awareness Training  '
      description: "Security awareness training materials include information %s's security policy, phishing and related attacks, social engineering, awareness about the acceptable use of end-user technologies, and personnel's role in protecting cardholder data."
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-505'
      name: 'List of Service Providers'
      description: '%s maintains a list of service providers including a description of the service provided.'
      activity: ''
      question: 'Is a list of service providers maintained, including a description of the service(s) provided?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-506'
      name: 'Service Providers Agreements'
      description: '%s maintains a written agreement that includes an acknowledgement that the service providers are responsible for the security of cardholder data the service providers possess or otherwise store, process or transmit on behalf of the customer, or to the extent that they could impact the security of the customer’s cardholder data environment.'
      activity: ''
      question: "Is a written agreement maintained that includes an acknowledgement that the service providers are responsible for the security of cardholder data the service providers possess or otherwise store, process, or transmit on behalf of the customer, or to the extent that they could impact the security of the customer’s cardholder data environment?\nNote: The exact wording of an acknowledgement will depend on the agreement between the two parties, the details of the service being provided, and the responsibilities assigned to each party. The acknowledgement does not have to include the exact wording provided in this requirement."
      tests: []
      domain: null
      category: null
    - code: 'DCF-507'
      name: 'Vendor Due Diligence'
      description: '%s performs due diligence activities prior to engaging with a new service provider or vendor (e.g., review of security questionnaires and compliance reports, review of vendor-provided policies, procedures, or other documents, analysis of delegated or shared responsibilities with the prospective vendor, etc.). Results of the due diligence activities including action items are documented.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-508'
      name: 'Annual PCI DSS Compliance of Service Providers Monitored'
      description: '%s maintains a program to monitor service providers’ PCI DSS compliance status at least annually.'
      activity: ''
      question: 'Is a program maintained to monitor service providers’ PCI DSS compliance status at least annually?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-509'
      name: 'Documented PCI DSS Responsibilities of Service Providers'
      description: '%s maintains documented information about which PCI DSS requirements are managed by each service provider, which are managed by the entity, and any shared responsibilities between service providers and the entity.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-510'
      name: 'Service Providers Acknowledge Security Responsibilities'
      description: '%s acknowledges in writing to customers through contracts, service agreements, or other means, that %s will be responsible for the security of account data it possesses or otherwise stores, processes, or transmits on behalf of its customers, or to the extent that the company could impact the security of the customer’s CDE.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-511'
      name: 'Incident Response Management'
      description: "%s's IRP addresses roles, responsibilities, and communication and contact strategies in the event of a compromise including notification of the payment brands, at a minimum."
      activity: ''
      question: 'Roles, responsibilities, and communication and contact strategies in the event of a compromise including notification of the payment brands, at a minimum?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-512'
      name: 'Specific Incident Response Procedures (SOPs)'
      description: "%s's IRP addresses specific incident response procedures."
      activity: ''
      question: 'Specific incident response procedures?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-513'
      name: 'Reporting Compromise'
      description: "%s's IRP addresses analysis of legal requirements for reporting compromises."
      activity: ''
      question: 'Analysis of legal requirements for reporting compromises?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-514'
      name: 'Critical System Coverage and Response'
      description: "%s's IRP addresses coverage and responses of all critical system components."
      activity: ''
      question: 'Coverage and responses of all critical system components?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-515'
      name: 'Inclusion of Incident Response Procedures from Payment Brands'
      description: "%s's IRP addresses reference or inclusion of incident response procedures from the payment brands."
      activity: ''
      question: 'Reference or inclusion of incident response procedures from the payment brands?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-516'
      name: 'Incident Response Training'
      description: '%s provides incident response training to personnel consistent with their assigned roles and responsibilities (e.g., incident identification and reporting for all personnel vs. incident handling for incident response team members, etc.). Personnel complete training within the timelines established in policies and procedures upon receiving system access or assuming an incident response role or responsibility, when required by system changes, and at periodic intervals.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-517'
      name: 'Monitoring Procedures for System Alerts'
      description: "%s's security incident response plan includes procedures for monitoring and responding to alerts from security monitoring systems including intrusion-detection and intrusion-prevention systems, network security controls, change-detection mechanisms for critical files, the change-and tamper-detection mechanism for payment pages, detection of unauthorized wireless access points, etc."
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-518'
      name: 'Incident Response Plan Review and Update'
      description: '%s has a process to modify and evolve the incident response plan according to lessons learned and to incorporate industry developments.'
      activity: ''
      question: 'Is a process developed and in place to modify and evolve the incident response plan according to lessons learned and to incorporate industry developments?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-519'
      name: 'PCI DSS Compliance Periodic Reviews'
      description: 'Management performs reviews at least once every three months to confirm that personnel are performing their tasks in accordance with all security policies and operational procedures. Reviews are performed with segregation of duties and include the following tasks: daily log reviews, configuration reviews for network security controls, applying configuration standards to new systems, responding to security alerts, and change-management processes. Results of the reviews are documented and retained.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-520'
      name: 'Incident Response Plan Reviewed Quarterly (Service Providers Only)'
      description: '%s perform reviews to confirm personnel are following security policies and operational procedures, at least quarterly.'
      activity: ''
      question: 'Are reviews performed at least quarterly?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-521'
      name: 'PCI DSS Compliance Periodic Reviews Documentation'
      description: 'Management documents the results of the reviews performed by management to confirm that personnel are performing their tasks in accordance with all security policies and operational procedures. The documentation includes remediation actions taken for any tasks that were found to not be performed and sign-off of results by personnel assigned responsibility for the PCI DSS compliance program.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-522'
      name: 'Anti-Malware Scans of Media'
      description: 'The implemented anti-malware solutions are configured to perform automatic scans or continuous behavioral analysis of systems or processes when removable electronic media is inserted, connected, or logically mounted within the environment.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-523'
      name: 'Inventory of Trusted Keys and Certificates to Protect PAN During Transmission'
      description: '%s maintains an inventory of the entity’s trusted keys and certificates used to protect primary account numbers (PANs) during transmission. For certificates, the inventory includes the issuing certificate authority and expiration date.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-524'
      name: 'Periodic Review of Application and System Accounts'
      description: 'Management performs a review of application and system accounts and their associated privileges periodically (per policy and compliance requirements) to ensure access remains appropriate for the function being performed. Changes resulting from the review, if any, are documented and implemented.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-525'
      name: 'Technical Controls to Prevent Copy/Relocation of PAN'
      description: '%s has implemented technical controls to prevent copy and/or relocation of primary account numbers (PAN) (e.g., using local hard drives, removable storage media, etc.) when personnel use remote-access technologies, except where documented authorization and a legitimate business need exists.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-526'
      name: 'Scope of Privacy Program'
      description: '%s has a well-defined documented scope that reflects the boundaries and applicability of its Privacy Program'
      activity: "1. Establish and Document the scope of the privacy program\n2. Include purposes for processing personal data, and boundaries for the data necessary\n3. Identify the data processing environment (e.g., geographic location, internal, cloud, third parties)"
      question: 'Has the organization established a scope that reflects the boundaries and applicability of its privacy program and management of personal data?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-527'
      name: 'Designated Data Protection Officer'
      description: '%s has appointed and documented responsibilities of an individual (e.g., data protection officer) responsible for developing, implementing, maintaining and monitoring an organization-wide governance and privacy program and acting as a point of contact to authorities and data subjects to ensure compliance with all applicable laws and regulations regarding the processing of PII.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-528'
      name: 'Management of Sensitive Information'
      description: '%s maintains policies and procedures to properly identify, label and store sensitive information (e.g., PII, PHI, Cardholder Data, etc.), and to manage and document the use, transfer, storage, and disposal of physical media containing sensitive information. Personnel are trained and made aware of how to handle sensitive information and report related incidents.'
      activity: "1. Ensure policies and procedures cover how to properly identify, mark, label, and store sensitive information; each type of sensitive data should be treated independently (e.g., PHI and cardholder data cannot be stored together on the account that they are both sensitive information)\n2. Establish procedures for the handling of media and storage devices where sensitive information will be kept; log related activities\n3. Train personnel in policies and procedures for managing and handling sensitive information, and reporting related incidents"
      question: 'Does the organization maintain policies and procedures to properly identify, label and store sensitive information (e.g., PII, PHI, Cardholder Data, etc.), and to manage and document the use, transfer, storage, and disposal of physical media containing sensitive information? Are personnel trained and made aware of how to handle sensitive information and report related incidents?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-529'
      name: 'Data Subject Consent'
      description: '%s has documented and implemented a process to obtain consent from data subjects prior to collecting PII. The organization obtains and records consent from data subjects according to the documented process.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-530'
      name: 'Data Subject Withdrawal of Consent'
      description: '%s provides mechanisms for data subjects to modify or withdraw their consent. %s acknowledges, logs, and documents instances of withdrawals of consent.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-531'
      name: 'Notification of Disclosures to Third Parties'
      description: '%s documents and maintains a record of authorized disclosures of PII to third parties (including what PII has been disclosed, to whom and when). %s also notifies customers of any legally binding requests for disclosure of PII, unless prohibited by law.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-532'
      name: 'International Transfer of Personal Data'
      description: '%s has established policies and procedures (e.g. Privacy Policy) for the transfer and transmission of PII to a non-EU country or international organization.'
      activity: "Personal data may be transferred to a non-EU country or international organization if that country or organization:\n(a) Is deemed to have adequate levels of protection, by the EU;\n(b) Has provided appropriate safeguards, and on condition that enforceable data subject rights and effective legal remedies for data subjects are available; or,\n(c) Under specific conditions to include: explicit consent of the data subject, public interest, contract fulfillment, establishment of legal claims, data subject vital interest, publicly available"
      question: 'Does the organization have policies and procedures for the international transfer/transmission of personal data to non-EU countries or international organizations?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-533'
      name: 'Joint PII Controllers'
      description: '%s has established and documented roles and responsibilities for the processing of PII (including PII protection and security requirements) with any joint PII controller (for example, through a joint controller agreement). The established roles and responsibilities are communicated to data subjects.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-534'
      name: 'Communication to Data Subjects'
      description: '%s provides data subjects with clear and easily accessible information identifying the PII controller and describing the processing of their PII.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-535'
      name: 'Organizational Context'
      description: '%s has identified its role and place in the data processing ecosystem, and has identified and documented responsibilities and expectations determined by applicable regulations, obligations, and internal and external factors, entities, and stakeholders, as they apply to its role.'
      activity: "1. Identify and appropriately communicate organization's industry sector and role in the data processing\n2. Identify, understand, and manage applicable legislation, regulations, and contractual obligations, relating to security and privacy\n3. Identify internal and external interested parties and stakeholders, including customers, supervisory authorities, shareholders, and suppliers\n4. Identify organizational governance, policies, and procedures\n5. Identify systems, products, services that support organizational priorities"
      question: 'Has the organization identified its internal and external context?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-536'
      name: 'Record of Processing Activity (ROPA)'
      description: '%s has established and documented records of processing activity (ROPA), which includes descriptions of the of lawful collection and use of PII as well as the specific purposes for which PII is processed.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-537'
      name: 'Data Processing Agreements'
      description: '%s has data processing agreements (DPAs) in place with sub-processors that include the minimum technical and organizational measures that the third parties need to implement to meet the objectives of %s’s privacy program.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-538'
      name: 'Data Protection Impact Assessment (DPIA)'
      description: '%s conducts a data protection impact assessment when planning for the processing of new PII, changing the processing of existing PII, or as otherwise required. Results of the assessment are documented and retained.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-539'
      name: 'Collection of PII from Special Categories'
      description: "%s's record of processing activity (ROPA) includes conditions for allowable collection of special categories of personal data."
      activity: "1. The ROPA outlines allowable conditions to collect special categories of personal data (e.g., racial or ethnic origin, political opinions, religious or philosophical beliefs, or trade union membership; genetic and/or biometric data for the purpose of uniquely identifying a natural person; data concerning health or a person's sex life or sexual orientation)\n2. The allowable conditions include: (a) data subject explicit consent; (b) employment/social security obligations as permitted by law; (c) protection of data subject's vital interests; (d) in course of legitimate activities with appropriate safeguards; (e) data made public by data subject; (f) establishment of legal claims; (g) substantial public interest; (h) medical purposes; (i) public health interest; (j) archiving purposes for public interest or scientific/historical research"
      question: 'Does the organization have an established record of processing activity for personal data?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-540'
      name: 'Timely Response to Data Subject Requests or Inquiries'
      description: 'Upon receiving a privacy right request, privacy inquiry, or privacy incident report, %s provides confirmation of receipt and responds to the request, inquiry, or report within the timeframes established by regulatory requirements.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-541'
      name: 'Procedures for Management of Data Subject Rights'
      description: '%s has defined and documented policies and procedures for handling and responding to requests from data subjects to exercise their data subject rights.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-542'
      name: 'Representative for Non-EU Controllers/Processors'
      description: '%s, which is a controller/processor not established in the EU, has designated in writing a representative in an EU member state to manage all data processing issues concerning %s.'
      activity: "1. A representative to the EU is mandated, if data processing of the organization is NOT occasional\n2. Designate, in writing, a representative (member of workforce or third-party) in an EU member state to manage data processing issues"
      question: 'If the organization is not established in the EU, does it have a designated representative in an EU member state, related to data processing activities?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-543'
      name: 'Communication of Changes in Subprocessors'
      description: '%s notifies customers of any intended changes (including additions and replacements) in subprocessors that process PII so that customers have an opportunity to object to such changes.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-544'
      name: 'Jurisdictions to Which PII Might Be Transferred'
      description: '%s has specified, documented, and communicated to customers the countries and international organizations to which PII can possibly be transferred.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-545'
      name: 'Policies for PII Management'
      description: '%s has a defined a policy for the management of personally identifiable information (PII) that outlines how PII is managed by the organization, in accordance with applicable privacy laws and regulations.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-546'
      name: 'Management of Financial Incentives'
      description: '%s has an established process and set of rules to manage financial incentives associated with consumer data.'
      activity: "Ensure that the privacy policy includes a documented process for:\n1. Calculating/estimating the value for data subject information, in order to offer financial incentives, special pricing or services based on data subject information.\n2. Clear and proper notification for any financial incentive, price or service difference, which should include, (a) a description of the terms; (b) process for opting in or out (withdrawal); (c) statement of data subject's value of information."
      question: 'Does the organization have a system to govern financial incentives related to consumer data.'
      tests: []
      domain: null
      category: null
    - code: 'DCF-547'
      name: 'Management of Data Subject Rights (Minors)'
      description: "%s's privacy policy includes the procedures for managing data rights related to data subjects below the age of 16."
      activity: "Ensure that the privacy policy includes a documented process for:\n1. Affirmative authorization (consent) of: (a) data subjects between 13 and 15; (b) parent/guardian of data subjects under 13.\n2. Opting out at any time.\n3. Verification of authority to consent through reasonable methods (e.g., consent form, online paid transaction, physical or virtual contact)."
      question: 'Does the organization have procedures to manage rights of data subjects under the age of 16?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-548'
      name: 'Management of Data Subject Rights (Household)'
      description: "%s's privacy policy includes the procedures for managing data rights related to data subjects of the same household."
      activity: "Ensure that the privacy policy includes a documented process for handling household requests (e.g., need-to-know, deletion) for:\n1. Password-protected accounts, through existing business practices.\n2. Accounts without password protection, after the following conditions are met: (a) joint request of all data subjects of the household; (b) individual identification of each data subject of the household; and, (c) verifiable consent from parent/guardian of data subjects under 13."
      question: 'Does the organization have procedures to manage rights of data subjects in the same household?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-549'
      name: 'Identity Verification for Data Subject Requests '
      description: '%s has established, documented, and implemented a method for verifying that the person making a privacy right request is the data subject or an authorized agent. If %s cannot confirm the identity or authorization of the requestor, %s notifies the requestor, denies the request, and retains supporting documentation.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-550'
      name: 'Specific Requirements for Managing Data Subject Rights'
      description: '%s has an established process for managing specific data subject requests, which include request submission methods, request response requirements, and opting in and out procedures.'
      activity: "Ensure that the privacy policy includes the following for personal data-related requests (e.g., disclose, revise, delete information, opting in or out):\n1. Request submission methods: (a) Email address, if an online only businesses with a direct relationship to data subjects; (b) two or more designated methods (toll-free telephone number, at a minimum), if not an online only business; (c) submitted through company website, if website available.\n2. Request response: (a) Disclose/deliver/confirm request for the preceding 12 months, in a written and readily usable format, and within 45 days of a verified request; (b) comply with opt-out request no later than 15 business days, and inform third parties of opt-out request.\n3. Two-step opt-in process: (a) opt-in request; and (b) separate opt-in confirmation."
      question: 'Does the organization have policies and procedures that address specific data subject rights?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-551'
      name: 'Specific Requirements for Data Subject Notices'
      description: "%s's notices to data subjects about their rights are easy to read and understandable, be clear and conspicuous, and readily accessible to data subjects with disabilities."
      activity: "Ensure that data subject right notices:\n1. Use plain and straightforward language, avoiding technical or legal jargon.\n2. Use a format that makes the notice conspicuous and are readily available for data subjects, prior to opting-in.\n3. Are available in languages that the company does business with.\n4. Follow industry-recognized guidelines to be accessible to data subjects with disabilities (e.g., Web Content Accessibility Guidelines); or, provide a process for how the information could be accessed for subjects with disabilities."
      question: 'Does the organization provide notices to data subjects, and outline what the notices should entail?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-552'
      name: 'Notice of Right to Opt-Out or Limit'
      description: '%s provides a clear and conspicuous notice for data subjects to opt-out of, or limit, disclosing personal information, which can be used, sold or shared.'
      activity: "If personal information of data subjects are to be sold or shared:\n1. Provide a notice of right to opt-out, which includes: (a) a description of the data subject's right to opt-out of the sale of their personal information; (b) the method by which a request to opt-out can be submitted; (c) instructions for any other method to submit an opt-out request.\n2. Provide the following clear and conspicuous links on the company website's homepage, or mobile application's landing page: (a) \"Do Not Sell or Share My Personal Information\" link that directs to the Notice of Right to Opt-Out; (b) \"Limit the Use of My Sensitive Personal Information\" link to enable data subjects to limit use or disclosure of their sensitive personal information to authorized uses only; or (c) A single Alternative Opt-out Link that is clearly-labeled as “Your Privacy Choices” or “Your California Privacy Choices,” which directs the data subject to a webpage to facilitate the opting-out process of both \"Do Not Sell or Share My Personal Information\" and \"Limit the Use of My Sensitive Personal Information\" links.\nNOTE: An alternative method to inform data subjects of their right to opt-out must be presented in the absence of a company website."
      question: 'Does the organization provide notice of right to opt-out?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-553'
      name: 'Special Requirements for Record Keeping'
      description: '%s has an established process for keeping records of the commercial use (e.g., sharing, buying, or selling) of the personal information of 10 million or more data subjects in a calendar year.'
      activity: "If personal information of over 10 million data subjects is collected in a calendar year, ensure the following metrics are compiled for the previous calendar year, and displayed in the privacy policy or posted on the company website:\n1. Number of requests to know that were received, and denied.\n2. Number of request to delete that were received, and denied.\n3. Number of requests to opt-out that were received, and denied.\n4. The median or mean number of days to respond to each request."
      question: 'Does the organization have record-keeping provisions for special cases?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-554'
      name: 'Timely Notice for Personal Data Collection'
      description: '%s provides notice for the collection of personal data at or before the point of collection.'
      activity: '1. Provide a timely notice for the collection of personal data at or before the point of collection.'
      question: 'Does the organization provide a timely notice for the collection of personal data at or before the point of collection.'
      tests: []
      domain: null
      category: null
    - code: 'DCF-555'
      name: 'Privacy by Design'
      description: "Privacy by design has been implemented into the design and architecture of %s's IT systems and business practices."
      activity: "1. Implement methods for obtaining consumer consent that avoid architecture that impacts or interferes with the consumer’s ability to make a choice (e.g. Dark Patterns).\n2. Implement methods for obtaining consumer opt-out preference signals."
      question: 'Does the organization have procedures to implement privacy by design?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-557'
      name: 'Shared Account Management'
      description: 'Group, shared, or generic account usage is prevented unless strictly necessary and supported by documented business justification and management approval. Mechanisms are in place to confirm individual user identity before access to the account is granted and to trace every action to an individual user.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-558'
      name: 'Restrictions on Software Installation and Execution'
      description: '%s has identified allowed software programs in the organization and implemented mechanisms to restrict and monitor the installation and execution of unauthorized software (e.g., through procedural methods or automated mechanisms such as corporate app stores and deny-all, allow-by-exception rules). The list of allowed software is reviewed and updated periodically as determined by the organization.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-559'
      name: 'Deny-by-Exception Rule for Unauthorized Applications'
      description: '%s has an allow-all, deny-by-exception rule in place for unauthorized software applications and implements procedures to deny execution.'
      activity: "1. Develop and maintain a list of unauthorized applications (e.g. blocklists)\n2. Implement a deny-by-exception rule\n3. Review and update list on regular intervals"
      question: 'Does the organization implement a deny-by-exception rule for unauthorized applications?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-560'
      name: 'Baselines for Detecting Anomalous Behavior'
      description: '%s has established baselines for normal behavior of networks, systems, and applications for the detection of anomalies.'
      activity: "1. Determine usage of network, system and application at normal and peak periods\n2. Determine time, location and frequency of access for each user or group\n2. Implement monitoring system configuration to detect anomalous behavior\n3. Define threshold for anomalous behavior; log events that exceed the threshold and alert appropriate members"
      question: 'Does the organization have established baselines of infrastructure activity and behavior?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-561'
      name: 'System Protection During Audits'
      description: '%s properly plans and implements protective measures for information system during audits.'
      activity: "1. Determine the scope of the audit and planned time of audit (e.g. during or outside of business hours) \n2. Identify necessary system access requests that require either: (a) read-only access, (b) administrator testing on behalf of the auditor, or (c) special or additional processing (e.g. running audit tools)\n3. Ensure appropriate management review and approval of system access requests prior to audits\n4. Establish and verify the security requirements (e.g. antivirus and patching) of the devices used for accessing the systems (e.g. laptops or tablets) before allowing the access\n5. Monitor and log all access for audit and test purposes"
      question: 'Does the organization implement system protections during audits?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-562'
      name: 'Management of Utility Programs'
      description: 'Access to manage utility programs (including anti-virus consoles and diagnostic, patching, backup, or network tools, or any other utility can be capable of overriding system and application controls) is restricted to authorized system administrators. Standard users cannot disable privileged utilities or modify their configurations.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-563'
      name: 'Environment Identification'
      description: '%s has an established process to properly identify different environments to reduce the risk of error.'
      activity: "1. Identify the different environments (e.g., test, development, production) applicable to the organization\n2. Display the environment type on menus"
      question: 'Does the organization have a process to properly identify different environments?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-564'
      name: 'Secure Development and Test Environments'
      description: '%s ensures that the development and test environments are properly through appropriate measures (e.g., updates, monitoring and access control, backups).'
      activity: "1. Ensure development and testing tools are regularly patched and updated\n2. Ensure secure configuration of systems and software\n3. Monitor and backup environments"
      question: 'Does the organization have established security measures for development and test environments?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-565'
      name: 'Managing Test Information'
      description: '%s has an established process for managing test information.'
      activity: "1. Monitor test environments to ensure that (a) operational environment level access control procedures are in place, (b) test information is only used for testing purposes, and (c) operational information copies are properly logged\n2. Require an authorization for each time operational information is copied to a test environment\n3. Ensure that operational information is properly deleted from test environments immediately after testing is complete"
      question: 'Does the organization have a process to manage test information?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-566'
      name: 'Management of Nonconformities'
      description: 'When a nonconformity is identified, %s performs a root-cause analysis and implements corrective actions to address the nonconformity. %s retains documentation of the analysis and subsequent actions taken and of the results of any corrective action.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-566.AI'
      name: 'Management of Nonconformities (AI)'
      description: 'When a nonconformity is identified, %s performs a root-cause analysis and implements corrective actions to address the nonconformity. %s retains documentation of the analysis and subsequent actions taken and of the results of any corrective action.'
      domain: null
      category: null
      activity: ''
      question: ''
      tests: []
    - code: 'DCF-567'
      name: 'Change Management Policy'
      description: '%s has a documented a policy that describes the requirements for managing changes across the organization, including changes to infrastructure, systems, and applications.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-568'
      name: 'Records of Competence'
      description: '%s maintains documentation of the necessary competence of personnel affecting its information security program.'
      activity: "1. Determine what competencies are necessary for personnel affecting the information security program.\n2. Retain documentation/records as evidence of competence, including education, training, experience, etc."
      question: 'Does the organization maintain records of personnel competence?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-569'
      name: 'Information Labeling'
      description: "%s has developed and implemented procedures for labeling of assets and information across the organization in accordance with the organization's information classification scheme."
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-570'
      name: 'Disciplinary Process'
      description: "%s has a defined disciplinary sanctions process to be enacted when a member of the workforce violates the company's policies or causes a security or privacy incident. Management retains documentation of instances when the disciplinary process was enacted."
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-571'
      name: 'Fire Detection and Suppression'
      description: 'Fire detection and suppression systems are installed in critical locations to protect people and assets in the event of a disaster. Maintenance is conducted periodically in accordance with manufacturer guidance.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-572'
      name: 'Temperature Monitoring Systems'
      description: 'Server rooms and data centers are air conditioned to maintain appropriate atmospheric conditions. Systems are in place to monitor and control air temperature and humidity at appropriate levels. Maintenance is conducted periodically in accordance with manufacturer guidance.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-573'
      name: 'Uninterruptible Power Supply'
      description: 'Uninterruptible power supply (UPS) systems units are in place to provide backup power in the event of an electrical failure in the data centers or server rooms. Maintenance is conducted periodically in accordance with manufacturer guidance.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-574'
      name: 'Mobile Device Management Software'
      description: 'A mobile device management (MDM) is installed in company-issued devices and bring-your-own devices used for company purposes to enforce security for assets off-premise (e.g., location tracking, remote locking and wiping, threat detection, restrictions on software installation, etc.)'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-575'
      name: 'Maintenance Management Policy'
      description: '%s has a defined policies and procedures for maintenance management to ensure that maintenance on organizational systems are conducted periodically in accordance with security requirements.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-576'
      name: 'System Information and Integrity Policy'
      description: '%s has a defined policy for system information and integrity that establishes procedures to ensure systems are established with system integrity monitoring.'
      activity: "1. Develop and maintain System Information and Integrity Policy.\n2. Implement each section of the System Information and Integrity Policy."
      question: 'Does the organization have a System and Integrity Policy?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-577'
      name: 'System Security Planning Policy'
      description: '%s has a defined policy for system security planning to ensure resources and information systems are established with effective security controls and control enhancements.'
      activity: "1. Develop and maintain a System Security Planning Policy.\n2. Implement each section of the System Security Planning Policy.\n3. Develop and maintain a System Security Plan (SSP)."
      question: 'Does the organization have a system security plan?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-578'
      name: 'System and Services Acquisition Policy'
      description: '%s has a defined policy for system and services acquisition that establishes the procedures for systems and services to be acquired with security requirements that align with business objectives.'
      activity: "1. Document and maintain a System and Services Acquisition Policy.\n2. Implement each section of the System and Services Acquisition Policy."
      question: 'Does the organization have a System Services and Acquisition Policy?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-579'
      name: 'Automated Access Management System in Place'
      description: '%s has an access management system in place using automated mechanisms to manage accounts (e.g., create, enable, modify,  monitor, report, disable, and remove).'
      activity: "1. Define the automated mechanisms used to support the management of system accounts.\n2. Ensure that the system accounts are properly managed by the automated mechanisms."
      question: 'Does the organization define automated mechanisms to support the management of system accounts?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-580'
      name: 'Disabling High Risk User Accounts '
      description: '%s has a process for disabling system accounts for users who pose a significant security and/or privacy risk.'
      activity: "1. Define significant risks that trigger the disabling of accounts.\n2. Establish duration and time period to disable an account."
      question: 'Does the organization have a process for disabling system accounts for high risk users?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-581'
      name: 'Encrypted Information Flow Control'
      description: '%s has procedures to prevent flow of encrypted information through flow control tools.'
      activity: "1. Identify flow control tools used.\n2. Define procedures to prevent flow control bypass."
      question: 'Are flow control mechanisms implemented?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-582'
      name: 'Accounts Unlocked by Admin'
      description: '%s only allows locked accounts to be unlocked by an administrator.'
      activity: 'Only allow administrators to unlock accounts.'
      question: 'Are accounts unlocked only by administrators?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-583'
      name: 'System Use Notification'
      description: '%s provides system use notifications to users prior to allowing access to the system, such as privacy and security notices, in accordance with applicable regulations.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-584'
      name: 'Limited Concurrent Sessions'
      description: '%s defines the maximum number of concurrent sessions for system accounts.'
      activity: 'Define the maximum number of concurrent sessions for system accounts or account types.'
      question: 'Is the maximum number of concurrent sessions defined?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-585'
      name: 'Permitted Actions Without Identification or Authentication'
      description: '%s defines specific user actions that are permitted without identification or authentication.'
      activity: 'Define specific user actions, if any, that do not require identification or authentication, in the System Security Plan.'
      question: 'Have specific user actions that are permitted without identification been defined?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-586'
      name: 'Remote Access to Security Information and Privileged Commands'
      description: '%s authorizes remote execution of identified privileged commands and remote access to security-relevant information.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-587'
      name: 'Wireless Configuration Authorization'
      description: '%s identifies and explicitly authorizes users that are allowed to independently configure wireless networking capabilities.'
      activity: '1. Identify and explicitly authorize users to configure wireless networking capabilities.'
      question: 'Has the organization identified and explicitly authorized users that are allowed to independently configure wireless networking capabilities?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-588'
      name: 'Wireless Transmission Power Levels Reduced'
      description: '%s calibrates the transmission power levels of selected radio antennas.'
      activity: "1. Select radio antennas\n2. Calibrate transmission power levels"
      question: 'Are wireless transmission power levels calibrated?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-589'
      name: 'Verified External Systems Controls'
      description: "External systems used to access %s's systems are properly vetted or have verified controls in place."
      activity: "1. Verify that the external system has implemented controls in line with %s's security and privacy policies.\n2. If applicable, retain approved system connection or processing agreements with the organizational entity hosting the external system."
      question: 'Are external systems used for system access properly vetted or have verified controls in place?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-590'
      name: 'Information Sharing'
      description: '%s uses automated tools to facilitate information sharing decisions by authorized users.'
      activity: "1. Identify the criteria/circumstances for information sharing.\n2. Identify users who are authorized to make information sharing decisions.\n3. Identify the tools/mechanisms to facilitate the decision-making process for information sharing."
      question: 'Does the organization employ automated systems to enable authorized users to make decisions regarding information sharing?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-591'
      name: 'Management of Publicly Accessible Content'
      description: 'Content posted or processed on publicly accessible systems is reviewed by knowledgeable personnel prior to posting to validate it does not include nonpublic sensitive information in accordance with company policies and procedures and applicable regulations. If discovered, nonpublic sensitive information is removed.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-592'
      name: 'Limit Personally Identifiable Information in Audit Records'
      description: '%s limits use of PII in audit records.'
      activity: "1. Limit use of PII in audit records\n2. Define elements to be limited in the Data Protection Policy"
      question: 'Is PII use limited in audit records?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-593'
      name: 'Correlate Log Records'
      description: '%s analyzes audit records in correlation with other information, including vulnerability scanning, performance, system monitoring, and physical monitoring.'
      activity: "1. Define data/information collected from other sources (e.g., vulnerability scanning, performance, system and physical monitoring).\n2. Analyze audit records in correlation with information from the other sources."
      question: 'Are audit records analyzed in correlation with other relevant information?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-594'
      name: 'Protect Audit Information'
      description: '%s uses cryptographic mechanisms to protect audit information and audit tools.'
      activity: 'Implement cryptographic mechanisms to protect the integrity of audit information and audit tools.'
      question: 'Are cryptographic mechanisms used to protect the integrity of audit information and audit tools?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-595'
      name: 'Non-repudiation Actions'
      description: '%s defines actions to be covered by non-repudiation and maintains records of the actions performed.'
      activity: "1. Define actions covered by non-repudiation.\n2. Provide irrefutable evidence of the actions performed."
      question: 'Does the organization define, and provide evidence of performance of, actions covered by non-repudiation?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-596'
      name: 'Authorized to Modify Logs'
      description: '%s only allows authorized personnel to modify log settings/configurations.'
      activity: "1. Identify individual/role authorized to change log system components.\n2. Define system, event criteria, and time threshold which logging is performed."
      question: 'Does the organization define log activities and personnel authorized to change logs?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-597'
      name: 'Baseline Configurations'
      description: '%s uses automated tools to maintain completeness, currency, accuracy, and availability of baseline configurations.'
      activity: "1. Identify the automated tools used to maintain the completeness, currency, accuracy, and availability of the baseline system configuration.\n2. Set baselines for change management, configuration management tools, hardware, software, firmware inventory tools, and network management tools."
      question: 'Are automated tools implemented to manage baseline configuration activities?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-598'
      name: 'Previous Baseline Configuration Versions Retained'
      description: '%s retains previous versions of system and component configuration to support rollback.'
      activity: "1. Define the number of versions to be retained.\n2. Retain versions in support of rollback"
      question: 'Are previous system and component configuration data and documentation retained for rollback?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-599'
      name: 'High Risk Area System Configuration'
      description: '%s has an established process to secure system and system components during travel to areas that pose significant risk.'
      activity: "1. Define and Identify high risk areas\n2. Identify systems or components approved for travel to high risk areas \n3. Define the configuration of the systems during travel\n4. Define post-travel controls applied to systems\n5. Maintain records of travel activity and implementation of post-travel controls"
      question: 'Are systems or system components configured appropriately when in high risk areas?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-600'
      name: 'Automated Configuration Change Management'
      description: '%s uses automated configuration change management tools to notify, document, prohibit, and highlight system changes.'
      activity: "1. Define the approval authorities who need to be notified, or request approvals from, for proposed changes.\n2. Define the personnel who need to be notified upon approval and completion of changes.\n3. Identify and implement automated tools that will: notify approval authorities of pending approvals; highlight proposed changes that have not been explicitly approved or disapproved within a specified timeframe; prohibit changes to the system until approvals are received; document all changes to the system; and, notify personnel when approved changes are complete."
      question: 'Does the organization utilize automated change management tools?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-601'
      name: 'Management of Cryptographic Mechanisms'
      description: '%s has established processes and procedures to manage cryptographic mechanisms that provide defined controls.'
      activity: "1. Define the cryptographic mechanisms that are managed.\n2. Define the controls provided by cryptographic mechanisms."
      question: "Are the organization's cryptographic mechanisms under configuration management?"
      tests: []
      domain: null
      category: null
    - code: 'DCF-602'
      name: 'Role-Based Contingency Training'
      description: "%s provides up-to-date contingency training on specified intervals to users based on the users' roles and responsibilities."
      activity: "1. Provide contingency training to system users based on their roles and responsibilities at defined intervals and when required by system changes.\n2. Define the time period to complete training from the time the role is assumed, and the frequency in which the training should be completed.\n3. Review and update contingency training content at a defined frequency."
      question: 'Is contingency training provided to system users consistent with assigned roles and responsibilities?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-603'
      name: 'Alternate Processing Site'
      description: '%s has an alternate processing site that is prepared to serve as the operational site for essential mission and business functions support.'
      activity: "1. Establish an alternate processing site.\n2. Establish configuration settings for systems at the alternate processing site consistent with the requirements for the primary site, along with considerations for essential supplies and logistics."
      question: 'Does the organization have an alternate processing site?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-604'
      name: 'Transaction Recovery Procedures'
      description: '%s has transaction recovery procedures for transaction-based systems.'
      activity: 'Implement transaction recovery procedures for transaction-based systems, including the use of mechanisms such as transaction rollback and transaction journaling.'
      question: 'Has the organization implemented transaction recovery for systems that are transaction bases.?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-605'
      name: 'PIV Credentials Acceptance'
      description: '%s accepts and electronically verifies Personal Identity Verification-compliant credentials.'
      activity: 'Maintain a process to accept and electronically-verify PIV-compliant credentials.'
      question: 'Does the organization accept and verify Personal Identity Verification-compliant credentials?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-606'
      name: 'Device Identification and Authentication'
      description: 'Devices accessing the system are identified (e.g., through media access control (MAC) addresses, internal protocol (IP) addresses, device-unique token identifiers, etc.). The identity of each device accessing or connecting to the system is authenticated or verified as a prerequisite to system access.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-607'
      name: 'System Identifier Management'
      description: '%s has documented policies and procedures for assigning unique identifiers to individuals, groups, roles, services, or devices. Reuse of identifiers is restricted.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-608'
      name: 'Management of At-risk Passwords'
      description: '%s maintains a list of commonly used, expected, or compromised passwords.'
      activity: "1. Maintain a list of commonly used, expected, or compromised passwords.\n2. Update the list at a defined interval."
      question: 'Does the organization maintain a list of commonly used, expected, or compromised passwords?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-609'
      name: 'Public Key Authentication'
      description: '%s has an established process for public key authentication for individuals, machines, and devices.'
      activity: "1. Enforce authorized access to corresponding private keys.\n2. For PKI, validate certificates by constructing and verifying a certification path to an accepted trust anchor; and, implement a local cache of revocation data to support path discovery and validation."
      question: 'Does the organization have a process for public key authentication?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-610'
      name: 'Authenticators Protected'
      description: '%s protects authenticators based on the highest security category of information on the system.'
      activity: "1. Determine the highest security category of system information.\n2. Protect authenticators at the determined level."
      question: 'Are authenticators protected  with the security category of the information to which use of the authenticator permits access?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-611'
      name: 'Obscured Authentication Feedback'
      description: '%s has implemented mechanisms to obscure the feedback of authentication information, such as usernames/passwords, during the authentication process where technically feasible (e.g., in company-developed systems or applications, configurable third-party systems, etc.).'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-612'
      name: 'Non-organizational User Authentication'
      description: '%s uses automated tools to identify and authenticate non-organizational users.'
      activity: 'Identify and authenticate non-organizational users prior to granting access.'
      question: 'Does the organization uniquely identify and authenticate non-organizational users or processes acting on behalf of non-organizational users?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-613'
      name: 'Identity Evidence Validation and Verification Methods'
      description: '%s has defined methods to validate and verify identity evidence consistent with system risks, roles, and privileges associated with the user account.'
      activity: 'Define methods of validation and verification of identity evidence.'
      question: 'Does the organization verify identity evidence ?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-614'
      name: 'Automated Maintenance Mechanisms'
      description: '%s has implemented automated mechanisms and tools to conduct maintenance, repairs, and replacement actions on organizational systems.'
      activity: ''
      question: ''
      tests:
          - id: 8034
      domain: null
      category: null
    - code: 'DCF-615'
      name: 'Approved Use of Maintenance Tools'
      description: 'Tools, techniques, or mechanisms used to perform system security maintenance are approved by management prior to use with supporting documentation.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-616'
      name: 'Remote Maintenance'
      description: '%s approves and monitors all nonlocal maintenance and diagnostic activities and retains documented evidence of the approval. %s validates that external session and network connections are terminated when nonlocal maintenance or diagnostic activities are completed.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-617'
      name: 'Maintenance Personnel Authorization'
      description: '%s has a documented process to authorize maintenance personnel or organizations to perform maintenance and keeps a documented list of authorized parties. %s assigns organizational personnel with required access authorizations and technical competence to supervise the maintenance activities of personnel without required access authorizations.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-618'
      name: 'Timely Maintenance Support'
      description: '%s has an established process for obtaining maintenance support and/or spare parts for system components.'
      activity: "1. Define system components for which maintenance support and/or spare parts are obtained.\n2. Establish the time period after a failure, within which maintenance support and/or spare parts are to be obtained."
      question: 'Does the organization obtain maintenance support and/or spare parts for system components?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-619'
      name: 'Media Sanitization'
      description: '%s sanitizes equipment and system media that contain sensitive prior to disposal, release for reuse, or release out of organizational control (e.g., removed from premises for off-site maintenance). %s reviews, approves, tracks, documents, and verifies media sanitization and disposal actions in accordance with company policies and procedures.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-620'
      name: 'Security/Privacy Resource Planning'
      description: '%s includes the needed resources for the implementation of information security and privacy programs in its capital planning.'
      activity: "1. Document the resources needed to implement the information security and privacy programs for capital planning and investment requests.\n2. Document the resources that were excluded from consideration.\n3. Align the documentation required to address the privacy and information security program in capital planning with applicable laws, regulations, standards, policies, and other obligations."
      question: 'Does the organization include information security and privacy program resources in its capital planning?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-621'
      name: 'Test Sanitization'
      description: '%s has an established process for testing media sanitization equipment and procedures.'
      activity: "1. Establish a process for testing media sanitization equipment and procedures.\n2. Define the frequency for testing sanitization equipment and procedures."
      question: 'Has sanitization procedures and test sanitization test equipment been defined?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-622'
      name: 'Access Control for Output Devices'
      description: '%s manages the physical access control for output devices.'
      activity: "1. Define the output devices that require physical access control.\n2. Implement controls for physical access to output devices."
      question: 'Does the organization have a process to control physical access to output devices?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-623'
      name: 'Privacy Program Plan'
      description: '%s has an established organization-wide privacy program plan.'
      activity: "1. Develop an organization-wide privacy program plan that provides an overview of the company's privacy program.\n2. The plan should include: (a) a description of the program structure and its dedicated resources; (b) an overview of the program's requirements; (c) a descriptions of the program's management and common controls; (d) roles and responsibilities of privacy officials; (e) a description of management commitment, compliance, and strategic objectives of the program.\n3. The plan must be approved by a senior official with responsibility and accountability for the company's privacy risk.\n4. Update the plan at defined intervals."
      question: 'Does the organization have an established organization-wide privacy program plan?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-624'
      name: 'Data Integrity Board'
      description: '%s has established a Data Integrity Board.'
      activity: "1. Establish a Data Integrity Board.\n2. The board must review proposals to conduct or participate in a matching program.\n3. The board must conduct an annual review of all matching programs in which the company has participated."
      question: 'Does the organization have a Data Integrity Board?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-625'
      name: 'Monitoring Physical Access'
      description: '%s has physical intrusion alarms and surveillance equipment in place to monitor physical access to the site where the system resides.'
      activity: "1. Install intrusion alarms and surveillance equipment at the site where the system resides.\n2. Monitor physical access to the site."
      question: 'Does the organization control physical access to output devices?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-626'
      name: 'Privacy Reporting'
      description: '%s has defined privacy reports, and maintains and appropriately disseminates those reports.'
      activity: "1. Develop defined privacy reports. \n2. Identify relevant oversight bodies and disseminate the report to them.\n3. Identify officials responsible for monitoring compliance with the privacy program.\n4. Review and update reports at defined intervals."
      question: 'Does the organization maintain privacy reports?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-627'
      name: 'Social Media Rules'
      description: '%s has defined rules of behavior for restricting social media, social networking sites, and external sites/application use.'
      activity: 'Define rules of behavior for restricting the use of social media and networking sites, and external sites/applications.'
      question: 'Does the organization have rules for behavior restrictions on social media, social networking sites, and external sites/applications?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-628'
      name: 'Risk Designation for Roles'
      description: '%s assigns risk designations to all company roles/positions.'
      activity: "1. Assign a risk designation to all organizational roles. \n2. Establish screening criteria for personnel in those roles.\n3. Review and update the risk designations at defined intervals."
      question: 'Does the organization have risk designations for its organizational roles and positions?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-629'
      name: 'Privacy Act Statements'
      description: '%s makes Privacy Act statements available on forms that collect information maintained in a privacy act system of records, or on separate forms that individuals can retain.'
      activity: "Make Privacy Act statements available on:\n1. Forms that collect information that will be maintained in a Privacy Act system of records; or,\n2. Separate forms that can be retained by individuals."
      question: 'Does the organization provide Privacy Act Statements?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-630'
      name: 'Restricted Use of Social Security Number'
      description: '%s eliminates unnecessary uses of Social Security numbers.'
      activity: "1. Eliminate the use, collection, and maintenance of social security numbers.\n2. Use alternatives to the use of Social Security numbers.\n3. Inform individuals if Social Security number disclosure is voluntary or mandatory.\n4. Inform individuals of intended use of Social Security number if disclosure is required."
      question: 'Does the organization take steps to eliminate the unnecessary use of Social Security numbers?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-631'
      name: 'Matching Program'
      description: '%s engages in a matching program.'
      activity: "1. Obtain approval to conduct the matching program from the Data Integrity Board.\n2. Develop and enter into a computer matching agreement.\n3. Publish a matching notice in the Federal Register.\n4. Independently verify the information produced by the matching program before taking adverse action against an individual.\n5. Provide notice to individuals when information is processed for the purpose of conducting a matching program, allowing them an opportunity to contest findings before adverse actions are taken."
      question: 'Does the organization engage in a matching program?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-632'
      name: 'Supply Chain Risk Assessment'
      description: '%s assesses and updates supply chain risks associated with system components and system services.'
      activity: "1. Define systems, system components, and system services to assess supply chain risks.\n2. Establish the frequency for updating the supply chain risk assessment."
      question: 'Are supply chain risks assessed?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-633'
      name: 'Corrective Actions for Discoverable Information'
      description: '%s has a process to implement corrective actions when system information is discoverable.'
      activity: "1. Define the corrective actions to be taken if system information is discoverable.\n2. Ensure corrective actions are taken when information about the system is confirmed as discoverable."
      question: 'Does the organization take corrective actions when system information is discoverable?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-634'
      name: 'Security and Privacy Resource Planning and Allocation'
      description: '%s has explicit budgeting and organizational programming line items for information security and privacy programs and the resources needed for them throughout the system development life cycle.'
      activity: "1. Identify the high-level information security and privacy program requirements in mission and business process planning, including funding for system and services acquisition, sustainment, and supply-chain-related risks throughout the system development cycle.\n2. Identify and document the required resources to protect the system in capital planning and the investment control process, and allocate them accordingly.\n3. Explicitly establish line items for information security and privacy in organizational programming and budgeting documentation."
      question: 'Does the organization plan and allocate resources for its security and privacy programs?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-635'
      name: 'Approved Sensitive Information Products'
      description: '%s only allows information technology products approved under the Federal Information Processing Standards (FIPS) 201 to be used for sensitive information capabilities.'
      activity: 'Only allow FIPS 201-approved products for sensitive information capabilities.'
      question: 'Does the organization only employ information technology products on the FIPS 201-approved products list for sensitive information capabilities?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-636'
      name: 'System Documentation Maintained'
      description: '%s maintains system documentation for the system, system component, and system services, and has procedures for responding to attempts to obtain documents when the documentation is unavailable or nonexistent.'
      activity: "1. Develop and maintain system administrator documentation that describes: (a) system configuration, installation, and operation of the system, component, or service; (b) effective use and maintenance of security and privacy functions and mechanisms; and (c) known system vulnerabilities in configuration and use of administrative/privileged functions.\n2. Develop and maintain system user documentation that describes: (a) user-accessible security and privacy functions and mechanisms, and their effective use; (b) user interaction methods for the secure use of the system, component, or service, and protection of individual privacy; (c) responsibilities of users in maintaining security.\n3. Identify personnel or roles to whom the documentation will be distributed.\n4. Define actions to take when there is an attempt to obtain system, system component, or system service documentation when the documentation is either unavailable or nonexistent."
      question: 'Does the organization maintain documentation for the system, system component, or service for administrators and users?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-637'
      name: 'Secure Development Process'
      description: "%s has documented software development procedures that outline the company's processes for secure development. The documented processes include references to industry standards and/or best practices for secure development, security requirement considerations (for example, secure authentication and logging, etc.), and consideration for information security issues during each stage of the software development life cycle."
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-638'
      name: 'Separation of User and System Management Functions'
      description: '%s has implemented separation controls (physical or logical) so that user functionality is separated from system management functionality.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-639'
      name: 'Shared System Information Security'
      description: '%s has implemented technical controls to prevent unauthorized and unintended information transfer via shared system resources such as registers, cache memory, main memory, hard disks, etc.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-640'
      name: 'Limit External Connections'
      description: '%s ensures that the number of external network connections to the system are limited.'
      activity: 'Define and enforce the number of external network connections that can be connected to the system.'
      question: 'Does the organization limit the number of external network connections?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-641'
      name: 'Proxy Server'
      description: '%s uses authenticated proxy servers at managed interfaces to route internal communication traffic to external networks.'
      activity: "1. Define internal communications traffic to be routed to external networks.\n2. Identify external networks to which internal communications traffic is to be routed.\n3. Ensure authenticated proxy servers at managed interfaces are used to route internal communications traffic to external networks."
      question: 'Does the organization route internal communication to external networks via proxy servers?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-642'
      name: 'Fail Secure for Boundary Protection Devices'
      description: '%s has implemented fail secure mechanisms to maintain the system in a secure state when a boundary protection device fails.'
      activity: 'Implement mechanisms to create a fail secure condition in the event of boundary protection devices failing.'
      question: 'Does the organization prevent the system from entering a non-secure state during the operational failure of boundary protection devices?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-643'
      name: 'Collaborative Computing Devices and Applications'
      description: 'Remote activation of collaborative computing devices and applications (e.g., networked white boards, cameras, microphones, etc.) is prohibited unless explicitly defined otherwise in company policies and procedures. Collaborative computing devices provide an explicit indication of use to users physically present at the devices (e.g., through pop-up menus, signals, etc.).'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-644'
      name: 'Mobile Code Management'
      description: '%s has defined acceptable and unacceptable mobile code and mobile code technologies in the enterprise (e.g., Java, JavaScript, ActiveX, Postscript, PDF, Flash animations, VBScript, etc.). %s has implemented usage restrictions for mobile code technologies to prevent the development, acquisition, and introduction of unacceptable mobile through policies, procedures, and/or technical mechanisms.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-645'
      name: 'Session Authentication Management'
      description: '%s ensures that communication at the session level is protected.'
      activity: 'Implement mechanisms to protect authenticity of communication sessions.'
      question: 'Does the organization protect communications at the session level?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-646'
      name: 'Separate Execution Domain'
      description: '%s maintains a separate execution domain for each executing system process.'
      activity: 'Implement process isolation mechanisms.'
      question: 'Does the organization maintain a separate execution domain for each executing system?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-647'
      name: 'System Monitoring Tools'
      description: '%s allows system monitoring tools and mechanisms to see encrypted communications traffic.'
      activity: "1. Define encrypted communications traffic to be made visible to system monitoring tools and mechanisms.\n2. Implement system monitoring tools and mechanisms to be provided access to encrypted communications traffic."
      question: 'Does the organization make provision for encrypted communication traffic to be visible to system monitoring tools and mechanisms?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-648'
      name: 'Unauthorized Network Services Monitoring and Alert'
      description: '%s detects network services that have not been authorized and alerts designated personnel when detected.'
      activity: "1. Define the authorization or approval processes for network services.\n2. When unauthorized network service is detected, either initiate an audit or alert designated personnel.\n3. If applicable, define designated roles and personnel to be alerted upon detection."
      question: 'Does the organization detect network services that have not been authorized or approved?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-649'
      name: 'Security and Privacy Function Verification'
      description: 'Correct operation of security and privacy functions will be verified and designated personnel will be alerted of the failed security and privacy verification resulting in system restart or shutdown.'
      activity: "1. Define privacy and security functions to be verified for correct operation.\n2. Establish system transitional states requiring the verification of security and privacy functions.\n3. Generate alerts when anomalies are discovered or when a verification test fails.\n4. Define the roles or personnel to be alerted."
      question: 'Are security and privacy functions verified?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-650'
      name: 'Integrity Checks (System and Software)'
      description: '%s performs integrity checks of systems software, firmware, and system information during transitional states.'
      activity: "1. Define the software and firmware that would require an integrity check.\n2. Define transitional states or security-relevant events that require integrity checks.\n3. Establish the frequency of performing an integrity check."
      question: 'Does the organization perform integrity checks on software, firmware, and information systems?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-651'
      name: 'Integrity Violation Automated Response'
      description: '%s has mechanisms in place to automatically shut down, restart, or implement controls on systems when integrity violations are discovered.'
      activity: "1. Establish criteria for system shut down, restart, or control implementation when integrity violations are discovered.\n2. Implement mechanisms to execute violation responses automatically.\n3. Define the controls to be implemented, when applicable."
      question: 'Does the organization have procedures that automatically respond to integrity violations when discovered?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-652'
      name: 'Mechanisms for Code Authentication'
      description: '%s has cryptographic mechanisms in place to authenticate software and firmware components prior to installation.'
      activity: "1. Define software or firmware components to be authenticated by cryptographic mechanisms prior to installation.\n2. Implement cryptographic mechanisms to authenticate software or firmware components prior to installation."
      question: 'Does the organization have code authentication mechanisms in place?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-653'
      name: 'Spam Protection'
      description: '%s has spam protection at system entry and exit points to detect unsolicited messages.'
      activity: "1. Employ spam protection mechanisms at system entry and exit points.\n3. Detect and act on unsolicited messages.\n4. Update spam protection with new releases and in accordance with the company configuration management plan."
      question: 'Does the organization employ spam protection at system entry and exit points ?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-654'
      name: 'System Memory Protection'
      description: '%s has controls in place to protect the system memory from unauthorized code execution.'
      activity: 'Define and implement controls to be implemented to protect the system memory from unauthorized code execution.'
      question: 'Has the organization implemented controls to protect the system memory from unauthorized code execution?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-655'
      name: 'Tamper Protection Procedures'
      description: '%s has implemented a procedure for protection against systems tampering.'
      activity: 'Implement anti-tamper technologies, tools, and techniques.'
      question: 'Does the organization implement tamper protection controls on the systems, system components, or system services?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-656'
      name: 'Authoritative Source Information'
      description: '%s provides authoritative source information for external name/address resolution queries.'
      activity: "1. Provide additional data origin authentication and integrity verification artifacts along with the authoritative name resolution data that the system returns in response to external name/address resolution queries.\n2. Provide the means to indicate the security status of child zones and to enable verification of a chain of trust among parent and child domains, when operating as part of a distributed, hierarchical namespace."
      question: 'Has the organization implemented a name/address resolution service?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-660'
      name: 'Risk Appetite'
      description: '%s has defined and approved risk appetite statements, including statements pertaining to cybersecurity risk, that convey expectations about the level of risk the organization is willing to accept in the pursuit of objectives. These statements are reviewed periodically and updated as necessary.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-661'
      name: 'Customer Key Management'
      description: '%s provides customers with the capability to manage their own data encryption keys.'
      activity: "1. Determine and document the scope and shared responsibility of managed keys\n2. Provide customers with capability to manage policies, procedures, and processes\n3. Enable customers to manage data encryption keys and key management system in accordance with the shared responsibility model"
      question: 'Does the organization enable customers to manage their own data encryption keys?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-662'
      name: 'Default Privacy Configuration'
      description: '%s implements a default privacy configuration aligned with all applicable laws and regulations.'
      activity: "1. Determine the regional privacy laws and regulations that apply to the organization\n2. Identify industry best practices for privacy principles\n3. Establish and implement default configurations"
      question: 'Does the organization implement a default privacy configuration aligned with all applicable laws and regulations?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-663'
      name: 'Oversight of Privacy Controls'
      description: '%s management reviews privacy policies on an annual basis.'
      activity: "1. Conduct a management level review of policies on an annual basis\n2. Conduct a management level review of policies when a significant change occurs"
      question: 'Are privacy policies reviewed by management annually?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-664'
      name: 'High-Risk Privileged Access Provisions'
      description: '%s has processes and procedures in place to grant access to predefined high risk privileged roles.'
      activity: "1. Identify high risk privileged access roles\n2. Define, implement, and continuously evaluate processes and procedures for access approval"
      question: 'Does the organization have processes and procedures in place to grant access to predefined high risk privileged roles?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-665'
      name: 'API Availability'
      description: '%s provides a list of APIs to enable data retrieval for interoperability and portability.'
      activity: "1. Establish a list of available APIs\n2. Maintain and document the API functionality and availability"
      question: 'Does the organization enable data retrieval  for interoperability and portability by providing a list of APIs?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-666'
      name: 'Control Ownership Per Shared Responsibility Model '
      description: '%s has delineated the shared ownership and applicability of all its controls according to its policies and procedures for the application of its shared responsibility model.'
      activity: "1. Define the organization's responsibility boundaries\n2. Ensure delineation, if any, is current\n3. Implement a process for communicating the security responsibility boundaries to third parties"
      question: 'Does the organization define and document the shared responsibility and control ownership in partnerships with customers?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-667'
      name: 'Redundant Equipment '
      description: '%s maintains the availability of redundant equipment located within a reasonable distance of critical business equipment.'
      activity: "1. Define and document the minimum acceptable distance between redundant physical systems in accordance with applicable industry standards\n2. Evaluate the availability and location of redundant equipment"
      question: 'Does the organization manage the availability and placement of redundant equipment?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-668'
      name: 'Information Governance Policy'
      description: '%s has a defined Information Governance Policy that covers policies and procedures to manage information governance across the organization in compliance with security policies, standards, and procedures.'
      activity: "1. Document and maintain an Information Governance Policy\n2. Implement each section of the Information Governance Policy"
      question: 'Does the organization have policies and procedures for information governance?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-669'
      name: 'Shared Responsibility Policy'
      description: '%s has a defined Shared Responsibility Policy that covers policies and procedures to delineate and manage shared security responsibilities between Cloud Service Providers (CSP) and Cloud Service Customers (CSC).'
      activity: "1. Document and maintain a Shared Responsibility Policy\n2. Implement each section of the Shared Responsibility Model"
      question: 'Does the organization have policies and procedures for shared responsibility?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-670'
      name: 'Interoperability and Portability'
      description: '%s manages interoperability and data portability across the organization in compliance with security policies, standards, and procedures.'
      activity: "1. Define the processes for interoperability and portability\n2. Implement the processes for interoperability and portability"
      question: 'Does the organization practice secure interoperability and portability?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-671'
      name: 'External Systems Inventoried'
      description: '%s properly maintains an inventory of all external assets.'
      activity: "1. Generate an inventory of external assets and their classification\n2. Maintain and update the inventory\n3. Assign and maintain ownership of assets"
      question: 'Does the organization maintain a catalog of external assets?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-672'
      name: 'Integrity Checks (Hardware)'
      description: '%s performs integrity checks of system hardware during transitional states.'
      activity: "1. Define hardware that would require an integrity check.\n2. Define transitional states or security-relevant events that require integrity checks.\n3. Establish the frequency of performing an integrity check."
      question: 'Does the organization perform integrity checks on hardware?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-673'
      name: 'Administrative Interface Access'
      description: '%s has restricted internet access to the administrative interface used to manage firewall configuration.'
      activity: "1. Define and establish acceptable business needs for internet access to administrative interface and firewall configuration.\n2. Ensure that acceptable use cases for access implement: (a) multi-factor authentication; or (b) IP allow list that limits access to a small range of trusted addresses combined with a properly managed password authentication approach."
      question: 'Does the organization restrict internet access to the administrative interface used to manage firewall configuration?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-674'
      name: 'Unnecessary Software Removed or Disabled'
      description: '%s has removed or disabled unnecessary software.'
      activity: "1. Evaluate currently installed software and determine which applications, system utilities and network services are unnecessary.\n2. Remove or disable unnecessary software."
      question: 'Has the organization removed or disabled unnecessary software?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-675'
      name: 'Auto-run Disabled'
      description: '%s has disabled auto-run features.'
      activity: "1. Determine if there are existing auto-run features which allow file execution without user authorization (e.g. downloaded file auto-run).\n2. Disable auto-run features."
      question: 'Has the organization disabled auto-run features?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-676'
      name: 'Brute-force Mitigation'
      description: '%s has configured login throttling or device locking to protect against brute-force attacks.'
      activity: "1. Configure login throttling, so that the number of times the user must wait between login attempts increases with each unsuccessful attempt; no more than 10 guesses in 5 minutes should be allowed.\n2. Configure device locking, so that devices are locked after more than 10 unsuccessful attempts."
      question: 'Has the organization configured login throttling or device locking to protect against brute-force attacks?'
      tests: []
      domain: null
      category: null
    - code: 'DCF-677'
      name: 'Software Update and Patch Management'
      description: '%s has implemented a software update management process where critical patches and application updates are installed for all authorized software within priority SLAs established in company policies.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-678'
      name: 'Network Security Policy'
      description: '%s has defined and documented a policy that outlines requirements for deployment, management and operation of network security controls at the company.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-680'
      name: 'Use of Cryptographic Controls Related to Sensitive Information'
      description: '%s provides information to customers as to how it protects sensitive information through cryptography, and how it supports the application of cryptographic safeguards by customers.'
      activity: "1. Provide evidence of customer communication (e.g., product documentation guides, help portal, contracts and agreements) that describes how cryptography is used to protect sensitive information (e.g., PII).\n2. Provide evidence of customer communication (e.g., product documentation guides, help portal, contracts and agreements) that describes what options are available to customers to implement their own cryptographic safeguards."
      question: "1. Does %s provide evidence of customer communication (e.g., product documentation guides, help portal, contracts and agreements) that describes how cryptography is used to protect sensitive information (e.g., PII)?\n2. Does %s provide evidence of customer communication (e.g., product documentation guides, help portal, contracts and agreements) that describes what options are available to customers to implement their own cryptographic safeguards?"
      tests: []
      domain: 'Data & Privacy'
      category: null
    - code: 'DCF-681'
      name: 'Phishing Simulations'
      description: "%s conducts periodic phishing simulations or social engineering tests as part of the company's security awareness initiatives."
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-682'
      name: 'Roles and Responsibilities for PCI DSS Compliance'
      description: 'Roles and responsibilities for PCI DSS compliance activities and requirements are documented and assigned (e.g., through a responsibility assignment (RACI) matrix or other format), which are reviewed by management at least annually and signed-off by relevant personnel to evidence their acknowledgement and understanding of their responsibilities.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-683'
      name: 'Password Expiration Periods for Customer User Access'
      description: 'For customer user access where passwords/passphrases are used as the only authentication factor, credentials are required to be changed at least once every 90 days. Alternatively, technical measures are implemented to dynamically analyze the security posture of accounts, and real-time access to resources is automatically determined accordingly.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-684'
      name: 'Redundancy of Processing'
      description: '%s has implemented redundancy strategies for equipment, systems, and processes as deemed necessary per the business continuity plan(s) to meet availability requirements (e.g., redundancy in network components, production resources, supporting utilities, service providers, processing sites, etc.)'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-685'
      name: 'Review of Security Awareness Program'
      description: "The security awareness program is reviewed at least annually and updated as needed to address any new threats and vulnerabilities that may impact the security of the company's environment or the information provided to personnel about their role in protecting sensitive data."
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-686'
      name: "PCI Targeted Frequency \nRisk Analysis"
      description: '%s performs a targeted risk analysis at least annually to identify the necessary frequency for PCI DSS compliance activities that provide flexibility for how frequently they are performed. Documentation of the risk analysis is maintained and includes: identification of assets, threats, factors that contribute to likelihood and impact, and resulting analysis with justification for how frequently each requirement will be performed.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-687'
      name: 'Email Protection Mechanisms'
      description: '%s has implemented processes and automated mechanisms to maintain the integrity of email communications and detect or protect against phishing attacks and malicious emails (e.g., DMARC, SPF and DKIM to prevent spoofed or modified emails from valid domains, etc.).'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-688'
      name: 'Return of Assets'
      description: '%s tracks and documents the return of all electronic and physical assets upon termination as part of the offboarding process. Access mechanisms such as keys, access cards, MFA tokens, are disabled or collected by IT or HR personnel.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-689'
      name: 'On-Call Team'
      description: 'Specific personnel are designated to be available on a 24/7 basis to respond to suspected or confirmed security incidents and operational issues through an on-call rotation schedule.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-690'
      name: "PCI Targeted Customized Approach \nRisk Analysis"
      description: '%s performs a targeted risk analysis at least annually for each PCI DSS requirement that the entity meets with the customized approach. Documented evidence is retained to include each element specified in PCI DSS 4.0 Appendix D: Customized Approach (including, at a minimum, a controls matrix and risk analysis) and evidence of approval by senior management.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-691'
      name: 'Marketing Express Consent'
      description: '%s obtains express consent from data subjects prior to using any PII processed under a contract for the purposes of marketing and advertising. Such consent is optional and not a condition for use of the service.'
      activity: ''
      question: ''
      tests: []
      domain: 'Data & Privacy'
      category: null
    - code: 'DCF-692'
      name: 'Secure Erasure of Temporary Data'
      description: '%s disposes of temporary files, documents, and data (e.g., log data, data from processors and memory, etc.) as soon as no longer needed or in accordance with documented retention periods.'
      activity: ''
      question: ''
      tests: []
      domain: 'Data & Privacy'
      category: null
    - code: 'DCF-693'
      name: 'Restriction of the Creation of Hardcopy Material'
      description: '%s restricts the creation of hardcopy material containing PII based on business justification through policy requirements or technical measures.'
      activity: ''
      question: 'Does the organization have a process to restrict the creation of hardcopy material that contains PII?'
      tests: []
      domain: 'Data & Privacy'
      category: null
    - code: 'DCF-694'
      name: 'Use of Unencrypted Portable Storage'
      description: '%s limits the use of unencrypted physical media and portable devices to only when strictly necessary. Use of unencrypted physical media is documented to include business justification and approval.'
      activity: ''
      question: ''
      tests: []
      domain: 'Data & Privacy'
      category: null
    - code: 'DCF-695'
      name: 'Access to Data on Pre-used Data Storage Space'
      description: '%s wipes or renders unreadable data in pre-used storage space when its re-assigned (e.g., re-assigned to a new customer or used for a different purpose).'
      activity: ''
      question: ''
      tests: []
      domain: 'Data & Privacy'
      category: null
    - code: 'DCF-696'
      name: 'Intended destination of PII'
      description: '%s has implemented technical measures to ensure that PII transmitted using a data-transmission network reaches its intended destination.'
      activity: ''
      question: 'Does the organization have established technical measures in place to ensure that PII transmitted using a data-transmission network reaches its intended destination?'
      tests: []
      domain: 'Data & Privacy'
      category: null
    - code: 'DCF-697'
      name: 'Public Cloud PII Protection Policy'
      description: '%s has established a policy to outline requirements for protection of PII in the public cloud.'
      activity: ''
      question: "Does the organization facilitate the implementation of PII protection controls in it's Public Cloud?"
      tests: []
      domain: 'Data & Privacy'
      category: null
    - code: 'DCF-698'
      name: 'Automated Mechanisms for Audit Log Reviews'
      description: '%s has implemented automated mechanisms for audit record reduction that supports correlated audit log review and analysis, such as centralized log management systems, event log analyzers, security information and event management (SIEM) solutions, etc.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-699'
      name: 'Authenticated Scanning for Internal Vulnerability Scans'
      description: 'All internal vulnerability scans are conducted via authenticated scanning using credentials with sufficient privileges to conduct a thorough scan. Credentials are managed securely in accordance with company policies and procedures. %s maintains documentation of any systems that are unable to accept credentials for authenticated scanning.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-700'
      name: 'Review of PCI Hardware and Software'
      description: '%s reviews hardware and software technologies at least once every 12 months to confirm whether they continue to meet the organization’s PCI DSS requirements. The review documentation includes a plan for remediating technologies that no longer meet the organization’s security and PCI DSS requirements, up to and including replacement of the technology, as appropriate.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-701'
      name: 'Review of PCI Requirements Upon Organizational Structure Changes'
      description: '%s performs a formal internal review of the impact to PCI DSS scope and applicability of controls upon changes to the organizational structure, such as company mergers or acquisitions, changes in personnel with responsibility for security controls, etc., with results communicated to executive management in accordance with documented company policies and procedures.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-702'
      name: 'PCI Scope Validation (Annual)'
      description: '%s performs a formal review of the documented PCI DSS scope at least once every 12 months and upon significant change to the environment to validate that all data flows, account data, system components, segmentation controls, and connections from third parties with access to the cardholder data environment are included in the scope of the compliance program. Results of the review including changes are documented and retained.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-703'
      name: 'PCI Scope Validation for Service Providers (Semi-Annual)'
      description: '%s performs a formal review of the documented PCI DSS scope at least once every six months and upon significant change to the environment to validate that all data flows, account data, system components, segmentation controls, and connections from third parties with access to the cardholder data environment are included in the scope of the compliance program. Results of the review including changes are documented and retained.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-704'
      name: 'PCI Compliance Training'
      description: '%s provides PCI DSS or specialized information security training to all personnel with PCI DSS compliance responsibilities at least once every 12 months that focuses on specific security topics, skills, processes, or methodologies that must be followed for those individuals to perform their compliance responsibilities effectively.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-705'
      name: 'PCI Scope Validation for Designated Entities (Quarterly)'
      description: '%s performs a formal review of the documented PCI DSS scope at least once every three months and upon significant change to the environment to validate that all data flows, account data, system components, segmentation controls, and connections from third parties with access to the cardholder data environment are included in the scope of the compliance program. Results of the review including changes are documented and retained.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-706'
      name: 'Multi-Tenant Segregation Between Provider and Customer '
      description: "For multi-tenant service providers, logical separation is implemented so that customers cannot access the provider's environment and the provider cannot access its customers’ environments without authorization."
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-707'
      name: 'Credentials for System Accounts Not Hard-Coded'
      description: '%s has implemented mechanisms to validate that authentication credentials for any application and system accounts are not hard coded in scripts, configuration/property files, or bespoke and custom source code.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-708'
      name: 'Software and Third Party Libraries Inventory '
      description: 'An inventory of bespoke and custom software and third-party software components, such as a software bill of materials (SBOM), is maintained and kept up to date (e.g., through the use of software composition analysis tools or other mechanisms).'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-709'
      name: 'IDS/IPS Addresses Covert Malware'
      description: 'Intrusion-detection and/or intrusion-prevention mechanisms in place detect, alert on/prevent, and address covert malware communication channels such as DNS tunneling.  Alerts generated by these mechanisms are responded to by personnel, or by automated means that ensure that such communications are blocked.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-710'
      name: 'Multi-Tenant Service Provider Supports Customer Pentest Requests'
      description: "%s supports its customers' needs for technical penetration testing by either providing access to the customer for conducting the test or providing evidence that comparable technical testing has been undertaken, such as penetration test results, upon customer request."
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-711'
      name: 'Incident Procedures Support Customers Forensic Investigations'
      description: '%s has documented processes or mechanisms to support and/or facilitate a prompt forensic investigation of related servers in the event of a suspected or confirmed security incident for any customer.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-712'
      name: 'Static Application Security Testing'
      description: '%s uses static application security testing (SAST) or equivalent tool as part of the CI/CD pipeline to detect vulnerabilities in the codebase. When vulnerabilities are identified, corrections are implemented prior to release as appropriate based on the nature of the vulnerability.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-713'
      name: 'Consoles in Sensitive Areas Locked'
      description: 'Console login screens for physical consoles within sensitive areas are locked to prevent unauthorized use.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-714'
      name: 'Lifecycle Events for User IDs and Authentication Factors Authorized'
      description: 'Changes to user IDs (addition, deletion, and modification), authentication factors, and other identifier objects are implemented only with documented management approval and in accordance with the privileges specified on the documented approval.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-715'
      name: 'Live PANs Not Used Outside of CDE'
      description: 'Live primary account numbers (PAN) are not used in pre-production environments except where those environments are within the scope of the PCI compliance program and managed in accordance with all applicable PCI DSS requirements.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-716'
      name: 'Application and System Accounts Authorized'
      description: 'Application and system accounts and related access privileges are provisioned based on documented authorization from management and limited to only the access needed for the operability of an application or system.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-717'
      name: 'Unique Authentication Factors'
      description: '%s assigns authentication factors such as physical or logical security tokens, smart cards, or certificates that are unique to each individual user and not shared among multiple users. Physical or technical controls, such as a PIN, biometric data, passwords, etc., are in place so that only the intended user can use that factor to gain access.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-718'
      name: 'SSL and Early TLS Vulnerability Verification'
      description: 'For any point-of-sale (POS) and point-of-interaction (POI) terminals in use at merchant or payment acceptance location that use SSL and/or early TLS, the entity verifies and retains evidence to show that devices are not susceptible to any known exploits for those protocols (e.g., through inspection of vendor documentation or system/network configuration details, etc.)'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-719'
      name: 'SSL and Early TLS Secure Service Offering'
      description: '%s provides a secure service offering for customers with point-of-interaction (POI) terminals that use SSL and early TLS to upgrade their POIs to eliminate vulnerabilities associated with these legacy protocols.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-720'
      name: 'Unauthorized Changes on Payment Pages  Detected'
      description: "A change- and tamper-detection mechanism is deployed to alert personnel to unauthorized modification, including indicators of compromise, changes, additions, and deletions, to the HTTP headers and the contents of payment pages as received by the consumer browser. The mechanism is configured to evaluate the received HTTP header and payment page at least weekly or periodically based on the entity's targeted risk analysis."
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-721'
      name: 'Payment Page Scripts Inventory and Verification'
      description: '%s maintains an inventory of all payment page scripts that are loaded and executed in the consumer’s browser, supported with documented business or technical justification. %s has implemented mechanisms to confirm that all payment page scripts authorized and to assure the integrity of each script.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-722'
      name: 'Service Provider Responds to PCI DSS Information Requests from Customers'
      description: '%s provides, upon customer request, information on PCI DSS compliance status and information about which PCI DSS requirements are the responsibility of %s versus the customer, as well as any shared responsibilities.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-723'
      name: 'Multi-Tenant Customers Log Availability'
      description: '%s provides audit log capability for each customer’s environment that is active by default. Logs are available for review only by the owning customer and log data and availability is consistent with compliance requirements. The log locations are clearly communicated to the owning customer.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-724'
      name: 'Incident Response Procedures When Cleartext PAN Detected'
      description: '%s documented incident response procedures include processes to respond, analyze, and address situations in the event that cleartext PAN is detected where it is not expected.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-725'
      name: 'MFA Configured to Prevent Misuse'
      description: 'Where multi-factor authentication (MFA) is used, system configurations are in place to prevent replay attacks, require at least two different types of authentication factors, and restrict bypassing of requirements by any users, including administrative users, unless specifically documented, and authorized by management on an exception basis, for a limited time period.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-726'
      name: 'Interactive Use of System and Application Accounts Managed'
      description: 'Where system or application accounts can be used for interactive login, interactive use is prevented unless needed for an exceptional circumstance and of limited duration. Instances of interactive use are supported by documented business justification and explicit approval by management, and every action taken is attributable to an individual user.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-727'
      name: 'Passwords for System and Application Accounts Changed Periodically'
      description: "%s changes passwords of any application and system accounts periodically (as defined by the entity's targeted risk analysis) and upon suspicion or confirmation of compromise. Passwords are configured with sufficient complexity to resist brute-force and guessing attacks and in accordance with company procedures."
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-728'
      name: 'Roles and Responsibilities for PCI DSS Compliance for Designated Entities'
      description: 'Roles and responsibilities for PCI DSS compliance activities and requirements are documented and assigned to one or more individuals with sufficient authority, including responsibilities for managing PCI DSS business-as-usual activities, annual assessments, continuous validation of PCI DSS requirements, and business-impact analysis to determine potential PCI DSS impacts for strategic business decision.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-729'
      name: 'Formal PCI DSS Compliance Program in Place'
      description: '%S has implemented a formal PCI DSS compliance that includes: definition of activities for maintaining and monitoring overall compliance including business-as-usual activities, annual PCI DSS assessment processes, processes for the continuous validation of PCI DSS requirement, and a process for performing business-impact analysis to determine potential PCI DSS impacts for strategic business decisions.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-730'
      name: 'Security Controls on Devices that Connect to the Internet'
      description: 'Security controls, such as local firewalls, endpoint protection software, etc., are installed on computing devices, including company- and employee-owned devices, that connect to both untrusted networks (including the Internet) and the environment to prevent the introduction of threats.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-731'
      name: 'Documented PAN Discovery Methodology'
      description: '%s has documented a data discovery methodology to identify all cleartext PAN existing in the environment through automated or manual mechanisms. The data discovery efforts are conducted at least once every three months and upon significant changes to the CDE or processes and the results are documented.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-732'
      name: 'Review of Cryptographic Suites and Protocols'
      description: '%s maintains a documented inventory of all cryptographic cipher suites and protocols in use, including purpose and where used. The inventory is reviewed at least once every 12 months including monitoring of industry trends regarding continued viability of all cryptographic cipher suites and protocols in use. Where appropriate, %s documents a plan to respond to anticipated changes in cryptographic vulnerabilities for suites and protocols in use.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-733'
      name: 'Review of Business as Usual Activity'
      description: 'Reviews are performed at least once every three months by personnel assigned to the PCI DSS compliance program to verify that all business as usual (BAU) activities are being performed and that personnel are following security policies and operational procedures. Results are documented and signed-off by personnel assigned responsibility for the PCI DSS compliance program and records are retained for at least 12 months.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-734'
      name: 'Incident Response Procedures for Cleartext PAN Removal'
      description: '%s documented incident response procedures include processes to be initiated upon detection of attempts to remove cleartext PAN from the CDE via an unauthorized channel, method, or process, including investigation of alerts by responsible personnel and procedures for remediating data leaks or process gaps to prevent prevent any data loss.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-735'
      name: 'Migration Plan in Place for SSL and Early TLS POS POI'
      description: 'For any existing connection points to POS POI terminals that use SSL and/or early TLS, %s has a documented risk mitigation and migration plan in place.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-736'
      name: 'PAN Discovery Mechanisms Assessed'
      description: '%s has a documented process to test the effectiveness of the methods used for data discovery to identify cleartext PAN in the environment. %s executes the process at least once every 12 months to confirm the completeness and accuracy of account data detection.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-737'
      name: 'Protected Storage of Secret and Private Keys for Account Data'
      description: 'Secret and private keys used to encrypt/decrypt stored account data are stored in within a secure cryptographic device (SCD) as at least two full-length key components or key shares in accordance with an industry-accepted method.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-738'
      name: 'Keyed Cryptographic Hashes of PAN'
      description: "Where hashes are used to render PAN unreadable, hashing methods results in keyed cryptographic hashes of the entire PAN which are managed in accordance with company's key management processes and procedures."
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-739'
      name: 'PCI DSS Scope Impact Assessment'
      description: '%s conducts a PCI DSS scope impact assessment for any changes to systems or networks, including additions of new systems and new network connections. The assessment includes identifying applicable PCI DSS requirements to the system or network, updating PCI DSS scope as appropriate, and documenting sign-offs of the results of the impact assessment by responsible personnel.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-740'
      name: 'PCI DSS Compliance Policy'
      description: '%s has a documented policy that outlines company requirements and activities to be implemented to support PCI DSS compliance.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-741'
      name: 'Logging and Monitoring Policy'
      description: '%s has a documented policy that outlines requirements for audit logging and monitoring of system activity at the company.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-742'
      name: 'Insider Threat Awareness and Training'
      description: '%s provides periodic awareness training on recognizing and reporting potential indicators of insider to managers and employees as deemed necessary by the organization. Training topics include how to communicate employee and management concerns regarding potential indicators of insider threat through appropriate organizational channels in accordance with established organizational policies and procedures.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-743'
      name: 'Split Tunneling Restriction'
      description: '%s has implemented configuration settings to prevent remote devices (e.g., laptops, smart phones, and tablets) from establishing a connection to organizational systems using split tunneling.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-744'
      name: 'Contact with Authorities'
      description: '%s has identified and documented authorities to be contacted (such as law enforcement, regulatory bodies, supervisory authorities) as well as the events or circumstances that would require communication. %s has also documented the methods and responsibilities for communication with authorities.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-745'
      name: 'Segregation of Duties'
      description: 'Management has identified conflicting duties and areas of responsibility in the organization and implemented strategies to achieve segregation of duties (e.g., access control, assigning responsibilities to different individuals, etc.). Where segregation of duties is not possible, management has identified and implemented mitigating controls to reduce the risk of fraud.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-746'
      name: 'Privacy Training'
      description: '%s has established training programs to help personnel understand their obligations and responsibilities for the protection of personally identifiable information (PII) and associated regulatory requirements. Personnel (including employees and contractors as applicable) are required to complete the training during onboarding and annually thereafter.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-747'
      name: 'Secure Log-on for Customers'
      description: "%s provides customers with the capabilities for secure log-on procedures for any user accounts under the customers' control (e.g., single sign-on, multi-factor authentication, masking of passwords, minimal information disclosures in error messages, etc.)"
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-748'
      name: 'Segmentation of Networks'
      description: '%s uses network segmentation and/or other techniques to isolate portions of the environment and to control traffic between them based on security and business needs.'
      activity: ''
      question: ''
      tests:
          - id: 253
      domain: null
      category: null
    - code: 'DCF-749'
      name: 'Leak Detection System'
      description: 'Critical facilities are equipped with a leak detection system to detect water in the event of a flood or leakage.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-750'
      name: 'Data Minimization Objectives'
      description: '%s has defined and documented data minimization objectives and the mechanisms (e.g., de-identification) that are used to meet those objectives.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-751'
      name: 'Basis for PII Transfers Between Jurisdictions'
      description: '%s has identified and documented the relevant basis for transfers of PII between jurisdictions. %s provides timely notices to customers of the basis for PII transfers between jurisdictions and of any intended changes in this regard.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-752'
      name: 'Obligations to Data Subjects'
      description: '%s has determined and documented the legal, regulatory, and business obligations to data subjects related to the processing of their PII and the mechanisms offered to data subjects to fulfill these obligations.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-753'
      name: 'Mechanisms to Object to PII Processing'
      description: '%s provides customers with a mechanism for data subjects to object to the processing of their PII (e.g. objections relating to the processing of PII for direct marketing purposes, etc.).'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-754'
      name: 'Right to Access'
      description: '%s has documented and implemented procedures and mechanisms to locate, retrieve, and provide a copy of the PII that is collected and/or processed when requested by the data subject, or to notify them if the PII has been deleted or de-identified.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-755'
      name: 'Data Subjects Information Requirements'
      description: '%s has determined and documented the information to be provided to data subjects regarding the processing of their PII and the timing of such a provision (e.g. prior to processing, within a certain time from when it is requested, etc.) based on legal, regulatory and business requirements.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-756'
      name: 'Dual Opt-In for Consent to Sell PII'
      description: '%s provides a dual opt-in mechanism for consent to sell or share personal information whereby the data subject first requests to opt-in and then, separately confirms their choice to opt-in.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-757'
      name: 'User and System Guides'
      description: '%s provides user guides, help articles, system documentation or other mechanisms to users to share information about the design and operation of the system and its boundaries. The information provided includes functional and nonfunctional requirements related to system processing and information specifications required to support the use of the system.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-758'
      name: 'Organization Supports Customers Privacy Obligations'
      description: '%s provides customers with information and mechanisms such that the customer can demonstrate compliance with their privacy obligations and obligations to data subjects (e.g., by sharing documented results of third party audits, providing mechanisms to delete and correct personal information, etc.). These provisions are outlined in the processing contract.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-759'
      name: 'PII Automated Decision Making'
      description: '%s has identified and implemented mechanisms to address the obligations (including legal obligations) to the data subjects resulting from decisions related to the data subject based solely on automated processing of PII (such as notifying the existence of automated decision making, allowing for the data subjects to object to such decision making or request human intervention, etc.).'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-760'
      name: 'Control of Audit Activities'
      description: 'Audit requirements and activities involving verification of operational systems are planned and agreed-upon by management to minimize disruptions to business processes and security risks (considering scope, access requirements, availability impact, etc.).'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-761'
      name: 'Incident Management Procedures for Collection of Evidence'
      description: "%s's documented incident response processes include guidelines for the identification, collection, acquisition and preservation of evidence related to information security incidents, including incident data and metadata."
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-762'
      name: 'Managing Changes to Supplier Services'
      description: 'Changes to the provision of services by vendors, including expansions of services and supplier changes, require review and due diligence activities and are authorized by management. Documentation of the due diligence activities and authorization is retained.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-763'
      name: 'Requirements for Protection of Intellectual Property Rights'
      description: "%s's policies, procedures, and agreements include requirements for protection of intellectual of property rights and use of proprietary software products."
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-764'
      name: 'Records of Transfers of PII'
      description: '%s maintains documented records of transfers of PII to or from third parties.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-765'
      name: 'Limit Collection of PII'
      description: 'Where any optionality in the collection and processing of PII exists, %s has disabled that option by default and only enabled by explicit choice of the data subject.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-766'
      name: 'PII Data Discovery Methodology'
      description: '%s has implemented a data discovery methodology to identify all PII in the environment through automated or manual mechanisms. The data discovery efforts are conducted periodically and results are documented.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-767'
      name: 'Incident Procedures When Unauthorized PII Detected'
      description: '%s documented incident response procedures include processes to respond, analyze, and address situations in the event that PII is detected where it is not authorized (e.g., system logs, etc.)'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-768'
      name: 'Personal Data Inventory'
      description: '%s maintains a documented inventory of all system components, products, or services that store or process PII to facilitate data discovery efforts. The inventory includes information about the the category of PII, the system where the data is stored or processed, the data elements, data owner, data action, retention policy, and other attributes deemed relevant by the organization.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-769'
      name: 'Rejection of Non-Legally Binding PII Disclosures'
      description: '%s rejects any requests for PII disclosures that are not legally binding and retains supporting documentation.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-770'
      name: 'Consulting with Customer Prior to PII Disclosures'
      description: 'When a data subject has authorized an agent to submit a privacy right request, %s confirms directly with the data subject that they provided the authorized agent permission to submit the request prior to fulfilling the request and retains supporting documentation.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-771'
      name: 'Infringing Instruction'
      description: '%s notifies customers if, in its opinion, a processing instruction infringes applicable legislation and/or regulation and retains documentation of the notification.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-772'
      name: 'PII Handling Mechanisms and Policies to Customers'
      description: '%s provides customers with the ability to return, transfer and/or dispose of PII in a secure manner, and provides customers with information on internal PII disposal policies.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-773'
      name: 'PII Controller Obligations to Notify Third Parties'
      description: '%s has documented and implemented policies, procedures and/or mechanisms to notify third parties with whom PII has been shared of any modification or withdrawal of consent or objections pertaining to the shared PII.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-774'
      name: 'Data Processing Monitoring'
      description: "Application/data processing for %s's system is logged and monitored to ensure processing is done completely and accurately. Errors in application/data processing are documented, investigated, escalated and corrected in accordance with policies and procedures."
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-775'
      name: 'Cloud Deletion Protection'
      description: '%s has enabled deletion protection for cloud resources to prevent irreversible data loss or downtime resulting from accidental or malicious actions.'
      activity: ''
      question: ''
      tests:
          - id: 8029
      domain: null
      category: null
    - code: 'DCF-776'
      name: 'Principle of Least Privilege'
      description: '%s assigns permissions through groups or roles based on the principle of least privilege and limits the use of wild-card permissions or broad-access patterns.'
      activity: ''
      question: ''
      tests:
          - id: 208
          - id: 217
          - id: 230
          - id: 8025
          - id: 8026
      domain: null
      category: null
    - code: 'DCF-777'
      name: 'Cloud Resource Tagging'
      description: '%s uses tags to assign metadata to cloud resources to facilitate identification, inventory, and classification of virtual assets.'
      activity: ''
      question: ''
      tests:
          - id: 8028
      domain: null
      category: null
    - code: 'DCF-778'
      name: 'Fraud Risk Assessment'
      description: "%s performs an evaluation of fraud risks at least annually, either as a separate evaluation or as part of the overall enterprise risk assessment. The evaluation of fraud risk is performed in accordance with the company's risk assessment methodology."
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-779'
      name: 'Cryptographic Key Rotation'
      description: '%s has implemented processes to change cryptographic keys periodically based on a defined schedule.'
      activity: ''
      question: ''
      tests:
          - id: 223
          - id: 254
          - id: 8031
      domain: null
      category: null
    - code: 'DCF-780'
      name: 'Web Filtering'
      description: "%s has implemented web filtering mechanisms to enforce the company's internet usage policies (e.g, block access to known malicious sites, prevent access to prohibited web resources, etc.)"
      activity: ''
      question: ''
      tests:
          - id: 311
          - id: 312
      domain: null
      category: null
    - code: 'DCF-781'
      name: 'Secure Login Procedures'
      description: '%s has implemented secure login procedures for in-house developed systems to deter enumeration or brute-force attacks (e.g., displaying limited information in login error messages without indicating which data is correct or incorrect, etc.)'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-782'
      name: 'Cloud Storage Lifecycle'
      description: '%s has configured lifecycle rules for cloud storage to delete data automatically after expiration of their retention periods.'
      activity: ''
      question: ''
      tests:
          - id: 8030
      domain: null
      category: null
    - code: 'DCF-783'
      name: 'Credentials Rotation'
      description: '%s has implemented processes to change credentials (secrets, access keys, API keys, etc.) periodically based on a defined schedule.'
      activity: ''
      question: ''
      tests:
          - id: 232
          - id: 8032
      domain: null
      category: null
    - code: 'DCF-784'
      name: 'Software Composition Analysis (SCA)'
      description: "%s checks software components and libraries for policy and license compliance, security risks, and supported versions (e.g. using software composition analysis (SCA) tools in development pipeline, etc.). If vulnerabilities in these software components or libraries are identified, fixes are implemented in accordance with the company's vulnerability management policies."
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-785'
      name: 'Supported System Components'
      description: '%s maintains secure and supported configuration standards and versions for system components and replaces those components when support is no longer available from the developer, vendor, or manufacturer. Where unsupported components cannot be replaced, %s identifies and documents risk mitigation strategies.'
      activity: ''
      question: ''
      tests:
          - id: 8027
      domain: null
      category: null
    - code: 'DCF-786'
      name: 'Defined Company Objectives'
      description: 'Management has defined company objectives, including mission and vision statements, operational objectives at the entity and functional levels, financial performance goals, and other objectives as appropriate, to serve as the basis for risk assessment activities (e.g., objectives related to security, compliance, risk mitigation, etc.). Management communicates its objectives and any changes to those objectives to personnel.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-787'
      name: 'Re-authentication Configurations'
      description: "%s has implemented security configurations to require entities to re-authenticate to systems periodically or upon specific conditions per the company's policies and procedures (e.g., session lifetimes, inactivity periods, etc.)."
      domain: null
      category: null
      activity: ''
      question: ''
      tests: []
    - code: 'DCF-788'
      name: 'CUI Data Inventory'
      description: '%s maintains a documented inventory of all system components, products, or services that store or process controlled unclassified information (CUI) to facilitate data discovery efforts. The inventory includes information about the type of data, the system where the data is stored or processed, the data elements (metadata), access controls, and other attributes deemed relevant by the organization. The inventory is reviewed and updated when there are changes in the location of CUI.'
      domain: null
      category: null
      activity: ''
      question: ''
      tests: []
    - code: 'DCF-789'
      name: 'Expectations of Interested Parties'
      description: '%s has identified and documented the interested parties, their requirements and expectations of the organization (e.g., security and privacy expectations of customers, compliance expectations of regulators, business expectations of partners, performance and risks expectations of directors and investors, etc.), and how these requirements and expectations will be addressed.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-790'
      name: 'System Security Plans'
      description: '%s has documented system security plans (SSPs) in accordance with compliance or regulatory requirements. The SSPs describe system components and boundaries, specific threats of concern to the organization, type of data processed, operational environments and the relationships with or connections to other systems, security safeguards in place or planned, responsible parties, and other relevant information. The SSPs are reviewed and updated periodically as determined by the organization.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-791'
      name: 'Plans of Action and Milestones'
      description: '%s has documented plans of action and milestones (POAM) that describe how any unimplemented security requirements will be met and how any planned mitigations will be implemented. The POAMs are updated periodically based on results of security assessments audits or reviews, and continuous monitoring activities.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-792'
      name: 'Regulation of Cryptographic Controls'
      description: 'Cryptographic standards used to protect sensitive information are compliant with relevant regulatory requirements (e.g., using FIPS-validated cryptography to protect confidential unclassified information (CUI), etc.).'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-793'
      name: 'Dedicated Accounts or Roles for Admin Functions'
      description: 'Separate, dedicated administrator accounts or roles are used by system users to access administrative or security functions on enterprise assets. Users access non-security functions through standard accounts or roles without elevated privileges.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-794'
      name: 'Internal Privacy Policies'
      description: "%s has established and documented internal privacy policies that outline the company's practices and expectations regarding personal data that personnel may access, store, transfer, or otherwise process as part of their job duties at the organization."
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-795'
      name: 'Transferred Personnel Access Validation'
      description: 'When personnel is reassigned or transferred to other positions in the organization, %s reviews and confirms the appropriateness of any existing logical and physical access authorizations. Access authorizations are modified if necessary to align with the new job duties. Documentation of the access validation is retained.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-796'
      name: 'Training for Posting Public Content'
      description: '%s has identified individuals that are authorized to post publicly accessible content on organizational systems and provides periodic training to these individuals to ensure that publicly accessible information does not contain controlled unclassified information (CUI).'
      domain: null
      category: null
      activity: ''
      question: ''
      tests: []
    - code: 'DCF-797'
      name: 'CUI Markings'
      description: '%s marks system media that contain controlled unclassified information (CUI) (e.g., diskettes, flash drives, magnetic tapes, external or removable solid state or magnetic drives, compact discs, and digital versatile discs, paper, microfilm, etc.) to indicate distribution limitations, handling caveats, and applicable CUI markings in accordance with policy and regulatory requirements.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-798'
      name: 'CUI Risk Assessment'
      description: "%s performs an evaluation of risk (including supply chain risk) of unauthorized disclosure resulting from the processing, storage, or transmission of controlled unclassified information (CUI), either as a separate evaluation or as part of the overall organizational risk assessment. The evaluation is performed in accordance with the company's risk assessment methodology and updated periodically."
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-799'
      name: 'Organizational Systems Maintenance'
      description: '%s performs periodic security maintenance on organizational system components (including hardware, firmware, and applications, as well as peripheral devices such as scanners, copiers, and printers, etc.) in accordance with its documented policies and procedures.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-800'
      name: 'AI Governance Policy'
      description: "%s has a documented policy for the governance of AI systems that establishes the company's rules and requirements for responsible AI practices."
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-801'
      name: 'AI Risk Management Policy'
      description: '%s has a documented policy for the management of risks related to AI systems, which outlines the scope and methodology or AI risk assessments, evaluation, and mitigation.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-802'
      name: 'AI Feedback Management'
      description: '%s has mechanisms and procedures in place to collect input from internal and external stakeholders regarding AI systems, their operations, and their impact.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-803'
      name: 'AI System Opt-Out'
      description: '%s has mechanisms in place to allow individuals or groups to opt out of or contest problematic AI system outcomes.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-804'
      name: 'AI Training'
      description: "%s has established training and education programs for responsible AI practices and system management to help personnel understand their obligations and responsibilities to comply with %s's AI-related policies and procedures. Personnel are required to complete the training during onboarding and annually thereafter."
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-805'
      name: 'AI Committees'
      description: '%s has identified and documented groups or committees of users, internal and external stakeholders, and experts for AI-related information sharing and feedback.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-806'
      name: 'AI Development and Evaluation Policy'
      description: "%s has a documented policy for the development, design, and evaluation of AI systems, which outlines the company's practices, roles, and responsibilities in the safe design, development, deployment, use, and evaluation of AI systems to minimize negative impacts."
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-807'
      name: 'Risk Tolerance'
      description: "%s has defined specific, measurable, and broadly understandable risk tolerance statements that describe the acceptable deviation from the level set by the organization's risk appetite and business objectives. These statements are reviewed periodically and updated as necessary."
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-808'
      name: 'Residual Risk'
      description: '%s documents negative residual risks within risk response plans, including risks that have been accepted, transferred, or subject to minimal mitigation.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-809'
      name: 'AI Practitioner Proficiency '
      description: '%s has defined, assessed and documented processes for operator and practitioner proficiency with AI system performance and trustworthiness, as well as relevant technical standards and certifications.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-810'
      name: 'Human Oversight over AI Systems'
      description: '%s has documented and implemented processes for human oversight over AI systems.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-811'
      name: 'AI System Fairness and Bias Evaluation'
      description: '%s has documented and implemented processes to evaluate fairness and bias in AI systems.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-812'
      name: 'AI Model Environmental Impact'
      description: '%s assesses and documents consideration for environmental impact related to AI systems.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-813'
      name: 'AI Risk Tracking'
      description: '%s has documented and implemented a process for tracking AI-related emerging risks that may not be measurable with currently available approaches.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-814'
      name: 'Security Impact Assessment for Changes'
      description: 'System changes are evaluated for security impact prior to implementation to determine whether the change introduces new vulnerabilities or security risks, or introduce the need for additional security measures. %s validates that security requirements for the system continue to be satisfied after the system changes have been implemented.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-815'
      name: 'Incident Response Training Content Review'
      description: 'The incident response training program and its contents are reviewed and updated at periodic intervals and after any events deemed significant by the organization (e.g., responses to actual incidents, assessment findings, changes in regulatory requirements, etc.).'
      domain: null
      category: null
      activity: ''
      question: ''
      tests: []
    - code: 'DCF-816'
      name: 'Physical Access Restrictions for Changes'
      description: '%s has defined, documented, approved, and enforced physical access restrictions associated with initiating changes to system components by authorized individuals.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-817'
      name: 'Cloud Security Configuration Monitoring'
      description: '%s has implemented automated tools to analyze the security configurations of its cloud environment(s) and continuously monitor for misconfigurations, vulnerabilities, and security risks (e.g., cloud security posture management software, configuration management tools, etc.).'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-818'
      name: 'Physical Access Devices Controlled'
      description: 'Physical access devices (e.g., keys, locks, combinations, card readers, etc.) are identified, controlled so that they are only available to authorized personnel, and managed through their lifecycle.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-819'
      name: 'Control of Removable Media'
      description: '%s restricts the use of removable media in the organization to only authorized media types with documented business justification. Use of all others type of media is prohibited (e.g., through policies and rules of behavior and/or technical mechanisms).'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-820'
      name: 'Password Minimum Change of Characters'
      description: "%s's systems enforce a minimum number of characters changed, as defined by the organization, when passwords are updated."
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-821'
      name: 'Removable System Media Ownership'
      description: '%s assigns identifiable owners (e.g., individuals, organizations, or projects) to removable system media used in the organization. %s prohibits the use of removable system media when such devices have no identifiable owner.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-822'
      name: 'Remote Access Management'
      description: '%s has established and documented usage restrictions, configuration requirements, and connection requirements for each type of allowable remote system access in the organization.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-823'
      name: 'Diagnostic Media Verification '
      description: "%s checks media containing diagnostic and test programs for malicious code before they are used in organizational systems (e.g., by checking the cryptographic hash or digital signatures, etc.) and retains supporting documentation. If media is found to contain malicious code, the incident is handled consistent with the company's incident handling policies and procedures."
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-824'
      name: 'Remote Connection Monitoring'
      description: '%s has implemented mechanisms to monitor remote access sessions on system components (e.g., servers, workstations, notebook computers, smart phones, and tablets).'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-825'
      name: 'Management of VoIP Technologies'
      description: '%s has defined and implemented usage restrictions and mechanisms to control and monitor the use of Voice over Internet Protocol (VoIP) technologies.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-826'
      name: 'Role-Based Security Training'
      description: '%s provides specialized information security training to all personnel with security-related duties, roles, and responsibilities are defined on specific security topics, skills, processes, or methodologies that must be followed for those individuals to perform their security responsibilities effectively. Training is provided prior to granting access to systems or sensitive data or performing assigned duties, when required by system changes or defined events, and at periodic intervals.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-827'
      name: 'Role-Based Security Training Program Updates'
      description: 'The role-based security training program and its contents are reviewed and updated at periodic intervals and after any events deemed significant by the organization (e.g., responses to actual incidents, assessment findings, changes in regulatory requirements, introduction of new tools, technologies, or processes, etc.).'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-828'
      name: 'Incident Handling Capability'
      description: '%s has implemented an incident-handling capability that is consistent with the incident response plan and includes preparation, detection and analysis, containment, eradication, recovery, and user response activities.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-829'
      name: 'Network Traffic Monitoring'
      description: '%s monitors and logs inbound and outbound communications traffic to detect unusual or unauthorized activities or events.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-830'
      name: 'Threat Modeling'
      description: '%s performs threat modeling, periodically or as needed as dictated by company policies and procedures, to identify use cases, threat agents, attack vectors and patterns, design patterns and risk mitigating factors.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-832'
      name: 'Review of Agreements and Contracts'
      description: 'Agreements or contracts with third parties (e.g., vendors, service providers, customers, partners, etc.) are reviewed prior to execution to validate they address all relevant requirements. Ad-hoc reviews are conducted for key existing agreements or contracts to validate ongoing appropriateness in accordance with company policies and procedures.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-833'
      name: 'Resilience Objectives'
      description: '%s has identified and documented resilience objectives (e.g., maximum tolerable downtimes (MTDS), recovery time objectives (RTOs), recovering point objectives (RPOs), etc.) for critical capabilities and services.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-834'
      name: 'Public Status Page'
      description: '%s has a publicly available status page to provide updates on incidents, recovery activities, progress in restoring operational capabilities, and resolution of incidents to interested parties.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-835'
      name: 'Asset Integrity Verification Before Restoration'
      description: '%s verifies the integrity of assets (e.g., backups, etc.) for indicators of compromise, file corruption, and other integrity issues before use in incident recovery and restoration activities. Evidence of the verification is retained.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-836'
      name: 'Periodic Physical Facility Reviews'
      description: '%s conducts periodic physical facility reviews to identify vulnerabilities and weaknesses in controls (e.g., physical access controls, environmental controls, geographic considerations, etc.) as dictated by company policies. Results are documented and incorporated into risk assessments and risk response strategies.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-837'
      name: 'Identity Verification for New Personnel'
      description: '%s verifies the identity of new personnel during the onboarding process using government-issued identity credentials (e.g., passport, visa, driver’s license, etc.). Records of the verification are retained.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-838'
      name: 'Endpoint Security Posture Validation'
      description: '%s has implemented mechanisms to validate the cyber health of endpoints/perform device posture checks before allowing them to access and use production resources.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-839'
      name: 'Verification of Software'
      description: '%s validates the authenticity and integrity of software artifacts prior to installation or use in accordance with company policies and procedures (e.g., cryptographic verification of digital signatures, etc.).'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-840'
      name: 'Data Inventory'
      description: '%s maintains an inventory of data types of interest (e.g., personally identifiable information (PII), protected health information (PHI), intellectual property, financial data, etc.). The inventory includes information about the data type, data owner, classification, location, and other attributes deemed relevant by the organization. Processes are in place to maintain the inventory current through periodic manual reviews or automated processes (e.g., data discovery tools).'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-841'
      name: 'Third-Party Assessments'
      description: '%s has implemented an external audit program where third-party audits of the effectiveness of the organization’s cybersecurity program and internal controls are conducted periodically by independent assessors.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-842'
      name: 'Cyber Risk Management Objectives'
      description: '%s has identified and documented measurable cybersecurity risk management objectives as part of strategic planning which are agreed-upon by relevant stakeholders (e.g., senior leadership, etc.)'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-843'
      name: 'Key Risk Indicators'
      description: '%s has defined key risk indicators (KRIs) to identify potential risks in the organization including likelihood and potential impact. The KRI metrics are monitored, reviewed, and reported to senior leadership periodically to inform strategic planning.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-844'
      name: 'Key Performance Indicators'
      description: '%s has defined cybersecurity key performance indicators (KPIs) to assess cybersecurity risk management performance. The KPI metrics are monitored, reviewed, and reported to senior leadership periodically to inform strategic planning.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-845'
      name: 'Cybersecurity Strategic Plan'
      description: 'Management has devised and documented a comprehensive a strategic plan for cybersecurity risk management that is reviewed and updated at least annually based on risk management strategy outcomes and after significant events.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-846'
      name: 'Strategic Risk Opportunities'
      description: "%s has identified, documented, estimated, evaluated, and prioritized strategic opportunities (i.e., positive risks) in the organization's risk evaluation (e.g., as part of cybersecurity risk assessments, strategic plans, and risk management plans)."
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-847'
      name: 'Vendor Risk Assessment'
      description: '%s performs a vendor risk assessment at least annually and when significant changes occur to the organization, to the vendor, or in other external forces (e.g., economic challenges, geopolitical instability, or technology advancements). Vendors are assigned a risk rating based on the results of the assessment and risk mitigation strategies are selected as needed for each vendor. Documentation is retained.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-848'
      name: 'Cybersecurity Resources'
      description: "Resources necessary for implementation of the cybersecurity program (e.g., department budget, hiring plan, etc.) are reviewed and approved at least annually by management to ensure resource allocation and investment is in line with the organization's cybersecurity and risk management strategy."
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-849'
      name: 'Exit Strategies for Supplier Relationships'
      description: '%s has defined and documented exit strategies for terminating or transitioning supplier relationships.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-867'
      name: 'Identity Assertions Managed '
      description: '%s has implemented standards-based approaches for identity assertions in all contexts including generation (e.g., data models, metadata), protection (e.g., digital signing, encryption), and verification (e.g., signature validation) of identity assertions.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-868'
      name: 'Cybersecurity Supply Chain Risk Management Program'
      description: '%s has defined and documented a cybersecurity supply chain risk management program, including a plan (with milestones), policies, and procedures that guide implementation and improvement of the program.'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-869'
      name: 'Integrated Cybersecurity Risk Management'
      description: 'Cybersecurity risk management activities and outcomes are included in enterprise risk management processes, and cybersecurity risks are aggregated and managed alongside other enterprise risks (e.g., documented in the enterprise risk register, etc.).'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-870'
      name: 'Integrated Cybersecurity Supply Chain Risk Management'
      description: 'Cybersecurity supply chain risk management activities and outcomes are included in enterprise risk management processes, and cybersecurity supply chain risks are aggregated and managed alongside other enterprise risks (e.g., documented in the enterprise risk register, etc.).'
      activity: ''
      question: ''
      tests: []
      domain: null
      category: null
    - code: 'DCF-871'
      name: 'Communication Lead'
      description: "%s has a designated individual responsible for implementing and executing the company's communication strategy, to include public and media communications, and incident reporting."
      domain: null
      category: null
      activity: ''
      question: ''
      tests: []
    - code: 'DCF-872'
      name: 'Incident Classification'
      description: '%s has a process in place to classify incidents in order to methodically determine their impact on the organization.'
      domain: null
      category: null
      activity: ''
      question: ''
      tests: []
    - code: 'DCF-873'
      name: 'Risk Management Framework Review'
      description: '%s conducts a review of its risk management framework periodically.'
      domain: null
      category: null
      activity: ''
      question: ''
      tests: []
    - code: 'DCF-874'
      name: 'Risk Management Framework Review Report'
      description: '%s submits a report of its risk management framework review to the appropriate authorities in the proper format, upon completion of its review.'
      domain: null
      category: null
      activity: ''
      question: ''
      tests: []
    - code: 'DCF-875'
      name: 'Threat-Lead Penetration Testing'
      description: 'An advanced and threat-lead penetration test (TLPT) on critical systems is conducted by an accredited, reputable, and capable independent third party.'
      domain: null
      category: null
      activity: ''
      question: ''
      tests: []
    - code: 'DCF-876'
      name: 'Project Management Policy'
      description: '%s has developed, documented, and implemented a policy that outlines requirements for project management to preserve the availability, authenticity, integrity, and confidentiality of data.'
      domain: null
      category: null
      activity: ''
      question: ''
      tests: []
    - code: 'DCF-877'
      name: 'AI Impact Assessment'
      description: '%s conducts an AI impact assessment of the potential consequences of development or use of AI systems, to individuals, groups, and societies, taking into account the technical and societal context of where the AI system is used. Assessment results are documented.'
      domain: null
      category: null
      activity: ''
      question: ''
      tests: []
    - code: 'DCF-878'
      name: 'AI Resources'
      description: 'Resources necessary for implementation of the AI program and its lifecycle maintenance (e.g., AI system components, data, tooling, human resources) are documented.'
      domain: null
      category: null
      activity: ''
      question: ''
      tests: []
    - code: 'DCF-879'
      name: 'AI Data Management'
      description: '%s has documented and implemented processes for managing AI-related data, to include the selection and acquisition of the data, quality standards, provenance, and preparation and use of the data.'
      domain: null
      category: null
      activity: ''
      question: ''
      tests: []
    - code: 'DCF-885'
      name: 'Active Discovery Tool Deployment'
      description: '%s has implemented and configured an active discovery tool to identify assets connected to the enterprise network; the tool is configured to run on at least a daily basis to ensure continuous asset discovery and visibility.'
      domain: 'Asset Management'
      category: 'Asset Inventory and Tracking'
      activity: ''
      question: ''
      tests: []
    - code: 'DCF-886'
      name: 'DHCP Logging and IP Address Management '
      description: '%s has enabled DHCP logging on all DHCP servers and/or utilizes an Internet Protocol (IP) Address Management (IPAM) tool to track and update the enterprise’s asset inventory. Logs generated by these systems are reviewed and used to update the asset inventory at least weekly, or more frequently as necessary, to ensure accurate asset visibility and compliance with security policies.'
      domain: 'Asset Management'
      category: 'Asset Inventory and Tracking'
      activity: ''
      question: ''
      tests: []
    - code: 'DCF-887'
      name: 'Passive Network Discovery for Asset Inventory Management'
      description: '%s utilizes a passive discovery tool to continuously identify assets connected to the enterprise network, scan results are reviewed and used to update the enterprise asset inventory at least weekly, or more frequently as necessary.'
      domain: 'Asset Management'
      category: 'Asset Inventory and Tracking'
      activity: ''
      question: ''
      tests: []
    - code: 'DCF-888'
      name: 'Record of Unauthorized Software'
      description: 'Any unauthorized software without documented exceptions shall either be removed from use on enterprise assets or receive a documented exception.'
      domain: 'Asset Management'
      category: 'Asset Inventory and Tracking'
      activity: ''
      question: ''
      tests: []
    - code: 'DCF-889'
      name: 'Automated Software Inventory Tool is Utilized'
      description: '%s utilize software inventory tools, when possible, throughout the enterprise to automate the discovery and documentation of installed software.'
      domain: 'Asset Management'
      category: 'Asset Inventory and Tracking'
      activity: ''
      question: ''
      tests: []
    - code: 'DCF-890'
      name: 'Authorized Libraries Allowlist '
      description: '%s uses technical controls to ensure that only authorized software libraries (e.g., .dll, .ocx, .so, etc.) are allowed to load into a system process. Allowlist shall be reassessed bi-annually, at minimum.'
      domain: 'Asset Management'
      category: 'Asset Inventory and Tracking'
      activity: ''
      question: ''
      tests: []
    - code: 'DCF-891'
      name: 'Authorized Scripts Allowlist'
      description: '%s uses technical controls (e.g., digital signatures, version control, etc.) to ensure that only authorized scripts (e.g., .ps1, .py, etc.) are allowed to execute. Allowlist shall be reassessed bi-annually, at minimum.'
      domain: 'Asset Management'
      category: 'Asset Inventory and Tracking'
      activity: ''
      question: ''
      tests: []
    - code: 'DCF-892'
      name: 'Trusted DNS Server Configuration'
      description: 'Configure trusted DNS servers on network infrastructure. Example implementations include configuring network devices to use enterprise-controlled DNS servers and/or reputable externally accessible DNS servers.'
      domain: 'Asset Management'
      category: 'Endpoint Security'
      activity: ''
      question: ''
      tests: []
    - code: 'DCF-893'
      name: 'Separate Enterprise Workspaces on Mobile End-User Devices'
      description: 'Where possible, %s enables separate enterprise workspaces on mobile end-user devices to separate enterprise applications and data from personal applications and data.'
      domain: 'Asset Management'
      category: 'Mobile Device Security'
      activity: ''
      question: ''
      tests: []
    - code: 'DCF-894'
      name: 'Service Account Inventory and Review'
      description: '%s has established and maintains an inventory of all service accounts. The inventory  includes the department owner, last review date, and purpose of each service account. %s performs service account reviews on a recurring schedule, at least quarterly, or more frequently as required, to ensure all active service accounts are authorized, necessary, and compliant with security and access management policies. Unauthorized or obsolete service accounts are remediated or removed in accordance with established deprovisioning procedures.'
      domain: 'Identity and Access Management'
      category: 'Identity Management'
      activity: ''
      question: ''
      tests: []
    - code: 'DCF-895'
      name: 'Annual Review of Authentication and Authorization Systems'
      description: '%s maintains an inventory of the enterprise’s authentication and authorization systems, including those hosted on-site or at a remote service provider. Inventory must be reviewed, at least, annually.'
      domain: 'Identity and Access Management'
      category: 'Access Control'
      activity: ''
      question: ''
      tests: []
    - code: 'DCF-896'
      name: 'Logging System, Log Management System, Threat Detection System'
      description: '%s uses a centralized system that collects and stores logs of system activity and sends alerts to personnel based on pre-configured rules. Access to logs is restricted to authorized personnel. , %s uses logging software that sends alerts to appropriate personnel. Corrective actions are performed, as necessary, in a timely manner., A threat detection system is in place to monitor web traffic and suspicious activity. When anomalous traffic activity is identified, alerts are automatically sent to personnel, investigated, and escalated through the incident management process, if necessary.'
      domain: 'Logging and Monitoring'
      category: 'Logging'
      activity: ''
      question: ''
      tests: []
    - code: 'DCF-897'
      name: 'Enterprise DNS Query Logging and Monitoring'
      description: '%s collects DNS query audit logs on enterprise assets, where appropriate and supported, to enhance security monitoring and threat detection.'
      domain: 'Logging and Monitoring'
      category: 'Operational Monitoring'
      activity: ''
      question: ''
      tests: []
    - code: 'DCF-898'
      name: 'Collection and Retention of URL Request Audit Logs'
      description: '%s collects and retains URL request audit logs on enterprise assets where feasible and supported by system capabilities.'
      domain: 'Logging and Monitoring'
      category: 'Logging'
      activity: ''
      question: ''
      tests: []
    - code: 'DCF-899'
      name: 'Collection of Command Line Audit Logs'
      description: '%s collects and retains command-line audit logs from administrative terminals, including but not limited to PowerShell, BASH, and remote administrative sessions.'
      domain: 'Logging and Monitoring'
      category: 'Logging'
      activity: ''
      question: ''
      tests: []
    - code: 'DCF-900'
      name: 'Service Provider Log Collection and Monitoring'
      description: '%s collects and retains service provider logs, where supported, to ensure visibility into authentication and authorization events, data creation and disposal activities, and user management changes.'
      domain: 'Logging and Monitoring'
      category: 'Operational Monitoring'
      activity: ''
      question: ''
      tests: []
    - code: 'DCF-901'
      name: 'Unauthorized Browser and Email Client Extension Restrictions'
      description: '%s restricts any unauthorized or unnecessary browser or email client plugins, extensions, and add-on applications.'
      domain: 'Network Security'
      category: 'Network Configurations'
      activity: ''
      question: ''
      tests: []
    - code: 'DCF-902'
      name: 'Anti-Exploitation Features Enabled'
      description: 'Where possible, %s has enabled anti-exploitation features on enterprise assets and software (e.g., Microsoft® Data Execution Prevention (DEP), Windows® Defender Exploit Guard (WDEG),  Apple® System Integrity Protection (SIP), Gatekeeper™, etc.)'
      domain: 'Vulnerability Management'
      category: 'Malware Protection'
      activity: ''
      question: ''
      tests: []
    - code: 'DCF-903'
      name: 'Centrally Manage Anti-Malware Software'
      description: '%s centrally manages enterprise anti-malware software.'
      domain: 'Vulnerability Management'
      category: 'Malware Protection'
      activity: ''
      question: ''
      tests: []
    - code: 'DCF-904'
      name: 'Monthly Security Alerting Threshold Tuning'
      description: '%s tune security event alerting thresholds, at least, on a monthly basis.'
      domain: 'Network Security'
      category: 'Network Configurations'
      activity: ''
      question: ''
      tests: []
    - code: 'DCF-905'
      name: 'Classify Service Providers'
      description: '%s classifies service providers based on data sensitivity, data volume, availability requirements, applicable regulations, inherent risk, and mitigated risk. Service provider classification is reviewed annually, or as needed.'
      domain: 'Third-Party Management'
      category: 'Third-Party Risk'
      activity: ''
      question: ''
      tests: []
    - code: 'DCF-910'
      name: 'List of Terminated Employees/Contractors'
      description: '%s maintains a list of terminated personnel with a termination checklist that includes the return of all previously issued software in the termination process; all corporate documents in the termination process; all equipment in the termination process; and, all other organizational assets such as mobile computing devices, credit cards, access cards, manuals, and information stored on electronic media in the termination process.'
      domain: 'Identity and Access Management'
      category: 'Access Control'
      activity: ''
      question: ''
      tests: []
    - code: 'DCF-911'
      name: 'Risk Assessment Conducted after Major Change'
      description: '%s conducts risk assessments after every major change in the environment.'
      domain: 'Risk Management'
      category: 'Risk Assessment'
      activity: ''
      question: ''
      tests: []
    - code: 'DCF-912'
      name: 'Cryptographic Modules'
      description: '%s employs cryptographic modules that are certified and that adhere to the minimum applicable standards when used to protect the confidentiality of information.'
      domain: 'Encryption'
      category: null
      activity: ''
      question: ''
      tests: []
    - code: 'DCF-913'
      name: 'Scan Unauthorized Mobile Devices'
      description: '%s conducts network monitoring scans to detect unauthorized mobile devices connected to the network.'
      domain: 'Network Security'
      category: null
      activity: ''
      question: ''
      tests: []
    - code: 'DCF-914'
      name: 'List of New Hires'
      description: '%s maintains a list of new hires (employees and contractors) for the past 12 months.'
      domain: 'People Operations'
      category: 'Personnel Security'
      activity: ''
      question: ''
      tests: []
    - code: 'DCF-915'
      name: 'List of Teleworkers'
      description: '%s maintains a list of teleworking personnel.'
      domain: 'People Operations'
      category: 'Personnel Security'
      activity: ''
      question: ''
      tests: []
    - code: 'DCF-916'
      name: 'Telework Agreements'
      description: '%s maintains signed teleworking agreements of personnel working at remote sites.'
      domain: 'People Operations'
      category: 'Personnel Security'
      activity: ''
      question: ''
      tests: []
    - code: 'DCF-917'
      name: 'List of In-Scope Facilities'
      description: '%s maintains a list of in-scope physical sites and facilities.'
      domain: 'Physical and Environmental Security'
      category: 'Physical Security '
      activity: ''
      question: ''
      tests: []
    - code: 'DCF-918'
      name: 'Disabled Wireless Access'
      description: '%s disables wireless access in the hardware configuration of devices that do not have an essential wireless business purpose.'
      domain: 'Network Security'
      category: null
      activity: ''
      question: ''
      tests: []
    - code: 'DCF-919'
      name: 'Electronic Commerce Service Agreements'
      description: '%s maintains documented agreements for e-commerce arrangements, including agreed terms of trading and details of authorization.'
      domain: 'Third-Party Management'
      category: 'Due Diligence'
      activity: ''
      question: ''
      tests: []
    - code: 'DCF-920'
      name: 'List of Personnel'
      description: '%s maintains a list of all personnel (employees and contractors) and third-party users.'
      domain: 'People Operations'
      category: null
      activity: ''
      question: ''
      tests: []
    - code: 'DCF-921'
      name: 'Senior Executive Training'
      description: '%s provides specialized information security training to executives with significant security responsibilities (e.g., system administrators). Training is provided prior to granting access to systems or sensitive data or performing assigned duties, when required by system changes or defined events, and at periodic intervals.'
      domain: 'People Operations'
      category: 'Training and Awareness'
      activity: ''
      question: ''
      tests: []
    - code: 'DCF-922'
      name: 'NDA Review'
      description: '%s reviews examine its requirements for confidentiality and non-disclosure agreements periodically according to policy, and when changes occur that influence these requirements. The review includes a review of applicable laws and regulations for the jurisdiction to which the agreements apply.'
      domain: 'People Operations'
      category: 'Screening'
      activity: ''
      question: ''
      tests: []
    - code: 'DCF-923'
      name: 'List of New Assets'
      description: '%s maintains a list of new assets and facilities. Management formally authorizes the use of all new assets prior to their operation, and the authorization is updated periodically. Documentation of management authorization is retained.'
      domain: 'Asset Management'
      category: 'Asset Inventory and Tracking'
      activity: ''
      question: ''
      tests: []
    - code: 'DCF-924'
      name: 'List of Open Source Software'
      description: '%s maintains a list of open source software in use, along with their licensing information and authority to use.'
      domain: 'Asset Management'
      category: 'Media Protection'
      activity: ''
      question: ''
      tests: []
    - code: 'DCF-925'
      name: 'Use of Open Source Software'
      description: '%s has established restrictions on the use of open source software in accordance with the secure configuration policies.'
      domain: 'Configuration Management'
      category: 'Change Management'
      activity: ''
      question: ''
      tests: []
    - code: 'DCF-926'
      name: 'Output Validation'
      description: '%s performs output validation during system development that includes plausibility checks, reconciliation control counts, sufficient information for a reader, procedures for responding to validation tests, responsibilities of personnel, and automated log of activities in the data output validation process.'
      domain: 'Technology Development and Acquisition'
      category: 'Secure Development'
      activity: ''
      question: ''
      tests: []
    - code: 'DCF-927'
      name: 'Supporting Evidence for Legal Action'
      description: '%s collects and retains evidence to support legal actions in accordance with the laws of the relevant jurisdiction.'
      domain: 'Governance'
      category: 'Legal'
      activity: ''
      question: ''
      tests: []
    - code: 'DCF-930'
      name: 'Firewall Rules'
      description: '%s ensures that an authorized person approves and documents firewall rules, showing the business need for a rule. The rules are reviewed periodically and rules that no longer serve a purpose are removed or disabled.'
      domain: 'Network Security'
      category: 'Network Configurations'
      activity: ''
      question: ''
      tests: []
    - code: 'DCF-935'
      name: 'Central Configuration Management'
      description: '%s manages configurations centrally.'
      domain: 'Configuration Management'
      category: 'Configurations'
      activity: ''
      question: ''
      tests: []
    - code: 'DCF-936'
      name: 'System and Component Integrity'
      description: '%s ensures the integrity of its systems and components through cryptographic means.'
      domain: 'Encryption'
      category: null
      activity: ''
      question: ''
      tests: []
    - code: 'DCF-937'
      name: 'Authentication of Non-user Accounts and Services'
      description: '%s ensures that non-user accounts and services are authenticated through appropriate and secure methods.'
      domain: 'Identity and Access Management'
      category: 'Authentication'
      activity: ''
      question: ''
      tests: []
    - code: 'DCF-938'
      name: 'Infrastructure as Code and Configuration Scanning'
      description: '%s performs Infrastructure as Code (IaC) and configuration scanning.'
      domain: 'Logging and Monitoring'
      category: 'Operational Monitoring'
      activity: ''
      question: ''
      tests: []
    - code: 'DCF-939'
      name: 'Redeployment of Immutable Resources'
      description: '%s executes changes through the redeployment of version controlled immutable resources, wherever possible.'
      domain: 'Configuration Management'
      category: 'Change Management'
      activity: ''
      question: ''
      tests: []
    - code: 'DCF-940'
      name: 'Vulnerability Patterns'
      description: '%s periodically reviews past incidents to determine any patterns of vulnerabilities.'
      domain: 'Vulnerability Management'
      category: 'Vulnerability Assessment'
      activity: ''
      question: ''
      tests: []
    - code: 'DCF-941'
      name: 'Immutable Infrastructure'
      description: '%s uses immutable infrastructure with strictly defined functionality and privileges by default.'
      domain: 'Configuration Management'
      category: 'Configurations'
      activity: ''
      question: ''
      tests: []
    - code: 'DCF-943'
      name: 'FedRAMP-authorized Services'
      description: '%s regularly confirms that services storing Federal information are all FedRAMP-authorized.'
      domain: 'Third-Party Management'
      category: 'Due Diligence'
      activity: ''
      question: ''
      tests: []
    - code: 'DCF-950'
      name: 'Intended Uses Transparency Disclosures'
      description: '%s maintains up-to-date intended uses transparency disclosures to define and communicate the purpose of its programs (e.g., privacy, AI). %s will notify appropriate stakeholders of updates to the disclosure, when appropriate.'
      domain: 'Privacy Management'
      category: 'Transparency'
      activity: ''
      question: ''
      tests: []

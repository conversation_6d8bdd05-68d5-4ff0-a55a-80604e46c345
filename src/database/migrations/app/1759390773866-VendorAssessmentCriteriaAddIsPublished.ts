import { MigrationInterface, QueryRunner } from "typeorm";

export class VendorAssessmentCriteriaAddIsPublished1759390773866 implements MigrationInterface {
    name = 'VendorAssessmentCriteriaAddIsPublished1759390773866'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`vendor_assessment_criteria\` ADD \`is_published\` tinyint(1) NOT NULL DEFAULT 0`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`vendor_assessment_criteria\` DROP COLUMN \`is_published\``);
    }

}

/* eslint-disable max-len */
import { NotImplementedException } from '@nestjs/common';
import { type DrataDataSource } from 'commons/classes/drata-data-source.class';
import { databaseConfig } from 'commons/configs/typeorm.config';
import { destroyDataSource } from 'commons/factories/data-source.manager';
import { createTenantConnectionFor } from 'commons/helpers/database-connection.helper';
import { pathResolve } from 'commons/helpers/file.helper';
import config from 'config';
import * as fs from 'fs';
import { isNil } from 'lodash';
import { PolloMessage } from 'pollo-logger/classes/pollo.message';
import { PolloLogger } from 'pollo-logger/pollo.logger';
import { MigrationInterface } from 'typeorm';
import { DrataQueryRunner } from 'commons/classes/drata-query-runner.class';

export class Rebase1716690674000 implements MigrationInterface {
    name = 'Rebase1716690674000';

    private logger = PolloLogger.logger(this.constructor.name);

    public async up(queryRunner: <PERSON>ata<PERSON>ueryRunner): Promise<void> {
        // get the options from the existing tenant connection
        const options: any = queryRunner.connection.options;

        // this needs to be unique per database
        const oneTimeDatabaseConnection = `${options.database}-${this.name}`;

        // default newTenantConnection for try
        let newTenantConnection: DrataDataSource | null = null;

        try {
            /**
             * establish a new connection JUST for this rebase migration
             * so we can set multipleStatements to TRUE
             * this is due to the MySQL dump Rebase.sql file we're reading in
             */
            newTenantConnection = await createTenantConnectionFor(oneTimeDatabaseConnection, {
                ...databaseConfig,
                host: options.host,
                port: options.port,
                database: options.database,
                multipleStatements: true,
                name: oneTimeDatabaseConnection,
            });

            // read in the SQL file
            const sql = fs.readFileSync(pathResolve(config.get('db.baseAppMigration')), 'utf8');

            // execute the SQL commands
            await newTenantConnection.query(sql);

            // Create typeorm_metadata table if it doesn't exist (may not be in Rebase.sql)
            await queryRunner.query(`
                CREATE TABLE IF NOT EXISTS \`typeorm_metadata\` (
                    \`type\` varchar(255) NOT NULL,
                    \`database\` varchar(255) DEFAULT NULL,
                    \`schema\` varchar(255) DEFAULT NULL,
                    \`table\` varchar(255) DEFAULT NULL,
                    \`name\` varchar(255) DEFAULT NULL,
                    \`value\` text
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            `);

            await queryRunner.query(
                `INSERT INTO \`typeorm_metadata\`(\`database\`, \`schema\`, \`table\`, \`type\`, \`name\`, \`value\`) VALUES (DEFAULT, (SELECT DATABASE()), ?, ?, ?, ?)`,
                [
                    'user_identity',
                    'GENERATED_COLUMN',
                    'deleted_at_timestamp',
                    "CASE WHEN deleted_at IS NULL THEN 0 ELSE TIMESTAMPDIFF(SECOND, '1970-01-01 00:00:00', deleted_at) END",
                ],
            );
        } catch (error) {
            this.logger.error(
                PolloMessage.msg(error.message)
                    .setContext(this.constructor.name)
                    .setSubContext(options.database)
                    .setError(error),
            );
        } finally {
            if (!isNil(newTenantConnection) && newTenantConnection.isInitialized) {
                await destroyDataSource(newTenantConnection.id);
            }
        }
    }

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    public async down(queryRunner: DrataQueryRunner): Promise<void> {
        throw new NotImplementedException();
    }
}

import { Body, Get, Param, Put, Query } from '@nestjs/common';
import {
    ApiBadRequestResponse,
    ApiNotFoundResponse,
    ApiOkResponse,
    ApiOperation,
    ApiParam,
    ApiTags,
} from '@nestjs/swagger';
import { User } from 'app/users/entities/user.entity';
import { PersonnelExpand } from 'app/users/personnel/enums/personnel-expand.enum';
import { PersonnelOrchestrationService } from 'app/users/personnel/orchestration/personnel/personnel-orchestration.service';
import { PersonnelCoreService } from 'app/users/personnel/services/personnel-core.service';
import { PersonnelCustomFieldsControlPlaneService } from 'app/users/personnel/services/personnel-custom-fields-control-plane-service';
import { PersonnelListRequestType } from 'app/users/personnel/types/personnel-list-request.type';
import { PersonnelModifyRequestType } from 'app/users/personnel/types/personnel-modify-request.type';
import { PersonnelRequestType } from 'app/users/personnel/types/personnel-request.type';
import { PersonnelWithCustomFields } from 'app/users/personnel/types/personnel-with-custom-fields.type';
import { Account } from 'auth/entities/account.entity';
import { Dto } from 'commons/decorators/dto.decorator';
import { GetAccount } from 'commons/decorators/get-account.decorator';
import { GetUser } from 'commons/decorators/get-user.decorator';
import { Permissions } from 'commons/decorators/permissions.decorator';
import { Area, ProductArea } from 'commons/decorators/product-area.decorator';
import { ApiResponse } from 'commons/enums/api-response.enum';
import { PermissionAction } from 'commons/enums/public-api-keys/permission-action.enum';
import { SortDirectionEnum } from 'commons/enums/sort-direction.enum';
import { SortTypeLimited } from 'commons/enums/sort-type-limited.enum';
import { ParseIntOrEmailPipe } from 'commons/pipes/parse-int-or-email-pipe';
import { CursorPage } from 'commons/types/cursor-page.type';
import config from 'config';
import { PublicV2Controller } from 'public-api-v2/controllers/public-v2.controller';
import { ExceptionResponsePublicV2Dto } from 'public-api-v2/dtos/exception-response.public-v2.dto';
import { PersonRequestPublicV2Dto } from 'public-api-v2/dtos/person-request.public-v2.dto';
import { PersonResponsePublicV2Dto } from 'public-api-v2/dtos/person-response.public-v2.dto';
import { PersonnelModifyRequestPublicV2Dto } from 'public-api-v2/dtos/personnel-modify-request.public-v2.dto';
import { PersonnelRequestPublicV2Dto } from 'public-api-v2/dtos/personnel-request.public-v2.dto';
import { PersonnelResponsePublicV2Dto } from 'public-api-v2/dtos/personnel-response.public-v2.dto';
import { PublicV2Route } from 'public-api-v2/public-v2.routes';

@ApiTags('Personnel')
@ProductArea(Area.PERSONNEL_PAGE)
export class PersonnelPublicV2Controller extends PublicV2Controller {
    constructor(
        private readonly personnelCoreService: PersonnelCoreService,
        private readonly personnelCustomFieldsControlPlaneService: PersonnelCustomFieldsControlPlaneService,
        private readonly personnelOrchestrationService: PersonnelOrchestrationService,
    ) {
        super();
    }

    @ApiOperation({
        summary: 'List Personnel',
        description: 'Get a paginated list of Personnel records.',
    })
    @ApiOkResponse({
        description: ApiResponse.OK,
        type: PersonnelResponsePublicV2Dto,
    })
    @ApiBadRequestResponse({
        description: ApiResponse.BAD_REQUEST,
        type: ExceptionResponsePublicV2Dto,
    })
    @Permissions(PermissionAction.PERSONNEL_GET)
    @Dto(PersonnelResponsePublicV2Dto)
    @Get(PublicV2Route.GET_PERSONNEL)
    listPersonnel(
        @Query() requestDto: PersonnelRequestPublicV2Dto,
    ): Promise<CursorPage<PersonnelWithCustomFields>> {
        const personnelRequest: PersonnelListRequestType = {
            cursor: requestDto.cursor || null,
            size: requestDto.size || (config.get('pagination.limit') as number),
            sort: requestDto.sort || SortTypeLimited.createdAt,
            sortDir: requestDto.sortDir || SortDirectionEnum.ASC,

            expand: requestDto.expand,
            employmentStatus: requestDto.employmentStatus,
        };

        return this.personnelCoreService.listPersonnelWithCursor(personnelRequest);
    }

    @ApiOperation({
        summary: 'Get Personnel',
        description: 'Get a single Personnel record.',
    })
    @ApiOkResponse({
        description: ApiResponse.OK,
        type: PersonResponsePublicV2Dto,
    })
    @ApiBadRequestResponse({
        description: ApiResponse.BAD_REQUEST,
        type: ExceptionResponsePublicV2Dto,
    })
    @ApiNotFoundResponse({
        description: ApiResponse.NOT_FOUND,
        type: ExceptionResponsePublicV2Dto,
    })
    @ApiParam({
        name: 'personnelId',
        description: "An integer Personnel ID or User's email address prefixed with `email:`",
        schema: {
            oneOf: [
                { type: 'number', description: 'Personnel ID' },
                {
                    type: 'string',
                    description:
                        "Email of the Personnel's User, prefixed with 'email:' e.g. `email:<EMAIL>`",
                },
            ],
        },
    })
    @Permissions(PermissionAction.PERSONNEL_GET_PERSONNEL_DETAILS)
    @Dto(PersonResponsePublicV2Dto)
    @Get(PublicV2Route.GET_PERSON)
    async getPerson(
        @Param('personnelId', ParseIntOrEmailPipe) personnelIdOrEmail: string | number,
        @Query() requestDto: PersonRequestPublicV2Dto,
    ): Promise<PersonnelWithCustomFields> {
        const personnelRequest: PersonnelRequestType = {
            expand: requestDto.expand,
        };

        const email = ParseIntOrEmailPipe.getEmail(personnelIdOrEmail);
        const result = email
            ? await this.personnelCoreService.getPersonnelByEmailV2(email, personnelRequest)
            : await this.personnelCoreService.getPersonnelByIdV2(
                  personnelIdOrEmail as number,
                  personnelRequest,
              );

        if (requestDto.expand?.includes(PersonnelExpand.complianceChecks)) {
            result.personnel.complianceChecks =
                await this.personnelOrchestrationService.getComplianceChecksWithExclusionsForPersonnel(
                    result.personnel,
                );
        }

        return result;
    }

    @ApiOperation({
        summary: 'Update Personnel',
        description:
            // eslint-disable-next-line max-len
            'Update a single Personnel record. Note: Once fields are manually updated, automatic updates from identity providers (IDP) and HRIS systems will be ignored for those fields. Use the resync endpoint to restore automatic updates.',
    })
    @ApiOkResponse({
        description: ApiResponse.OK,
        type: PersonResponsePublicV2Dto,
    })
    @ApiBadRequestResponse({
        description: ApiResponse.BAD_REQUEST,
        type: ExceptionResponsePublicV2Dto,
    })
    @ApiNotFoundResponse({
        description: ApiResponse.NOT_FOUND,
        type: ExceptionResponsePublicV2Dto,
    })
    @ApiParam({
        name: 'personnelId',
        description: "An integer Personnel ID or User's email address prefixed with `email:`",
        schema: {
            oneOf: [
                { type: 'number', description: 'Personnel ID' },
                {
                    type: 'string',
                    description:
                        "Email of the Personnel's User, prefixed with 'email:' e.g. `email:<EMAIL>`",
                },
            ],
        },
    })
    @Permissions(PermissionAction.PERSONNEL_PUT)
    @Dto(PersonResponsePublicV2Dto)
    @Put(PublicV2Route.PUT_PERSON)
    async modifyPerson(
        @GetAccount() account: Account,
        @GetUser() user: User,
        @Param('personnelId', ParseIntOrEmailPipe) personnelIdOrEmail: string | number,
        @Body() personnelModifyRequestPublicV2Dto: PersonnelModifyRequestPublicV2Dto,
    ): Promise<PersonnelWithCustomFields> {
        const personnelModifyRequestType: PersonnelModifyRequestType = {
            startedAt: personnelModifyRequestPublicV2Dto.startedAt,
            separatedAt: personnelModifyRequestPublicV2Dto.separatedAt,
            employmentStatus: personnelModifyRequestPublicV2Dto.employmentStatus,
            notHumanReason: personnelModifyRequestPublicV2Dto.notHumanReason,
        };

        const customFields = personnelModifyRequestPublicV2Dto.customFields;

        const email = ParseIntOrEmailPipe.getEmail(personnelIdOrEmail);
        const personnelId = email
            ? (await this.personnelCoreService.getPersonnelByEmailV2(email, {})).personnel.id
            : (personnelIdOrEmail as number);

        return this.personnelCustomFieldsControlPlaneService.modifyPersonnel(
            account,
            user,
            personnelId,
            personnelModifyRequestType,
            customFields,
        );
    }
}

import { Module } from '@nestjs/common';
import { AppModule } from 'app/app.module';
import { AssetsCoreModule } from 'app/assets/assets-core.module';
import { AssetsCustomFieldsControlPlaneModule } from 'app/assets/assets-custom-fields-control-plane.module';
import { AssetsOrchestrationModule } from 'app/assets/assets-orchestration.module';
import { BackgroundCheckModule } from 'app/background-check/background-check.module';
import { CloudStorageModule } from 'app/cloud-storage/cloud-storage.module';
import { CompaniesOrchestrationModule } from 'app/companies/companies-orchestration.module';
import { ConnectionsCoreModule } from 'app/companies/connections/connections-core.module';
import { WorkspacesCoreModule } from 'app/companies/workspaces-core.module';
import { ControlCustomFieldsControlPlaneModule } from 'app/control/control-custom-fields-control-plane.module';
import { ControlModule } from 'app/control/control.module';
import { CustomDataModule } from 'app/custom-data/custom-data.module';
import { DevicesCoreModule } from 'app/devices/devices-core.module';
import { DocumentLibraryOrchestrationModule } from 'app/document-library/document-library-orchestration.module';
import { GlobalNoteModule } from 'app/global-note/global-note.module';
import { ControlsOrchestrationModule } from 'app/grc/controls-orchestration.module';
import { GrcModule } from 'app/grc/grc.module';
import { MonitorsCoreModule } from 'app/monitors/monitors-core.module';
import { NotesOrchestrationModule } from 'app/notes/notes-orchestration.module';
import { QuestionnairesCoreModule } from 'app/questionnaires/questionnaires-core.module';
import { RiskCoreModule } from 'app/risk-management/risk-core.module';
import { RiskManagementModule } from 'app/risk-management/risk-management.module';
import { RiskOrchestrationModule } from 'app/risk-management/risk-orchestration.module';
import { ComplianceChecksOrchestrationModule } from 'app/users/compliance-checks-orchestration.module';
import { PersonnelCoreModule } from 'app/users/personnel-core.module';
import { PersonnelOrchestrationModule } from 'app/users/personnel/orchestration/personnel/personnel-orchestration.module';
import { PersonnelCustomFieldsControlPlaneModule } from 'app/users/personnel/personnel-custom-fields-control-plane-module';
import { UsersCoreModule } from 'app/users/users-core.module';
import { UserPoliciesCoreModule } from 'app/users/users-policies-core.module';
import { UsersPoliciesOrchestrationModule } from 'app/users/users-policies-orchestration.module';
import { UsersModule } from 'app/users/users.module';
import { VendorsCoreModule } from 'app/users/vendors-core.module';
import { VendorsOrchestrationModule } from 'app/users/vendors-orchestration.module';
import { VendorsCustomFieldsControlPlaneModule } from 'app/vendors/vendors-custom-fields-control-plane.module';
import { ModuleType, ModuleTypes } from 'commons/decorators/module-type.decorator';
import { AssetsPublicV2Controller } from 'public-api-v2/controllers/assets.public-v2.controller';
import { BackgroundChecksPublicV2Controller } from 'public-api-v2/controllers/background-checks.public-v2.controller';
import { CompaniesPublicV2Controller } from 'public-api-v2/controllers/companies.public-v2.controller';
import { ControlNotesPublicV2Controller } from 'public-api-v2/controllers/control-notes.public-v2.controller';
import { ControlOwnersPublicV2Controller } from 'public-api-v2/controllers/control-owners.public-v2.controller';
import { ControlsPublicV2Controller } from 'public-api-v2/controllers/controls.public-v2.controller';
import { CustomConnectionsPublicV2Controller } from 'public-api-v2/controllers/custom-connections.public-v2.controller';
import { DeviceDocumentsPublicV2Controller } from 'public-api-v2/controllers/device-documents.public-v2.controller';
import { DevicesPublicV2Controller } from 'public-api-v2/controllers/devices-public-v2.controller';
import { EventsPublicV2Controller } from 'public-api-v2/controllers/events.public-v2.controller';
import { EvidenceLibraryPublicV2Controller } from 'public-api-v2/controllers/evidence-library.public-v2.controller';
import { MonitorsPublicV2Controller } from 'public-api-v2/controllers/monitors.public-v2.controller';
import { PersonnelPublicV2Controller } from 'public-api-v2/controllers/personnel.public-v2.controller';
import { RiskDocumentsPublicV2Controller } from 'public-api-v2/controllers/risk-documents.public-v2.controller';
import { RiskManagementPublicV2Controller } from 'public-api-v2/controllers/risk-management.public-v2.controller';
import { RiskNotesPublicV2Controller } from 'public-api-v2/controllers/risk-notes.public-v2.controller';
import { RolesPublicV2Controller } from 'public-api-v2/controllers/roles.public-v2.controller';
import { UserDocumentsPublicV2Controller } from 'public-api-v2/controllers/user-documents.public-v2.controller';
import { UsersPoliciesPublicV2Controller } from 'public-api-v2/controllers/user-policies.public-v2.controller';
import { UsersPublicV2Controller } from 'public-api-v2/controllers/users.public-v2.controller';
import { VendorDocumentsPublicV2Controller } from 'public-api-v2/controllers/vendor-documents.public-v2.controller';
import { VendorSecurityReviewsPublicV2Controller } from 'public-api-v2/controllers/vendor-security-reviews.public-v2.controller';
import { VendorsPublicV2Controller } from 'public-api-v2/controllers/vendors.public-v2.controller';
import { WorkspacesPublicV2Controller } from 'public-api-v2/controllers/workspaces-public-v2.controller';
import { TenancyModule } from 'tenancy/tenancy.module';

@ModuleType(ModuleTypes.BASE)
@Module({
    controllers: [
        AssetsPublicV2Controller,
        BackgroundChecksPublicV2Controller,
        CompaniesPublicV2Controller,
        ControlNotesPublicV2Controller,
        ControlOwnersPublicV2Controller,
        ControlsPublicV2Controller,
        CustomConnectionsPublicV2Controller,
        DeviceDocumentsPublicV2Controller,
        DevicesPublicV2Controller,
        EventsPublicV2Controller,
        EvidenceLibraryPublicV2Controller,
        PersonnelPublicV2Controller,
        RiskDocumentsPublicV2Controller,
        RiskNotesPublicV2Controller,
        RiskManagementPublicV2Controller,
        RolesPublicV2Controller,
        UserDocumentsPublicV2Controller,
        UsersPoliciesPublicV2Controller,
        UsersPublicV2Controller,
        MonitorsPublicV2Controller,
        VendorDocumentsPublicV2Controller,
        VendorsPublicV2Controller,
        WorkspacesPublicV2Controller,
        VendorSecurityReviewsPublicV2Controller,
    ],
    imports: [
        AssetsCoreModule,
        AssetsCustomFieldsControlPlaneModule,
        AssetsOrchestrationModule,
        CloudStorageModule,
        BackgroundCheckModule,
        CompaniesOrchestrationModule,
        ComplianceChecksOrchestrationModule,
        ConnectionsCoreModule,
        ControlCustomFieldsControlPlaneModule,
        ControlModule,
        ControlsOrchestrationModule,
        DevicesCoreModule,
        DocumentLibraryOrchestrationModule,
        CustomDataModule,
        GrcModule,
        MonitorsCoreModule,
        NotesOrchestrationModule,
        PersonnelCoreModule,
        PersonnelOrchestrationModule,
        //If this import is removed - beacon fails. Need to research this.
        TenancyModule,
        PersonnelCustomFieldsControlPlaneModule,
        GlobalNoteModule,
        QuestionnairesCoreModule,
        // eslint-disable-next-line local-rules/restrict-commerce-module-imports
        RiskCoreModule,
        RiskManagementModule,
        RiskOrchestrationModule,
        UsersModule,
        UserPoliciesCoreModule,
        UsersCoreModule,
        UsersPoliciesOrchestrationModule,
        VendorsCustomFieldsControlPlaneModule,
        VendorsCoreModule,
        VendorsOrchestrationModule,
        WorkspacesCoreModule,
    ],
})
export class AppV2Module extends AppModule {}

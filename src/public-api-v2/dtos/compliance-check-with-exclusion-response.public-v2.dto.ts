import { ApiProperty } from '@nestjs/swagger';
import { ComplianceCheckWithExclusion } from 'app/users/personnel/types/compliance-check-with-exclusion.type';
import { IsOptional } from 'class-validator';
import { ComplianceCheckExclusionResponsePublicV2Dto } from 'public-api-v2/dtos/compliance-check-exclusion-response.public-v2.dto';
import { ComplianceCheckResponsePublicV2Dto } from 'public-api-v2/dtos/compliance-check-response.public-v2.dto';

export class ComplianceCheckWithExclusionResponsePublicV2Dto extends ComplianceCheckResponsePublicV2Dto {
    @IsOptional()
    @ApiProperty({
        required: false,
        nullable: true,
        type: ComplianceCheckExclusionResponsePublicV2Dto,
        description:
            'Exclusion details for this compliance check, only present when status is EXCLUDED',
    })
    exclusion: ComplianceCheckExclusionResponsePublicV2Dto | null;

    build(
        complianceCheckWithExclusion: ComplianceCheckWithExclusion,
    ): ComplianceCheckWithExclusionResponsePublicV2Dto {
        const { exclusion } = complianceCheckWithExclusion;
        super.build(complianceCheckWithExclusion);
        this.exclusion = exclusion
            ? new ComplianceCheckExclusionResponsePublicV2Dto().build(exclusion)
            : null;

        return this.send();
    }
}

import { EmploymentStatus } from '@drata/enums';
import { ApiProperty } from '@nestjs/swagger';
import { PersonnelWithCustomFields } from 'app/users/personnel/types/personnel-with-custom-fields.type';
import { IsOptional } from 'class-validator';
import { ResponsePublicDto } from 'commons/dtos/public/v1/response.public.dto';
import { getCustomFieldsFeatureRequirement } from 'commons/helpers/feature-requirement-messages.helper';
import { ComplianceCheckWithExclusionResponsePublicV2Dto } from 'public-api-v2/dtos/compliance-check-with-exclusion-response.public-v2.dto';
import { CustomFieldResponsePublicV2Dto } from 'public-api-v2/dtos/custom-field-response.public-v2.dto';
import { UserCompactResponsePublicV2Dto } from 'public-api-v2/dtos/user-compact-response.public-v2';

export class PersonResponsePublicV2Dto extends ResponsePublicDto {
    @ApiProperty({
        type: 'number',
        example: 1,
        description: 'Personnel ID',
    })
    id: number;

    @ApiProperty({
        type: 'number',
        example: 1,
        description: 'User ID associated with this Personnel',
    })
    userId: number;

    @ApiProperty({
        required: false,
        type: UserCompactResponsePublicV2Dto,
        description: 'The User associated with this Personnel, only returned when `expand[]=user` is passed.',
    })
    user?: UserCompactResponsePublicV2Dto;

    @ApiProperty({
        type: 'string',
        enum: EmploymentStatus,
        enumName: 'EmploymentStatusEnum',
        description: 'The employment status of the Personnel',
    })
    employmentStatus: string;

    @IsOptional()
    @ApiProperty({
        required: false,
        nullable: true,
        type: 'string',
        example: 'This is not a real personnel, but a placeholder for anyone in charge of X',
        description:
            'Indicates why the employment status of this Personnel is marked as `OUT_OF_SCOPE`.',
    })
    notHumanReason: string | null;

    @IsOptional()
    @ApiProperty({
        required: false,
        nullable: true,
        description:
            // eslint-disable-next-line max-len
            'The User who marked the employment status as `OUT_OF_SCOPE` and provided the `notHumanReason`, only returned when `expand[]=reasonProvider` is passed.',
        type: UserCompactResponsePublicV2Dto,
    })
    reasonProvider?: UserCompactResponsePublicV2Dto | null;

    @ApiProperty({
        required: false,
        isArray: true,
        type: ComplianceCheckWithExclusionResponsePublicV2Dto,
        description: 'Compliance checks for this Personnel, only returned when `expand[]=complianceChecks` is passed.',
    })
    complianceChecks?: ComplianceCheckWithExclusionResponsePublicV2Dto[];

    @ApiProperty({
        nullable: true,
        type: 'string',
        format: 'date-time',
        example: '2023-01-01T00:00:00.000Z',
        description: 'The date when this Personnel was onboarded onto the company system',
    })
    startedAt: Date | null;

    @IsOptional()
    @ApiProperty({
        required: false,
        nullable: true,
        type: 'string',
        format: 'date-time',
        example: '2023-12-31T00:00:00.000Z',
        description: 'The date when this Personnel was separated from the company system',
    })
    separatedAt: Date | null;

    @ApiProperty({
        nullable: true,
        format: 'date-time',
        type: 'string',
        description:
            // eslint-disable-next-line max-len
            'The date when this Personnel was manually updated. If this value is set, changes in the IDP or HRIS will not be reflected in the employment status.',
    })
    statusUpdatedAt: Date | null;

    @ApiProperty({
        format: 'date-time',
        type: 'string',
        example: '2023-01-01T00:00:00.000Z',
        description: 'Personnel created date timestamp',
    })
    createdAt: Date;

    @ApiProperty({
        format: 'date-time',
        type: 'string',
        example: '2023-01-01T00:00:00.000Z',
        description: 'Personnel updated date timestamp',
    })
    updatedAt: Date;

    @ApiProperty({
        isArray: true,
        type: CustomFieldResponsePublicV2Dto,
        required: false,
        description: `Custom Fields. Only returned when \`expand[]=customFields\` is passed. ${getCustomFieldsFeatureRequirement()}`,
    })
    customFields?: CustomFieldResponsePublicV2Dto[];

    build(personnelWithCustomFields: PersonnelWithCustomFields): PersonResponsePublicV2Dto {
        const { personnel, customFields } = personnelWithCustomFields;

        this.id = personnel.id;
        this.employmentStatus = EmploymentStatus[personnel.employmentStatus];

        this.userId = personnel.fkUserId;
        // The schema says user could be null but in practice it cannot be. So
        // the only reason it would be nil is they didn't choose to expand.
        if (personnel.user) {
            this.user = new UserCompactResponsePublicV2Dto().build(personnel.user);
        }

        this.notHumanReason = personnel.notHumanReason;
        if (personnel.reasonProvider !== undefined) {
            this.reasonProvider = personnel.reasonProvider
                ? new UserCompactResponsePublicV2Dto().build(personnel.reasonProvider)
                : null;
        }

        if (personnel.complianceChecks !== undefined) {
            this.complianceChecks = this.list(
                personnel.complianceChecks,
                ComplianceCheckWithExclusionResponsePublicV2Dto,
            );
        }

        if (customFields !== undefined) {
            this.customFields = this.list(customFields, CustomFieldResponsePublicV2Dto);
        }

        this.startedAt =
            personnel.startedAt ?? (personnel.startDate ? new Date(personnel.startDate) : null);
        this.separatedAt =
            personnel.separatedAt ??
            (personnel.separationDate ? new Date(personnel.separationDate) : null);
        this.statusUpdatedAt = personnel.statusUpdatedAt ?? null;
        this.createdAt = personnel.createdAt;
        this.updatedAt = personnel.updatedAt;

        return this;
    }
}

import { ApiProperty } from '@nestjs/swagger';
import { ComplianceCheckExclusion } from 'app/compliance-check-exclusions/entities/compliance-check-exclusion.entity';
import { ResponsePublicDto } from 'commons/dtos/public/v1/response.public.dto';
import config from 'config';

export class ComplianceCheckExclusionResponsePublicV2Dto extends ResponsePublicDto {
    @ApiProperty({
        type: 'number',
        example: 1,
        description: 'Compliance Check Exclusion ID',
    })
    id: number;

    @ApiProperty({
        type: 'string',
        description: 'Reason for the Exclusion',
        example: 'Employee is on extended leave',
    })
    reason: string;

    @ApiProperty({
        type: 'number',
        description: 'ID of the user who created the Exclusion',
        example: 1,
    })
    createdById: number;

    @ApiProperty({
        type: 'string',
        description: 'Email of the user who created the Exclusion',
        example: '<EMAIL>',
    })
    createdByEmail: string;

    @ApiProperty({
        type: 'string',
        format: 'date-time',
        description: 'Creation timestamp of the Exclusion',
        example: config.get('swagger.examples.dateTime'),
    })
    createdAt: Date;

    @ApiProperty({
        type: 'string',
        format: 'date-time',
        description: 'Start date of the Exclusion',
        example: config.get('swagger.examples.dateTime'),
    })
    startDate: Date;

    @ApiProperty({
        type: 'string',
        format: 'date-time',
        nullable: true,
        description: 'End date of the Exclusion',
        example: config.get('swagger.examples.dateTime'),
    })
    endDate: Date | null;

    build(
        complianceCheckExclusion: ComplianceCheckExclusion,
    ): ComplianceCheckExclusionResponsePublicV2Dto {
        const { id, reason, startDate, endDate, createdAt, createdBy } = complianceCheckExclusion;

        this.id = id;
        this.reason = reason;
        this.createdById = createdBy.id;
        this.createdByEmail = createdBy.email;
        this.createdAt = createdAt;
        this.startDate = startDate;
        this.endDate = endDate ?? null;

        return this.send();
    }
}

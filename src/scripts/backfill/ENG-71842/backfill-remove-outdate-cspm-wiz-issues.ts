import { ConnectionState } from '@drata/enums';
import { HttpService } from '@nestjs/axios';
import { WizDataResponse } from 'app/apis/classes/wiz/wiz-issues-data-response.class';
import { WizQueryBuilder } from 'app/apis/classes/wiz/wiz-query.builder';
import { WizSdk } from 'app/apis/services/wiz/wiz.sdk';
import { CspmIssue } from 'app/cloud-secure-posture-management/entities/cspm-issue.entity';
import { CspmIssueRepository } from 'app/cloud-secure-posture-management/repository/cspm-issue.repository';
import { CspmTest } from 'app/cloud-secure-posture-management/types/cspm-test.type';
import { WizIssuesDataResponse } from 'app/cloud-secure-posture-management/types/wiz-issues-data-response.type';
import { ConnectionEntity } from 'app/companies/connections/entities/connection.entity';
import { Account } from 'auth/entities/account.entity';
import { program } from 'commander';
import { RestrictedTenantBackfill } from 'commons/backfill/restricted-tenant-backfill';
import { DrataDataSource } from 'commons/classes/drata-data-source.class';
import { ClientType } from 'commons/enums/auth/client-type.enum';
import { ProviderType } from 'commons/enums/auth/provider-type.enum';
import { stringProcess } from 'commons/helpers/cli.helper';
import config from 'config';
import { getCustomRepository } from 'database/typeorm/typeorm.extensions.helper';
import { chunk, isEmpty } from 'lodash';
import { In, IsNull, Not } from 'typeorm';

class BackfillRemoveOutdatedCspmWizIssues extends RestrictedTenantBackfill {
    protected setScriptOptions(): void {
        program
            .option(
                '-i, --account-ids [account-ids...]',
                'One or more account ids to limit the accounts to process (space-delimited)',
                stringProcess,
                [],
            )
            .option(
                '-d, --account-domains [account-domains...]',
                'One or more account domains to limit the tests to run for (space-delimited)',
                stringProcess,
                [],
            )
            .option('--dry-run', 'Run the backfill without making any changes to the databases')
            .option(
                '-c, --connection-ids [connection-ids...]',
                'One or more connection ids to limit the tests to run for (space-delimited)',
                stringProcess,
                [],
            )
            .option(
                '-ra --run-all [boolean]',
                'Override the account limit and run for all',
                (value: string) => {
                    if (value?.toLocaleLowerCase() === 'true') {
                        return true;
                    }

                    if (!value || value?.toLocaleLowerCase() === 'false') {
                        return false;
                    }

                    console.error('Run All flag is neither true or false:', value);
                    program.help();
                },
                false,
            )
            .parse(process.argv);
    }

    protected setDryRun(): void {
        const { dryRun } = program.opts();
        this.isDryRun = dryRun;
        if (this.isDryRun) {
            this.logInfo({ msg: 'DRY RUN MODE: No changes will be made' });
        }
    }

    protected async runTenantBackfill(
        account: Account,
        tenantConnection: DrataDataSource,
    ): Promise<void> {
        const connectionRepository = tenantConnection.getRepository(ConnectionEntity);

        const { connectionIds } = program.opts();

        if (isEmpty(connectionIds)) {
            this.logError({
                msg: 'Connection IDs parameter is required',
                account,
                error: new Error("-c, --connection-ids [connection-ids...] can't be empty"),
            });
            process.exit(1);
        }

        const cspmConnections = await connectionRepository.find({
            where: {
                providerType: ProviderType.CSPM,
                clientType: ClientType.WIZ,
                id: In(connectionIds),
                connectedAt: Not(IsNull()),
                state: ConnectionState.ACTIVE,
            },
        });

        if (isEmpty(cspmConnections)) {
            this.logWarn({
                msg: `No cspm connections found`,
                account,
            });

            return;
        }

        const cspmIssueRepository = getCustomRepository(CspmIssueRepository, tenantConnection);

        for await (const connection of cspmConnections) {
            this.logInfo({
                msg: `Found cspm connection ${connection.id}`,
                account,
            });

            const metadata = connection.getMetadata();
            const { cspmTests } = metadata;

            if (!metadata.clientKey || !metadata.clientSecret || !metadata.serverUrl) {
                this.logError({
                    msg: `Connection ${connection.id} missing required Wiz credentials`,
                    account,
                    error: new Error(
                        `Connection ${connection.id} missing required Wiz credentials`,
                    ),
                });
                continue;
            }

            for await (const cspmTest of cspmTests) {
                const savedIssues = await cspmIssueRepository.listCspmIssuesExternalIds(
                    connection,
                    cspmTest as CspmTest,
                );

                this.logInfo({
                    msg: `Found ${savedIssues.length} saved issues for risk type ${cspmTest.riskType}`,
                    account,
                });

                const issuesChunks = chunk(savedIssues, config.get('cspm.api.maxRiskTypeResults'));

                for await (const issueChunk of issuesChunks) {
                    const chunkIds = issueChunk.map(issue => issue.externalId);
                    const criteria = {
                        ...cspmTest,
                        criteria: {
                            ...cspmTest.criteria,
                            issuesIds: chunkIds,
                        },
                    } as CspmTest;
                    try {
                        // Fetch issues directly from Wiz GraphQL API
                        const testIssuesResult = await this.fetchWizIssues(connection, criteria);
                        const testIssues = testIssuesResult.data;

                        // Always check for differences, regardless of count
                        const currentIssueIds = new Set(testIssues.map(issue => issue.externalId));
                        const chunkIssuesToRemove = issueChunk.filter(
                            savedIssue => !currentIssueIds.has(savedIssue.externalId),
                        );

                        // Compare testIssues with savedIssues and remove issues that are no longer present
                        this.logInfo({
                            msg: `Fetched ${testIssues.length} current issues from provider for comparison`,
                            account,
                        });

                        if (chunkIssuesToRemove.length > 0) {
                            if (this.isDryRun) {
                                this.logInfo({
                                    msg: `DRY RUN: Would soft delete ${chunkIssuesToRemove.length} issues`,
                                    account,
                                });
                            } else {
                                this.logInfo({
                                    msg: `Soft deleting ${chunkIssuesToRemove.length} issues`,
                                    account,
                                });
                                await cspmIssueRepository.softDelete({
                                    connection: {
                                        id: connection.id,
                                    },
                                    externalId: In(
                                        chunkIssuesToRemove.map(issue => issue.externalId),
                                    ),
                                });
                            }
                        }
                    } catch (error) {
                        this.logError({
                            msg: `Failed to fetch issues from Wiz API for chunk`,
                            account,
                            error,
                            metadata: {
                                chunkSize: issueChunk.length,
                                riskType: cspmTest.riskType,
                                connectionId: connection.id,
                                chunkIds: chunkIds.slice(0, 10), // First few IDs for debugging
                            },
                        });
                        continue; // Skip this chunk and continue with the next one
                    }
                }
            }
        }
    }

    /**
     * Fetch issues directly from Wiz GraphQL API without bootstrapping NestJS
     */
    private async fetchWizIssues(
        connection: ConnectionEntity,
        criteria: CspmTest,
    ): Promise<{ data: CspmIssue[]; total: number }> {
        const httpService = new HttpService();
        const wizSdk = new WizSdk(httpService, connection.getMetadata());
        const query = WizQueryBuilder.buildByCriteria(criteria);
        const response = await wizSdk.getGraphQLResponse<WizIssuesDataResponse>(query);
        const wizDataResponse = new WizDataResponse(response, criteria.riskType);
        return wizDataResponse.getTestResult();
    }
}

void new BackfillRemoveOutdatedCspmWizIssues().runTenantsBackfill();

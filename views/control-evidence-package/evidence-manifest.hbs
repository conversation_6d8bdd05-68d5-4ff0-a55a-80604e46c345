<html lang='en'>
    <head>
        <meta charset='UTF-8' />
        <meta name='viewport' content='width=device-width, initial-scale=1.0' />
        <title>{{title}}</title>
        <style>
            body {
                font-family: Arial, sans-serif;
                margin: 20px;
                line-height: 1.6;
            }
            h1 {
                color: #333;
                border-bottom: 2px solid #007acc;
                padding-bottom: 10px;
            }
            table {
                width: 100%;
                border-collapse: collapse;
                margin-top: 20px;
            }
            th,
            td {
                padding: 12px;
                text-align: left;
                border-bottom: 1px solid #ddd;
            }
            th {
                background-color: #f8f9fa;
                font-weight: bold;
                color: #333;
            }
            tr:hover {
                background-color: #f5f5f5;
            }
            code {
                background-color: #f1f1f1;
                padding: 2px 4px;
                border-radius: 3px;
                font-family: 'Courier New', monospace;
            }
            a {
                color: #007acc;
                text-decoration: none;
            }
            a:hover {
                text-decoration: underline;
            }
            .description {
                color: #666;
                margin-bottom: 20px;
            }
            .controls {
                margin-bottom: 20px;
                display: flex;
                justify-content: space-between;
                align-items: center;
                flex-wrap: wrap;
                gap: 10px;
            }
            .search-container {
                display: flex;
                align-items: center;
                gap: 10px;
            }
            .search-input {
                padding: 8px 12px;
                border: 1px solid #ddd;
                border-radius: 4px;
                font-size: 14px;
                width: 250px;
            }
            .search-input:focus {
                outline: none;
                border-color: #007acc;
                box-shadow: 0 0 0 2px rgba(0, 122, 204, 0.2);
            }
            .pagination {
                display: flex;
                align-items: center;
                gap: 10px;
            }
            .pagination button {
                padding: 6px 12px;
                border: 1px solid #ddd;
                background: white;
                cursor: pointer;
                border-radius: 4px;
                font-size: 14px;
            }
            .pagination button:hover:not(:disabled) {
                background-color: #f5f5f5;
            }
            .pagination button:disabled {
                opacity: 0.5;
                cursor: not-allowed;
            }
            .pagination .current-page {
                background-color: #007acc;
                color: white;
                border-color: #007acc;
            }
            .pagination-info {
                font-size: 14px;
                color: #666;
            }
            .no-results {
                text-align: center;
                padding: 40px;
                color: #666;
                font-style: italic;
            }
        </style>
    </head>
    <body>
        <h1>{{title}}</h1>
        <p class='description'>
            This manifest shows the mapping between abbreviated filenames and their original paths.
        </p>

        <div class='controls'>
            <div class='search-container'>
                <label for='evidence-search'>Search Evidence:</label>
                <input
                    type='text'
                    id='evidence-search'
                    class='search-input'
                    placeholder='Type to search evidence...'
                    autocomplete='off'
                />
            </div>
            <div class='pagination'>
                <button id='prev-btn' onclick='changePage(-1)'>Previous</button>
                <span class='pagination-info'>
                    Page
                    <span id='current-page'>1</span>
                    of
                    <span id='total-pages'>1</span>
                </span>
                <button id='next-btn' onclick='changePage(1)'>Next</button>
            </div>
        </div>

        <div class='pagination-info' style='margin-bottom: 10px;'>
            Showing
            <span id='showing-start'>0</span>-<span id='showing-end'>0</span>
            of
            <span id='total-records'>0</span>
            records
        </div>

        <table id='evidence-table'>
            <thead>
                <tr>
                    <th>Evidence</th>
                    <th>Original Path</th>
                    <th>Abbreviated Filename (/Evidence)</th>
                </tr>
            </thead>
            <tbody id='table-body'>
                <!-- Table rows will be populated by JavaScript -->
            </tbody>
        </table>

        <div id='no-results' class='no-results' style='display: none;'>
            No evidence found matching your search criteria.
        </div>

        <script>
            // Store all data for filtering and paginationconst allData = [
            {{#each manifestEntries}}
                { controlName: "{{controlName}}", displayPath: "{{displayPath}}", abbreviatedPath: "{{abbreviatedPath}}",
                abbreviatedFilename: "{{abbreviatedFilename}}" }{{#unless @last}},{{/unless}}
            {{/each}}
            ]; let filteredData = [...allData]; let currentPage = 1; const recordsPerPage = 10;
            //Initialize the table function initializeTable() { updateDisplay();
            setupEventListeners(); } // Set up event listeners function setupEventListeners() {
            const searchInput = document.getElementById('evidence-search');
            searchInput.addEventListener('input', function() { filterData(this.value); }); }
            //Filter data based on search term function filterData(searchTerm) { const term =
            searchTerm.toLowerCase().trim(); if (term === '') { filteredData = [...allData]; } else
            { filteredData = allData.filter(item => item.controlName.toLowerCase().includes(term) ||
            item.displayPath.toLowerCase().includes(term) ||
            item.abbreviatedFilename.toLowerCase().includes(term) ); } currentPage = 1; // Reset to
            first page when filtering updateDisplay(); } // Change page function
            changePage(direction) { const totalPages = Math.ceil(filteredData.length /
            recordsPerPage); const newPage = currentPage + direction; if (newPage >= 1 && newPage <=
            totalPages) { currentPage = newPage; updateDisplay(); } } // Update the table display
            function updateDisplay() { const totalRecords = filteredData.length; const totalPages =
            Math.ceil(totalRecords / recordsPerPage); const startIndex = (currentPage - 1) *
            recordsPerPage; const endIndex = Math.min(startIndex + recordsPerPage, totalRecords);
            const currentPageData = filteredData.slice(startIndex, endIndex); // Update table body
            const tableBody = document.getElementById('table-body'); const noResults =
            document.getElementById('no-results'); const table =
            document.getElementById('evidence-table'); if (totalRecords === 0) { table.style.display
            = 'none'; noResults.style.display = 'block'; } else { table.style.display = 'table';
            noResults.style.display = 'none'; tableBody.innerHTML = currentPageData.map(item => `
            <tr> <td>${escapeHtml(item.controlName)}</td>
            <td><code>${escapeHtml(item.displayPath)}</code></td> <td> <a
            href="${escapeHtml(item.abbreviatedPath)}"
            title="ControlEvidence/Evidence/${escapeHtml(item.abbreviatedFilename)}"
            download>${escapeHtml(item.abbreviatedFilename)}</a> </td> </tr> `).join(''); } //
            Update pagination info document.getElementById('current-page').textContent =
            totalRecords > 0 ? currentPage : 0; document.getElementById('total-pages').textContent =
            Math.max(totalPages, 1); document.getElementById('showing-start').textContent =
            totalRecords > 0 ? startIndex + 1 : 0;
            document.getElementById('showing-end').textContent = endIndex;
            document.getElementById('total-records').textContent = totalRecords; // Update
            pagination buttons document.getElementById('prev-btn').disabled = currentPage <= 1;
            document.getElementById('next-btn').disabled = currentPage >= totalPages || totalRecords
            === 0; } // Escape HTML to prevent XSS function escapeHtml(text) { const div =
            document.createElement('div'); div.textContent = text; return div.innerHTML; } //
            Initialize when page loads document.addEventListener('DOMContentLoaded',
            initializeTable);
        </script>
    </body>
</html>

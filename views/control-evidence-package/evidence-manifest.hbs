<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{title}}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            line-height: 1.6;
        }
        h1 {
            color: #333;
            border-bottom: 2px solid #007acc;
            padding-bottom: 10px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #333;
        }
        tr:hover {
            background-color: #f5f5f5;
        }
        code {
            background-color: #f1f1f1;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
        }
        a {
            color: #007acc;
            text-decoration: none;
        }
        a:hover {
            text-decoration: underline;
        }
        .description {
            color: #666;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <h1>{{title}}</h1>
    <p class="description">
        This manifest shows the mapping between abbreviated filenames and their original paths.
    </p>
    <table>
        <thead>
            <tr>
                <th>Evidence</th>
                <th>Original Path</th>
                <th>Abbreviated Filename (/Evidence)</th>
            </tr>
        </thead>
        <tbody>
            {{#each manifestEntries}}
            <tr>
                <td>{{controlName}}</td>
                <td><code>{{displayPath}}</code></td>
                <td>
                    <a href="{{abbreviatedPath}}" title="ControlEvidence/Evidence/{{abbreviatedFilename}}" download>{{abbreviatedFilename}}</a>
                </td>
            </tr>
            {{/each}}
        </tbody>
    </table>
</body>
</html>

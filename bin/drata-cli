#!/usr/bin/env bash

# Need to load the credentials for the target environment when running a remote command (using -r flag)

ENV=""
DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" >/dev/null 2>&1 && pwd)"
NODE_MODULES="${DIR}/../node_modules"
TS_NODE="${NODE_MODULES}/.bin/ts-node"
BUILD_DIRECTORY="${DIR}/../dist"
SRC_DIRECTORY="${DIR}/../src"
ORMCONFIG_PATH="${DIR}/../ormconfig.json"
ORMCONFIG_OTHERS_PATH="${DIR}/../ormconfig-*.json"
# DB backups global variables
DB_BACKUPS_ROOT_FOLDER="${DIR}/../db-backups"
LOCAL_DEV="${DIR}/../config/localdev.yml"
# mtls certificates
MTLS_ROOT_FOLDER="${DIR}/../mtls"
# set default code path; will determine final path after NODE_CONFIG_ENV is set from --environment arg
CODE_PATH="$SRC_DIRECTORY"
IS_BUILD_MODE=0
COMMAND_ID=$(echo $RANDOM)
COMMAND=$@
export NODE_CONFIG_ENV="${NODE_CONFIG_ENV:-localdev}"

# Suppress the AWS SDK V2 upgrade message so we don't worry about errors in datadog
export AWS_SDK_JS_SUPPRESS_MAINTENANCE_MODE_MESSAGE=1

# AWS default variables (QA is the default)
REMOTE=0
FORCE=0
RUN_ARBITRARY_CMD=0
SHOULD_ECHO=1

# Formatting
PADDING="-36"
BOLD="\033[1m"
NORMAL="\033[0m" # Reset all styles
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
MAGENTA='\033[0;35m'
CYAN='\033[0;36m'
WHITE='\033[0;37m'

function help() {
    echo -e ""
    echo -e "Drata CLI 🏄‍♀️"
    echo -e ""
    echo -e "$0 [options] <command>"
    echo -e ""
    echo -e "Options:"
    printf "\t%${PADDING}s %s\n" "-h --help" "Display this help"
    printf "\t%${PADDING}s %s\n" "-e --environment <devops|dev|qa|prod>" "Set the environment to run a remote command (default: qa)"
    printf "\t%${PADDING}s %s\n" "--region <us-west-2|eu-central-1|us-east-2|ap-southeast-2|ap-southeast-4>" "Set the AWS region (required when -e is set to prod, default for devops|dev|qa: us-west-2)"
    echo -e ""
    echo -e "Commands:"
    printf "\t%${PADDING}s %s\n" "autopilot" "Run the autopilot CLI (for more info, run: $0 autopilot -h)"
    printf "\t%${PADDING}s %s\n" "autopilot2" "Run the autopilot 2 CLI (for more info, run: $0 autopilot2 -h)"
    printf "\t%${PADDING}s %s\n" "schedule-workflow" "Manage scheduled workflows in Temporal (for more info, run: $0 schedule-workflow -h)"
    printf "\t%${PADDING}s %s\n" "trigger-temporal-workflow" "Trigger workflow in temporal (for more info, run: $0 trigger-temporal-workflow -h)"
    printf "\t%${PADDING}s %s\n" "syncs" "Run the identity synchronization CLI (for more info, run: $0 syncs -h)"
    printf "\t%${PADDING}s %s\n" "cron" "Run the cron CLI (for more info, run: $0 cron -h)"
    printf "\t%${PADDING}s %s\n" "complianceChecks" "Run the compliance checks CLI (for more info, run: $0 complianceChecks -h)"
    printf "\t%${PADDING}s %s\n" "connectionRegions" "Run the connection regions CLI (for more info, run: $0 connectionRegions -h)"
    printf "\t%${PADDING}s %s\n" "controlEvidencePackage" "Run control package generation for a given audit (for more info, run: $0 controlEvidencePackage -h)"
    printf "\t%${PADDING}s %s\n" "encryption" "Run the encryption util CLI (for more info, run: $0 encryption -h)"
    printf "\t%${PADDING}s %s\n" "notifications:company" "Run the company notifications util CLI (for more info, run: $0 notifications -h)"
    printf "\t%${PADDING}s %s\n" "swagger:docs" "Create swagger docs for the API endpoints (for more info, run: $0 swagger:docs -h)"
    printf "\t%${PADDING}s %s\n" "migration:check" "Checks if a migration is needed for the regional database"
    printf "\t%${PADDING}s %s\n" "migration:check:tenant" "Checks if a migration is needed for the tenant database"
    printf "\t%${PADDING}s %s\n" "migration:generate <name>" "Create a migration file prefixed by <name>"
    printf "\t%${PADDING}s %s\n" "migration:generate:tenant <name>" "Create a migration file for tenant database prefixed by <name>"
    printf "\t%${PADDING}s %s\n" "migration:revert" "Revert to previous migration"
    printf "\t%${PADDING}s %s\n" "migration:revert:tenant" "Revert to previous migration on tenant database"
    printf "\t%${PADDING}s %s\n" "migration:revert:all" "Revert to previous migration on all databases"
    printf "\t%${PADDING}s %s\n" "migration:run" "Run latest migrations"
    printf "\t%${PADDING}s %s\n" "migration:run:tenant" "Run latest migrations on tenant database"
    printf "\t%${PADDING}s %s\n" "migration:run:all" "Run latest migrations on all databases"
    printf "\t%${PADDING}s %s\n" "createCustomBackup <name>" "Create a database(s) local backup (all but 'sys', 'mysql', 'information_schema', 'performance_schema')."
    printf "\t%${PADDING}s %s\n" "restoreCustomBackup <name>" "Restores <name> local backup. The local backup should be stored in '/api/db-backups/custom/' directory"
    printf "\t%${PADDING}s %s\n" "nukeAndPave" "Nuke the existing databases and create new databases (for more info -h)"
    printf "\t%${PADDING}s %s\n" "nukeAndPaveEmpty" "Nuke the existing databases and create new databases with no tenant (for more info -h)"
    printf "\t%${PADDING}s %s\n" "nukeAndPaveAnnual" "Nuke the existing databases and create new databases with two tenants (for more info -h)"
    printf "\t%${PADDING}s %s\n" "nukeAndPaveMax" "Nuke the existing databases and create new databases with specified amount of users and groups. (for more info -h)"
    printf "\t%${PADDING}s %s\n" "publishPolicyHtml" "Publish policy template content html to target policy content html (for more info, run: $0 publishPolicyHtml -h)"
    printf "\t%${PADDING}s %s\n" "publishWysiwygComments" "Publish site admin WYSIWYG comments to tenants (for more info, run: $0 publishWysiwygComments -h)"
    printf "\t%${PADDING}s %s\n" "airtable-sync" "Sync the seed files with Airtable data, run: $0 airtable -h)"
    printf "\t%${PADDING}s %s\n" "grc-sync" "Sync DB seed files with Airtable data (use -t or -f to specify testIds or framework slug)"
    printf "\t%${PADDING}s %s\n" "grc-validate" "Validate GRC data between Airtable, YAMLs and regional DBs"
    printf "\t%${PADDING}s %s\n" "backfill" "Run a backfill script from either scripts/backfill or scripts/recurring (for more info, run: $0 backfill -h)"
    printf "\t%${PADDING}s %s\n" "schema:drop" "Drop the schema"
    printf "\t%${PADDING}s %s\n" "schema:log" "Display the schema"
    printf "\t%${PADDING}s %s\n" "schema:sync" "Sync the schema"
    printf "\t%${PADDING}s %s\n" "seed:config" "Configure the seed utility"
    printf "\t%${PADDING}s %s\n" "seed:run" "Run the seed utility"
    printf "\t%${PADDING}s %s\n" "generateMDM" "Generating a boiler plate data for a new MDM (for more info, run: $0 generateMDM -h)"
    printf "\t%${PADDING}s %s\n" "createCertificates" "Generating MLTS certificate files from MTLS keys"
    printf "\t%${PADDING}s %s\n" "buildCatalog" "Removes the unnecessary requirements from an oscal catalog json by comparing it to the profiles. For more details run: $0 buildCatalog -h"
    printf "\t%${PADDING}s %s\n" "testSalesforceInvite" "Generate a test tenant creation message"
    printf "\t%${PADDING}s %s\n" "generateTenants" "Generate multiple test tenants (options: --count, --with-data, --multi-host) Check https://www.notion.so/drata/Stress-Testing-the-API-and-Temporal-Workflows-with-generate-tenants-CLI-278095aed4f180238348dc2ccf5f2873?source=copy_link for more detailed instructions."
    printf "\t%${PADDING}s %s\n" "updateSlackDrataAppHomeTab" "Updates the Drata slack App using the blocks declared in the seeds json file."
    printf "\t%${PADDING}s %s\n" "runOpenSearchEngineClientTest" "Runs a simple cli to help test open search client connectivity"
    printf "\t%${PADDING}s %s\n" "testNotificationPlatform" "Sends an email using notification platform knock"
    printf "\t%${PADDING}s %s\n" "test-snapshot-collector" "Collect and compare test snapshots"

    echo -e ""
    exit 0
}

if [ "$#" == 0 ]; then
    help
fi

function split() {
    local str="$1"
    local delimiter="$2"

    arr=(${str//$delimiter/ })
    echo ${arr[@]}
}

function exitWithWarn() {
    fancyEcho "WARN: $1" 40
    exit 1
}

function exitWithError() {
    fancyEcho "ERROR: $1" 50
    exit 1
}

function fancyEcho() {
    if [[ $SHOULD_ECHO -eq 0 ]]; then
        return
    fi

    local msg="$1"
    # DEBUG = 20, LOG = 30, WARN = 40, ERROR = 50
    local level="${2:-30}"
    if [[ "$ENV" == "devops" || "$ENV" =~ ^dev || "$ENV" == "qa" || "$ENV" == "prod" ]]; then
        # output json
        echo -e "{\"msg\": \"${msg}\", \"level\": ${level}}"
    else
        echo -e "$msg"
    fi
}

function setShouldEcho() {
    for arg in "${@}"; do
        if [[ $arg == "-h" || $arg == "--help" ]]; then
            SHOULD_ECHO=0
            break
        fi
    done
}

function setNetworkConfig() {
    case "$ENV" in
    "devops")
        # Default region
        REGION="us-west-2"
        # private subnets
        SUBNETS="[\"subnet-0ab4d65df9f534974\",\"subnet-0ccebbdd96d0cd6a6\",\"subnet-04f3ddc0b26d9b862\",\"subnet-01ef24de321720e77\"]"
        # security group ID for dt-devops-api-green-ecs-service
        SECURITY_GROUPS="[\"sg-0c37f393cb5d7b4c2\"]"
        ;;
    "dev")
        # Default region
        REGION="us-west-2"
        # private subnets
        SUBNETS="[\"subnet-018d2281c01a797c0\",\"subnet-0a68e10a9e646ec0e\",\"subnet-0bde3018409fa9178\",\"subnet-0b318e55b819afd70\"]"
        case "$DEV_NUM" in
        "01")
            SECURITY_GROUPS="[\"sg-0e056b235e3c8ee15\"]"
            ;;
        "02")
            SECURITY_GROUPS="[\"sg-0091874f4f38b6aff\"]"
            ;;
        "03")
            SECURITY_GROUPS="[\"sg-014f273ac288a20c3\"]"
            ;;
        "04")
            SECURITY_GROUPS="[\"sg-0d39043d50d69d4e7\"]"
            ;;
        *)
            # Fallback for the OG dev environment
            # security group ID for dt-dev-api-green-ecs-service
            SECURITY_GROUPS="[\"sg-020431183777a356b\"]"
            ;;
        esac
        ;;
    "qa")
        case "$REGION" in
        "us-west-2")
            # private subnets
            SUBNETS="[\"subnet-04aea25f61ae2d46f\",\"subnet-0405c3e35a17c998b\",\"subnet-0ca6d7474a04275c5\",\"subnet-0db52bcb24cdd3315\"]"
            # security group ID for dt-qa-api-green-ecs-service
            SECURITY_GROUPS="[\"sg-0fc092cf2693c67c9\"]"
            ;;        
        "eu-central-1")
            # private subnets
            SUBNETS="[\"subnet-05170f009f09b450c\",\"subnet-0142013c703227430\",\"subnet-002dd6c6cf43369ed\"]"
            # security group ID for dt-qa-api-green-ecs-service
            SECURITY_GROUPS="[\"sg-01189951148ee3d71\"]"
            ;;        
        "ap-southeast-2")
            # private subnets
            SUBNETS="[\"subnet-0d834542aa55dea06\",\"subnet-0ee286a73096414a4\",\"subnet-06f4e77feb80b81f0\"]"
            # security group ID for dt-qa-api-green-ecs-service
            SECURITY_GROUPS="[\"sg-038ed5473b6688592\"]"
            ;;
        esac
        ;;
    "prod")
        case "$REGION" in
        "us-west-2")
            # private subnets
            SUBNETS="[\"subnet-0d8de23afca2a251e\",\"subnet-062c6624daed7de68\",\"subnet-0370a9d2960184fff\",\"subnet-081e62d07865a65de\"]"
            # security group ID for dt-prod-api-green-ecs-service
            SECURITY_GROUPS="[\"sg-0f6f42545181cde03\"]"
            ;;
        "eu-central-1")
            # private subnets
            SUBNETS="[\"subnet-0374395b981339fd7\",\"subnet-0661a6b7d5794d824\",\"subnet-0c4f317a015960693\"]"
            # security group ID for dt-prod-api-green-ecs-service
            SECURITY_GROUPS="[\"sg-0c00b75f72946b273\"]"
            ;;
        "us-east-2")
            # private subnets
            SUBNETS="[\"subnet-07e23525b9f1bd595\",\"subnet-06acbffdaae6e0a33\",\"subnet-03bb8f8500fe0e45f\"]"
            # security group ID for dt-prod-api-green-ecs-service
            SECURITY_GROUPS="[\"sg-0397a0fee2ca1e10d\"]"
            ;;
        "ap-southeast-2")
            # private subnets
            SUBNETS="[\"subnet-0e2f25e138fd98ac3\", \"subnet-0f4693f6cd59b4051\", \"subnet-0f0f2ed76acace70c\"]"
            # security group ID for dt-prod-api-green-ecs-service
            SECURITY_GROUPS="[\"sg-05088a59946ed88c0\"]"
            ;;
        "ap-southeast-4")
            # private subnets
            SUBNETS="[\"subnet-0670584c15670bbaa\", \"subnet-0b4cce508b9515b82\", \"subnet-0b653477eb24170c0\"]"
            # security group ID for dt-prod-api-green-ecs-service
            SECURITY_GROUPS="[\"sg-0c27f8c55b4295a3a\"]"
            ;;
        *)
            exitWithError "Invalid REGION... Exiting"
            ;;
        esac
        ;;
    *)
        exitWithError "Invalid ENV... Exiting"
        ;;
    esac

    NETWORK_CONFIG=$(
        cat <<EOF
{
"awsvpcConfiguration": {
    "subnets": ${SUBNETS},
    "securityGroups": ${SECURITY_GROUPS},
    "assignPublicIp": "DISABLED"
}
}
EOF
    )
}

function isAwsCliInstalled() {
    if ! command -v aws &>/dev/null; then
        exitWithError "aws-cli is not installed...\nTo install aws-cli, go to: https://docs.aws.amazon.com/cli/latest/userguide/install-cliv2.html\n"
    fi
}

function isJqInstalled() {
    if ! command -v jq &>/dev/null; then
        exitWithError "jq is not installed...\nTo install jq, run: brew install jq\n"
    fi
}

function isRunningInAWS() {
    # Check for ECS-specific environment variables
    if [[ -n "$ECS_CONTAINER_METADATA_URI" || -n "$AWS_EXECUTION_ENV" ]]; then
        return 0  # True, we are running in AWS
    else
        return 1  # False, we are not running in AWS
    fi
}

function runRemoteTask() {
    isAwsCliInstalled
    isJqInstalled
    setNetworkConfig
    local STATUS=
    local CMD="$1"
    shift
    local ARGS="$@"
    local COMMAND_JSON="[\"./bin/drata-cli\", \"${CMD}\", \"${ARGS}\"]"

    if [[ $RUN_ARBITRARY_CMD == 1 ]]; then
        COMMAND_JSON="[\"./bin/drata-cli\", \"--cmd\", \"${CMD}\", \"${ARGS}\"]"
    fi

    if isRunningInAWS; then
        # If we're in AWS, add the --confirmed=true flag to skip confirmation
        if [[ "$CMD" == "autopilot" && "$ARGS" == *"--atomic"* && "$ARGS" != *"--confirmed=true"* ]]; then
            ARGS="$ARGS --confirmed=true"
            COMMAND_JSON="[\"./bin/drata-cli\", \"${CMD}\", \"${ARGS}\"]"
        fi
    fi

    if [ -n "$JENKINS_USER" ]; then
        USER_EMAIL="$JENKINS_USER"
        CLI_MODE="jenkins"
    else
        # extract user email from a string that looks like this:
        # 'AROAXXXXXBBBBBHAB4BUR:<EMAIL>'
        USER_EMAIL=$(aws sts get-caller-identity | jq -r '.UserId' | cut -d ':' -f 2)
        CLI_MODE="manual"
    fi

    if [ -z "$USER_EMAIL" ]; then
        exitWithError "Could not verify user identity"
    fi

    if [[ $CMD == migration* ]]; then
        MODE="migration"
    else
        MODE="$CMD"
    fi

    OVERRIDES=$(
        cat <<EOF
{
"containerOverrides": [
    {
    "name": "${CONTAINER}",
    "command": $COMMAND_JSON,
    "environment": [
        { "name": "AUTOPILOT_ENABLED", "value": "true" },
        { "name": "TASK_STARTED_BY", "value": "${USER_EMAIL}" },
        { "name": "MODE", "value": "${MODE}" }
    ]
    },
    {
    "name": "datadog-agent",
    "environment": [{ "name": "DD_TAGS", "value": "env:${ENV} mode:${MODE} cli_mode:${CLI_MODE}" }]
    },
    {
    "name": "log-router",
    "environment": [{ "name": "DD_TAGS_FIRELENS", "value": "env:${ENV},mode:${MODE},cli_mode:${CLI_MODE}" }]
    }
]
}
EOF
    )
    PROPAGATE_TAGS='--propagate-tags TASK_DEFINITION'

    case $CMD in
        autopilot)
            EXTRA_TAGS="--tags key=drata:operations:component,value=ap1 key=drata:terraform:managed,value=false"
            ;;
        autopilot2)
            EXTRA_TAGS="--tags key=drata:operations:env,value=${ENV} key=env,value=${ENV} key=drata:operations:owner,value=@appdev key=namespace,value=dt key=drata:operations:project,value=ap2 key=drata:terraform:managed,value=false"
            PROPAGATE_TAGS=''
            ;;
        *-workflow)
            EXTRA_TAGS="--tags key=drata:operations:component,value=waas key=drata:terraform:managed,value=false"
            ;;
        migration:*)
            EXTRA_TAGS="--tags key=drata:operations:component,value=migrations key=drata:terraform:managed,value=false"
            ;;
        backfill)
            EXTRA_TAGS="--tags key=drata:operations:component,value=backfill key=drata:terraform:managed,value=false"
            ;;
        *)
            EXTRA_TAGS="--tags key=drata:terraform:managed,value=false"
            ;;
    esac

    TASKARN=$(aws --region $REGION ecs run-task \
        --cluster $CLUSTER \
        --task-definition $TASKDEF \
        --overrides="$OVERRIDES" \
        --network-configuration="$NETWORK_CONFIG" \
        $PROPAGATE_TAGS \
        $EXTRA_TAGS \
        --launch-type "FARGATE" | jq -r '.tasks[0] | .taskArn')


    if [ "$TASKARN" == "" ]; then
        exitWithError "Could not start task..."
    fi

    fancyEcho "Launched task: $TASKARN"
    fancyEcho "Datadog logs link: https://app.datadoghq.com/logs?query=task_arn:`echo $TASKARN | sed 's/.*task\///g'`"
    echo -n "Running."

    while [ "$STATUS" != "STOPPED" ]; do
        sleep 5
        echo -n "."
        STATUS=$(aws --region $REGION ecs describe-tasks \
            --cluster $CLUSTER \
            --tasks "$TASKARN" | jq -r '.tasks[0] | .lastStatus')
    done
    echo -e "\r\033[K"

    # TODO - need to find a way to show output...
    # since we implemented datadog, we don't have a cloudwatch log-group...
    fancyEcho "The task has finished running. Check Datadog for logs"
    # TASKID=$(echo $TASKARN | sed -e 's/^arn:aws:ecs:[a-z0-9-]*:[0-9]*:task\/[a-z0-9_-]*\///')
    # if ! command -v ecs-cli &> /dev/null; then
    #     echo "Warning: ecs-cli is not installed, therefore we cannot get remote logs..."
    #     echo "To install ecs-cli, google it"
    #     echo ""
    # else
    #     echo "Fetching remote logs..."
    #     ecs-cli logs --region $REGION --cluster $CLUSTER --task-id $TASKID
    # fi
}

function setCodePath() {
    if [ ! -d "$SRC_DIRECTORY" ]; then
        if [ -d "$BUILD_DIRECTORY" ]; then
            IS_BUILD_MODE=1
            CODE_PATH="$BUILD_DIRECTORY"
        else
            exitWithError "Could not find code path"
        fi
    fi
}

function typeOrmClean() {
    (rm -f $ORMCONFIG_PATH) && (rm -f $ORMCONFIG_OTHERS_PATH)
    return $?
}

function preTypeOrm() {
    $TS_NODE -r tsconfig-paths/register "$@"
    return $?
}

function typeOrm() {
    $TS_NODE -r tsconfig-paths/register ${NODE_MODULES}/typeorm/cli.js "$@"
    return $?
}

function runArbitraryCommand() {
    local RUN_CMD="$@"
    if [ $REMOTE == 1 ]; then
        runRemoteTask "$RUN_CMD"
    else
        fancyEcho "Running arbitrary command...\n"
        $RUN_CMD
    fi
    return $?
}

function runAutopilot() {
    confirmRunAtomicForSpecificTenants "$@"
    if [ $REMOTE == 1 ]; then
        runRemoteTask autopilot "$@"
    else
        fancyEcho "Running autopilot CLI...\n"
        node ${NODE_GC_OPTIONS} --max-http-header-size=16384 ${BUILD_DIRECTORY}/cli/autopilot.cli.js $@
    fi
    return $?
}

function countAccounts() {
    local flag=$1
    shift
    local args=("$@")
    local count=0
    local start_counting=false

    for arg in "${args[@]}"; do
        if [ "$start_counting" = true ]; then
            if [[ "$arg" == -* ]]; then
                break
            fi
            ((count++))
        fi
        if [ "$arg" = "$flag" ]; then
            start_counting=true
        fi
    done

    echo $count
}

function confirmRunAtomicForSpecificTenants() {
    # make arguments array-like
    local args=("$@")
    # get the items
    local params="${args[@]}"

    local numberOfAccounts=0

    # check for run all in params
    runAtomicForSpecificSpecificTenants=false
    if [[ "$params" == *"--atomic"* ]]; then
        case "$params" in
            *-d*|*--account-domains*|*-i*|*--account-ids*)
                runAtomicForSpecificSpecificTenants=true
                ;;
            *)
                runAtomicForSpecificSpecificTenants=false
                ;;
        esac
    else
        runAtomicForSpecificSpecificTenants=false
    fi

    # Prompt if run atomic for specific tenants is found and true
    if [ "$runAtomicForSpecificSpecificTenants" == true ]; then
        acceptable_responses=("yes" "y")

        if [[ "$params" == *"-i"* || "$params" == *"--account-ids"* ]]; then
            numberOfAccounts=$(countAccounts "-i" "${args[@]}")
            if [ "$numberOfAccounts" -eq 0 ]; then
                numberOfAccounts=$(countAccounts "--account-ids" "${args[@]}")
            fi
        else
            numberOfAccounts=$(countAccounts "-d" "${args[@]}")
            if [ "$numberOfAccounts" -eq 0 ]; then
                numberOfAccounts=$(countAccounts "--account-domains" "${args[@]}")
            fi
        fi

        # skip confimation if the confirmed flag true (needed inside aws container) or we are running in aws
        if isRunningInAWS || [[ "$params" == *"--confirmed=true"* ]]; then
            fancyEcho "Running atomic autopilot for specific domains/accounts"
        else
            echo -e "${YELLOW}${BOLD}! ${YELLOW}${BOLD}'--atomic' flag will spawn an ECS task for every account to run its Autopilot. This will incur additional infrastructure costs. Use with caution${NORMAL}"
            echo -e "${GREEN}${BOLD}? ${WHITE}${BOLD}Are you sure you want to run Autopilot with '--atomic' flag for ${numberOfAccounts} accounts? ${CYAN}${BOLD}(y/n)${NORMAL}"
            read -t 30 -r confirmRunAtomicForSpecificSpecificTenants
            confirmRunAtomicForSpecificSpecificTenants=$(echo "$confirmRunAtomicForSpecificSpecificTenants" | tr '[:upper:]' '[:lower:]')
            case $confirmRunAtomicForSpecificSpecificTenants in
                yes|y)
                    ;;
                *)
                    echo -e "${RED}${BOLD}X ${YELLOW}${BOLD}Unable to run atomic autopilot for specific accounts. Autopilot has been cancelled.${NORMAL}"
                    exit 0
                    ;;
            esac
        fi
    fi
}

function runAutopilot2() {
    # not sure if others processes require output so toggle ONLY on this function
    setShouldEcho $@

    if [ $REMOTE == 1 ]; then
        runRemoteTask autopilot2 "$@"
    else
        fancyEcho "Running autopilot 2.0 CLI with arguments: {$*}\n"
        node --max-http-header-size=16384 ${BUILD_DIRECTORY}/cli/autopilot-2.cli.js $@
    fi

    fancyEcho "Autopilot 2.0 ran with arguments: {$*}\n"
    return $?
}

function runTemporalWorkflowScheduleCommands() {
    setShouldEcho $@

    if [ $REMOTE == 1 ]; then
        INPUT_ARG=""
        COMMAND_ARGS=()

        while [[ $# -gt 0 ]]; do
            case $1 in
                --input)
                    # Handle --input flag
                    INPUT_ARG="$2"
                    shift 2
                    ;;
                *)
                    # Collect other flags and their values
                    COMMAND_ARGS+=("$1")
                    shift
                    ;;
            esac
        done

        if [ -n "$INPUT_ARG" ]; then
            # Replace double-quotes with escaped double-quotes
            ESCAPED_INPUT_ARG=$(echo "$INPUT_ARG" | sed 's/"/\\"/g')
            COMMAND_ARGS+=("--input")
            COMMAND_ARGS+=("$ESCAPED_INPUT_ARG")
        fi

        runRemoteTask schedule-workflow ${COMMAND_ARGS[@]}
    else
        fancyEcho "Running Temporal Scheduler CLI...\n"
        node --max-http-header-size=16384 ${BUILD_DIRECTORY}/cli/schedule-workflow.cli.js $@
    fi

    return $?
}

function runTemporalWorkflow() {
    setShouldEcho $@

    if [ $REMOTE == 1 ]; then
        runRemoteTask trigger-temporal-workflow "$@"
    else
        fancyEcho "Running temporal workflow CLI...\n"
        node ${BUILD_DIRECTORY}/cli/trigger-temporal-workflow.cli.js $@
    fi

    return $?
}

function runSynchronizations() {
    if [ $REMOTE == 1 ]; then
        runRemoteTask syncs "$@"
    else
        fancyEcho "Running synchronizations CLI...\n"
        node --max-http-header-size=16384 ${BUILD_DIRECTORY}/cli/synchronizations.cli.js $@
    fi
    return $?
}

function runCron() {
    if [ $REMOTE == 1 ]; then
        runRemoteTask cron "$@"
    else
        fancyEcho "Running crons CLI...\n"
        node --max-http-header-size=16384 ${BUILD_DIRECTORY}/cli/cron.cli.js $@
    fi
    return $?
}

function runComplianceChecks() {
    if [ $REMOTE == 1 ]; then
        runRemoteTask complianceChecks "$@"
    else
        fancyEcho "Running complianceChecks CLI...\n"
        node --max-http-header-size=16384 ${BUILD_DIRECTORY}/cli/compliance-checks.cli.js $@
    fi
    return $?
}

function runConnectionRegions() {
    if [ $REMOTE == 1 ]; then
        runRemoteTask connectionRegions "$@"
    else
        fancyEcho "Running connectionRegions CLI...\n"
        node --max-http-header-size=16384 ${BUILD_DIRECTORY}/cli/connection-regions.cli.js $@
    fi
    return $?
}

function runControlEvidencePackage() {
    if [ $REMOTE == 1 ]; then
        runRemoteTask controlEvidencePackage "$@"
    else
        fancyEcho "Running controlEvidencePackage CLI...\n"
        node --max-http-header-size=16384 ${BUILD_DIRECTORY}/cli/control-evidence-package.cli.js $@
    fi
    return $?
}


function runEncryption() {
    if [ $REMOTE == 1 ]; then
        runRemoteTask encryption "$@"
    else
        fancyEcho "Running encryption CLI...\n"
        node --max-http-header-size=16384 ${BUILD_DIRECTORY}/cli/utils/encryption.cli.js "$@"
    fi
    return $?
}

function runCompanyNotifications() {
    if [ $REMOTE == 1 ]; then
        runRemoteTask notifications:company "$@"
    else
        fancyEcho "Running company notifications CLI...\n"
        node --max-http-header-size=16384 ${BUILD_DIRECTORY}/cli/company-notifications.cli.js $@
    fi
    return $?
}

function runSwaggerCli() {
    if [ $REMOTE == 1 ]; then
        exitWithWarn "This command does not support running remotely on AWS"
    fi

    if [[ " $* " =~ [[:space:]]--rebuild[[:space:]] ]]; then
        fancyEcho "Rebuilding..."
        yarn build
    fi

    fancyEcho "Running Swagger docs CLI...\n"
    node --max-http-header-size=16384 ${BUILD_DIRECTORY}/cli/swagger.cli.js "$@"
    return $?
}

function migrationCheck() {
    if [ $REMOTE == 1 ]; then
        exitWithWarn "This command does not support running remotely on AWS"
    fi
    fancyEcho "Checking if a migration file for the regional database needs to be created\n"
    typeOrmClean &&
        preTypeOrm ${CODE_PATH}/scripts/write-typeorm-config && \
        typeOrm migration:generate --check --dataSource ds-global.ts "src/database/migrations/global/DriftDetection" && \
        rm ds-global.ts
    return $?
}

function migrationCheckTenant() {
    if [ $REMOTE == 1 ]; then
        exitWithWarn "This command does not support running remotely on AWS"
    fi
    fancyEcho "Checking if a migration file for tenant databases needs to be created\n"
    typeOrmClean &&
        preTypeOrm ${CODE_PATH}/scripts/write-typeorm-tenant-config &&
        typeOrm migration:generate --check --dataSource ds-tenant.ts "src/database/migrations/app/DriftDetection" && \
        rm ds-tenant.ts
    return $?
}

function migrationGenerate() {
    if [ $REMOTE == 1 ]; then
        exitWithWarn "This command does not support running remotely on AWS"
    fi
    fancyEcho "Generating migration file for regional database\n"
    typeOrmClean && \
        preTypeOrm ${CODE_PATH}/scripts/write-typeorm-config && \
        typeOrm migration:generate --dataSource ds-global.ts "src/database/migrations/global/$@" && \
        rm ds-global.ts
    return $?
}

function migrationGenerateTenant() {
    if [ $REMOTE == 1 ]; then
        exitWithWarn "This command does not support running remotely on AWS"
    fi
    fancyEcho "Generating migration file for tenant databases\n"
    typeOrmClean && \
        preTypeOrm ${CODE_PATH}/scripts/write-typeorm-tenant-config && \
        typeOrm migration:generate --dataSource ds-tenant.ts "src/database/migrations/app/$@" && \
        rm ds-tenant.ts
    return $?
}

function migrationRun() {
    if [ $REMOTE == 1 ]; then
        runRemoteTask migration:run
    else
        fancyEcho "Running migration for regional database\n"
        $TS_NODE -r tsconfig-paths/register ${CODE_PATH}/scripts/global-migrations
    fi
    return $?
}

function migrationRunTenant() {
    if [ $REMOTE == 1 ]; then
        runRemoteTask migration:run:tenant "$@"
    else
        fancyEcho "Running migration for tenant databases\n"
        local args=( "$@" )
        $TS_NODE -r tsconfig-paths/register ${CODE_PATH}/scripts/tenant-migrations $args
    fi
    return $?
}

function migrationRevert() {
    if [[ "$ENV" == "prod" ]]; then
        exitWithWarn "Cannot revert migrations in prod or qa, remove the broken migration and create a new fix forward migration"
    fi

    if [ $REMOTE == 1 ]; then
        runRemoteTask migration:revert
    else
        fancyEcho "Reverting migration for regional database\n"
        $TS_NODE -r tsconfig-paths/register ${CODE_PATH}/scripts/global-migrations --revert
    fi
    return $?
}

function migrationRevertTenant() {
    if [[ "$ENV" == "prod" ]]; then
        exitWithWarn "Cannot revert migrations in prod or qa, remove the broken migration and create a new fix forward migration"
    fi

    if [ $REMOTE == 1 ]; then
        runRemoteTask migration:revert:tenant "$@"
    else
        fancyEcho "Reverting migration for tenant databases\n"
        local args=("$@")
        $TS_NODE -r tsconfig-paths/register ${CODE_PATH}/scripts/tenant-migrations --revert $args
    fi
    return $?
}

function downloadS3Backup() {
    local S3_BACKUP="$1"
    local LOCAL_BACKUP_PATH="$2"
    local S3_BACKUP_VERSION="$3"
    local DB_BACKUP_S3_BUCKET="dt-dev-nuke-and-pave-backups-us-west-2-bucket/backups-${S3_BACKUP_VERSION}/$S3_BACKUP"

    rm -rf ${LOCAL_BACKUP_PATH}/* || true &&
        mkdir -p ${LOCAL_BACKUP_PATH} || true &&
        fancyEcho "Downloading ${S3_BACKUP_VERSION} backup from s3 bucket..."
    aws s3 sync "s3://$DB_BACKUP_S3_BUCKET" "$LOCAL_BACKUP_PATH"
}

function uploadS3Backup() {
    local LOCAL_BACKUP_PATH="$1"
    local S3_BACKUP_DIR="$2"
    local S3_BACKUP_VERSION="$3"
    local DB_BACKUP_S3_BUCKET="dt-dev-nuke-and-pave-backups-us-west-2-bucket/backups-${S3_BACKUP_VERSION}/${S3_BACKUP_DIR}"

    fancyEcho "Clearing target directory on s3 bucket..."
    aws s3 rm "s3://$DB_BACKUP_S3_BUCKET" --recursive

    fancyEcho "Pushing ${LOCAL_BACKUP_PATH} to s3 bucket..."
    aws s3 sync "$LOCAL_BACKUP_PATH" "s3://$DB_BACKUP_S3_BUCKET"
}

function localDatabaseBackup() {
    if [[ "$NODE_CONFIG_ENV" == "devops" || "$NODE_CONFIG_ENV" == dev* || "$NODE_CONFIG_ENV" == "qa" || "$NODE_CONFIG_ENV" == "prod" ]]; then
        exitWithError "This command can not be ran on remote env"
    fi

    # save params
    local CMD="$1"
    local DB_BACKUP_PATH="$2"
    shift 2
    local ARGS=("$@")

    # set up flags defaults
    local DB_BACKUP_MIGRATIONS=1
    local DB_BACKUP_USERNAME=drata
    local DB_BACKUP_HOST=127.0.0.1
    local DB_BACKUP_PORT=3306
    local DB_BACKUP_PASSWORD_FLAG=0
    local DB_BACKUP_TARGET_FOLDER="$DB_BACKUPS_ROOT_FOLDER/$DB_BACKUP_PATH"

    for flags in "${ARGS[@]}"; do
        case $flags in
        -u | --db-username)
            shift
            DB_BACKUP_USERNAME="$1"
            ;;
        -p | --db-password)
            DB_BACKUP_PASSWORD_FLAG=1
            ;;
        -s3 | --restore-s3-backup)
            shift
            DB_S3_BACKUP=1
            ;;
        -s | --skip-migrations)
            DB_BACKUP_MIGRATIONS=0
            ;;
        esac
        shift
    done

    local DB_HOST_AND_PORT="-h $DB_BACKUP_HOST -P $DB_BACKUP_PORT"
    if [[ "$ENV" == "localdev.region.eu" ]]; then
        DB_HOST_AND_PORT="-h $DB_BACKUP_HOST -P 3307"
    fi

    if [[ "$ENV" == "localdev.region.apac" ]]; then
        DB_HOST_AND_PORT="-h $DB_BACKUP_HOST -P 3308"
    fi

    # set up script variables
    local DB_SELECT_ALL_BUT_SYSTEM_DBS="USE information_schema; SELECT SCHEMA_NAME FROM SCHEMATA WHERE SCHEMA_NAME NOT IN ('sys', 'mysql', 'information_schema', 'performance_schema');"
    # adjust DB_USER_AND_PASS according to flags
    local DB_USER_AND_PASS="-u $DB_BACKUP_USERNAME"
    if [ $DB_BACKUP_PASSWORD_FLAG == 1 ]; then
        DB_USER_AND_PASS="$DB_USER_AND_PASS -p"
    fi

    if [ "$NODE_CONFIG_ENV" = "releasehub" ]; then
        DB_USER_AND_PASS="-h $DB_HOST -u $DB_USER -p$DB_PASSWORD"
    fi

    # check if we can restore
    if [ "$CMD" == "restore" ]; then
        # Check if there are .sql files in the provided path in order to restore
        files=$(
            shopt -s nullglob dotglob
            echo $DB_BACKUP_TARGET_FOLDER/*.sql
        )
        if ! ((${#files})); then
            # If empty, return error, there is nothing to restore.
            fancyEcho "No backup available!"
            return 1
        fi
    fi

    # select all non system databases, either if we restore or create we use the value
    local databases=$(mysql --protocol=TCP ${DB_HOST_AND_PORT} ${DB_USER_AND_PASS} -se "$DB_SELECT_ALL_BUT_SYSTEM_DBS")

    # backup creation section
    if [ "$CMD" == "create" ]; then
        fancyEcho "Creating database backups..."
        #  Preparing folder
        rm -rf ${DB_BACKUP_TARGET_FOLDER}/* || true
        mkdir -p ${DB_BACKUP_TARGET_FOLDER} || true
        # create a .sql file per database found

        PIDS=()
        DATABASES=()
        for database in ${databases}; do
            fancyEcho "Backing up database: ${database}"
            mysqldump --protocol=TCP ${DB_HOST_AND_PORT} ${DB_USER_AND_PASS} ${database} >"${DB_BACKUP_TARGET_FOLDER}/${database}.sql" &
            PID=$!
            PIDS+=("$PID")
            DATABASES+=("$database")
        done

        for i in "${!PIDS[@]}"; do
            wait "${PIDS[$i]}"
            exitCode=$?
            if [ $exitCode -ne 0 ]; then
                FAILED_DUMPS+=("${DATABASES[$i]}")
                echo "❌ Backup failed for ${DATABASES[$i]} (Exit Code: $exitCode)"
            else
                echo "Backup succeeded for ${DATABASES[$i]}"
            fi
        done

        if [ "${#FAILED_DUMPS[@]}" -ne 0 ]; then
            fancyEcho "⚠️ Some backups failed: ${FAILED_DUMPS[*]}"
            exit 1
        else
            fancyEcho "\n ✅ All backups completed successfully. \n"
            return 0
        fi
    fi

    # Proceeding with restore when files exist
    # First we clean the database
    fancyEcho "Dropping all databases..."

    PIDS=()
    DATABASES=()
    for database in ${databases}; do
        fancyEcho "Dropping database: ${database}"
        mysql --protocol=TCP ${DB_HOST_AND_PORT} ${DB_USER_AND_PASS} -se "DROP DATABASE IF EXISTS \`${database}\`" &
        PID=$!
        PIDS+=("$PID")
        DATABASES+=("$database")
    done

    for i in "${!PIDS[@]}"; do
        wait "${PIDS[$i]}"
        exitCode=$?
        if [ $exitCode -ne 0 ]; then
            FAILED_DROPS+=("${DATABASES[$i]}")
            echo "❌ Dropping failed for ${DATABASES[$i]} (Exit Code: $exitCode)"
        else
            echo "Dropping succeeded for ${DATABASES[$i]}"
        fi
    done

    if [ "${#FAILED_DROPS[@]}" -ne 0 ]; then
        fancyEcho "⚠️ Some databases were not dropped successfully: ${FAILED_DROPS[*]}"
        exit 1
    else
        fancyEcho "\n ✅ All databases were dropped successfully. \n"
    fi

    # start restore existing databases within the backups folder
    fancyEcho "Restoring backup..."

    if [ "$NODE_CONFIG_ENV" = "releasehub" ]; then
        # db user on Release envs is not allowed to set this, so we'll just remove it
        sed -i 's/DEFINER=\S\+//g' $DB_BACKUP_TARGET_FOLDER/*.sql
    fi

    if [[ $DB_S3_BACKUP == 1 ]] && [[ $NODE_CONFIG_ENV == 'localdev' ]]; then
        tenantBackups=$(find "$DB_BACKUP_TARGET_FOLDER" -name "*.sql" -not -name "drata.sql")
        for file in ${tenantBackups}; do
            # there's no drata-app user on local mysql, so we'll just remove it
            sed -i '' '/DEFINER/d' $file
        done
    fi

    databasesFiles=$(ls $DB_BACKUP_TARGET_FOLDER/*.sql)

    PIDS=()
    DATABASES=()
    for file in ${databasesFiles}; do
        databaseName=$(basename -- "$file")
        fancyEcho "Creating and restoring database: ${databaseName}"
        (
            mysql --protocol=TCP ${DB_HOST_AND_PORT} ${DB_USER_AND_PASS} -se "CREATE DATABASE IF NOT EXISTS \`${databaseName%%.*}\` DEFAULT CHARACTER SET = \`utf8mb4\` DEFAULT COLLATE = \`utf8mb4_unicode_ci\`;"
            mysql --protocol=TCP ${DB_HOST_AND_PORT} ${DB_USER_AND_PASS} ${databaseName%%.*} <${file}
        ) &
        PID=$!
        PIDS+=("$PID")
        DATABASES+=("$databaseName")
    done

    for i in "${!PIDS[@]}"; do
        wait "${PIDS[$i]}"
        exitCode=$?
        if [ $exitCode -ne 0 ]; then
            FAILED_RESTORES+=("${DATABASES[$i]}")
            echo "❌ Backup restore failed for ${DATABASES[$i]} (Exit Code: $exitCode)"
        else
            echo "Backup restore succeeded for ${DATABASES[$i]}"
        fi
    done

    if [ "${#FAILED_RESTORES[@]}" -ne 0 ]; then
        fancyEcho "⚠️ Some backups restore failed: ${FAILED_RESTORES[*]}"
        exit 1
    else
        fancyEcho "\n ✅ All backups restore completed successfully. \n"
    fi

    if [[ $DB_S3_BACKUP == 1 ]] && [[ $NODE_CONFIG_ENV == 'localdev' ]]; then
        fancyEcho "Modifying tenant database host"
        mysql --protocol=TCP ${DB_HOST_AND_PORT} ${DB_USER_AND_PASS} -D drata -se "UPDATE account SET db_host = '127.0.0.1';"
        mysql --protocol=TCP ${DB_HOST_AND_PORT} ${DB_USER_AND_PASS} -D drata -se "UPDATE tenant_database_host SET friendly_name = '127';"
        mysql --protocol=TCP ${DB_HOST_AND_PORT} ${DB_USER_AND_PASS} -D drata -se "UPDATE tenant_database_host SET dns_name = '127.0.0.1';"
    fi

    fancyEcho "\n ✅ Backup finished! \n"

    if [ "$NODE_CONFIG_ENV" = "releasehub" ]; then
        # reset database dns host (backup points to localhost by default)
        node /usr/src/app/dist/database/helpers/releasehub.helper.js
    fi

    # Check to run migrations
    if [ $DB_BACKUP_MIGRATIONS == 1 ]; then
        migrationRun && migrationRunTenant
        local result=$?
        if [ "$result" != 0 ]; then
            return 1
        fi
    fi

    return 0
}

function remoteDatabaseBackup() {
    if [[ "$NODE_CONFIG_ENV" != dev && "$NODE_CONFIG_ENV" != dev-* ]]; then
        exitWithError "This command can be run on dev environments only"
    fi

    # save params
    local CMD="$1"
    local DB_BACKUP_PATH="$2"
    shift 2
    local ARGS=("$@")

    local DB_BACKUP_MIGRATIONS=1
    local TENANT_DB_HOST=$(echo "$TENANT_DBS" | jq -r '.[0].host')
    local TENANT_DB_PORT=$(echo "$TENANT_DBS" | jq -r '.[0].port')
    local DB_BACKUP_TARGET_FOLDER="$DB_BACKUPS_ROOT_FOLDER/$DB_BACKUP_PATH"
    local DB_CREDS="-u ${DB_USER} -p${DB_PASSWORD}"
    local REGIONAL_DB_CONNECTION_OPTIONS="--protocol=TCP -h ${DB_HOST} -P ${DB_PORT} ${DB_CREDS}"
    local TENANT_DB_CONNECTION_OPTIONS="--protocol=TCP -h ${TENANT_DB_HOST} -P ${TENANT_DB_PORT} ${DB_CREDS}"
    local DB_SELECT_ALL_BUT_SYSTEM_DBS="USE information_schema; SELECT SCHEMA_NAME FROM SCHEMATA WHERE SCHEMA_NAME NOT IN ('sys', 'mysql', 'information_schema', 'performance_schema', 'datadog', 'drata');"

    mute_warnings() {
        "$@" 2> >(grep -v 'Warning' >&2)
    }

    for flags in "${ARGS[@]}"; do
        case $flags in
        -s | --skip-migrations)
            DB_BACKUP_MIGRATIONS=0
            ;;
        esac
        shift
    done

    # backup creation section
    if [ "$CMD" == "create" ]; then
        fancyEcho "Creating database backups..."
        #  Preparing folder
        rm -rf ${DB_BACKUP_TARGET_FOLDER}/* || true
        mkdir -p ${DB_BACKUP_TARGET_FOLDER} || true

        local DUMP_OPTIONS="--set-gtid-purged=OFF --no-tablespaces"

        fancyEcho "Backing up regional database"
        mute_warnings mysqldump $DUMP_OPTIONS $REGIONAL_DB_CONNECTION_OPTIONS drata >"${DB_BACKUP_TARGET_FOLDER}/drata.sql"

        fancyEcho "Backing up tenant databases"
        local tenantDatabases=$(mute_warnings mysql $TENANT_DB_CONNECTION_OPTIONS -se "$DB_SELECT_ALL_BUT_SYSTEM_DBS" )

        PIDS=()
        DATABASES=()
        for database in ${tenantDatabases}; do
            fancyEcho "Backing up tenant database: ${database}"
            mute_warnings mysqldump $DUMP_OPTIONS $TENANT_DB_CONNECTION_OPTIONS ${database} >"${DB_BACKUP_TARGET_FOLDER}/${database}.sql" &
            PID=$!
            PIDS+=("$PID")
            DATABASES+=("$database")
        done

        for i in "${!PIDS[@]}"; do
            wait "${PIDS[$i]}"
            exitCode=$?
            if [ $exitCode -ne 0 ]; then
                FAILED_DUMPS+=("${DATABASES[$i]}")
                echo "❌ Backup failed for ${DATABASES[$i]} (Exit Code: $exitCode)"
            else
                echo "Backup succeeded for ${DATABASES[$i]}"
            fi
        done

        if [ "${#FAILED_DUMPS[@]}" -ne 0 ]; then
            fancyEcho "⚠️ Some backups failed: ${FAILED_DUMPS[*]}"
            exit 1
        else
            fancyEcho "\n ✅ All backups completed successfully. \n"
            return 0
        fi
    fi

    # check if we can restore
    if [ "$CMD" == "restore" ]; then
        # Check if there are .sql files in the provided path in order to restore
        files=$(
            shopt -s nullglob dotglob
            echo $DB_BACKUP_TARGET_FOLDER/*.sql
        )
        if ! ((${#files})); then
            # If empty, return error, there is nothing to restore.
            fancyEcho "No backup available!"
            return 1
        fi
    fi

    fancyEcho "Dropping regional database..."
    mute_warnings mysql $REGIONAL_DB_CONNECTION_OPTIONS -se "DROP DATABASE IF EXISTS drata"

    fancyEcho "Creating and restoring regional database"
    regionalBackup=$(ls $DB_BACKUP_TARGET_FOLDER/drata.sql)
    mute_warnings mysql $REGIONAL_DB_CONNECTION_OPTIONS -se "CREATE DATABASE IF NOT EXISTS drata DEFAULT CHARACTER SET = \`utf8mb4\` DEFAULT COLLATE = \`utf8mb4_unicode_ci\`;"
    mute_warnings mysql $REGIONAL_DB_CONNECTION_OPTIONS drata <${regionalBackup}
    fancyEcho "Regional backup finished!"

    fancyEcho "Modifying tenant database host"
    FRIENDLY_NAME=$(echo "$TENANT_DB_HOST" | cut -d'.' -f1)
    mute_warnings mysql $REGIONAL_DB_CONNECTION_OPTIONS -D drata -se "UPDATE account SET db_host = '${TENANT_DB_HOST}';"
    mute_warnings mysql $REGIONAL_DB_CONNECTION_OPTIONS -D drata -se "UPDATE tenant_database_host SET friendly_name = '${FRIENDLY_NAME}';"
    mute_warnings mysql $REGIONAL_DB_CONNECTION_OPTIONS -D drata -se "UPDATE tenant_database_host SET dns_name = '${TENANT_DB_HOST}';"
    fancyEcho "Modifying tenant database host finished!"

    fancyEcho "Dropping tenant databases..."
    local tenantDatabases=$(mute_warnings mysql $TENANT_DB_CONNECTION_OPTIONS -se "$DB_SELECT_ALL_BUT_SYSTEM_DBS" )

    PIDS=()
    DATABASES=()
    for database in ${tenantDatabases}; do
        fancyEcho "Dropping tenant database: ${database}"
        mute_warnings mysql $TENANT_DB_CONNECTION_OPTIONS -se "DROP DATABASE IF EXISTS \`${database}\`;" &
        PID=$!
        PIDS+=("$PID")
        DATABASES+=("$database")
    done

    for i in "${!PIDS[@]}"; do
        wait "${PIDS[$i]}"
        exitCode=$?
        if [ $exitCode -ne 0 ]; then
            FAILED_DROPS+=("${DATABASES[$i]}")
            echo "❌ Dropping failed for ${DATABASES[$i]} (Exit Code: $exitCode)"
        else
            echo "Dropping succeeded for ${DATABASES[$i]}"
        fi
    done

    if [ "${#FAILED_DROPS[@]}" -ne 0 ]; then
        fancyEcho "⚠️ Some tenant databases were not dropped successfully: ${FAILED_DROPS[*]}"
        exit 1
    else
        fancyEcho "\n ✅ All tenant databases were dropped successfully. \n"
    fi

    tenantBackups=$(find "$DB_BACKUP_TARGET_FOLDER" -name "*.sql" -not -name "drata.sql")

    PIDS=()
    DATABASES=()
    for file in ${tenantBackups}; do
        databaseName=$(basename -- "$file")
        fancyEcho "Creating and restoring database: ${databaseName}"
        (
            # db user on remote envs is not allowed to set this, so we'll just remove it
            sed -i 's/DEFINER=\S\+//g' $file
            mute_warnings mysql $TENANT_DB_CONNECTION_OPTIONS -se "CREATE DATABASE IF NOT EXISTS \`${databaseName%%.*}\` DEFAULT CHARACTER SET = \`utf8mb4\` DEFAULT COLLATE = \`utf8mb4_unicode_ci\`;"
            mute_warnings mysql $TENANT_DB_CONNECTION_OPTIONS ${databaseName%%.*} <${file}
        ) &
        PID=$!
        PIDS+=("$PID")
        DATABASES+=("$databaseName")
    done

    for i in "${!PIDS[@]}"; do
        wait "${PIDS[$i]}"
        exitCode=$?
        if [ $exitCode -ne 0 ]; then
            FAILED_RESTORES+=("${DATABASES[$i]}")
            echo "❌ Backup restore failed for ${DATABASES[$i]} (Exit Code: $exitCode)"
        else
            echo "Backup restore succeeded for ${DATABASES[$i]}"
        fi
    done

    if [ "${#FAILED_RESTORES[@]}" -ne 0 ]; then
        fancyEcho "⚠️ Some tenant backups restore failed: ${FAILED_RESTORES[*]}"
        exit 1
    else
        fancyEcho "\n ✅ All tenant backups restore completed successfully. \n"
    fi

    # Check to run migrations
    if [ $DB_BACKUP_MIGRATIONS == 1 ]; then
        migrationRun && migrationRunTenant
    fi

    return 0
}

function createCustomBackupHelp() {
    local CMD=$1
    echo -e ""
    echo -e "createCustomBackup options 🚀"
    echo -e ""
    echo -e "Options:"
    printf "\t%${PADDING}s %s\n" "-h --help" "Display this help"
    printf "\t%${PADDING}s %s\n" "-s3 --push-to-s3" "Push the created custom sql backup to an S3 bucket. The backup will be saved in dt-dev-nuke-and-pave-backups-us-west-2-bucket/backups-custom/<your backup name>"
    printf "\t%${PADDING}s %s\n" "-u --db-username" "Database username to be used for mysqldump and mysql commands"
    printf "\t%${PADDING}s %s\n" "-p --db-password" "Adds the -p flag to mysqldump and mysql commands, so the user is prompted for password"
    exit 0
}

function createCustomBackup() {
    if [[ "$ENV" == "devops" || "$ENV" == "qa" || "$ENV" == "prod" ]]; then
        exitWithError "This command can be run on local and dev envs only"
    fi

    if [[ "$1" == -* ]]; then
        ARGS="$@"
    else
        BACKUP_NAME="$1"
        shift
        ARGS="$@"
    fi

    local CMD="createCustomBackup"

    if [ $REMOTE == 1 ]; then
        runRemoteTask "$CMD" "$BACKUP_NAME" "$ARGS"
    else
        for flags in $ARGS; do
            case $flags in
            -h | --help)
                createCustomBackupHelp "$CMD"
                return $?
                ;;
            -s3 | --push-to-s3)
                shift
                PUSH_TO_S3=1
                ;;
            esac
        done

        if [[ -z "$BACKUP_NAME" ]]; then
            exitWithError "Please provide a name to create a backup. Use $ ./bin/drata-cli createCustomBackup -h for more info."
        fi

        echo ""
        echo "BACKUP_NAME: $BACKUP_NAME"
        echo "ARGS: $ARGS"
        echo ""

        local CMD="create"
        if [[ "$NODE_CONFIG_ENV" == "dev" || "$NODE_CONFIG_ENV" == dev-* ]]; then
            fancyEcho "Executing remoteDatabaseBackup"
            remoteDatabaseBackup "$CMD" "$LOCAL_BACKUP_FOLDER" ${ARGS}
            # Always push a remote backup to s3
            PUSH_TO_S3=1
        else
            fancyEcho "Executing localDatabaseBackup"
            local BACKUP_PATH="custom/${BACKUP_NAME}"
            localDatabaseBackup "$CMD" "$BACKUP_PATH" ${ARGS}
        fi

        if [[ $PUSH_TO_S3 == 1 ]]; then
            local LOCAL_BACKUP_PATH="$DB_BACKUPS_ROOT_FOLDER/$BACKUP_PATH"
            AWS_PROFILE=dev uploadS3Backup $LOCAL_BACKUP_PATH $BACKUP_NAME "custom"
        fi

        return $?
    fi

}

function restoreCustomBackupHelp() {
    local CMD=$1
    echo -e ""
    echo -e "restoreCustomBackup options 🚀"
    echo -e ""
    echo -e "Options:"
    printf "\t%${PADDING}s %s\n" "-h --help" "Display this help"
    printf "\t%${PADDING}s %s\n" "-s3 --restore-s3-backup" "Download the custom sql backup from a S3 bucket and restore it. The backup should be stored in dt-dev-nuke-and-pave-backups-us-west-2-bucket/backups-custom/ directory."
    printf "\t%${PADDING}s %s\n" "-s --skip-migrations" "Skip migrations after the database restore."
    printf "\t%${PADDING}s %s\n" "-u --db-username" "Database username to be used for mysqldump and mysql commands"
    printf "\t%${PADDING}s %s\n" "-p --db-password" "Adds the -p flag to mysqldump and mysql commands, so the user is prompted for password"
    exit 0
}

function restoreCustomBackup() {
    if [[ "$ENV" == "devops" || "$ENV" == "qa" || "$ENV" == "prod" ]]; then
        exitWithError "This command can be run on local and dev envs only"
    fi

    if [[ "$1" == -* ]]; then
        ARGS="$@"
    else
        BACKUP_NAME="$1"
        shift
        ARGS="$@"
    fi

    local CMD="restoreCustomBackup"

    if [ $REMOTE == 1 ]; then
        runRemoteTask "$CMD" "$BACKUP_NAME" "$ARGS"
    else
        # Set up defaults
        local DB_S3_BACKUP=0

        for flags in $ARGS; do
            case $flags in
            -h | --help)
                restoreCustomBackupHelp "$CMD"
                return $?
                ;;
            -s3 | --restore-s3-backup)
                shift
                DB_S3_BACKUP=1
                ;;
            esac
        done

        if [[ -z "$BACKUP_NAME" ]]; then
            exitWithError "Please provide a name to restore backup. Use $ ./bin/drata-cli -h for more info."
        fi

        echo ""
        echo "BACKUP_NAME: $BACKUP_NAME"
        echo "ARGS: $ARGS"
        echo ""

        local LOCAL_BACKUP_FOLDER="custom/${BACKUP_NAME}"
        local LOCAL_BACKUP_PATH="$DB_BACKUPS_ROOT_FOLDER/$LOCAL_BACKUP_FOLDER"

        if [ $DB_S3_BACKUP == 1 ]; then
            AWS_PROFILE=dev downloadS3Backup "$BACKUP_NAME" "$LOCAL_BACKUP_PATH" "custom"
        fi

        local CMD="restore"
        if [[ "$NODE_CONFIG_ENV" == "dev" || "$NODE_CONFIG_ENV" == dev-* ]]; then
            fancyEcho "Executing remoteDatabaseBackup"
            remoteDatabaseBackup "$CMD" "$LOCAL_BACKUP_FOLDER" ${ARGS}

            return $?
        else
            localDatabaseBackup "$CMD" "$LOCAL_BACKUP_FOLDER" ${ARGS}

            return $?
        fi
    fi
}

function nukeAndPaveHelp() {
    local CMD=$1
    echo -e ""
    echo -e "nukeAndPave options 🚀"
    echo -e ""
    echo -e "$CMD [options] <command>"
    echo -e ""
    echo -e "Options:"
    printf "\t%${PADDING}s %s\n" "-h --help" "Display this help"
    printf "\t%${PADDING}s %s\n" "-s3 --restore-s3-backup" "Download the latest nukeAndPave sql backup from a S3 bucket and restore it. The backup is built from the latest release branch."
    printf "\t%${PADDING}s %s\n" "-s3-main --restore-main-s3-backup" "Download the latest nukeAndPave sql backup from a S3 bucket and restore it. The backup is built from the latest main branch."
    printf "\t%${PADDING}s %s\n" "-l --restore-local-backup" "Use a local backup to restore your db, if no local backup a normal $CMD will be run"
    printf "\t%${PADDING}s %s\n" "-s --skip-migrations" "Skip migrations after the a database restore."
    printf "\t%${PADDING}s %s\n" "--load-socpilot" "Load Socpilot's data"
    printf "\t%${PADDING}s %s\n" "--socpilot-events <num>" "Specify how many event row you want to load. Will be rounded down to nearest multiple of 100k. Default: 100k"
    printf "\t%${PADDING}s %s\n" "--socpilot-env [qa|prod]" "Specify which Socpilot account you want to use. Default: qa"
    printf "\t%${PADDING}s %s\n" "-u --db-username" "Database username to be used for mysqldump and mysql commands"
    printf "\t%${PADDING}s %s\n" "-p --db-password" "Adds the -p flag to mysqldump and mysql commands, so the user is prompted for password"
    exit 0
}

nukeAndPaveBase() {
    if [ "$ENV" == "prod" ]; then
        exitWithError "This command can not be run on production"
    fi

    local CMD="$1"
    local SCRIPT="$2"
    shift 2
    local ARGS="$@"

    if [ $REMOTE == 1 ]; then
        runRemoteTask "$CMD" "$SCRIPT" "$ARGS"
    else
        # Set up defaults
        local DB_S3_BACKUP=0
        local DB_BACKUP_LOCAL=0
        local DB_BACKUP_FOLDER=""
        local LOAD_SOCPILOT=0
        local SOCPILOT_EVENTS=100000
        local SOCPILOT_ENV=qa

        # Update variables accordingly with flags
        for flags in $ARGS; do
            case $flags in
            -h | --help)
                nukeAndPaveHelp "$CMD"
                return $?
                ;;
            -s3 | --restore-s3-backup)
                shift
                DB_BACKUP_FOLDER="s3/$CMD"
                DB_S3_BACKUP=1
                S3_BACKUP_VERSION="release"
                ;;
            -s3-main | --restore-main-s3-backup)
                shift
                DB_BACKUP_FOLDER="s3/$CMD"
                DB_S3_BACKUP=1
                S3_BACKUP_VERSION="main"
                ;;
            -l | --restore-local-backup)
                shift
                DB_BACKUP_FOLDER="local/$CMD"
                DB_BACKUP_LOCAL=1
                ;;
            --load-socpilot)
                LOAD_SOCPILOT=1
                shift
                ;;
            --socpilot-events)
                shift
                # replace "k" with "000" so that we can use 100k as value
                if [[ "$1" =~ k$ ]]; then
                    SOCPILOT_EVENTS="${1%k}000"
                else
                    SOCPILOT_EVENTS="$1"
                fi
                ;;
            --socpilot-env)
                shift
                SOCPILOT_ENV="$1"
                ;;
            *)
                shift
                ;;
            esac
        done
        fancyEcho "Executing $CMD ..."

        # If running on remote environment
        if [[ "$NODE_CONFIG_ENV" == "devops" || "$NODE_CONFIG_ENV" == "qa" || "$NODE_CONFIG_ENV" == "prod" ]]; then
            if [ $DB_S3_BACKUP == 1 ] || [ $DB_BACKUP_LOCAL == 1 ]; then
                fancyEcho "Backup options are enabled only for dev environments"
                fancyEcho "Please refer to the documentation in notion: "
                fancyEcho "https://www.notion.so/drata/nukeAndPave-cached-and-Custom-Backups-14824465681e4f28b971248c8f2c1de3"
            fi
        else
            if [ $DB_S3_BACKUP == 1 ]; then
                # Call function to download latest s3 backup.
                local localBackUpFolder="$DB_BACKUPS_ROOT_FOLDER/$DB_BACKUP_FOLDER"
                AWS_PROFILE=dev downloadS3Backup "$CMD" "$localBackUpFolder" "$S3_BACKUP_VERSION"

                if [[ "$NODE_CONFIG_ENV" == "dev" || "$NODE_CONFIG_ENV" == dev-* ]]; then

                    if [ $LOAD_SOCPILOT == 1 ]; then
                        fancyEcho "Loading Socpilot dump is not yet supported with nukeAndPave s3 backup done on remote environments"
                        exit 1
                    fi

                    fancyEcho "Executing remoteDatabaseBackup"
                    remoteDatabaseBackup "restore" "$DB_BACKUP_FOLDER" ${ARGS}
                    local result=$?
                else
                    # Try to restore backup
                    localDatabaseBackup "restore" "$DB_BACKUP_FOLDER" ${ARGS}
                    local result=$?
                fi

                # If database backup restore is successful exit process, if not proceed
                if [ "$result" == 0 ]; then
                    if [ $LOAD_SOCPILOT == 1 ]; then
                        AWS_PROFILE=dev loadSocpilot $SOCPILOT_ENV $SOCPILOT_EVENTS
                    fi
                    exit 0
                fi
            fi

            if [ $DB_BACKUP_LOCAL == 1 ]; then
                # Try to restore local backup
                localDatabaseBackup "restore" "$DB_BACKUP_FOLDER" ${ARGS}
                local result=$?
                # If database backup restore is successful exit process, if not proceed
                if [ "$result" == 0 ]; then
                    exit 0
                fi
            fi
        fi

        # if no backup is used then do usual nukeAndPave call
        fancyEcho "Nuking ALL databases and then re-creating them"
        typeOrmClean &&
            preTypeOrm ${CODE_PATH}/scripts/write-typeorm-nuke-config &&
            NODE_OPTIONS="--max-old-space-size=8192" $TS_NODE -r tsconfig-paths/register ${CODE_PATH}/$SCRIPT $ARGS

        # Check if the previous command failed
        if [ $? -ne 0 ]; then
            echo "nukeAndPave script failed, exiting..."
            exit 1
        fi

        # No default backup for servers
        if [[ "$NODE_CONFIG_ENV" == "devops" || "$NODE_CONFIG_ENV" == dev* || "$NODE_CONFIG_ENV" == "qa" || "$NODE_CONFIG_ENV" == "prod" ]]; then
            return $?
        fi

        if [ $LOAD_SOCPILOT == 1 ]; then
            AWS_PROFILE=dev loadSocpilot $SOCPILOT_ENV $SOCPILOT_EVENTS
        fi

        # If local env then create a backup by default.
        fancyEcho "Preparing backup..."
        localDatabaseBackup "create" "local/$CMD" ${ARGS}
    fi
    return $?
}

function loadSocpilot() {
    SOCPILOT_ENV=$1
    NUM_EVENTS=$2
    ROWS_PER_FILE=100000
    NUM_FILES=$(($NUM_EVENTS / $ROWS_PER_FILE))
    DB_CONNECTION_OPTIONS="-udrata"

    if [ "$NODE_CONFIG_ENV" = "releasehub" ]; then
        DB_CONNECTION_OPTIONS="-h $DB_HOST -u$DB_USER -p$DB_PASSWORD"
    fi

    TENANT_FILES=("tenant_db_dump.sql.tgz")
    if [ "$NUM_FILES" -gt 0 ]; then
        for i in $(seq $NUM_FILES); do
            TENANT_FILES+=("tenant_events_$i.sql.tgz")
        done
    fi

    INCLUDE_OPTIONS=""
    for element in "${TENANT_FILES[@]}"; do
        INCLUDE_OPTIONS="${INCLUDE_OPTIONS} --include $element"
    done

    SOCPILOT_FOLDER="${DB_BACKUPS_ROOT_FOLDER}/socpilot"
    local DB_BACKUP_S3_BUCKET="dt-dev-nuke-and-pave-backups-us-west-2-bucket/socpilot/$SOCPILOT_ENV"
    rm ${SOCPILOT_FOLDER}/tenant/* || true && mkdir -p ${SOCPILOT_FOLDER}/tenant || true
    rm ${SOCPILOT_FOLDER}/regional/* || true && mkdir -p ${SOCPILOT_FOLDER}/regional || true

    fancyEcho "Downloading Socpilot tenant db dumps"
    aws s3 sync "s3://$DB_BACKUP_S3_BUCKET/tenant" "$SOCPILOT_FOLDER/tenant" --exact-timestamps --delete --exclude "*" $INCLUDE_OPTIONS

    if [ "$SOCPILOT_ENV" == "prod" ]; then
        ACCOUNT_ID="5f1f8fba-9139-4dfc-870f-c98519a0edb1"
    elif [ "$SOCPILOT_ENV" == "qa" ]; then
        ACCOUNT_ID="c0c5e01b-b31f-458e-964f-028554eb3abe"
    else
        exitWithError "Invalid env selected"
    fi

    # load tenant database
    mysql $DB_CONNECTION_OPTIONS -se "CREATE DATABASE IF NOT EXISTS \`$ACCOUNT_ID\`"
    archives=$(ls $SOCPILOT_FOLDER/tenant/*.sql.tgz)
    for archive in ${archives}; do
        tar -xf $archive -C "$SOCPILOT_FOLDER"

        sqlFilename=$(echo "$archive" | grep -oE '(\w+\.sql)')
        filePath=$SOCPILOT_FOLDER/tenant/$sqlFilename

        if [ "$NODE_CONFIG_ENV" = "releasehub" ]; then
            # remove statements that drata-app user can't execute
            sed -i '/SET @@SESSION/d' $filePath
            sed -i '/SET @@GLOBAL/d' $filePath
            sed -i 's/DEFINER=\S\+//g' $filePath
        fi

        fancyEcho "Loading $filePath"
        mysql $DB_CONNECTION_OPTIONS $ACCOUNT_ID < $filePath
        rm $filePath
    done

    fancyEcho "Downloading Socpilot data from regional db"
    aws s3 sync "s3://$DB_BACKUP_S3_BUCKET/regional" "$SOCPILOT_FOLDER/regional" --exact-timestamps --delete

    # load regional db data
    archives=$(ls $SOCPILOT_FOLDER/regional/*.tgz)
    for archive in ${archives}; do
        tar -xf $archive -C "$SOCPILOT_FOLDER"

        sqlFilename=$(echo "$archive" | grep -oE '(\w+\.sql)')
        filePath=$SOCPILOT_FOLDER/regional/$sqlFilename

        if [ "$NODE_CONFIG_ENV" = "releasehub" ]; then
            # remove statements that drata-app user can't execute
            sed -i '/SET @@SESSION/d' $filePath
            sed -i '/SET @@GLOBAL/d' $filePath
            sed -i 's/DEFINER=\S\+//g' $filePath
        fi

        fancyEcho "Loading $filePath"
        mysql $DB_CONNECTION_OPTIONS drata < $filePath
        rm $filePath
    done

    UPDATED_DB_HOST="127.0.0.1"
    if [ "$NODE_CONFIG_ENV" = "releasehub" ]; then
        UPDATED_DB_HOST=$DRATA_GLOBAL_AND_TENANT_RDS_DB_POOL_HOST
    fi

    fancyEcho "Updating Socpilot tenant in the regional database"
    SELECT_DATABASE_ID="SELECT id FROM tenant_database_host where dns_name = '$UPDATED_DB_HOST'"
    DATABASE_ID=$(mysql $DB_CONNECTION_OPTIONS drata -se "$SELECT_DATABASE_ID")
    mysql $DB_CONNECTION_OPTIONS drata -se "UPDATE account SET \
        db_host = '$UPDATED_DB_HOST', \
        fk_tenant_database_host_id = '$DATABASE_ID', \
        act_as_type = 3, \
        act_as_expires_at = '2100-01-01'"
    mysql $DB_CONNECTION_OPTIONS drata -se "UPDATE \`$ACCOUNT_ID\`.user u, entry e SET u.fk_entry_id = e.id WHERE e.id <> u.fk_entry_id AND e.email = u.email"
    mysql $DB_CONNECTION_OPTIONS drata -se "INSERT INTO account_entry_map (fk_entry_id, fk_account_id) SELECT id, '$ACCOUNT_ID' FROM entry WHERE email = '<EMAIL>'"
    mysql $DB_CONNECTION_OPTIONS $ACCOUNT_ID -se "UPDATE act_as a INNER JOIN company c ON a.id = c.fk_act_as_id SET a.type = 3, a.expires_at = '2100-01-01', a.deleted_at = NULL"
}

function schemaDrop() {
    if [ $REMOTE == 1 ]; then
        if [ "$ENV" == "prod" ]; then
            exitWithError "This command can not be ran on production"
        fi
        runRemoteTask schema:drop
    else
        fancyEcho "Dropping the schema\n"
        $TS_NODE ${NODE_MODULES}/typeorm/cli.js schema:drop
    fi
    return $?
}

function schemaLog() {
    if [ $REMOTE == 1 ]; then
        if [ "$ENV" == "prod" ]; then
            exitWithError "This command can not be ran on production"
        fi
        runRemoteTask schema:log
    else
        fancyEcho "Logging the schema\n"
        $TS_NODE ${NODE_MODULES}/typeorm/cli.js schema:log
    fi
    return $?
}

function schemaSync() {
    if [ $REMOTE == 1 ]; then
        if [ "$ENV" == "prod" ]; then
            exitWithError "This command can not be ran on production"
        fi
        runRemoteTask schema:sync
    else
        fancyEcho "Syncing the schema\n"
        $TS_NODE ${NODE_MODULES}/typeorm/cli.js schema:sync
    fi
    return $?
}

function seedConfig() {
    if [ $REMOTE == 1 ]; then
        exitWithWarn "This command is currently not supported running remotely on AWS"
    else
        fancyEcho "Configure the seeder\n"
        $TS_NODE ${NODE_MODULES}/typeorm-seeding/dist/cli.js config
    fi
    return $?
}

function seedRun() {
    if [ $REMOTE == 1 ]; then
        exitWithWarn "This command is currently not supported running remotely on AWS"
    else
        fancyEcho "Running the seeder\n"
        typeOrmClean &&
            preTypeOrm ${CODE_PATH}/scripts/write-typeorm-all-config &&
            $TS_NODE ${NODE_MODULES}/typeorm-seeding/dist/cli.js seed -n ormconfig-all.json "$@"
    fi
    return $?
}

function setVariables() {
    if [[ "$ENV" =~ ^dev-[0-9]{2,} ]]; then
        DEV_NUM=$(echo "$ENV" | cut -d '-' -f 2)
        ENV="dev"
        CLUSTER="dt-dev-api-${DEV_NUM}-ecs-cluster"
        TASKDEF="dt-dev-api-api-${DEV_NUM}-ecs-td"
    else
        CLUSTER="dt-${ENV}-api-green-ecs-cluster"
        TASKDEF="dt-${ENV}-api-api-green-ecs-td"
    fi

    CONTAINER="api"

    if [[ -n "${DRATA_CLI_TASKDEF_OVERRIDE}" ]]; then
        TASKDEF="${DRATA_CLI_TASKDEF_OVERRIDE}"
    fi
}

function publishPolicyHtml() {
    if [ $REMOTE == 1 ]; then
        runRemoteTask publishPolicyHtml "$@"
    else
        fancyEcho "Running publishPolicyHtml CLI...\n"
        $TS_NODE -r tsconfig-paths/register ${CODE_PATH}/scripts/publish-policy-html $@
    fi
    return $?
}

function publishWysiwygComments() {
    if [ $REMOTE == 1 ]; then
        runRemoteTask publishWysiwygComments "$@"
    else
        echo -e "Running publishWysiwygComments CLI...\n"
        $TS_NODE -r tsconfig-paths/register ${CODE_PATH}/scripts/publish-wysiwyg-comments $@
    fi
    return $?
}

function confirmRunAllBackfill() {
    # make arguments array-like
    local args=("$@")
    # the first item should be the backfill name
    local backfillName="${args[0]}"
    # get the remaining items
    local params="${args[@]:1}"

    # check for run all in params
    runAll=false
    if [[ "$params" == "-ra"* || "$params" == *"--run-all"* ]]; then
        if [[ "$params" == *"-ra=true"* || "$params" == *"--run-all=true"* || "$params" == *"-ra true"* || "$params" == *"--run-all true"* ]]; then
            runAll=true
        elif [[ "$params" == *"-ra=false"* || "$params" == *"--run-all=false"* || "$params" == *"-ra false"* || "$params" == *"--run-all false"* ]]; then
            runAll=false
        else
            # default if -ra or --run-all is present without true/false
            runAll=true
        fi
    fi

    # Prompt if run all is found and true
    if [ "$runAll" == true ]; then
        acceptable_responses=("yes" "y")
        echo -e "${GREEN}${BOLD}? ${WHITE}${BOLD}Are you sure you want to run ALL accounts for backfill ${backfillName}? ${CYAN}${BOLD}(y/n)${NORMAL}"
        read -t 30 -r confirmRunAll
        confirmRunAll=$(echo "$confirmRunAll" | tr '[:upper:]' '[:lower:]')
        case $confirmRunAll in
            yes|y)
                ;;
            *)
                echo -e "${RED}${BOLD}X ${YELLOW}${BOLD}Unable to run backfills for all accounts. Backfill has been cancelled.${NORMAL}"
                exit 0
                ;;
        esac
    fi
}

function backfill() {
    confirmRunAllBackfill "$@"

    if [ $REMOTE == 1 ]; then
        runRemoteTask backfill "$@"
    else
        # make arguments array-like
        local args=("$@")
        local ext="ts"
        if [ $IS_BUILD_MODE == 1 ]; then
            ext="js"
        fi

        # if we only have one argument, let's split on spaces
        if [ "$#" == 1 ]; then
            args=($(split "$@" " "))
        fi

        # the first item should be the backfill name
        local backfillFullName="${args[0]}"
        # split the file name and method if it exists
        local backfillName="${backfillFullName%%#*}"
        # If the "#" sign is not prsent, this will equal the name
        local backfillMethodName="${backfillFullName##*#}"

        # get the remaining items
        local params="${args[@]:1}"
        echo "NUM: ${#args[@]}"
        echo "backfillName: ${backfillName}"
        echo "params: ${params}"


        if [[ ${params} =~ "--run-as-workflow" || " ${params} " =~ [[:space:]]-w[[:space:]] || " ${params} " =~ [[:space:]]-w$ ]]; then
            fancyEcho "Running backfill CLI, trying mapped workflows only\n"
            $TS_NODE -r tsconfig-paths/register "${CODE_PATH}/app/worker/workflows/backfills/start-backfill-runner.${ext}" --backfill-name=$backfillName $params
        else
            fancyEcho "Running backfill CLI, trying /backfill/ path first, will try /recurring/ path if failure\n"
            local backfillRunPath=""
            local backfillPath="${CODE_PATH}/scripts/backfill/${backfillName}"
            local recurringPath="${CODE_PATH}/scripts/recurring/${backfillName}"
            local executorFilePath="${CODE_PATH}/scripts/backfill-executor.${ext}"

            # check where the file exists before running the backfill
            if [ -f "${backfillPath}.${ext}" ]; then
                backfillRunPath="${backfillPath}"
            elif [ -f "${recurringPath}.${ext}" ]; then
                backfillRunPath="${recurringPath}"
            else
                fancyEcho "Backfill not found: ${backfillName}.${ext}\n"
            fi

            # run the backfill with or without executor as needed
            if [ "$backfillRunPath" != "" ] && [ "$backfillMethodName" != "$backfillName" ]; then
                $TS_NODE -r tsconfig-paths/register "${executorFilePath}" "${backfillRunPath}#${backfillMethodName}" $params
            elif [ "$backfillRunPath" != "" ]; then
                $TS_NODE -r tsconfig-paths/register "${backfillRunPath}" $params
            fi
        fi
    fi
    return $?
}

function airtable() {
    echo "airtable $1"
    if [ $REMOTE == 1 ]; then
        runRemoteTask airtable "$@"
    else
        fancyEcho "Running airtable CLI...\n"
        $TS_NODE -r tsconfig-paths/register ${CODE_PATH}/scripts/airtable-sync $@
    fi
    return $?
}

function grc_sync() {
    echo "grc_sync $1"

    if [ $REMOTE == 1 ]; then
        runRemoteTask grc_sync "$@"
    else
        fancyEcho "Running grc-sync CLI...\n"
        $TS_NODE -r tsconfig-paths/register ${CODE_PATH}/scripts/grc/grc-sync "$@"
    fi

    return $?
}

function framework2() {
    echo "framework2 $1"

    if [ $REMOTE == 1 ]; then
        runRemoteTask framework2 "$@"
    else
        fancyEcho "Running framework2 CLI...\n"
        $TS_NODE -r tsconfig-paths/register ${CODE_PATH}/scripts/grc/frameworks2-convert "$@"
    fi

    return $?
}

function grc_validate() {
    echo "grc_validate $1"

    if [ $REMOTE == 1 ]; then
        runRemoteTask grc_validate "$@"
    else
        fancyEcho "Running grc-validate CLI...\n"
        $TS_NODE -r tsconfig-paths/register ${CODE_PATH}/scripts/grc/grc-validate "$@"
    fi

    return $?
}

function airtable_v2() {
    if [ $REMOTE == 1 ]; then
        runRemoteTask airtable_v2 "$@"
    else
        # make arguments array-like
        local args=("$@")
        local ext="ts"
        if [ $IS_BUILD_MODE == 1 ]; then
            ext="js"
        fi

        # if we only have one argument, let's split on spaces
        if [ "$#" == 1 ]; then
            args=($(split "$@" " "))
        fi

        local airtableScriptName="${args[0]}"

        fancyEcho "Running airtable CLI..."

        local airtableScriptPath="${CODE_PATH}/scripts/airtable/${airtableScriptName}.${ext}"

        if [ -f $airtableScriptPath ]; then
            fancyEcho "Running airtable script: ${airtableScriptName}"
            $TS_NODE -r tsconfig-paths/register ${CODE_PATH}/scripts/airtable/${airtableScriptName} $@
        else
            fancyEcho "\nAirtable script not found: ${airtableScriptName}.${ext}\n"
        fi
    fi
    return $?
}

function generateMDM() {
    if [ $REMOTE == 1 ]; then
        exitWithWarn "This command does not support running remotely on AWS"
    fi
    fancyEcho "Generating boiler plate for a new MDM\n"
    node --max-http-header-size=16384 ${BUILD_DIRECTORY}/cli/mdm/mdm-boiler-plate $@
    return $?
}

function createCertificates() {
    #check that command is not being run on a remote environment

    if [ $REMOTE == 1 ]; then
        exitWithError "This command can not be run on remote env"
    fi

    #set variables
    local MTLS_TARGET_FOLDER="$MTLS_ROOT_FOLDER/$MTLS_PATH"
    local CA_CERTIFICATE=$(yq eval '.mtls.ca' ${LOCAL_DEV})
    local CLIENT_KEY_CERTIFICATE=$(yq eval '.mtls.clientKey' ${LOCAL_DEV})
    local CLIENT_CERTIFICATE=$(yq eval '.mtls.clientCert' ${LOCAL_DEV})

    fancyEcho "Creating/updating mtls directory..."
    #Preparing folder and certificates
    mkdir -p ${MTLS_TARGET_FOLDER} || true

    #create ca certificate
    fancyEcho "Creating/updating ca-cert.pem certificate..."
    touch "${MTLS_ROOT_FOLDER}/ca-cert.pem"
    echo "${CA_CERTIFICATE}" > ${MTLS_ROOT_FOLDER}/ca-cert.pem

    #create client key
    fancyEcho "Creating/updating client.key.pem certificate..."
    touch "${MTLS_ROOT_FOLDER}/client.key.pem"
    echo "${CLIENT_KEY_CERTIFICATE}" > ${MTLS_ROOT_FOLDER}/client.key.pem

    #create client certficate
    fancyEcho "Creating/updating client.crt certificate..."
    touch "${MTLS_ROOT_FOLDER}/client.crt"
    echo "${CLIENT_CERTIFICATE}" > ${MTLS_ROOT_FOLDER}/client.crt
}

function cleanup() {
    unset IS_DRATA_CLI
}

function sigintExit() {
    fancyEcho "Process exited"
    exit 1;
}

function setupEnviroment() {
    if [ $ENV == "localdev" ] || [ $ENV == "localdev.region.eu" ] || [ $ENV == "localdev.region.apac" ]; then
        export NODE_CONFIG_ENV=$ENV
    else
        REMOTE=1
        setVariables
    fi
}

function logCliCommandStart() {
    node ${BUILD_DIRECTORY}/cli/utils/log-cli-command.js --event start \
        --cmd "$COMMAND" \
        --command-id "$COMMAND_ID"
}

function logCliCommandEnd() {
    node ${BUILD_DIRECTORY}/cli/utils/log-cli-command.js --event end \
        --cmd "$COMMAND" \
        --command-id "$COMMAND_ID" \
        --exit-code "$1"
}


function buildCatalog() {
    if [ $REMOTE == 1 ]; then
        exitWithWarn "This command does not support running remotely on AWS"
    else
        fancyEcho "Starting to run buildCatalog"
        $TS_NODE -r tsconfig-paths/register ${CODE_PATH}/scripts/build-catalog "$CATALOG_PATH" "$PROFILE_PATH" "$@"
    fi
}

function testSalesforceInvite() {
    $TS_NODE -r tsconfig-paths/register ${CODE_PATH}/scripts/test-salesforce-invite
}

function runCustomData() {
    if [ $REMOTE == 1 ]; then
        runRemoteTask custom-data "$@"
    else
        fancyEcho "Running custom data CLI...\n"
        node --max-http-header-size=16384 ${BUILD_DIRECTORY}/cli/custom-data.cli.js $@
    fi
    return $?
}

function runSearchEngineCreate() {
    if [ $REMOTE == 1 ]; then
        runRemoteTask search-engine:create "$@"
    else
        fancyEcho "Running search engine create CLI...\n"
        node --max-http-header-size=16384 ${BUILD_DIRECTORY}/cli/search-engine-create.cli.js $@
    fi
    return $?
}

function runSearchEngineSync() {
    if [ $REMOTE == 1 ]; then
        runRemoteTask search-engine:sync "$@"
    else
        fancyEcho "Running search engine sync CLI...\n"
        node --max-http-header-size=16384 ${BUILD_DIRECTORY}/cli/search-engine-sync.cli.js $@
    fi
    return $?
}

function updateSlackDrataAppHomeTab() {
    if [ $REMOTE == 1 ]; then
        runRemoteTask updateSlackDrataAppHomeTab "$@"
    else
        fancyEcho "Running update Slack Drata app home tab CLI...\n"
        $TS_NODE -r tsconfig-paths/register ${CODE_PATH}/scripts/update-slack-drata-app-home-tab $@
    fi
    return $?
}

function runTestSnapshotCollector() {
    if [ $REMOTE == 1 ]; then
        exitWithWarn "This command does not support running remotely"
    else
        fancyEcho "Running test snapshot collector...\n"
        $TS_NODE -r tsconfig-paths/register ${CODE_PATH}/scripts/test-snapshot-collector "$@"
    fi
    return $?
}

function runSyncComparisonCollector() {
    if [ $REMOTE == 1 ]; then
        exitWithWarn "This command does not support running remotely"
    else
        fancyEcho "Running sync comparison collector...\n"
        $TS_NODE -r tsconfig-paths/register ${CODE_PATH}/scripts/sync-comparison-collector "$@"
    fi
    return $?
}

function runOpenSearchEngineClientTest() {
    if [ $REMOTE == 1 ]; then
        runRemoteTask runOpenSearchEngineClientTest "$@"
    else
        fancyEcho "Running open search engine client test CLI...\n"
        node --max-http-header-size=16384 ${BUILD_DIRECTORY}/cli/open-search-client-test.js $@
    fi
    return $?
}

function runTestNotificationPlatform() {
    if [ $REMOTE == 1 ]; then
        runRemoteTask runTestNotificationPlatform
    else
        fancyEcho "Running notification platform test...\n"
        $TS_NODE -r tsconfig-paths/register ${CODE_PATH}/scripts/notification-platform-test
    fi
    return $?
}

trap cleanup EXIT
trap sigintExit SIGINT

logCliCommandStart
# this allows us to determine the app context
export IS_DRATA_CLI="1"

# set the path to the code
setCodePath

# Loop through arguments and process them
ARG_EXIT_CODE=0
while true; do
    case $1 in
    -h | --help)
        help
        shift
        ARG_EXIT_CODE=$?
        break
        ;;
    -e | --environment)
        ENV="$2"
        setupEnviroment
        shift 2
        continue
        ;;
    -r | --region)
        REGION="$2"
        shift 2
        continue
        ;;
    --cmd)
        RUN_ARBITRARY_CMD=1
        shift
        arg_command="$1"
        shift
        arg_command_args="$@"
        runArbitraryCommand "$arg_command" "$arg_command_args"
        ARG_EXIT_CODE=$?
        break
        ;;
    "autopilot")
        shift
        runAutopilot "$@"
        ARG_EXIT_CODE=$?
        break
        ;;
    "autopilot2")
        shift
        runAutopilot2 "$@"
        ARG_EXIT_CODE=$?
        break
        ;;
    "schedule-workflow")
        shift
        runTemporalWorkflowScheduleCommands "$@"
        ARG_EXIT_CODE=$?
        break
        ;;
    "trigger-temporal-workflow")
        shift
        runTemporalWorkflow "$@"
        ARG_EXIT_CODE=$?
        break
        ;;
    "syncs")
        shift
        runSynchronizations "$@"
        ARG_EXIT_CODE=$?
        break
        ;;
    "cron")
        shift
        runCron "$@"
        ARG_EXIT_CODE=$?
        break
        ;;
    "complianceChecks")
        shift
        runComplianceChecks "$@"
        ARG_EXIT_CODE=$?
        break
        ;;
    "connectionRegions")
        shift
        runConnectionRegions "$@"
        ARG_EXIT_CODE=$?
        break
        ;;
    "controlEvidencePackage")
        shift
        runControlEvidencePackage "$@"
        ARG_EXIT_CODE=$?
        break
        ;;
    "custom-data")
        shift
        runCustomData "$@"
        ARG_EXIT_CODE=$?
        break
        ;;
    "encryption")
        shift
        runEncryption "$@"
        ARG_EXIT_CODE=$?
        break
        ;;
    "notifications:company")
        shift
        runCompanyNotifications "$@"
        ARG_EXIT_CODE=$?
        break
        ;;
    "swagger:docs")
        shift
        runSwaggerCli "$@"
        ARG_EXIT_CODE=$?
        break
        ;;
    "typeorm:init")
        shift
        typeOrmClean && preTypeOrm ${CODE_PATH}/scripts/write-typeorm-config
        ARG_EXIT_CODE=$?
        break
        ;;
    "migration:check")
        shift
        migrationCheck
        ARG_EXIT_CODE=$?
        break
        ;;
    "migration:check:tenant")
        shift
        migrationCheckTenant
        ARG_EXIT_CODE=$?
        break
        ;;
    "migration:generate")
        shift
        migrationGenerate "$@"
        ARG_EXIT_CODE=$?
        break
        ;;
    "migration:generate:tenant")
        shift
        migrationGenerateTenant "$@"
        ARG_EXIT_CODE=$?
        break
        ;;
    "migration:revert")
        migrationRevert
        ARG_EXIT_CODE=$?
        break
        ;;
    "migration:revert:tenant")
        shift
        migrationRevertTenant "$@"
        ARG_EXIT_CODE=$?
        break
        ;;
    "migration:revert:all")
        migrationRevert && migrationRevertTenant "$@"
        ARG_EXIT_CODE=$?
        break
        ;;
    "migration:run")
        migrationRun
        ARG_EXIT_CODE=$?
        break
        ;;
    "migration:run:tenant")
        shift
        migrationRunTenant "$@"
        ARG_EXIT_CODE=$?
        break
        ;;
    "migration:run:all")
        migrationRun && migrationRunTenant "$@"
        ARG_EXIT_CODE=$?
        break
        ;;
    "restoreCustomBackup")
        shift
        eval set -- "$@"
        restoreCustomBackup "$@"
        ARG_EXIT_CODE=$?
        break
        ;;
    "createCustomBackup")
        shift
        eval set -- "$@"
        createCustomBackup "$@"
        ARG_EXIT_CODE=$?
        break
        ;;
    "nukeAndPave")
        shift
        nukeAndPaveBase "nukeAndPave" "scripts/nuke-and-pave" "$@"
        ARG_EXIT_CODE=$?
        break
        ;;
    "nukeAndPaveAnnual")
        shift
        nukeAndPaveBase "nukeAndPaveAnnual" "scripts/nuke-and-pave-annual" "$@"
        ARG_EXIT_CODE=$?
        break
        ;;
    "nukeAndPaveMax")
        shift
        nukeAndPaveBase "nukeAndPaveMax" "scripts/nuke-and-pave-max" "$@"
        ARG_EXIT_CODE=$?
        break
        ;;
    "nukeAndPaveEmpty")
        shift
        nukeAndPaveBase "nukeAndPaveEmpty" "scripts/nuke-and-pave-empty" "$@"
        ARG_EXIT_CODE=$?
        break
        ;;
    "schema:drop")
        schemaDrop
        ARG_EXIT_CODE=$?
        break
        ;;
    "schema:log")
        schemaLog
        ARG_EXIT_CODE=$?
        break
        ;;
    "schema:sync")
        schemaSync
        ARG_EXIT_CODE=$?
        break
        ;;
    "seed:config")
        seedConfig
        ARG_EXIT_CODE=$?
        break
        ;;
    "seed:run")
        shift
        seedRun "$@"
        ARG_EXIT_CODE=$?
        break
        ;;
    "publishPolicyHtml")
        shift
        publishPolicyHtml "$@"
        ARG_EXIT_CODE=$?
        break
        ;;
    "publishWysiwygComments")
        shift
        publishWysiwygComments "$@"
        ARG_EXIT_CODE=$?
        break
        ;;
    "backfill")
        shift
        backfill "$@"
        ARG_EXIT_CODE=$?
        break
        ;;
    "airtable_v2")
        shift
        airtable_v2 "$@"
        ARG_EXIT_CODE=$?
        break
        ;;
    "airtable")
        shift
        airtable "$@"
        ARG_EXIT_CODE=$?
        break
        ;;
    "grc_sync")
        shift
        grc_sync "$@"
        ARG_EXIT_CODE=$?
        break
        ;;
    "framework2")
        shift
        framework2 "$@"
        ARG_EXIT_CODE=$?
        break
        ;;
    "grc_validate")
        shift
        grc_validate "$@"
        ARG_EXIT_CODE=$?
        break
        ;;
    "generateMDM")
        shift
        generateMDM "$@"
        break
        ;;
    "buildCatalog")
        shift
        buildCatalog "$@"
        break
        ;;
    "createCertificates")
        shift
        createCertificates "$@"
        ARG_EXIT_CODE=$?
        break
        ;;
    "testSalesforceInvite")
        testSalesforceInvite
        ARG_EXIT_CODE=$?
        break
        ;;
    "updateSlackDrataAppHomeTab")
        shift
        updateSlackDrataAppHomeTab "$@"
        ARG_EXIT_CODE=$?
        break
        ;;
    "search-engine:create")
        shift
        runSearchEngineCreate "$@"
        ARG_EXIT_CODE=$?
        break
        ;;
    "search-engine:sync")
        shift
        runSearchEngineSync "$@"
        ARG_EXIT_CODE=$?
        break
        ;;
    "runOpenSearchEngineClientTest")
        shift
        runOpenSearchEngineClientTest "$@"
        ARG_EXIT_CODE=$?
        break
        ;;
    "testNotificationPlatform")
        runTestNotificationPlatform
        ARG_EXIT_CODE=$?
        break
        ;;
    "test-snapshot-collector")
        shift
        runTestSnapshotCollector "$@"
        ARG_EXIT_CODE=$?
        break
        ;;
    "sync-comparison-collector")
        shift
        runSyncComparisonCollector "$@"
        ARG_EXIT_CODE=$?
        break
        ;;
    "generateTenants")
        # Local-only: block remote use (-e/--environment)
        if [[ -n "${ENV}" && "${ENV}" != "localdev" ]]; then
            echo "generateTenants is local-only and not available with -e/--environment."
            ARG_EXIT_CODE=1
            break
        fi
        shift
        node --max-http-header-size=16384 ${BUILD_DIRECTORY}/cli/generate-tenants.cli.js "$@"
        ARG_EXIT_CODE=$?
        break
        ;;
    *)
        echo "Command: $1 not recognized."
        break
        ;;
        
    esac
done

logCliCommandEnd $ARG_EXIT_CODE

# if running remote command, use that output
if [[ $REMOTE == 0 ]]; then
    if [[ ARG_EXIT_CODE -eq 0 ]]; then
        fancyEcho "Finished!!! ✨"
    else
        exitWithError "Something went wrong 💔"
    fi
fi

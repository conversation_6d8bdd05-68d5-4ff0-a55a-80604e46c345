accountMigration:
    region: 'us-west-2'
    s3:
        bucketName: 'dt-devops-tenant-migration-us-west-2'

accountSnapshot:
    s3:
        bucketName: 'dt-devops-tenant-migration-us-west-2'

api:
    name: '[DEVOPS] Drata'

apideck:
    oauth:
        redirect: 'https://app.devops.drata.com/callback/cloud'
asana:
    oauth2:
        redirect: 'https://app.devops.drata.com/callback/asana'

atlassian:
    oauth2:
        redirect: 'https://app.devops.drata.com/callback/atlassian'

aws:
    accountId: '************'
    eventbridge:
        enabled: true
        busName: 'dt-devops-drata-shared-eb-bus'
    s3:
        appBucket: 'dt-devops-app-us-west-2'
        cdnBucket: 'dt-devops-cdn-us-west-2'
        eventBucket: 'dt-devops-event-us-west-2'
        autopilotBucket: 'dt-devops-autopilot-data-us-west-2'
    runner:
        subnets:
            [
                'subnet-0ab4d65df9f534974',
                'subnet-0ccebbdd96d0cd6a6',
                'subnet-04f3ddc0b26d9b862',
                'subnet-01ef24de321720e77',
            ]
        securityGroups: 'sg-0ba05fed24a8bff76'
        name: 'api'
        region: 'us-west-2'

azureDevops:
    boards:
        oauth2:
            redirect: 'https://app.devops.drata.com/callback/azuredevops-boards'
    repos:
        oauth2:
            redirect: 'https://app.devops.drata.com/callback/azuredevops-repos'
    devops:
        oauth2:
            redirect: 'https://app.devops.drata.com/callback/azuredevops'

bitbucket:
    oauth2:
        key: '9n8KK43pTgBeBw5ZMM'

checkr:
    oauth2:
        key: '3e69c98a9e539e4a7186c18a'

clickup:
    oauth2:
        redirect: 'https://app.devops.drata.com/callback/clickup'

curricula:
    api:
        url: https://dev.curricula.com/api/v1
    oauth2:
        key: 'G4bZwgypl6NkL10zWEnzae23DrVd7xBv'
        redirect: 'https://app.devops.drata.com/callback/curricula'
        tokenUrl: 'https://dev.curricula.com/oauth/token'

db:
    ssl: true

datadog:
    enabled: true

digitalocean:
    oauth2:
        key: '****************************************************************'
        redirect: 'https://app.devops.drata.com/callback/digitalocean'

github:
    versionControl:
        applicationId: 100937 # Drata [ACME] Free Org [DEV/TESTING]
        # applicationId: 117128 # Drata [ACME] Paid Org [DEV/TESTING]
    issues:
        applicationId: 100938 # Drata [ACME] Free Org [DEV/TESTING]
        # applicationId: 117130 # Drata [ACME] Paid Org [DEV/TESTING]
    code:
        applicationId: 821119 # Drata [ACME] Free Org [DEV/TESTING]

gitlab:
    oauth2:
        redirect: 'https://app.devops.drata.com/callback/gitlab'

gitlabIssues:
    oauth2:
        redirect: 'https://app.devops.drata.com/callback/gitlab-issues'

jira:
    oauth2:
        key: 'JCT7KGNJQtxMN9HdCH1t2odEQjDuN6f1'
        redirect: 'https://app.devops.drata.com/callback/jira'

jwt:
    cookie:
        domain: '.drata.com'
        webRefreshTokenKey: 'web-refresh-token-devops'
        siteAdminRefreshTokenKey: 'admin-refresh-token-devops'

karmacheck:
    api:
        webhookDomain: 'https://api.devops.drata.com'

linear:
    oauth2:
        redirect: 'https://app.devops.drata.com/callback/linear'

lightrun:
    enabled: false

mtls:
    commonName: api-ap.int.devops.drata.net
    ca: |-
        -----BEGIN CERTIFICATE-----
        MIIFmzCCA4OgAwIBAgIRAMkEt4llGsj+3c9q7enRDF4wDQYJKoZIhvcNAQENBQAw
        ZzELMAkGA1UEBhMCVVMxEzARBgNVBAoMCkRyYXRhIEluYy4xEzARBgNVBAgMCkNh
        bGlmb3JuaWExGjAYBgNVBAMMEURyYXRhIFJvb3QgQ0EgUlNBMRIwEAYDVQQHDAlT
        YW4gRGllZ28wHhcNMjMwNTIyMTg0MjM2WhcNMjkwNTIyMTk0MjM2WjBnMQswCQYD
        VQQGEwJVUzETMBEGA1UECgwKRHJhdGEgSW5jLjETMBEGA1UECAwKQ2FsaWZvcm5p
        YTEaMBgGA1UEAwwRRHJhdGEgUm9vdCBDQSBSU0ExEjAQBgNVBAcMCVNhbiBEaWVn
        bzCCAiIwDQYJKoZIhvcNAQEBBQADggIPADCCAgoCggIBAJ7xEjfbR8kG/URmDofc
        8PI0UJTIU+K17zjx2aJRYCBYg440Ix4AbuQO4ibs21Xo/la/pVshlId6RMqfeJT2
        kkt+or4HDht95gJksVazhDhzQhLaj2ZDuKlAo5XOTjv7J8UZTEXv4DiqkDVcmJmm
        h5csQPijikDf84pZl6y4iAhKrJJFYjb9oeYFHfXsv8FpswyGX4TOPKv594rLy42I
        s4Kg+8vCBVZ9oPwIF4c2dksIls1YIoqrV7EiqAYSoF3smD2vgl3IMPDSSzhgVVCp
        ucNNXaLw3uNsorN25NgC6/SkK2UQsDUkOoKs26cl8M9vliEPbOHzOmcEn+W9E1WN
        fWO4hwi3/lOfYJvU9+7Ylo+nbISKoDrWxEJj+MlC4iVHMqtG7By+PfAYEWgQIMUQ
        H4wc8LpUa4sTyGbbxsJyBXaqPM3hn/uAuCbZFf/iGzUua0b1HJp793scnXuoKlq0
        bCsc61O1CWsAIPeAuq5+ScUe33gq7qIdQZNntybJkQ7nPxQ1aCUbdgpGMOAhJm9a
        XfL1Efqg/sU8qDDnMR/V0eKDqUQJ0wC5PTVY+P3q97SgJ8aX0sApzZgouOk+wAYa
        AyljbNmfMbqUSDIY5wsB8234sqIQC14mvdUS/g9nMOpsPy1DYmL+IHVf70bjgCgA
        YJfamUML3GUYNum0gD3rTsIjAgMBAAGjQjBAMA8GA1UdEwEB/wQFMAMBAf8wHQYD
        VR0OBBYEFP61uOUTSbcR40bhNfZFzNNZtPyuMA4GA1UdDwEB/wQEAwIBhjANBgkq
        hkiG9w0BAQ0FAAOCAgEAWL3buKFd/+q5GYupJ7xe4Cgz9WNWrBuk1qjSkfIR6JG7
        MrfADkVMBjKJwRwdA0ANkKpYR1ZbY3C18MBnuTPmJgrMQR/FglbxJLS6XB76Y9Te
        epvk6NOoWST9iO8bDLSQhkhvtqjl1UvdUJvmLfp8RaFg4z3HcC3SNu8qpZVEVbey
        W0t43uM5DSWIqsmf2LYwI5fEZnj3OTU44wLUSpbxA/lHeOCbJe/tajsaMl8ZjMNk
        HqiMYcsOkd1gptcz2ghUSHUvj4X00s2Zc70pMr7nMu8jR7XMP9DtfQWWqcI5bAj3
        iiFojiSIMMyafSHx6pX2uJNvwtjZxY6jGjufwi0S5EJUf+19PX0JO1l+salv43Fp
        ZrF0A5CxXgbkbFneRURabELv//FyQge6SJ1A3Ti8/ecpn3o4J1DHZbgSLj/hDuur
        n/ZFaNHGGpvOR9q5c0AnMSBm1BSnGpmMacuiPB48xNwNaHYblAHmZ/Dd80qgK2ea
        ZDQTi+cfQ+E+OO4gDLpLvvSbAWtMLpngjvauYBHEuGwwfGMIP7xCQkGi34d6Y1si
        bHo8ed0b0/x9jX8aKtxMVvCqekY0LlPid5EPPT7cb3vXzUPk+jC0GIPuIoNQb+cz
        lvlEdzOTEUC2FhZuBMbYbSk7wO0dJWRpbXBNYyM1shrlRx3C2EGUmBWCP1xWl+g=
        -----END CERTIFICATE-----

opensearch:
    aws:
        region: us-west-2
        node: https://opensearch.int.devops.drata.net

riskManagement:
    assessmentReport: 'dist/app/risk-management/docs/assessmentReportTemplate.docx'

riskAssessment:
    assessmentReport: 'dist/app/users/risk-assessment/docs/assessmentReportTemplate.docx'

redis:
    enable_ssl: true

safebase:
    sso:
        jwtIssuer: 'https://api.devops.drata.com'

salesforce:
    api:
        url: 'https://drata--dratafull1.sandbox.lightning.force.com/lightning'
    connection:
        oauth2:
            redirect: http://app.devops.drata.com/callback/salesforce

slack:
    enabled: true

server:
    allowedCns:
        - 'orchestrator.int.devops.drata.net'

temporal:
    clusterAddress: 'us-west-2.aws.api.temporal.io:7233'
    namespace: 'devops.wb2on'
    autopilot:
        namespace: 'devops.wb2on'
        queueName: 'autopilot_queue_devops'

queue:
    assessmentCompleted:
        name: 'ap-devops-us-west-2-api-ap2-queue'
        url: 'https://sqs.us-west-2.amazonaws.com/************/ap-devops-us-west-2-api-ap2-queue'

region:
    NA:
        api: https://api.devops.drata.com
        adminApp: https://admin.devops.drata.com

url:
    publicApi: 'https://public-api.devops.drata.com'
    trustCenterPartnerApi: 'https://tc-api.devops.drata.com'
    webApp: 'https://app.devops.drata.com'
    drataApp: 'https://app-multiverse.devops.drata.com'
    adminApp: 'https://admin.devops.drata.com'
    appCdn: 'https://img-devops.dratacdn.com'

workos:
    redirectUri: 'https://api.devops.drata.com/auth/sso/callback'

zoho:
    oauth2:
        redirect: 'https://app.devops.drata.com/callback/zoho'

zoom:
    oauth2:
        key: AIgxTswBSS6i2aj9F935ig
        redirect: https://app.devops.drata.com/callback/zoom

# Time at which the tenant reminder workflow should run. Temporarily set for 1 am CTZ
remindersSchedule:
    hour: 6
    minute: 0
    timezone: America/Chicago

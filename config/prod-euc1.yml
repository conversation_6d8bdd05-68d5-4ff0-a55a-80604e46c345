accountMigration:
    region: 'eu-central-1'
    s3:
        bucketName: 'dt-prod-tenant-migration-eu-central-1-bucket'

accountSnapshot:
    s3:
        bucketName: 'dt-prod-tenant-migration-eu-central-1-bucket'

amply:
    ipUuid1: 'eaae2d65-3290-434b-8574-e2c0d3604759'
    ipUuid2: '74eb0d28-0e19-41ec-912c-c4c91f63ed62'
    template:
        main: '2be5cd6c-cd0a-4eb4-a8b2-898f158b7bc0'
        controlStatus: 'c690d306-ee61-4577-a659-2e5481f4e36b'
        trustCenter: 'fe43293d-3887-453f-8033-9106d8c7ef0e'

apideck:
    oauth:
        redirect: 'https://app.drata.com/callback/cloud'
api:
    name: 'Dr<PERSON>'
    autopilotTenantBatchSize: 5
    autopilotSerializedGroupDomains:
        ion:
            [
                'ionmarkets.com',
                'ioncommodities.com',
                'iongroup-deprecated.com',
                'ionanalytics.com',
                'consultants.cedacri.it',
            ]
    connectionMetricsLogging: true

asana:
    oauth2:
        key: '****************' # PROD Asana App
        redirect: 'https://app.drata.com/callback/asana'

atlassian:
    oauth2:
        redirect: 'https://app.drata.com/callback/atlassian'

aws:
    accountId: '************'
    eventbridge:
        enabled: false
        busName: 'dt-prod-drata-shared-eb-bus'
        region: 'eu-central-1'
    s3:
        appBucket: 'dt-prod-app-eu-central-1-bucket'
        cdnBucket: 'dt-prod-cdn-eu-central-1-bucket'
        eventBucket: 'dt-prod-event-eu-central-1-bucket'
        autopilotBucket: 'dt-prod-autopilot-data-eu-central-1-bucket'
        region: 'eu-central-1'
    runner:
        subnets:
            ['subnet-0374395b981339fd7', 'subnet-0661a6b7d5794d824', 'subnet-0c4f317a015960693']
        securityGroups: 'sg-0c00b75f72946b273'
        name: 'api'
        region: 'eu-central-1'

azureDevops:
    boards:
        oauth2:
            redirect: 'https://app.drata.com/callback/azuredevops-boards'
    repos:
        oauth2:
            redirect: 'https://app.drata.com/callback/azuredevops-repos'
    devops:
        oauth2:
            redirect: 'https://app.drata.com/callback/azuredevops'

bitbucket:
    oauth2:
        key: 'Kr9Xw7yMGT8mSr9YvF'
    code:
        oauth2:
            key: 'RwYnYhaq2RFNpB2XSD'

certn:
    api:
        url: 'https://api.certn.co'
    display:
        url: 'https://app.certn.co/hr/applications/'

checkr:
    api:
        url: 'https://api.checkr.com/v1'
    display:
        url: 'https://dashboard.checkr.com/candidates'
    oauth2:
        key: '3df7fd1d1ceb34aa0a0851ff'
        redirect: 'https://app.drata.com/callback/checkr'
        tokenUrl: 'https://api.checkr.com/oauth/tokens'

clickup:
    oauth2:
        key: '9DIPXT201W8OYGX9S7GGDYIRVYBL6F5E'
        redirect: 'https://app.drata.com/callback/clickup'

confluence:
    oauth2:
        key: 'B47Mp3B1cA85GTDFjsC4RNcfR2yI0Dnr'
        redirect: 'https://app.drata.com/callback/confluence'

curricula:
    api:
        url: https://mycurricula.com/api/v1
    oauth2:
        key: 'AkqO9D8mB36y7jE7b0dgeL5rz4ZXwRbo'
        redirect: 'https://app.drata.com/callback/curricula'
        tokenUrl: 'https://mycurricula.com/oauth/token'

cube:
    api:
        url: 'https://exciting-mole.aws-us-west-2-t-18712.cubecloudapp.dev/cubejs-api/v1'

datadog:
    enabled: true

db:
    ssl: true

decodable:
    lambda:
        host: 'decodable.int.euc1.prod.drata.net'
        version: 'v1'
        method: 'POST'

digitalocean:
    oauth2:
        key: '****************************************************************'
        redirect: 'https://app.drata.com/callback/digitalocean'

docraptor:
    testMode: false

docusign:
    api:
        clientId: b06fcb28-d7d6-4f65-8575-a924cd49df33
        server: https://account.docusign.com
        webhookBaseUrl: https://api.eu.drata.com
        ndaBaseUrl: https://app.docusign.com/documents/details
    userAccessReview:
        baseUrl: admin.docusign.com # needs confirmation

gitlab:
    oauth2:
        key: 'fcf3078d8f5b1e19b229b148edab7e3b4de2d0ecc7641adeca3e0623974f7a31'
        redirect: 'https://app.drata.com/callback/gitlab'

gitlabIssues:
    oauth2:
        key: '8026033a0c58201b447e3557eea0138ecb38e45077fd2661d2768fc453e92c9d'
        redirect: 'https://app.drata.com/callback/gitlab-issues'

github:
    versionControl:
        applicationId: 89446
    issues:
        applicationId: 95115
    code:
        applicationId: 863127

goodhire:
    api:
        url: https://api.goodhire.com/v1
    display:
        url: https://app.goodhire.com/member/report?CandidateId=

google:
    clientEmail: '<EMAIL>'
    oauth:
        key: '************-onfe4lo3578qtr2i3ejnavn58mhcj4ri.apps.googleusercontent.com'

gusto:
    api:
        url: 'https://api.gusto.com'
    oauth2:
        key: 'IZ1xQCj_rDzdVig-GCC_HLSnwBPxxxBU_4-1pXiJ41g'
        tokenUrl: 'https://api.gusto.com/oauth/token'

hrisBackgoundChecks:
    - socpilot.com
    - abnormalsecurity.com

hud:
    enabled: true

jira:
    oauth2:
        key: 'tNwZWb0ZC7Km2w7xcqFg6D1lEg7ZE0uB'
        redirect: 'https://app.drata.com/callback/jira'

jwt:
    cookie:
        domain: '.drata.com'
        webRefreshTokenKey: 'web-refresh-token-eu'
        siteAdminRefreshTokenKey: 'admin-refresh-token-eu'

karmacheck:
    api:
        url: 'https://api.karmacheck.io'
        webhookDomain: 'https://api.eu.drata.com'
    display:
        url: 'https://app.karmacheck.com/background_check/'

launchdarkly:
    log:
        output: 'error'
        level: 'warn'

lightrun:
    enabled: true

linear:
    oauth2:
        key: '6b5c129958ef100e31925fd47fe5a354'
        redirect: 'https://app.drata.com/callback/linear'

microsoft365GccHigh:
    oauth2:
        key: 'efc0a746-a5d2-440b-88c5-7b2612b3bac8'

microsoftTeams:
    oauth2:
        key: '8753e997-df17-410d-b59c-c8fadebe5929'

mtls:
    commonName: api-ap.int.euc1.prod.drata.net

office365:
    oauth2:
        key: '1736d532-556b-4889-b722-d014446cdb99'

opensearch:
    enable: true # setting to true for this env
    monitoring:
        indexerEnabled: true # enable the indexer which will automatically update the monitoring results index based upon monitoring related drata platform actions
    aws:
        region: eu-central-1
        node: https://opensearch.int.euc1.prod.drata.net
    client:
        keepAlive: true
        keepAliveMsecs: 5000
        maxFreeSockets: 50
        maxRetries: 3
        maxSockets: 100

pusher:
    appId: '*******'
    key: '022e434b6f9b4a12b6bf'

region:
    NA:
        api: https://api.drata.com
        adminApp: 'https://admin.drata.com'
    EU:
        api: https://api.eu.drata.com
        adminApp: 'https://admin.eu.drata.com'
    APAC:
        api: https://api.apac.drata.com
        adminApp: 'https://admin.apac.drata.com'

riskManagement:
    assessmentReport: 'dist/app/risk-management/docs/assessmentReportTemplate.docx'

riskAssessment:
    assessmentReport: 'dist/app/users/risk-assessment/docs/assessmentReportTemplate.docx'

redis:
    enable_ssl: true

rippling:
    api:
        url: 'https://api.rippling.com/platform/api'
    oauth2:
        key: 'f8A23oTAGFgffU8tFjY84FD1EkQoT0MuuBnT4P7T'
        tokenUrl: https://app.rippling.com/api/o/token/

safebase:
    sso:
        enabled: true
        jwtIssuer: 'https://api.eu.drata.com'
    api:
        baseUrl: https://app.safebase.io

salesforce:
    enabled: true
    setAccountIdUrl: 'https://hooks.zapier.com/hooks/catch/********/bwpllpp/'
    api:
        clientId: '3MVG9riCAn8HHkYWNkKgvtYHwJLwi.gBis6swbLXAajqbNZIqmI6S9d5X5NBvzThSGK4DlwIurNQizvQ3hk0e'
        audience: 'https://login.salesforce.com'
        username: '<EMAIL>'
        url: 'https://drata.lightning.force.com/lightning'
    connection:
        oauth2:
            redirect: https://app.drata.com/callback/salesforce

scannerUploader:
    s3:
        region: 'eu-central-1'
    sqs:
        region: 'eu-central-1'

segment:
    enabled: true

server:
    allowedCns:
        - 'orchestrator.int.euc1.prod.drata.net'

services:
    vendorDirectory:
        domain: 'https://vendors.service.drata.net'

slack:
    enabled: true
    oauth2:
        key: '*************.*************'

temporal:
    clusterAddress: 'eu-central-1.aws.api.temporal.io:7233'
    namespace: 'prod-euc1.wb2on'
    autopilot:
        queueName: 'autopilot_queue_prod'
    tracingEnabled: true

typeform:
    formIds:
        vendorRiskQuestionnaire: 'jsC7A4m5'
        riskAssessment:
            ENGINEERING: 'haaIqRBs'
            LEGAL: 'JkMddYe7'
            HR: 'KYJjLGZy'
            SECURITY: 'DbzMuou2'
            FINANCE: 'mAigotpo'
            SALES: 'V2mQKjTf'
    webhook:
        url: https://api.eu.drata.com

queue:
    assessmentCompleted:
        name: 'ap-prod-eu-central-1-api-ap2-queue'
        url: 'https://sqs.eu-central-1.amazonaws.com/************/ap-prod-eu-central-1-api-ap2-queue'
        region: 'eu-central-1'

url:
    publicApi: 'https://public-api.eu.drata.com'
    trustCenterPartnerApi: 'https://tc-api.eu.drata.com'
    webApp: 'https://app.drata.com'
    drataApp: 'https://app2.drata.com'
    adminApp: 'https://admin.eu.drata.com'
    appCdn: 'https://img-prod.eu.dratacdn.com'
    vendorHubApp: 'https://vendorhub.eu.drata.com'

vellum:
    enabled: true
    deployment:
        tprmQuestionnaireSummary: 'tprm-questionnaire-deployment-production'
        lightControlsRecommendationFromPolicy: 'policy-control-mapping-light-recommendation-production'

vetty:
    api:
        url: 'https://api.vetty.co'
    display:
        url: https://client.vetty.co/client/dashboard

workos:
    clientId: 'project_01F23C64KAFTW21CW0FDSW0KRD'
    siteAdminOrgId: 'org_01HSQ4CMC7YNR3J2Z4C5QJEEJT'
    redirectUri: 'https://api.eu.drata.com/auth/sso/callback'
    auditLogs:
        enabled: true

zoho:
    oauth2:
        key: 1000.0TZS523CS6X1IRRGFKQKPEGATYUH5K
        redirect: 'https://app.drata.com/callback/zoho'

zoom:
    oauth2:
        key: fRAAdBpfR4iGFvXvD1OkKQ
        redirect: https://app.drata.com/callback/zoom

complianceAsCode:
    s3:
        findingsBucket: ac-prod-finding-eu-central-1-bucket
        iacUploadBucket: ac-prod-metamodel-eu-central-1-bucket
    sqs:
        iacValidationStarted:
            name: 'LaunchValidationQueue'
            url: 'https://sqs.eu-central-1.amazonaws.com/079862243583/LaunchValidationQueue'
            region: 'eu-central-1'
            enabled: true

hubspot:
    oauth2:
        key: ************************************

# Time at which the tenant reminder workflow should run. Temporarily set for 1 am CTZ
remindersSchedule:
    hour: 7
    minute: 0
    timezone: Europe/Berlin

okta:
    siteAdmin:
        clientCredentials:
            issuer: 'https://drata.okta.com/oauth2/default'
            retool:
                clientId: '0oatclaznzUhLzkzJ697'

accountMigration:
    region: 'us-west-2'
    s3:
        bucketName: 'dt-dev-04-tenant-migration-us-west-2'

accountSnapshot:
    s3:
        bucketName: 'dt-dev-04-tenant-migration-us-west-2'

api:
    name: '[DEV-04] Drata'

asana:
    oauth2:
        redirect: 'https://app-04.dev.drata.com/callback/asana'

aws:
    eventbridge:
        enabled: true
        busName: 'dt-dev-04-drata-shared-eb-bus'
    runner:
        subnets:
            [
                'subnet-018d2281c01a797c0',
                'subnet-0a68e10a9e646ec0e',
                'subnet-0bde3018409fa9178',
                'subnet-0b318e55b819afd70',
            ]
        securityGroups: 'sg-0d39043d50d69d4e7'
        name: 'api'
        region: 'us-west-2'

azureDevops:
    boards:
        oauth2:
            redirect: 'https://app-04.dev.drata.com/callback/azuredevops-boards'
    repos:
        oauth2:
            redirect: 'https://app-04.dev.drata.com/callback/azuredevops-repos'
    devops:
        oauth2:
            redirect: 'https://app-04.dev.drata.com/callback/azuredevops'

bitbucket:
    oauth2:
        key: '9n8KK43pTgBeBw5ZMM'
    code:
        oauth2:
            key: 'jB2JgLTHPHDQrDAWXY'

checkr:
    oauth2:
        key: 'a708af968c6c9a37b972e48c'

clickup:
    oauth2:
        redirect: 'https://app-04.dev.drata.com/callback/clickup'

confluence:
    oauth2:
        key: 'ttURwW4l0kq3654B00mCP9pP0wPHKIOO'
        redirect: 'https://app-04.dev.drata.com/callback/confluence'

curricula:
    api:
        url: https://dev.curricula.com/api/v1
    oauth2:
        key: 'G4bZwgypl6NkL10zWEnzae23DrVd7xBv'
        redirect: 'https://app-04.dev.drata.com/callback/curricula'
        tokenUrl: 'https://dev.curricula.com/oauth/token'

datadog:
    enabled: true

db:
    ssl: true

digitalocean:
    oauth2:
        key: '****************************************************************'
        redirect: 'https://app-04.dev.drata.com/callback/digitalocean'

docusign:
    api:
        clientId: b06fcb28-d7d6-4f65-8575-a924cd49df33
        webhookBaseUrl: https://api-04.dev.drata.com
        ndaBaseUrl: https://appdemo.docusign.com/documents/details

email:
    disabled: false

github:
    versionControl:
        applicationId: 100937 # Drata [ACME] Free Org [DEV/TESTING]
        # applicationId: 117128 # Drata [ACME] Paid Org [DEV/TESTING]
    issues:
        applicationId: 100938 # Drata [ACME] Free Org [DEV/TESTING]
        # applicationId: 117130 # Drata [ACME] Paid Org [DEV/TESTING]
    code:
        applicationId: 821119 # Drata [ACME] Free Org [DEV/TESTING]

gitlab:
    oauth2:
        redirect: 'https://app-04.dev.drata.com/callback/gitlab'

gitlabIssues:
    oauth2:
        redirect: 'https://app-04.dev.drata.com/callback/gitlab-issues'

jira:
    oauth2:
        key: 'AtHaJyNGODx6edvDyluZB6dylsv76sHw'
        redirect: 'https://app-04.dev.drata.com/callback/jira'

jwt:
    cookie:
        domain: '.drata.com'
        webRefreshTokenKey: 'web-refresh-token-dev'
        siteAdminRefreshTokenKey: 'admin-refresh-token-dev'

karmacheck:
    api:
        webhookDomain: 'https://api-04.dev.drata.com'

linear:
    oauth2:
        redirect: 'https://app-04.dev.drata.com/callback/linear'

lightrun:
    enabled: false

mtls:
    commonName: api-04-ap.int.dev.drata.net

opensearch:
    enable: true # setting to true for this env
    monitoring:
        indexerEnabled: true # enable the indexer which will automatically update the monitoring results index based upon monitoring related drata platform actions
    aws:
        region: us-west-2
        node: https://opensearch.int.dev.drata.net
    client:
        keepAlive: true
        keepAliveMsecs: 5000
        maxFreeSockets: 50
        maxRetries: 3
        maxSockets: 100
        pingTimeout: 3000

riskManagement:
    assessmentReport: 'dist/app/risk-management/docs/assessmentReportTemplate.docx'

riskAssessment:
    assessmentReport: 'dist/app/users/risk-assessment/docs/assessmentReportTemplate.docx'

redis:
    enable_ssl: true

safebase:
    sso:
        jwtIssuer: 'https://api-04.dev.drata.com'

salesforce:
    api:
        url: 'https://drata--dratafull1.sandbox.lightning.force.com/lightning'
    connection:
        oauth2:
            redirect: /callback/salesforce

segment:
    #enabled temporarily for testing
    enabled: true

server:
    allowedCns:
        - 'orchestrator-04.int.dev.drata.net'

slack:
    enabled: true

queue:
    assessmentCompleted:
        name: 'ap-dev-us-west-2-api-ap2-04-queue'
        url: 'https://sqs.us-west-2.amazonaws.com/025187613910/ap-dev-us-west-2-api-ap2-04-queue'

temporal:
    clusterAddress: 'us-west-2.aws.api.temporal.io:7233'
    namespace: 'dev-04.wb2on'
    autopilot:
        queueName: 'autopilot_queue_dev_04'

typeform:
    webhook:
        url: https://api-04.dev.drata.com

url:
    publicApi: 'https://public-api-04.dev.drata.com'
    trustCenterPartnerApi: 'https://tc-api-04.dev.drata.com'
    webApp: 'https://app-04.dev.drata.com'
    drataApp: 'https://app-04-multiverse.dev.drata.com'
    adminApp: 'https://admin-04.dev.drata.com'
    appCdn: 'https://img-dev.dratacdn.com'
    vendorHubApp: 'https://vendorhub-04.dev.drata.com'

region:
    NA:
        api: https://api-04.dev.drata.com
        adminApp: https://admin-04.dev.drata.com

workos:
    redirectUri: 'https://api-04.dev.drata.com/auth/sso/callback'

zoho:
    oauth2:
        redirect: 'https://app-04.dev.drata.com/callback/zoho'

apideck:
    enabled: true
    api:
        key: 'sk_live_f8fe059e-8aad-451c-a319-2f69601eb29b-nUTsnRPtdgXlOyo9Dj4-098c23c8-00f3-45aa-95cf-95fd0f3fba13'
    app:
        id: 'L7Nb570iCq6MvAk6hT8l2EgjVC4xxjY9d6v6hUN'
    oauth:
        redirect: 'https://app-04.dev.drata.com/callback/cloud'

# Time at which the tenant reminder workflow should run. Temporarily set for 1 am CTZ
remindersSchedule:
    hour: 6
    minute: 0
    timezone: America/Chicago

aiAgent:
    api:
        baseUrl: 'https://agent-usw2-00.data-dev.drata.com'
